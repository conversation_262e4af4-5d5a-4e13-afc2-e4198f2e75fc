package main

import (
	"bytes"
	"fmt"
	"io"
	"net"
	"strings"
	"sync"

	"github.com/beego/beego/v2/core/logs"
)

var bufPool = &sync.Pool{
	New: func() interface{} {
		return make([]byte, 8*1024)
	},
}

var log = logs.GetBeeLogger()

func handleConnection(conn net.Conn) {
	defer conn.Close()
	buf := bytes.NewBuffer(nil)
	tmp := bufPool.Get().([]byte)
	defer bufPool.Put(tmp)
	for {
		n, err := conn.Read(tmp)
		if err != nil {
			if err == io.EOF {
				break
			}
			log.Error("Error reading:", err)
			break
		}
		buf.Write(tmp[:n])
		for {
			line, err := buf.ReadString('\n')
			if err == io.EOF {
				break
			}
			if err != nil {
				log.Error("Error reading line:", err)
				break
			}
			addr := conn.RemoteAddr().String()
			wt := strings.LastIndex(addr, ":")
			if wt > -1 {
				addr = addr[:wt]
			}
			// 解析日志级别和消息
			level, msg := parseLogLevel(line)
			msg = fmt.Sprintf("[%s] %s", addr, msg)

			// 使用对应级别打印
			switch level {
			case logs.LevelError:
				log.Error(msg)
			case logs.LevelWarning:
				log.Warn(msg)
			case logs.LevelDebug:
				log.Debug(msg)
			case logs.LevelAlert:
				log.Alert(msg)
			default:
				log.Info(msg)
			}
		}
		if buf.Len() > 1024 {
			buf.Reset()
		}
	}

}

/*
*
server.json配置  投递日志
conn配置参考结构体 connWriter

    "Log": {
      "conn": {
        "net": "tcp",
        "reconnectOnMsg": true,
        "reconnect": true,
        "addr": "127.0.0.1:6002"
      }
    }

*
*/

func main() {
	logs.Async()
	logs.RegisterFormatter("global", &CFormat{})
	logs.SetGlobalFormatter("global")
	// 单个文件最大500m
	// 最多存30天
	err := logs.SetLogger(logs.AdapterMultiFile,
		`{
        "filename": "app.log",
      "daily": true,
      "rotate": true,
      "prefix": "",
      "maxsize": 536870912,
      "maxDays": 30,
      "formatter": "global",
      "separate": [
        "error",
        "info",
        "warning"
      ]
    }`)

	if err != nil {
		panic(err)
	}

	listener, err := net.Listen("tcp", ":6002")
	if err != nil {
		panic(err)
	}

	for {
		conn, err := listener.Accept()
		if err != nil {
			log.Error("Error accepting connection:", err)
			continue
		}

		go handleConnection(conn)
	}
}

type CFormat struct {
}

func (C *CFormat) Format(lm *logs.LogMsg) string {
	return fmt.Sprintf("%s", lm.Msg)
}

func parseLogLevel(line string) (level int, msg string) {
	// 通过颜色码判断日志级别
	switch {
	case strings.Contains(line, "\u001B[97;41m"): // 红色背景
		return logs.LevelError, line
	case strings.Contains(line, "\u001B[97;43m"): // 黄色背景
		return logs.LevelWarning, line
	case strings.Contains(line, "\u001B[97;45m"): // 浅紫色背景
		return logs.LevelDebug, line
	case strings.Contains(line, "\u001B[97;42m"): // 绿色背景
		return logs.LevelAlert, line
	default:
		return logs.LevelInfo, line
	}
}
