const crypto = require('crypto');
const fs = require('fs');
// 读取输入参数
const args = process.argv.slice(2);
if (args.length !== 1) {
    console.log('输入错误');
    process.exit(1);
}

const path = args[0]
const data = fs.readFileSync(path, 'utf8');

const fy = JSON.stringify(JSON.parse(data), null, 2)

const hash = crypto.createHash('md5');
hash.update(fy);
const md5 = hash.digest('hex');
console.log(md5)