package main

import (
	"fmt"
	"testing"
	"train/base/script"
	"train/base/structs"
	comm "train/common"
	"train/db"
)

// go test -run TestGenPvpRobot
func TestGenPvpRobot(t *testing.T) {
	script.LoadSeverConfig()

	db.InitMongoDB(comm.GetSetting()["MongodbURL"].(string), comm.GetSetting()["MongodbDB"].(string))
	db.InitRedis(comm.GetSetting()["RedisURL"].(string), comm.GetSetting()["RedisPassword"].(string))
	structs.TestRobot(2)
	fmt.Println("TestGenPvpRobot:")
}
