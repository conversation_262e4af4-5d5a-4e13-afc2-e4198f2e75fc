[{"id": 101, "next": 102, "skip": 101, "gameTime": {"hour": 8, "min": 0}, "advancedAllow": false, "ignoreClose": "guide/OpenAnim", "logic": {"wait": "waitEvent", "waitArgs": ["GUIDE_OPEN_ANIM_END"]}, "mark": "OPEN_ANIM"}, {"id": 102, "next": 103, "skip": 106, "unlockFunc": "CURRENCY_1", "advancedAllow": false}, {"id": 103, "next": 104, "skip": 106, "unlockFunc": "CURRENCY_2", "advancedAllow": false}, {"id": 104, "next": 105, "skip": 106, "unlockFunc": "CURRENCY_3", "advancedAllow": false}, {"id": 105, "next": 106, "skip": 106, "advancedAllow": false}, {"id": 106, "next": 107, "skip": 110, "plotType": "PLOT", "plotKey": "guidePlot_key_1_1", "delayTime": 0, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 107, "next": 108, "skip": 110, "guide": {"location": "TRAIN_HEAD", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "repairTrain", "wait": "waitEvent", "waitArgs": ["GUIDE_REPAIR_TRAIN_END"]}, "mark": "REPAIR_TRAIN"}, {"id": 108, "next": 109, "skip": 110, "plotType": "PLOT", "plotKey": "guidePlot_key_1_2", "delayTime": 0, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 109, "next": 110, "skip": 110, "advancedAllow": false, "logic": {"call": "enterTrain", "wait": "waitEvent", "waitArgs": ["GUIDE_ENTER_TRAIN_END"]}, "mark": "ENTER_TRAIN"}, {"id": 110, "next": 201, "skip": 110, "plotType": "PLOT", "plotKey": "guidePlot_key_1_3", "guide": {"type": "HAND_B", "offset": {"y": -50}}, "mask": false, "delayTime": 0.2, "advancedAllow": false, "logic": {"call": "showPlotClickOne"}}, {"id": 201, "next": 202, "skip": 201, "unlockFunc": "EXPLORE", "advancedAllow": false}, {"id": 202, "next": 204, "skip": 201, "advancedAllow": false, "logic": {"call": "gotoPlanet", "wait": "waitEvent", "waitArgs": ["GUIDE_GOTO_PLANET_END"]}}, {"id": 203, "next": 205, "skip": 212, "guide": {"location": "BUTTON_EXPLORE", "type": "HAND", "offset": {"x": 32, "y": 27}}, "mask": false, "delayTime": 0.1, "advancedAllow": true, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 204, "next": 205, "skip": 204, "plotType": "GUIDE", "plotKey": "guidePlot_key_2_1", "guide": {"location": "BUTTON_LANDED", "type": "HAND"}, "mask": false, "delayTime": 0.15, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 16.72, "y": 265.501}}, "ignoreClose": "planet/planetMove|planet/PlanetTravel", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 205, "next": 206, "skip": 205, "plotType": "GUIDE", "plotKey": "guidePlot_key_2_2", "guide": {"location": "BUTTON_INTERACT", "type": "HAND", "offset": {"y": 20}}, "mask": false, "delayTime": 0.3, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": -73.199, "y": 270.3975}}, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PLANET_MINE_HP_CHANGE"], "check": "checkMine", "checkEvent": "REACH_PLANET_NODE"}}, {"id": 206, "next": 207, "skip": 206, "plotType": "GUIDE", "plotKey": "guidePlot_key_2_3", "guide": {"location": "BUTTON_INTERACT", "type": "HAND", "offset": {"y": 20}}, "mask": false, "delayTime": 3, "advancedAllow": true, "guider": {"type": 1005, "offset": {"x": -73.199, "y": 270.3975}}, "logic": {"call": "showClick", "wait": "waitPlanetNodeEnd", "waitArgs": ["1001-1-1"], "waitAct": "waitEvent", "waitActArgs": ["CLAIM_MINE_REWARD_START"]}}, {"id": 207, "next": 208, "skip": 206, "unlockFunc": "BAG", "advancedAllow": false, "logic": {"call": "showPlanetBag"}}, {"id": 208, "next": 209, "skip": 206, "advancedAllow": false, "logic": {"call": "stopCollect", "wait": "waitEvent", "waitArgs": ["CLAIM_MINE_REWARD_END"]}}, {"id": 209, "next": 210, "skip": 206, "plotType": "PLOT", "plotKey": "guidePlot_key_2_4", "delayTime": 0, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 210, "next": 211, "skip": 206, "advancedAllow": false, "logic": {"call": "startCollect"}}, {"id": 211, "next": 212, "skip": 211, "guide": {"location": "BUTTON_INTERACT", "type": "HAND", "offset": {"y": 20}}, "mask": false, "delayTime": 2, "advancedAllow": true, "logic": {"call": "showComboClick", "wait": "waitPlanetNodeEnd", "waitArgs": ["1001-1-2"]}}, {"id": 212, "next": 213, "skip": 212, "guide": {"location": "BUTTON_INTERACT", "type": "HAND", "offset": {"y": 20}}, "mask": false, "delayTime": 2, "advancedAllow": true, "logic": {"call": "showComboClick", "wait": "waitPlanetNodeEnd", "waitArgs": ["1001-1-3"]}}, {"id": 213, "next": 214, "skip": 212, "plotType": "PLOT", "plotKey": "guidePlot_key_2_5", "delayTime": 0, "advancedAllow": false, "logic": {"call": "showPlot", "check": "checkEvent", "checkEvent": "CLAIM_MINE_REWARD_END"}}, {"id": 214, "next": 215, "skip": 212, "unlockFunc": "PLANET_BACK", "advancedAllow": false, "logic": {"call": "showPlanetBack"}}, {"id": 215, "next": 301, "skip": 212, "guide": {"location": "BUTTON_BACK_1", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 301, "next": 302, "skip": 306, "gameTime": {"hour": 8, "min": 15}, "advancedAllow": false, "logic": {"call": "initRolePos", "check": "isMainScene", "checkEvent": "WIND_ENTER_303"}}, {"id": 302, "next": 303, "skip": 306, "plotType": "PLOT", "plotKey": "guidePlot_key_3_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 303, "next": 304, "skip": 306, "unlockFunc": "BUILD", "delayTime": 0.8, "advancedAllow": false, "logic": {"call": "unlockFunction"}}, {"id": 304, "next": 305, "skip": 306, "guide": {"location": "BUTTON_BUILD", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["SELECT_BUILD_ENTER"]}}, {"id": 305, "next": 306, "skip": 306, "guide": {"location": "TRAIN_ITEM_INDEX_1_UNLOCK", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "train/SelectBuild", "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["train/BuildDetail"]}}, {"id": 306, "next": 307, "skip": 306, "guide": {"location": "BUTTON_FIX", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "train/SelectBuild|train/BuildDetail", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["UNLOCK_BUILD"], "waitAct": "waitEvent", "waitActArgs": ["BUILD_OVER_TRAIN_ITEM"]}}, {"id": 307, "next": 308, "skip": 307, "plotType": "PLOT", "plotKey": "guidePlot_key_3_2", "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 308, "next": 309, "skip": 308, "advancedAllow": false, "logic": {"call": "role<PERSON>leep"}}, {"id": 309, "next": 310, "skip": 309, "plotType": "PLOT", "plotKey": "guidePlot_key_3_3", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 310, "next": 311, "skip": 309, "unlockFunc": "TASK", "advancedAllow": false}, {"id": 311, "next": 401, "skip": 309, "advancedAllow": false, "logic": {"call": "unlockFunctionTask"}}, {"id": 401, "next": 402, "skip": 413, "plotType": "PLOT", "plotKey": "guidePlot_key_4_1", "guide": {"location": "BUTTON_EXPLORE", "type": "HAND", "offset": {"x": 32, "y": 27}}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 391, "y": 344.9225}}, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 402, "next": 403, "skip": 402, "guide": {"location": "BUTTON_INTERACT", "type": "HAND", "offset": {"y": 20}}, "mask": false, "delayTime": 2, "advancedAllow": true, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PLANET_MINE_HP_CHANGE"], "check": "checkMine", "checkEvent": "REACH_PLANET_NODE"}}, {"id": 403, "next": 404, "skip": 411, "plotType": "PLOT", "plotKey": "guidePlot_key_4_2", "delayTime": 0, "advancedAllow": false, "logic": {"call": "showPlot", "check": "checkMonster", "checkEvent": "REACH_PLANET_NODE"}}, {"id": 404, "next": 405, "skip": 411, "advancedAllow": false}, {"id": 405, "next": 406, "skip": 411, "guide": {"location": "BUTTON_INTERACT", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"callReset": "resetTeam406", "call": "showClick", "wait": "waitPnl", "waitArgs": ["battle/BattleReady"]}}, {"id": 406, "next": 407, "skip": 407, "plotType": "GUIDE", "plotKey": "guidePlot_key_4_3", "guide": {"location": "CHARACTER_BATTLE_1005", "type": "HAND"}, "mask": false, "delayTime": 0.2, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 221.615, "y": 274}}, "ignoreClose": "battle/BattleReady", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["GUIDE_INTO_TEAM"]}}, {"id": 407, "next": 408, "skip": 411, "plotType": "GUIDE", "plotKey": "guidePlot_key_4_4", "guide": {"location": "BUTTON_START_BATTLE", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": -39.5, "y": 304}}, "ignoreClose": "battle/BattleReady", "logic": {"callReset": "resetAutoSpeed", "call": "showClick", "wait": "waitPnl", "waitArgs": ["battle/Battle"]}}, {"id": 408, "next": 409, "skip": 411, "ignoreClose": "battle/Battle", "logic": {"wait": "waitEvent", "waitArgs": ["BATTLE_SHOW_START_ROUND"]}}, {"id": 409, "next": 410, "skip": 411, "plotType": "GUIDE", "plotKey": "guidePlot_key_4_5", "guide": {"location": "BUTTON_START_ROUND", "type": "HAND"}, "mask": false, "delayTime": 0, "advancedAllow": false, "guider": {"type": 1005, "offset": {"y": 360}}, "ignoreClose": "battle/Battle", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["GUIDE_BATTLE_COLLIDE"]}}, {"id": 410, "next": 411, "skip": 411, "plotType": "GUIDE", "plotKey": "guidePlot_key_4_6", "guide": {"location": "BATTLE_GUIDER"}, "delayTime": 0.2, "advancedAllow": false, "guider": {"type": 1005, "offset": {"y": 360}}, "ignoreClose": "battle/Battle", "logic": {"call": "showBattleGuider", "wait": "waitEvent", "waitArgs": ["BATTLE_SHOW_START_ROUND"]}}, {"id": 411, "next": 412, "skip": 411, "plotType": "GUIDE", "plotKey": "guidePlot_key_4_7", "guide": {"location": "BUTTON_START_ROUND"}, "mask": false, "delayTime": 0, "advancedAllow": false, "guider": {"type": 1005, "offset": {"y": 360}}, "ignoreClose": "battle/Battle", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PLANET_NODE_SEVER_DIE"], "waitAct": "waitEventFreeTouch", "waitActArgs": ["PLANET_NODE_COMPLETE"]}}, {"id": 412, "next": 413, "skip": 413, "plotType": "PLOT", "plotKey": "guidePlot_key_4_8", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot", "check": "checkFood", "checkEvent": "TARGET_PLANET_NODE"}}, {"id": 413, "next": 414, "skip": 413, "guide": {"location": "CUR_PLANET_NODE", "type": "HAND"}, "mask": false, "delayTime": 0.3, "advancedAllow": true, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PLANET_NODE_SEVER_DIE"], "waitAct": "waitEventFreeTouch", "waitActArgs": ["PLANET_NODE_END"]}}, {"id": 414, "next": 415, "skip": 413, "plotType": "PLOT", "plotKey": "guidePlot_key_4_9", "delayTime": 0.25, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 415, "next": 501, "skip": 413, "guide": {"location": "BUTTON_BACK_1", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["common/UI"]}}, {"id": 501, "next": 502, "skip": 505, "gameTime": {"hour": 8, "min": 33}, "advancedAllow": false, "logic": {"call": "initRolePos2"}}, {"id": 502, "next": 503, "skip": 505, "plotType": "PLOT", "plotKey": "guidePlot_key_5_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 503, "next": 504, "skip": 505, "guide": {"location": "BUTTON_BUILD", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["SELECT_BUILD_ENTER"]}}, {"id": 504, "next": 505, "skip": 505, "guide": {"location": "TRAIN_ITEM_INDEX_1_UNLOCK", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "train/SelectBuild", "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["train/BuildDetail"]}}, {"id": 505, "next": 506, "skip": 505, "guide": {"location": "BUTTON_FIX", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "train/SelectBuild|train/BuildDetail", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["UNLOCK_BUILD"], "waitAct": "waitEvent", "waitActArgs": ["BUILD_OVER_TRAIN_ITEM"]}}, {"id": 506, "next": 507, "skip": 508, "plotType": "GUIDE", "plotKey": "guidePlot_key_5_2", "guide": {"location": "BUTTON_BUILD", "type": "HAND"}, "mask": false, "delayTime": 0.6, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": -31.4271, "y": 217.8731}}, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["SELECT_BUILD_ENTER"]}}, {"id": 507, "next": 508, "skip": 508, "guide": {"location": "TRAIN_ITEM_INDEX_1_UNLOCK", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "train/SelectBuild", "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["train/BuildDetail"]}}, {"id": 508, "next": 509, "skip": 508, "guide": {"location": "BUTTON_FIX", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "train/SelectBuild|train/BuildDetail", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["UNLOCK_BUILD"], "waitAct": "waitEvent", "waitActArgs": ["BUILD_OVER_TRAIN_ITEM"]}}, {"id": 509, "next": 510, "skip": 508, "advancedAllow": false, "logic": {"call": "roleEat"}}, {"id": 510, "next": 601, "skip": 508, "plotType": "PLOT", "plotKey": "guidePlot_key_5_3", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 601, "next": 602, "skip": 608, "guide": {"location": "BUTTON_EXPLORE", "type": "HAND", "offset": {"x": 32, "y": 27}}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 602, "next": 603, "skip": 602, "advancedAllow": false, "logic": {"waitAct": "waitEvent", "waitActArgs": ["GUIDE_CLOCK_DROP"], "check": "checkClockworkComplete", "checkEvent": "PLANET_NODE_COMPLETE"}}, {"id": 603, "next": 604, "skip": 602, "plotType": "PLOT", "plotKey": "guidePlot_key_6_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 604, "next": 605, "skip": 602, "guide": {"location": "CLOCKWORK", "type": "HAND"}, "mask": false, "delayTime": 0.3, "advancedAllow": true, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["planet/ItemUnlockPnl"]}}, {"id": 605, "next": 606, "skip": 608, "plotType": "PLOT", "plotKey": "guidePlot_key_6_2", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot", "check": "checkSaveCat", "checkEvent": "TARGET_PLANET_NODE"}}, {"id": 606, "next": 607, "skip": 608, "guide": {"location": "BUTTON_ITEM", "type": "HAND_B", "location_drag": "SAVE_CAT"}, "mask": false, "delayTime": 1.4, "advancedAllow": true, "logic": {"call": "showDrag", "wait": "waitEvent", "waitArgs": ["GUIDE_SAVE_CAT_END"]}}, {"id": 607, "next": 608, "skip": 608, "plotType": "PLOT", "plotKey": "guidePlot_key_6_3", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 608, "next": 609, "skip": 608, "advancedAllow": false, "logic": {"call": "unlockSaveCat"}}, {"id": 609, "next": 701, "skip": 608, "guide": {"location": "BUTTON_BACK_1", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["common/UI"], "check": "checkSaveCatEnd", "checkEvent": "PLANET_NODE_END"}}, {"id": 701, "next": 702, "skip": 707, "gameTime": {"hour": 8, "min": 53}, "advancedAllow": false, "logic": {"callReset": "resetDorm1Camera", "call": "dogSitIdle"}}, {"id": 702, "next": 703, "skip": 702, "unlockFunc": "CHECKIN_DORM_1", "delayTime": 0.8, "advancedAllow": false, "logic": {"call": "unlockFunction"}}, {"id": 703, "next": 704, "skip": 706, "plotType": "GUIDE", "plotKey": "guidePlot_key_7_1", "guide": {"location": "BUTTON_CHECKIN_DORM_1", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": -100, "y": -200}}, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["role/RoleCheckInPnl"]}}, {"id": 704, "next": 705, "skip": 706, "advancedAllow": false}, {"id": 705, "next": 706, "skip": 706, "advancedAllow": false}, {"id": 706, "next": 707, "skip": 706, "plotType": "GUIDE", "plotKey": "guidePlot_key_7_2", "guide": {"location": "CHECKIN_DORM_1_1006", "type": "HAND", "offset": {"y": -30}}, "mask": false, "delayTime": 0.4, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 540, "y": 135}}, "ignoreClose": "role/RoleCheckInPnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PASSENGER_CHECK_IN"]}, "mark": "BUILD_BED_START"}, {"id": 707, "next": 801, "skip": 707, "advancedAllow": false, "logic": {"call": "roleCheckIn"}}, {"id": 801, "next": 802, "skip": 807, "gameTime": {"hour": 8, "min": 54}, "advancedAllow": false, "logic": {"call": "dogCatIdle"}}, {"id": 802, "next": 803, "skip": 802, "unlockFunc": "STAR", "advancedAllow": false}, {"id": 803, "next": 804, "skip": 803, "plotType": "PLOT", "plotKey": "guidePlot_key_8_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 804, "next": 805, "skip": 804, "plotType": "GUIDE", "plotKey": "guidePlot_key_8_2", "guide": {"location": "UI_CURRENCY_STAR", "type": "ARROWS", "direction": "TOP", "offset": {"x": 0, "y": -50}}, "mask": false, "delayTime": 0, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 0, "y": -285}}, "logic": {"call": "showGuider"}}, {"id": 805, "next": 806, "skip": 806, "unlockFunc": "OUTPUT", "logic": {"call": "showOutput"}}, {"id": 806, "next": 807, "skip": 806, "unlockFunc": "SPEED_UP", "advancedAllow": false, "logic": {"call": "unlockFunction"}}, {"id": 807, "next": 901, "skip": 807, "plotType": "PLOT", "plotKey": "guidePlot_key_8_3", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 901, "next": 902, "skip": 903, "advancedAllow": false, "logic": {"call": "dogCatIdle"}}, {"id": 902, "next": 903, "skip": 902, "plotType": "GUIDE", "plotKey": "guidePlot_key_9_1", "guide": {"location": "BUTTON_BUILD"}, "mask": false, "delayTime": 0.3, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": -248.3937, "y": 217.8731}}, "logic": {"call": "showGuider", "check": "checkCanUnlockRightBed", "checkEvent": "UPDATE_STARDUST"}}, {"id": 903, "next": 904, "skip": 903, "advancedAllow": false, "logic": {"call": "roleSleep2", "check": "checkUnlockRightBed", "checkEvent": "BUILD_OVER_TRAIN_ITEM"}}, {"id": 904, "next": 1001, "skip": 904, "plotType": "PLOT", "plotKey": "guidePlot_key_9_2", "delayTime": 1, "advancedAllow": false, "logic": {"call": "showPlot", "wait": "dogFreedom"}, "mark": "BUILD_BED_END"}, {"id": 1001, "next": 1003, "skip": 1001, "advancedAllow": false, "logic": {"callReset": "resetTeam1006", "check": "checkMonster", "checkEvent": "REACH_PLANET_NODE"}}, {"id": 1002, "next": 1003, "skip": 1013, "guide": {"location": "BUTTON_EXPLORE", "type": "HAND", "offset": {"x": 32, "y": 27}}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"callReset": "resetTeam1006", "call": "showClick", "check": "checkInMainAndNoPassSchoolMonster2", "checkEvent": "WIND_ENTER_303"}}, {"id": 1003, "next": 1004, "skip": 1005, "advancedAllow": false, "ignoreClose": "battle/BattleReady", "logic": {"call": "ignoreSelectRoleNode2", "check": "checkBattleReady", "checkEvent": "PNL_ENTER_210"}}, {"id": 1004, "next": 1005, "skip": 1005, "plotType": "PLOT", "plotKey": "guidePlot_key_10_1", "delayTime": 0.5, "advancedAllow": false, "ignoreClose": "battle/BattleReady", "logic": {"call": "showPlot"}}, {"id": 1005, "next": 1006, "skip": 1005, "advancedAllow": false, "ignoreClose": "battle/BattleReady", "logic": {"call": "showSelectRoleNode2"}}, {"id": 1006, "next": 1007, "skip": 1007, "guide": {"location": "CHARACTER_BATTLE_1006", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "battle/BattleReady", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["GUIDE_INTO_TEAM"], "check": "checkBattleReady", "checkEvent": "PNL_ENTER_210"}}, {"id": 1007, "next": 1008, "skip": 1012, "guide": {"location": "BUTTON_START_BATTLE", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "battle/BattleReady", "logic": {"callReset": "resetAutoSpeed", "call": "showClick", "wait": "waitPnl", "waitArgs": ["battle/Battle"], "check": "checkBattleReady", "checkEvent": "PNL_ENTER_210"}}, {"id": 1008, "next": 1009, "skip": 1012, "ignoreClose": "battle/Battle", "logic": {"wait": "waitEvent", "waitArgs": ["BATTLE_SHOW_START_ROUND"]}}, {"id": 1009, "next": 1010, "skip": 1012, "guide": {"location": "BUTTON_START_ROUND", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "battle/Battle", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["BATTLE_ROUND_START"]}}, {"id": 1010, "next": 1011, "skip": 1012, "plotType": "GUIDE", "plotKey": "guidePlot_key_10_2", "guide": {"location": "BATTLE_GUIDER"}, "delayTime": 0, "advancedAllow": false, "guider": {"type": 1005, "offset": {"y": 360}}, "ignoreClose": "battle/Battle", "logic": {"call": "showBattleGuider", "check": "checkBattle2001Death", "checkEvent": "GUIDE_BATTLE_DEATH"}}, {"id": 1011, "next": 1012, "skip": 1012, "plotType": "GUIDE", "plotKey": "guidePlot_key_10_3", "guide": {"location": "BATTLE_GUIDER"}, "delayTime": 0, "advancedAllow": false, "guider": {"type": 1005, "offset": {"y": 360}}, "ignoreClose": "battle/Battle", "logic": {"call": "showBattleGuider", "check": "checkEvent", "checkEvent": "GUIDE_BATTLE_SHOW_SKILL_TIPS"}}, {"id": 1012, "next": 1013, "skip": 1012, "advancedAllow": false, "ignoreClose": "battle/Battle", "logic": {"call": "freeTouch", "wait": "waitEvent", "waitArgs": ["PLANET_NODE_SEVER_DIE"]}}, {"id": 1013, "next": 1101, "skip": 1013, "plotType": "PLOT", "plotKey": "guidePlot_key_10_4", "advancedAllow": false, "logic": {"call": "showPlot", "check": "checkTreeHouse", "checkEvent": "GUIDE_REACH_TREE_HOUSE"}}, {"id": 1101, "next": 1103, "skip": 1101, "advancedAllow": false}, {"id": 1102, "next": 1103, "skip": 1105, "guide": {"location": "BUTTON_EXPLORE", "type": "HAND", "offset": {"x": 32, "y": 27}}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 1103, "next": 1104, "skip": 1104, "advancedAllow": false, "logic": {"call": "showGuideTask", "check": "checkTreeHouse", "checkEvent": "GUIDE_REACH_TREE_HOUSE"}}, {"id": 1104, "next": 1105, "skip": 1104, "guide": {"location": "PLANET_1_STAIRS", "type": "HAND", "offset": {"x": -95, "y": -198}}, "mask": false, "delayTime": 3.5, "advancedAllow": true, "ignoreClose": "guide/GuideTaskPnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PLANET_NODE_SEVER_DIE"], "check": "checkTreeHouse", "checkEvent": "GUIDE_REACH_TREE_HOUSE"}}, {"id": 1105, "next": 1201, "skip": 1105, "advancedAllow": false}, {"id": 1201, "next": 1202, "skip": 1201, "unlockFunc": "STAR_MAP", "advancedAllow": false, "logic": {"call": "unlockFunction", "check": "checkInMainCompleteSchool", "checkEvent": "WIND_ENTER_303"}}, {"id": 1202, "next": 1203, "skip": 1205, "guide": {"location": "STAR_MAP", "type": "HAND", "offset": {"x": 32, "y": 27}}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["planet/PlanetChoose"]}}, {"id": 1203, "next": 1204, "skip": 1203}, {"id": 1204, "next": 1205, "skip": 1205, "guide": {"location": "PLANET_INDEX_2", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "planet/PlanetChoose", "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["planet/PlanetEnter"]}}, {"id": 1205, "next": 1401, "skip": 1205, "guide": {"location": "BUTTON_GOTO", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "planet/PlanetChoose|planet/PlanetEnter", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["BACK_SELECT_PLANET_PNL"]}, "mark": "MOVE_PLANET_2_START"}, {"id": 1301, "next": 1302, "skip": 1301, "plotType": "GUIDE", "plotKey": "guidePlot_key_13_1", "guide": {"location": "BUTTON_RECEIVE", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 600}}, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["TASK_COMPLETE"], "check": "checkTaskDetailHeadBuild", "checkEvent": "TASK_DETAIL_ENTER"}}, {"id": 1302, "skip": 1301, "advancedAllow": false, "logic": {"call": "showBuild1015_1_1"}}, {"id": 1401, "skip": 1401, "plotType": "GUIDE", "plotKey": "guidePlot_key_14_1", "guide": {"location": "BUTTON_EXPLORE", "offset": {"x": 32, "y": 27}}, "delayTime": 0.3, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 391, "y": 344.9225}}, "logic": {"call": "showGuider", "check": "checkReachGarden", "checkEvent": "REACH_PLANET"}, "mark": "MOVE_PLANET_2_END"}, {"id": 1501, "next": 1502, "skip": 1501, "plotType": "PLOT", "plotKey": "guidePlot_key_15_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlotAndStopCollect", "check": "checkGardenMine", "checkEvent": "REACH_PLANET_NODE|GARDEN_FIRST_LAND_OVER"}}, {"id": 1502, "next": 1503, "skip": 1505, "plotType": "PLOT", "plotKey": "guidePlot_key_15_2", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot", "check": "checkMinePART", "checkEvent": "HERO_COLLECT_NOEFFECT"}}, {"id": 1503, "next": 1504, "skip": 1505, "unlockFunc": "TOOL", "advancedAllow": false, "logic": {"call": "unlockFunction"}}, {"id": 1504, "next": 1505, "skip": 1505, "plotType": "GUIDE", "plotKey": "guidePlot_key_15_3", "guide": {"location": "BUTTON_TOOL", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 200, "y": 200}}, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["tool/ToolMake"]}}, {"id": 1505, "next": 1506, "skip": 1505, "guide": {"location": "BUTTON_TOOL_BUILD", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "tool/ToolMakePnl", "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["tool/ToolMakeResultPnl"]}}, {"id": 1506, "next": 1507, "skip": 1505, "ignoreClose": "tool/ToolMakePnl|tool/ToolMakeResultPnl", "logic": {"call": "freeTouch", "wait": "waitPnlClose", "waitArgs": ["tool/ToolMakeResultPnl"]}}, {"id": 1507, "next": 1508, "skip": 1505, "plotType": "PLOT", "plotKey": "guidePlot_key_15_4", "guide": {"location": "BUTTON_TOOLLIST_3"}, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "tool/ToolMakePnl", "logic": {"call": "showPlot"}}, {"id": 1508, "next": 1601, "skip": 1505, "guide": {"location": "BUTTON_BACK_6", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "tool/ToolMakePnl", "logic": {"call": "showClick", "wait": "waitPnlClose", "waitArgs": ["tool/ToolMakePnl"]}}, {"id": 1601, "next": 1602, "skip": 1608, "advancedAllow": false}, {"id": 1602, "next": 1603, "skip": 1602, "logic": {"check": "checkBattleFailPnl_Garden1", "checkEvent": "PNL_ENTER_210"}}, {"id": 1603, "next": 1604, "skip": 1602, "plotType": "PLOT", "plotKey": "guidePlot_key_16_1", "delayTime": 0.5, "ignoreClose": "battle/Battle|battle/BattleResult", "logic": {"call": "showPlot"}}, {"id": 1604, "next": 1605, "skip": 1602, "guide": {"location": "BUTTON_BACK_7", "type": "HAND"}, "mask": false, "delayTime": 0.1, "ignoreClose": "battle/Battle|battle/BattleResult", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 1605, "next": 1606, "skip": 1605, "unlockFunc": "CHARACTER", "delayTime": 0.8, "advancedAllow": false, "logic": {"call": "unlockFunction"}}, {"id": 1606, "next": 1607, "skip": 1608, "guide": {"location": "BUTTON_CHARACTER", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["role/Role"], "check": "isMainScene", "checkEvent": "WIND_ENTER_303"}}, {"id": 1607, "next": 1608, "skip": 1607, "plotType": "GUIDE", "plotKey": "guidePlot_key_16_2", "guide": {"location": "BUTTON_LEVELUP", "type": "HAND"}, "delayTime": 0.3, "advancedAllow": true, "ignoreClose": "role/RolePnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PASSENGER_LEVEL_UP"]}}, {"id": 1608, "next": 1609, "skip": 1608, "plotType": "GUIDE", "plotKey": "guidePlot_key_16_3", "guide": {"location": "BUTTON_LEVELUP", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": true, "guider": {"type": 1005, "offset": {"x": 73.498, "y": 206.167}}, "ignoreClose": "role/RolePnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PASSENGER_LEVEL_UP"]}}, {"id": 1609, "skip": 1608, "plotType": "PLOT", "plotKey": "guidePlot_key_16_4", "delayTime": 0.5, "advancedAllow": false, "ignoreClose": "role/RolePnl", "logic": {"call": "showPlot"}}, {"id": 1701, "next": 1702, "skip": 1701, "advancedAllow": false, "logic": {"call": "roleStopThink", "check": "checkCurveChaseEnd", "checkEvent": "PLANET_NODE_END"}}, {"id": 1702, "next": 1703, "skip": 1701, "plotType": "PLOT", "plotKey": "guidePlot_key_17_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 1703, "next": 1704, "skip": 1701, "guide": {"location": "BUTTON_BACK_1", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 1704, "next": 1705, "skip": 1704, "unlockFunc": "LOTTERY", "delayTime": 0.8, "advancedAllow": false, "logic": {"call": "unlockFunction", "check": "isMainScene", "checkEvent": "WIND_ENTER_303"}}, {"id": 1705, "next": 1706, "skip": 1705, "unlockFunc": "SHOP", "advancedAllow": false}, {"id": 1706, "next": 1707, "skip": 1707, "guide": {"location": "BUTTON_LOTTERY", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["jackpot/JackPot"]}}, {"id": 1707, "next": 1708, "skip": 1707, "guide": {"location": "BUTTON_LOTTERY_ONCE", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "jackpot/JackPotPnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["JACKPOT_SUCCESS"]}}, {"id": 1708, "next": 1709, "skip": 1707, "guide": {"location": "BLANK_1", "type": "HAND"}, "mask": false, "delayTime": 2, "advancedAllow": true, "ignoreClose": "jackpot/JackPotPnl", "logic": {"call": "freeTouch", "wait": "waitEvent", "waitArgs": ["GUIDE_JACKPOT_END2"]}}, {"id": 1709, "next": 1710, "skip": 1707, "plotType": "PLOT", "plotKey": "guidePlot_key_17_2", "delayTime": 0.5, "advancedAllow": false, "ignoreClose": "jackpot/JackPotPnl", "logic": {"call": "showPlot"}}, {"id": 1710, "skip": 1707, "guide": {"location": "BLANK_1", "type": "HAND"}, "mask": false, "delayTime": 2, "advancedAllow": true, "ignoreClose": "jackpot/JackPotPnl", "logic": {"call": "freeTouch", "wait": "waitEvent", "waitArgs": ["GUIDE_JACKPOT_END4"]}}, {"id": 1901, "next": 1902, "skip": 1903, "advancedAllow": false, "logic": {"check": "checkNoteBook", "checkEvent": "TARGET_PLANET_NODE"}}, {"id": 1902, "next": 1903, "skip": 1902, "plotType": "PLOT", "plotKey": "guidePlot_key_19_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 1903, "skip": 1903, "guide": {"location": "NOTE_BOOK", "type": "HAND"}, "mask": false, "delayTime": 0.3, "advancedAllow": true, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PLANET_NODE_SEVER_DIE"]}}, {"id": 2001, "next": 2002, "skip": 2001, "plotType": "PLOT", "plotKey": "guidePlot_key_20_1", "guide": {"location": "TRAIN_ITEM_INDEX_1_LEVELUP"}, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 43, "y": 322}}, "logic": {"call": "showPlot", "check": "checkCanGuideTrainInPlanet", "checkEvent": "PLANET_NODE_COMPLETE"}}, {"id": 2002, "next": 2003, "skip": 2001, "guide": {"location": "BUTTON_BACK_1", "type": "HAND"}, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 2003, "next": 2004, "skip": 2006, "unlockFunc": "TRAIN", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "unlockFunction", "check": "checkCanGuideTrainInMain", "checkEvent": "WIND_ENTER_303"}}, {"id": 2004, "next": 2005, "skip": 2006, "guide": {"location": "BUTTON_TRAIN", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["train/TrainConsolePnl"]}}, {"id": 2005, "next": 2006, "skip": 2006, "guide": {"location": "TRAIN_INDEX_2", "type": "HAND", "offset": {"y": -50}}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "train/TrainConsolePnl", "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["train/CarriageDetailPnl"]}}, {"id": 2006, "next": 2007, "skip": 2006, "guide": {"location": "BUTTON_BUY", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "train/TrainConsolePnl|train/CarriageDetailPnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["SHOW_CARRIAGECOST_TIPS"], "waitAct": "waitEvent", "waitActArgs": ["FOCUS_CARRIAGE_END"]}}, {"id": 2007, "next": 2008, "skip": 2006, "plotType": "GUIDE", "plotKey": "guidePlot_key_20_3", "guide": {"location": "SEPECIAL_ENGINE"}, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 0, "y": 392}}, "logic": {"call": "showGuider"}}, {"id": 2008, "next": 2009, "skip": 2009, "logic": {"call": "showFocusCarriage", "callArgs": [1016], "check": "checkEngineBuildEnd", "checkEvent": "WIND_ENTER_303|CARRIAGE_BUILD_END"}}, {"id": 2009, "next": 2010, "skip": 2009, "plotType": "GUIDE", "plotKey": "guidePlot_key_20_4", "guide": {"location": "SEPECIAL_CATDOOR_ENGINE", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 0, "y": 270}}, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["CREATE_NEW_CARRIAGE"], "waitAct": "waitEvent", "waitActArgs": ["TRAIN_BUILD_OVER"]}}, {"id": 2010, "skip": 2009, "plotType": "PLOT", "plotKey": "guidePlot_key_20_5", "guide": {"location": "BUTTON_BUILD_ENGINE"}, "mask": false, "delayTime": 0.75, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 2101, "skip": 2101, "unlockFunc": "ACHIEVEMENT", "advancedAllow": false, "logic": {"call": "unlockFunction", "check": "checkInMainAndUnlockAchievement", "checkEvent": "WIND_ENTER_303"}}, {"id": 2201, "next": 2202, "skip": 2203, "logic": {"call": "showFocusCarriage", "callArgs": [1016], "check": "checkUnlockEnginePowerInMain", "checkEvent": "BUILD_OVER_TRAIN_ITEM|WIND_ENTER_303"}}, {"id": 2202, "next": 2203, "skip": 2203, "plotType": "PLOT", "plotKey": "guidePlot_key_22_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 2203, "skip": 2203, "unlockFunc": "WORK_ENGINE", "logic": {"call": "unlockFunction"}}, {"id": 2301, "next": 2302, "skip": 2306, "advancedAllow": false, "logic": {"check": "checkInMainAndUnlockResonance", "checkEvent": "UNLOCK_FUNTION"}}, {"id": 2302, "next": 2303, "skip": 2306, "plotType": "PLOT", "plotKey": "guidePlot_key_23_1", "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 2303, "next": 2304, "skip": 2306, "guide": {"location": "BUTTON_CHARACTER", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["role/RolePnl"]}}, {"id": 2304, "next": 2305, "skip": 2306, "guide": {"location": "CAN_RESONANCE_ROLE", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "role/RolePnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["ROLE_PNL_ON_SELECT"]}}, {"id": 2305, "next": 2306, "skip": 2306, "guide": {"location": "BUTTON_RESONANCE", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "role/RolePnl", "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["resonance/ResonanceChangePnl"]}}, {"id": 2306, "skip": 2306, "guide": {"location": "RESONANCE_YES", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "role/RolePnl|resonance/ResonanceChangePnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["SET_RESONANCE"]}}, {"id": 2401, "next": 2402, "skip": 2402, "plotType": "GUIDE", "plotKey": "guidePlot_key_24_1", "guide": {"location": "TRANS_LIST_1", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 600, "y": 100}}, "ignoreClose": "role/RolePnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["ROLE_PNL_ON_SELECT"], "check": "checkOpenTransPnl", "checkEvent": "ROLE_PNL_CHANGE_TAB"}}, {"id": 2402, "skip": 2402, "plotType": "GUIDE", "plotKey": "guidePlot_key_24_2", "guide": {"location": "TRANS"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 0, "y": -200}}, "ignoreClose": "role/RolePnl", "logic": {"call": "showGuider"}}, {"id": 2501, "next": 2502, "skip": 2504, "advancedAllow": false, "logic": {"check": "firstEnterMachinePlanet", "checkEvent": "WIND_ENTER_303|PLANET_LAND_ANIM_END"}}, {"id": 2502, "next": 2503, "skip": 2504, "logic": {"call": "showFirstEnterMachinePlanet"}}, {"id": 2503, "next": 2504, "skip": 2504, "plotType": "PLOT", "plotKey": "guidePlot_key_25_1", "delayTime": 0.2, "logic": {"call": "showPlot"}}, {"id": 2504, "next": 2505, "skip": 2504, "plotType": "GUIDE", "plotKey": "guidePlot_key_25_2", "guide": {"location": "PLANET_AREA", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 600, "y": 0}}, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["planetEntry/PlanetAreaPnl"]}}, {"id": 2505, "next": 2801, "skip": 2504, "guide": {"location": "PLANET_AREA_1", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "planetEntry/PlanetAreaPnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 2601, "next": 2602, "skip": 2601, "guide": {"location": "CUR_PLANET_NODE", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["PLANET_NODE_SEVER_DIE"], "check": "checkBlackHoleKey", "checkEvent": "TARGET_PLANET_NODE"}}, {"id": 2602, "next": 2603, "skip": 2601, "ignoreClose": "planet/ItemUnlockPnl", "logic": {"call": "freeTouch", "wait": "waitEvent", "waitArgs": ["ADD_PLANET_ITEM"]}}, {"id": 2603, "skip": 2601, "plotType": "PLOT", "plotKey": "guidePlot_key_26_1", "delayTime": 0.5, "logic": {"call": "showPlot"}}, {"id": 4701, "next": 4702, "skip": 4701, "logic": {"check": "checkEvent", "checkEvent": "GUIDE_START_UNLOCK_BLACK_HOLE"}}, {"id": 4702, "next": 4703, "skip": 4701, "unlockFunc": "PLAY_BLACKHOLE", "logic": {"call": "unlockPlay"}}, {"id": 4703, "next": 4704, "skip": 4701, "plotType": "PLOT", "plotKey": "guidePlot_key_47_1", "logic": {"call": "showPlot"}}, {"id": 4704, "skip": 4701, "guide": {"location": "BLACK_HOLE_ENTRY", "type": "HAND"}, "delayTime": 1.5, "advancedAllow": true, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["GUIDE_END_UNLOCK_BLACK_HOLE"]}}, {"id": 4801, "next": 4802, "skip": 4801, "logic": {"check": "checkEnterBlackHole", "checkEvent": "PNL_ENTER_210"}}, {"id": 4802, "skip": 4801, "plotType": "PLOT", "plotKey": "guidePlot_key_48_1", "ignoreClose": "blackHole/BlackHolePnl|blackHole/BlackHoleReadyPnl", "logic": {"call": "showPlot"}}, {"id": 2801, "next": 2802, "skip": 2802, "advancedAllow": false, "logic": {"check": "secondEnterMachinePlanet", "checkEvent": "WIND_ENTER_303"}}, {"id": 2802, "skip": 2802, "plotType": "PLOT", "plotKey": "guidePlot_key_28_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 2901, "next": 2902, "skip": 2901, "advancedAllow": false, "logic": {"call": "stopCollect", "check": "checkUnlockTower", "checkEvent": "PLANET_NODE_COMPLETE"}}, {"id": 2902, "next": 2903, "skip": 2901, "plotType": "PLOT", "plotKey": "guidePlot_key_29_1", "logic": {"call": "showPlot"}}, {"id": 2903, "skip": 2901, "plotType": "GUIDE", "plotKey": "guidePlot_key_29_2", "guide": {"location": "BUTTON_BACK_8", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1005, "offset": {"x": 300, "y": 150}}, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 4901, "next": 4902, "skip": 4903, "logic": {"check": "checkEvent", "checkEvent": "GUIDE_UNLOCK_TOWER"}}, {"id": 4902, "next": 4903, "skip": 4903, "plotType": "PLOT", "plotKey": "guidePlot_key_49_1", "logic": {"call": "showPlot"}}, {"id": 4903, "skip": 4903, "unlockFunc": "PLAY_TOWER", "logic": {"call": "unlockPlay"}}, {"id": 2701, "next": 2702, "skip": 2704, "logic": {"check": "checkUnlockDeepExplore", "checkEvent": "WIND_ENTER_303"}}, {"id": 2702, "next": 2703, "skip": 2704, "logic": {"call": "showPnl", "callArgs": ["daily_task/DailyTaskCallPnl"]}}, {"id": 2703, "next": 2704, "skip": 2704, "ignoreClose": "daily_task/DailyTaskCallPnl", "logic": {"call": "freeTouch", "wait": "waitPnlClose", "waitArgs": ["daily_task/DailyTaskCallPnl"]}}, {"id": 2704, "next": 4301, "skip": 2704, "plotType": "PLOT", "plotKey": "guidePlot_key_27_1", "delayTime": 0.5, "logic": {"call": "showPlot"}}, {"id": 4301, "next": 4302, "skip": 4302, "priority": -100, "advancedAllow": false, "logic": {"check": "checkCanDeepExplore", "checkEvent": "WIND_ENTER_303|PLANET_LAND_ANIM_END"}}, {"id": 4302, "next": 4303, "skip": 4302, "unlockFunc": "DEEP_EXPLORE", "logic": {"call": "unlockPlay"}}, {"id": 4303, "skip": 4302, "plotType": "PLOT", "plotKey": "guidePlot_key_43_1", "logic": {"call": "showPlot"}}, {"id": 5001, "next": 5002, "skip": 5002, "logic": {"check": "checkEnterDeepExploreReady", "checkEvent": "PNL_ENTER_210"}}, {"id": 5002, "skip": 5002, "plotType": "PLOT", "plotKey": "guidePlot_key_50_1", "delayTime": 0.5, "ignoreClose": "planetEntry/PlanetAreaDeepPnl|planetEntry/DeepReadyPnl", "logic": {"call": "showPlot"}}, {"id": 4401, "next": 4402, "skip": 4401, "advancedAllow": false, "logic": {"check": "checkDeepExploreStart", "checkEvent": "PNL_ENTER_210"}}, {"id": 4402, "next": 4501, "skip": 4401, "plotType": "PLOT", "plotKey": "guidePlot_key_44_1", "delayTime": 0.5, "advancedAllow": false, "ignoreClose": "planetEntry/DeepExplorePnl", "logic": {"call": "showPlot"}, "mark": "DEEP_EXPLORE_START"}, {"id": 4501, "next": 4502, "skip": 4501, "advancedAllow": false, "logic": {"check": "checkDeepExploreEnd", "checkEvent": "CLOSE_PNL_204"}}, {"id": 4502, "next": 4503, "skip": 4501, "plotType": "PLOT", "plotKey": "guidePlot_key_45_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}, "mark": "DEEP_EXPLORE_END"}, {"id": 4503, "next": 4504, "skip": 4501, "guide": {"location": "BUTTON_BACK_1", "type": "HAND"}, "delayTime": 0.1, "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["WIND_ENTER_303"]}}, {"id": 4504, "skip": 4504, "unlockFunc": "PROFILE", "logic": {"call": "unlockFunction"}}, {"id": 3101, "next": 3102, "skip": 3108, "advancedAllow": false, "logic": {"check": "checkCanUnlockDailyTask", "checkEvent": "WIND_ENTER_303"}}, {"id": 3102, "next": 3103, "skip": 3104, "plotType": "PLOT", "plotKey": "guidePlot_key_31_1", "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 3103, "next": 3104, "skip": 3104, "unlockFunc": "PLAY_DAILY_TASK", "advancedAllow": false, "logic": {"call": "unlockFunction"}}, {"id": 3104, "next": 3105, "skip": 3104, "plotType": "GUIDE", "plotKey": "guidePlot_key_31_2", "guide": {"location": "BUTTON_DAILY_TASK", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "guider": {"type": 1015, "offset": {"x": 600, "y": 0}}, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["daily_task/DailyTaskPnl"]}}, {"id": 3105, "next": 3106, "skip": 3104, "advancedAllow": false, "ignoreClose": "daily_task/DailyTaskPnl", "logic": {"call": "freeTouch", "wait": "waitPnlClose", "waitArgs": ["daily_task/DailyTaskPnl"]}}, {"id": 3106, "next": 3107, "skip": 3108, "plotType": "PLOT", "plotKey": "guidePlot_key_31_3", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 3107, "next": 3108, "skip": 3108, "advancedAllow": false, "logic": {"call": "focusDailyTaskRole"}}, {"id": 3108, "skip": 3108, "guide": {"location": "DAILY_TASK_ROLE", "type": "HAND"}, "mask": false, "delayTime": 0.1, "logic": {"call": "showClick", "wait": "waitPnl", "waitArgs": ["collect/CollectTaskPnl"]}}, {"id": 4601, "next": 4602, "skip": 4601, "advancedAllow": false, "logic": {"check": "checkUnlockCollect", "checkEvent": "WIND_ENTER_303|PLANET_LAND_ANIM_END"}}, {"id": 4602, "next": 4603, "skip": 4601, "plotType": "PLOT", "plotKey": "guidePlot_key_46_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 4603, "skip": 4601, "unlockFunc": "PLAY_COLLECT", "logic": {"call": "unlockPlay"}}, {"id": 3001, "next": 3002, "skip": 3003, "advancedAllow": false, "logic": {"check": "firstEnterPlanet_1009", "checkEvent": "WIND_ENTER_303|PLANET_LAND_ANIM_END"}}, {"id": 3002, "next": 3003, "skip": 3003, "logic": {"call": "sendEvent", "callArgs": ["GUIDE_FIRST_ENTER_PLANET_1009_COME"], "wait": "waitEvent", "waitArgs": ["GUIDE_FIRST_ENTER_PLANET_1009_COME_END"]}}, {"id": 3003, "next": 3004, "skip": 3003, "plotType": "PLOT", "plotKey": "guidePlot_key_30_1", "delayTime": 0.5, "advancedAllow": false, "logic": {"call": "showPlot"}}, {"id": 3004, "skip": 3003, "logic": {"call": "sendEvent", "callArgs": ["GUIDE_FIRST_ENTER_PLANET_1009_LEAVE"], "wait": "waitEvent", "waitArgs": ["GUIDE_FIRST_ENTER_PLANET_1009_LEAVE_END"]}, "mark": "FIRST_ENTER_PLANET_1009"}, {"id": 3801, "next": 3802, "skip": 3801, "advancedAllow": false, "logic": {"check": "checkCompleteMole", "checkEvent": "PLANET_NODE_COMPLETE"}}, {"id": 3802, "skip": 3801, "plotType": "PLOT", "plotKey": "guidePlot_key_38_2", "delayTime": 0.5, "logic": {"call": "showPlot"}}, {"id": 5101, "next": 5102, "skip": 5104, "logic": {"check": "checkCanUnlockOre", "checkEvent": "PNL_ENTER_210"}}, {"id": 5102, "next": 5103, "skip": 5104, "plotType": "PLOT", "plotKey": "guidePlot_key_51_1", "delayTime": 0.7, "logic": {"call": "showPlot"}}, {"id": 5103, "next": 5104, "skip": 5104, "logic": {"call": "sendEvent", "callArgs": ["GUDIE_MOVE_TO_ORE"], "wait": "waitEvent", "waitArgs": ["GUDIE_MOVE_TO_ORE_END"]}}, {"id": 5104, "skip": 5104, "guide": {"type": "TOUCH_MOVE", "offset": {"x": 652}}, "logic": {"call": "showTouchMove", "wait": "waitEvent", "waitArgs": ["GUIDE_TOUCH_MOVE_END"]}, "mark": "START_ORE_PUZZLE"}, {"id": 3201, "next": 3202, "skip": 3202, "advancedAllow": false, "ignoreClose": "ore/OreMakePnl", "logic": {"check": "checkEnterMakeEquipPnl", "checkEvent": "PNL_ENTER_210"}}, {"id": 3202, "next": 3203, "skip": 3202, "guide": {"location": "BUTTON_MAKE_EQUIP", "type": "HAND"}, "ignoreClose": "ore/OreMakePnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["ORE_EQUIP_MAKE"]}}, {"id": 3203, "next": 3204, "skip": 3202, "advancedAllow": false, "ignoreClose": "ore/OreMakePnl|ore/OreMakeResultPnl", "logic": {"call": "freeTouch", "wait": "waitPnl", "waitArgs": ["ore/OreMakeComparePnl"]}}, {"id": 3204, "next": 3301, "skip": 3202, "guide": {"location": "BUTTON_WEAR_EQUIP", "type": "HAND"}, "mask": false, "delayTime": 0.1, "advancedAllow": false, "ignoreClose": "ore/OreMakePnl|ore/OreMakeComparePnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["BATTLE_EQUIP_CHANGE"]}}, {"id": 3301, "next": 3302, "skip": 3303, "advancedAllow": false, "logic": {"check": "checkCanMakeNextEquip", "checkEvent": "PNL_ENTER_210"}, "mark": "MAKE_NEXT_EQUIP"}, {"id": 3302, "next": 3303, "skip": 3303, "plotType": "PLOT", "plotKey": "guidePlot_key_33_1", "delayTime": 0.3, "ignoreClose": "ore/OreMakePnl", "logic": {"call": "showPlot"}}, {"id": 3303, "skip": 3303, "plotType": "GUIDE", "plotKey": "guidePlot_key_33_2", "guide": {"location": "BUTTON_NEXT_QUALITY_EQUIP", "type": "HAND"}, "mask": false, "delayTime": 0.1, "guider": {"type": 1005, "offset": {"x": -261, "y": -200}}, "ignoreClose": "ore/OreMakePnl", "logic": {"call": "showClick", "wait": "waitEvent", "waitArgs": ["GUIDE_NEXT_QUALITY_EQUIP"]}}, {"id": 3401, "next": 3402, "skip": 3401, "advancedAllow": false, "logic": {"check": "firstEnterPlanet_1007", "checkEvent": "WIND_ENTER_303|PLANET_LAND_ANIM_END"}}, {"id": 3402, "skip": 3401, "plotType": "PLOT", "plotKey": "guidePlot_key_34_1", "delayTime": 0.5, "logic": {"call": "showPlot"}}, {"id": 5301, "next": 5302, "skip": 5303, "logic": {"check": "checkEvent", "checkEvent": "GUIDE_UNLOCK_TRANSPORT"}}, {"id": 5302, "next": 5303, "skip": 5303, "plotType": "PLOT", "plotKey": "guidePlot_key_53_1", "logic": {"call": "showPlot"}}, {"id": 5303, "skip": 5303, "unlockFunc": "PLAY_TRANSPORT", "logic": {"call": "unlockPlay"}}, {"id": 3501, "next": 3502, "skip": 3501, "advancedAllow": false, "ignoreClose": "transport/TransportPnl", "logic": {"check": "checkEnterTransportPnl", "checkEvent": "PNL_ENTER_210"}}, {"id": 3502, "skip": 3501, "plotType": "PLOT", "plotKey": "guidePlot_key_35_1", "delayTime": 0.5, "guider": {"type": 1005, "offset": {"x": 80, "y": -100}}, "ignoreClose": "transport/TransportPnl", "logic": {"call": "showPlot"}}, {"id": 3601, "next": 3602, "skip": 3601, "advancedAllow": false, "logic": {"call": "stopCollect", "check": "checkCanUnlockSpaceStoneBranch", "checkEvent": "PLANET_NODE_COMPLETE"}}, {"id": 3602, "next": 3603, "skip": 3601, "logic": {"call": "showAbnormal1"}, "mark": "START_SPACE_STONE"}, {"id": 3603, "next": 3604, "skip": 3601, "plotType": "PLOT", "plotKey": "guidePlot_key_36_1", "logic": {"call": "showPlot"}}, {"id": 3604, "skip": 3601, "logic": {"call": "startCollect"}}, {"id": 5401, "next": 5402, "skip": 5403, "logic": {"check": "checkEvent", "checkEvent": "GUIDE_UNLOCK_SPACE_STONE"}}, {"id": 5402, "next": 5403, "skip": 5403, "logic": {"call": "showAbnormal2"}}, {"id": 5403, "skip": 5403, "unlockFunc": "PLAY_SPACE_STONE", "logic": {"call": "unlockPlay"}}, {"id": 3901, "next": 3902, "skip": 3901, "advancedAllow": false, "logic": {"check": "firstEnterPlanet_1008", "checkEvent": "WIND_ENTER_303|PLANET_LAND_ANIM_END"}}, {"id": 3902, "skip": 3901, "plotType": "PLOT", "plotKey": "guidePlot_key_39_1", "logic": {"call": "showPlot"}}, {"id": 4001, "next": 4002, "skip": 4001, "advancedAllow": false, "logic": {"check": "checkStarFragEnd", "checkEvent": "PLANET_NODE_COMPLETE"}}, {"id": 4002, "skip": 4001, "plotType": "PLOT", "plotKey": "guidePlot_key_40_1", "logic": {"call": "showPlot"}}, {"id": 4101, "next": 4102, "skip": 4101, "advancedAllow": false, "logic": {"check": "checkEnterInstancePnl", "checkEvent": "PNL_ENTER_210"}}, {"id": 4102, "skip": 4101, "plotType": "PLOT", "plotKey": "guidePlot_key_41_1", "guide": {"location": "INSTANCE_LEVEL", "type": "HAND"}, "delayTime": 0.3, "ignoreClose": "instance/InstanceLevelPnl", "logic": {"call": "showPlot"}}, {"id": 4201, "next": 4202, "skip": 4201, "advancedAllow": false, "logic": {"check": "checkCanClaimInstanceReward", "checkEvent": "CLOSE_PNL_204|PNL_ENTER_210"}}, {"id": 4202, "skip": 4201, "plotType": "PLOT", "plotKey": "guidePlot_key_42_1", "delayTime": 0.3, "guider": {"type": 1005, "offset": {"x": 200, "y": 300}}, "ignoreClose": "instance/InstanceLevelPnl", "logic": {"call": "showPlot"}}]