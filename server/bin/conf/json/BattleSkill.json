[{"id": 1001, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 11001, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 2}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.4}]}, {"id": 1002, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 1}]}, {"id": 11002, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MIN_HP", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.5}]}, {"id": 1003, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 3}], "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "HP", "valueType": "INT", "value": 1}]}, {"id": 11003, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 3}], "effect": [{"type": "BUFF"}]}, {"id": 111003, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.15}]}, {"id": 1004, "contentValue": [1], "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MAX_HP", "camp": "ENEMY", "count": 3}], "effect": [{"type": "CHANGE_DAMAGE", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 11004, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MAX_HP", "camp": "ENEMY", "count": 3}], "effect": [{"type": "BUFF"}]}, {"id": 111004, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SAME_BUFF", "camp": "TEAMMATE", "count": 3}], "effect": [{"type": "CHANGE_DAMAGE", "buff": {"repeat": -1, "times": 1}, "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.2}]}, {"id": 1005, "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "SENDER", "count": 1}], "effect": [{"type": "BUFF"}]}, {"id": 101005, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SENDER", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 11005, "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "SENDER", "count": 1}], "effect": [{"type": "CHANGE_DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.4}]}, {"id": 1006, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.7}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.7}]}, {"id": 11006, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "BUFF"}]}, {"id": 111006, "trigger": {"type": "ATTACK"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 2}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 1.2}]}, {"id": 1007, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "TAIL_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 11007, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "TAIL_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "TURE_DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 1008, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "ENEMY", "count": 1}], "effect": [{"type": "ATTACK", "valueType": "INT", "value": 1}, {"type": "HP", "valueType": "INT", "value": 1}, {"type": "BUFF"}]}, {"id": 101008, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SENDER", "count": 1}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "HP", "value": 0.2}]}, {"id": 11008, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "ENEMY", "count": 1}], "effect": [{"type": "BUFF"}]}, {"id": 111008, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SENDER", "count": 1}], "effect": [{"type": "HP_GAIN_BUFF", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "HP", "value": 0.3}]}, {"id": 1009, "trigger": {"type": "HIT", "object": "SELF"}, "object": [{"type": "ALL", "camp": "TEAMMATE"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 11009, "contentValue": [1], "trigger": {"type": "HIT", "object": "SELF"}, "object": [{"type": "SENDER", "camp": "ENEMY", "count": 1}], "effect": [{"type": "ATTACK_BUFF", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}]}, {"id": 1010, "trigger": {"type": "DEATH", "object": "FRONT", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.5}]}, {"id": 11010, "trigger": {"type": "DEATH", "object": "FRONT", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "SELF"}], "times": 1, "effect": [{"type": "TRIGGER"}]}, {"id": 111010, "trigger": {"type": "LIVE", "object": "ALL", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "HP", "value": 0.4}]}, {"id": 1011, "contentValue": [1], "trigger": {"type": "BATTLE_START"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "ATTACK_BUFF", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.5}]}, {"id": 11011, "contentValue": [2], "trigger": {"type": "BATTLE_START"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 2}], "effect": [{"type": "ATTACK_BUFF", "buff": {"times": 2}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}]}, {"id": 1012, "trigger": {"type": "KILL", "object": "SELF"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.4}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.2}]}, {"id": 11012, "trigger": {"type": "KILL", "object": "SELF"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.1}]}, {"id": 1013, "trigger": {"type": "HIT", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "RECIPIENT", "camp": "TEAMMATE", "count": 1}, {"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.35}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.35}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ORG_HP", "value": -0.3, "objectIndex": 1}]}, {"id": 11013, "trigger": {"type": "HIT", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "RECIPIENT", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "BUFF"}]}, {"id": 111013, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BUFFER", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.17}]}, {"id": 1014, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SENDER", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 1.1}]}, {"id": 11014, "trigger": {"type": "DEATH_KILL", "object": "SELF"}, "object": [{"type": "REBIRTH"}], "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "HP", "valueType": "INT", "value": 1}, {"type": "CHANGE_SKILL", "valueType": "PER", "perAttributeType": "DAMAGE", "value": -0.5}]}, {"id": 1015, "trigger": {"type": "SUMMON", "object": "ALL", "camp": "ENEMY"}, "object": [{"type": "MIN_HP_ROLE", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.7}]}, {"id": 11015, "trigger": {"type": "SUMMON", "object": "ALL", "camp": "ENEMY"}, "object": [{"type": "MIN_HP_ROLE", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "rateType": "REV_COUNT", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.2}]}, {"id": 1016, "contentValue": [3], "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "SUMMON_MUMMY", "camp": "TEAMMATE", "count": 1}], "times": 3, "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.2}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.2}]}, {"id": 11016, "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "SUMMON_MUMMY", "camp": "TEAMMATE", "count": 1}], "times": 3, "effect": [{"type": "CHANGE_ATTACK", "rateType": "USE_COUNT", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.1}, {"type": "CHANGE_HP", "rateType": "USE_COUNT", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.1}]}, {"id": 1017, "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "TAIL_INDEX", "camp": "ENEMY", "count": 1}], "roundTimes": 1, "effect": [{"type": "MOVE_POSITION", "valueType": "INT", "value": 2}, {"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 11017, "contentValue": [1], "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "TAIL_INDEX", "camp": "ENEMY", "count": 1}], "roundTimes": 1, "effect": [{"type": "ATTACK_BUFF", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.25}]}, {"id": 1019, "contentValue": [2, 1], "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "cd": 2, "effect": [{"type": "CHANGE_DAMAGE", "buff": {"rounds": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 11019, "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "cd": 2, "effect": [{"type": "ATTACK_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}, {"type": "HP_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}]}, {"id": 1020, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "FRONT", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 11020, "contentValue": [2], "trigger": {"type": "BATTLE_START"}, "object": [{"type": "FRONT", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "BUFF", "buff": {"times": 2}}]}, {"id": 111020, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.2}]}, {"id": 1021, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MAX_HP", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 11021, "contentValue": [2], "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MAX_HP", "camp": "ENEMY", "count": 1}], "effect": [{"type": "BUFF", "buff": {"times": 2}}]}, {"id": 111021, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.1}, {"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.2}]}, {"id": 1022, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MIN_HP", "camp": "ENEMY", "count": 3}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 11022, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MIN_HP", "camp": "ENEMY", "count": 3}], "effect": [{"type": "HP_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}]}, {"id": 1023, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 2}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 11023, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 2}], "effect": [{"type": "ATTACK_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.2}, {"type": "HP_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.2}]}, {"id": 1024, "contentValue": [1], "trigger": {"type": "DEATH", "object": "FRONT", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "SELF"}], "effect": [{"type": "IMMUNE_DAMAGE", "buff": {"repeat": -1, "times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 2}, {"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 11024, "trigger": {"type": "DEATH", "object": "FRONT", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "SELF"}], "effect": [{"type": "BUFF", "buff": {"dep": "IMMUNE_DAMAGE"}}]}, {"id": 111024, "trigger": {"type": "ATTACKED", "object": "SELF"}, "object": [{"type": "SENDER"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "RECIPIENT", "perAttributeType": "ATTACK", "value": 0.2}, {"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.2}]}, {"id": 1025, "trigger": {"type": "HIT", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "RECIPIENT", "camp": "TEAMMATE", "count": 1}, {"type": "SELF"}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ORG_HP", "value": -0.3, "objectIndex": 1}]}, {"id": 11025, "contentValue": [1], "trigger": {"type": "HIT", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "RECIPIENT", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "IMMUNE_DAMAGE", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.15}]}, {"id": 1026, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "HEAD", "camp": "ENEMY", "count": 2}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 11026, "contentValue": [1], "trigger": {"type": "BATTLE_START"}, "object": [{"type": "HEAD", "camp": "ENEMY", "count": 2}], "effect": [{"type": "BUFF", "buff": {"times": 1, "delayRounds": 1}}]}, {"id": 111026, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1027, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "ALL", "camp": "ENEMY"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 11027, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "ALL", "camp": "ENEMY"}], "effect": [{"type": "ATTACK_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}]}, {"id": 1028, "contentValue": [1], "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 3}], "effect": [{"type": "IMMUNE_DAMAGE", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 11028, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 3}], "effect": [{"type": "ATTACK_BUFF", "buff": {"dep": "IMMUNE_DAMAGE"}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 1029, "trigger": {"type": "ATTACK_AFTER", "object": "FRONT", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.5}]}, {"id": 11029, "trigger": {"type": "ATTACK_AFTER", "object": "FRONT", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "SELF"}], "effect": [{"type": "TRIGGER"}]}, {"id": 111029, "trigger": {"type": "KILL", "object": "FRONT", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.25}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "HP", "value": 0.25}]}, {"id": 1030, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "ALL", "camp": "TEAMMATE"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1031, "trigger": {"type": "HIT", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.4}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.4}]}, {"id": 11031, "trigger": {"type": "HIT", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "BUFF"}]}, {"id": 111031, "contentValue": [1], "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BUFFER", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "IMMUNE_DAMAGE", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "HP", "value": 0.3}]}, {"id": 1032, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "ALL", "camp": "TEAMMATE"}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1033, "trigger": {"type": "HIT", "object": "SELF"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 11033, "trigger": {"type": "HIT", "object": "SELF"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HIT", "value": 0.5}, {"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1034, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "HEAD_INDEX", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 11034, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "HEAD_INDEX", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "BUFF"}]}, {"id": 111034, "contentValue": [1], "trigger": {"type": "KILL", "object": "SELF"}, "object": [{"type": "SELF", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "ATTACK_BUFF", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.6}]}, {"id": 1035, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.8}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.8}]}, {"id": 11035, "contentValue": [2], "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "BUFF", "buff": {"times": 2}}]}, {"id": 111035, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "HP", "value": 0.2}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.2}]}, {"id": 1036, "trigger": {"type": "ATTACK_AFTER", "object": "FRONT", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 11036, "trigger": {"type": "ATTACK_AFTER", "object": "FRONT", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "CHANGE_DAMAGE", "buff": {"repeat": -1, "activeObject": "SENDER"}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.2}]}, {"id": 1037, "trigger": {"type": "ATTACK_BEFORE", "object": "SELF"}, "object": [{"type": "MIN_HP", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.75}]}, {"id": 11037, "trigger": {"type": "ATTACK_BEFORE", "object": "SELF"}, "object": [{"type": "MIN_HP", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1038, "trigger": {"type": "SUMMON", "object": "ALL", "camp": "ENEMY"}, "object": [{"type": "RECIPIENT", "camp": "ENEMY"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 11038, "contentValue": [2], "trigger": {"type": "SUMMON", "object": "ALL", "camp": "ENEMY"}, "object": [{"type": "RECIPIENT", "camp": "ENEMY"}], "effect": [{"type": "BUFF", "buff": {"times": 2}}]}, {"id": 111038, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.2}]}, {"id": 1039, "trigger": {"type": "SUMMON", "object": "ALL", "camp": "ENEMY"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.5}]}, {"id": 11039, "trigger": {"type": "SUMMON", "object": "ALL", "camp": "ENEMY"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "rateType": "REV_COUNT", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.2}, {"type": "CHANGE_HP", "rateType": "REV_COUNT", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.2}]}, {"id": 1040, "trigger": {"type": "HIT", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 11040, "trigger": {"type": "HIT", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 2}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HIT", "value": 0.1}]}, {"id": 1041, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 2}], "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.8}]}, {"id": 11041, "contentValue": [2], "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 2}], "effect": [{"type": "BUFF", "buff": {"times": 2}}]}, {"id": 111041, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.3}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "HP", "value": 0.3}]}, {"id": 1042, "trigger": {"type": "ATTACK_AFTER"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 1}, {"type": "SELF"}], "effect": [{"type": "DAMAGE", "valueType": "INT", "perAttributeType": "HP", "value": 1}, {"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5, "objectIndex": 1}]}, {"id": 11042, "contentValue": [1], "trigger": {"type": "ATTACK_AFTER"}, "object": [{"type": "SELF"}], "effect": [{"type": "IMMUNE_DAMAGE", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1043, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "HP", "valueType": "PER", "perObjectType": "MAX_HP", "perAttributeType": "HP", "value": 0.8}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 1}]}, {"id": 11043, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "ATTACK_GAIN_DEBUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}, {"type": "HP_GAIN_DEBUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}]}, {"id": 1044, "trigger": {"type": "ATTACK_AFTER_AHEAD"}, "object": [{"type": "RANDOM", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1045, "trigger": {"type": "DEATH_ANY"}, "object": [{"type": "RANDOM", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1046, "trigger": {"type": "HIT"}, "object": [{"type": "RANDOM", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1047, "trigger": {"type": "SUMMON_ANY"}, "object": [{"type": "RECIPIENT", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1048, "trigger": {"type": "ATTACK_BEFORE"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": -0.2}]}, {"id": 1049, "trigger": {"type": "ATTACK_AFTER"}, "object": [{"type": "RANDOM", "camp": "ENEMY", "count": 3}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1050, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "RANDOM"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 1051, "trigger": {"type": "HIT"}, "object": [{"type": "RANDOM", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 2008, "trigger": {"type": "HIT", "object": "SELF"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 2060, "trigger": {"type": "HP_REDUCE", "object": "SELF", "count": 15}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 1}], "times": 6, "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.15}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.15}]}, {"id": 2061, "trigger": {"type": "USE_SKILL", "object": "ALL", "camp": "ENEMY"}, "object": [{"type": "SENDER"}], "effect": [{"type": "COPY"}]}, {"id": 2063, "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "ALL", "camp": "ENEMY"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.25}]}, {"id": 2064, "trigger": {"type": "HIT", "object": "ALL", "camp": "SELF_TEAMMATE"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.25}]}, {"id": 12064, "trigger": {"type": "SKILL_KILL", "object": "SELF"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 2100, "trigger": {"type": "HIT", "object": "ALL", "camp": "SELF_TEAMMATE"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.25}]}, {"id": 12100, "trigger": {"type": "SKILL_KILL", "object": "SELF"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}, {"value": 0}]}, {"id": 2101, "contentValue": [2], "trigger": {"type": "POS_HEAD"}, "object": [{"type": "SELF"}], "effect": [{"type": "BUFF", "buff": {"delayRounds": 2}}]}, {"id": 102101, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "RUN_AWAY"}]}, {"id": 2065, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 1}, {"type": "SUMMON", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.5}, {"type": "SUMMONN_ID", "value": 3002}, {"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5, "objectIndex": 1}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.5, "objectIndex": 1}, {"type": "SUMMONN_ID", "value": 3001, "objectIndex": 1}]}, {"id": 12065, "trigger": {"type": "NONE"}}, {"id": 3001, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 2}], "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.5}, {"type": "SUMMONN_ID", "value": 3003}]}, {"id": 3002, "trigger": {"type": "SUMMON", "object": "ALL", "camp": "ENEMY"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 2}], "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 0.5}, {"type": "SUMMONN_ID", "value": 3003}]}, {"id": 2066, "trigger": {"type": "HP_GT", "object": "SELF", "count": 50}, "object": [{"type": "SELF"}], "effect": [{"type": "ATTACK_BUFF", "buff": {"dep": "TRIGGER"}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 2}]}, {"id": 2067, "trigger": {"type": "KILL", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "SENDER"}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 2068, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MAX_HP", "camp": "ENEMY", "count": 2}], "effect": [{"type": "BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 102068, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 1}, {"type": "FRONT", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.5}, {"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.5, "objectIndex": 1}]}, {"id": 12068, "contentValue": [3], "trigger": {"type": "NEAR_DEATH", "object": "SELF"}, "object": [{"type": "SELF"}], "times": 1, "effect": [{"type": "IMMUNE_DAMAGE", "buff": {"times": 3}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "HP", "value": 1}]}, {"id": 2079, "trigger": {"type": "KILL", "object": "SELF"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.8}]}, {"id": 12079, "contentValue": [2], "trigger": {"type": "KILL", "object": "SELF"}, "object": [{"type": "HEAD", "camp": "ENEMY", "count": 2}], "effect": [{"type": "BUFF", "buff": {"times": 2}}]}, {"id": 112079, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.4}]}, {"id": 2080, "trigger": {"type": "DEATH", "object": "SUMMON", "camp": "TEAMMATE"}, "object": [{"type": "REBIRTH_SENDER"}], "times": 2, "effect": [{"type": "REBIRTH", "roleTimes": 1}]}, {"id": 12080, "contentValue": [1], "trigger": {"type": "DEATH", "object": "SUMMON", "camp": "TEAMMATE"}, "object": [{"type": "REBIRTH_SENDER"}], "times": 2, "effect": [{"type": "IMMUNE_DAMAGE", "roleTimes": 1, "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.7}]}, {"id": 2071, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 12071, "trigger": {"type": "DEATH", "object": "ALL", "camp": "ENEMY"}, "object": [{"type": "SUMMON", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 2}, {"type": "HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 3}]}, {"id": 2085, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SENDER"}], "effect": [{"type": "BUFF"}]}, {"id": 102085, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 12085, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "SENDER"}], "effect": [{"type": "BUFF"}]}, {"id": 112085, "trigger": {"type": "DEATH", "object": "SELF"}, "object": [{"type": "BEHIND", "camp": "TEAMMATE", "count": 1}], "effect": [{"type": "USE_BUFFER_SKILL"}]}, {"id": 2073, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MAX_HP", "camp": "ENEMY", "count": 1}, {"type": "MAX_ATTACK", "camp": "ENEMY", "count": 1}], "effect": [{"type": "CHANGE_DAMAGE", "buff": {"repeat": -1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}, {"type": "CHANGE_ATTACK", "buff": {"repeat": -1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -1, "objectIndex": 1}]}, {"id": 12073, "contentValue": [30], "trigger": {"type": "HP_LTE", "object": "SELF", "count": 30}, "object": [{"type": "MAX_HP", "camp": "ENEMY", "count": 1}, {"type": "MAX_ATTACK", "camp": "ENEMY", "count": 1}], "times": 1, "effect": [{"type": "CHANGE_DAMAGE", "buff": {"repeat": -1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}, {"type": "CHANGE_ATTACK", "buff": {"times": 1, "repeat": -1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -1, "objectIndex": 1}]}, {"id": 2034, "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "TAIL_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "CHANGE_POSITION", "valueType": "INT", "value": 1}, {"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 12034, "contentValue": [1], "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "TAIL_INDEX", "camp": "ENEMY", "count": 1}], "roundTimes": 1, "effect": [{"type": "ATTACK_BUFF", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.25}]}, {"id": 2005, "contentValue": [0, 0], "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "CHANGE_DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 12005, "trigger": {"type": "DEATH", "object": "ALL", "camp": "TEAMMATE"}, "object": [{"type": "HEAD_INDEX", "camp": "ENEMY", "count": 1}], "effect": [{"type": "ATTACK_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}, {"type": "HP_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.2}]}, {"id": 2010, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MIN_HP", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.75}]}, {"id": 12010, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "MIN_HP", "camp": "ENEMY", "count": 1}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}]