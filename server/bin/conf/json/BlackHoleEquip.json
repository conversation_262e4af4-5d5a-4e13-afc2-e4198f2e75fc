[{"id": 1001, "content": "content_blackHoleItem_1001", "icon": "tech_icon_1001", "weight": 10, "target": 2, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 1002, "content": "content_blackHoleItem_1002", "icon": "tech_icon_1002", "weight": 10, "target": 1, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 1003, "content": "content_blackHoleItem_1003", "icon": "tech_icon_1003", "weight": 10, "target": 3, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1004, "content": "content_blackHoleItem_1004", "icon": "tech_icon_1004", "weight": 10, "target": 2, "trigger": {"type": "ATTACK_AFTER", "object": "ANIMAL_TYPE", "camp": "TEAMMATE"}, "object": [{"type": "RECIPIENT"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1.2}], "load": 2, "ready": {"func": "playSkillReady_Tech1004"}}, {"id": 1005, "content": "content_blackHoleItem_1005", "icon": "tech_icon_1005", "weight": 10, "target": 3, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "ALL", "camp": "ENEMY"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.8}], "load": 1, "ready": {"func": "playSkillReady_Tech1005"}}, {"id": 1006, "content": "content_blackHoleItem_1006", "icon": "tech_icon_1006", "weight": 10, "target": 2, "contentValue": [1], "trigger": {"type": "BATTLE_BEFORE"}, "object": [{"type": "ANIMAL_TYPE", "camp": "TEAMMATE"}], "effect": [{"type": "IMMUNE_DAMAGE", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.6}, {"type": "BUFF", "buff": {"dep": "IMMUNE_DAMAGE"}}], "load": 1, "addBuff": {"func": "playAddBuff_Tech"}, "buffMountPoint": "<PERSON><PERSON><PERSON>"}, {"id": 101006, "content": "content_blackHoleItem_1006", "icon": "tech_icon_1006", "target": 2, "trigger": {"type": "ATTACKED", "object": "SELF"}, "object": [{"type": "SENDER"}], "effect": [{"type": "DAMAGE", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.6}]}, {"id": 1007, "content": "content_blackHoleItem_1007", "icon": "tech_icon_1007", "weight": 10, "target": 2, "contentValue": [1], "trigger": {"type": "BATTLE_BEFORE"}, "object": [{"type": "ANIMAL_TYPE", "camp": "TEAMMATE"}], "effect": [{"type": "IMMUNE_DAMAGE", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1.2}], "load": 1, "addBuff": {"func": "playAddBuff_Tech"}, "buffMountPoint": "<PERSON><PERSON><PERSON>"}, {"id": 1008, "content": "content_blackHoleItem_1008", "icon": "tech_icon_1008", "weight": 10, "target": 3, "contentValue": [1], "trigger": {"type": "BATTLE_BEFORE"}, "object": [{"type": "ALL", "camp": "TEAMMATE"}], "effect": [{"type": "IMMUNE_DAMAGE", "buff": {"times": 1}, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.6}], "load": 1, "addBuff": {"func": "playAddBuff_Tech"}, "buffMountPoint": "<PERSON><PERSON><PERSON>"}, {"id": 1009, "content": "content_blackHoleItem_1009", "icon": "tech_icon_1009", "weight": 10, "target": 2, "contentValue": [1], "trigger": {"type": "HIT", "object": "ANIMAL_TYPE", "camp": "TEAMMATE"}, "object": [{"type": "RECIPIENT"}], "effect": [{"type": "CHANGE_HP", "roleTimes": 1, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1.1}], "load": 1, "skill": {"func": "playSkillCurve"}}, {"id": 1010, "content": "content_blackHoleItem_1010", "icon": "tech_icon_1010", "weight": 10, "target": 2, "contentValue": [3], "trigger": {"type": "HIT", "object": "ANIMAL_TYPE", "camp": "TEAMMATE"}, "object": [{"type": "RECIPIENT"}], "effect": [{"type": "BUFF", "roleTimes": 1, "buff": {"times": 3}}], "load": 1, "skill": {"func": "playSkillCurve"}}, {"id": 101010, "content": "content_blackHoleItem_1010", "icon": "tech_icon_1010", "target": 2, "trigger": {"type": "ROUND_START"}, "object": [{"type": "SELF"}], "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "BUFFER", "perAttributeType": "ATTACK", "value": 0.4}]}, {"id": 1011, "content": "content_blackHoleItem_1011", "icon": "tech_icon_1011", "weight": 10, "target": 2, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}]}, {"id": 1012, "content": "content_blackHoleItem_1012", "icon": "tech_icon_1012", "weight": 10, "target": 3, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}]}, {"id": 1013, "content": "content_blackHoleItem_1013", "icon": "tech_icon_1013", "weight": 10, "target": 2, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.6}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.6}]}, {"id": 1014, "content": "content_blackHoleItem_1014", "icon": "tech_icon_1014", "weight": 10, "target": 1, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.6}, {"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.6}]}, {"id": 1015, "content": "content_blackHoleItem_1015", "icon": "tech_icon_1015", "weight": 5, "target": 1, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_SKILL", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 1016, "content": "content_blackHoleItem_1016", "icon": "tech_icon_1016", "weight": 10, "target": 3, "trigger": {"type": "LIVE", "object": "ALL", "camp": "TEAMMATE", "count": 1}, "object": [{"type": "ALL", "camp": "TEAMMATE"}], "effect": [{"type": "CHANGE_ATTACK", "times": 1, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}, {"type": "CHANGE_HP", "times": 1, "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 1}], "load": 1, "skill": {"func": "playSkillCurve"}}, {"id": 1017, "content": "content_blackHoleItem_1017", "icon": "tech_icon_1017", "weight": 10, "target": 3, "trigger": {"type": "BATTLE_AFTER"}, "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 1018, "content": "content_blackHoleItem_1018", "icon": "tech_icon_1018", "weight": 10, "target": 3, "trigger": {"type": "BATTLE_AFTER"}, "effect": [{"type": "CHANGE_HP", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 1019, "content": "content_blackHoleItem_1019", "icon": "tech_icon_1019", "weight": 10, "target": 3, "trigger": {"type": "BATTLE_START"}, "object": [{"type": "ALL", "camp": "ENEMY"}], "effect": [{"type": "ATTACK_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.5}, {"type": "HP_GAIN_BUFF", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": -0.5}], "load": 1, "ready": {"func": "playSkillReady_Tech1005"}}, {"id": 1020, "content": "content_blackHoleItem_1020", "icon": "tech_icon_1020", "weight": 10, "target": 2, "trigger": {"type": "HIT", "object": "ANIMAL_TYPE", "camp": "TEAMMATE"}, "object": [{"type": "ANIMAL_TYPE", "camp": "TEAMMATE"}], "effect": [{"type": "CHANGE_ATTACK", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.5}], "load": 1, "skill": {"func": "playSkillCurve"}}, {"id": 1021, "content": "content_blackHoleItem_1021", "icon": "tech_icon_1021", "weight": 10, "target": 2, "trigger": {"type": "BATTLE_BEFORE"}, "object": [{"type": "ANIMAL_TYPE", "camp": "TEAMMATE"}], "effect": [{"type": "ATTACK_GAIN_BUFF", "rateType": "ANIMAL_TYPE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.1}, {"type": "HP_GAIN_BUFF", "rateType": "ANIMAL_TYPE", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.1}], "load": 1, "skill": {"func": "playSkillCurve"}}, {"id": 1022, "content": "content_blackHoleItem_1022", "icon": "tech_icon_1015", "weight": 5, "target": 2, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_SKILL", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.3}]}, {"id": 1023, "content": "content_blackHoleItem_1023", "icon": "tech_icon_1015", "weight": 5, "target": 3, "trigger": {"type": "NONE"}, "effect": [{"type": "CHANGE_SKILL", "valueType": "PER", "perObjectType": "SELF", "perAttributeType": "ATTACK", "value": 0.15}]}]