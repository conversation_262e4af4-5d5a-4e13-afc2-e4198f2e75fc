[{"id": 201, "priority": 10, "type": "EXPLORE_PLANET_COMPLETE", "name": "name_achievement_201", "simple": "simple_achievement_201"}, {"id": 101, "trigger": {"type": "BUILT_CARRIAGE", "id": 1013}, "priority": 21, "type": "BUILD_TRAINITEM_LEVEL", "name": "name_achievement_101", "simple": "simple_achievement_101"}, {"id": 102, "trigger": {"type": "BUILT_CARRIAGE", "id": 1016}, "priority": 22, "type": "BUILD_TRAINITEM_LEVEL", "name": "name_achievement_102", "simple": "simple_achievement_102"}, {"id": 103, "trigger": {"type": "BUILT_CARRIAGE", "id": 1014}, "priority": 23, "type": "BUILD_TRAINITEM_LEVEL", "name": "name_achievement_103", "simple": "simple_achievement_103"}, {"id": 104, "trigger": {"type": "BUILT_CARRIAGE", "id": 1017}, "priority": 24, "type": "BUILD_TRAINITEM_LEVEL", "name": "name_achievement_104", "simple": "simple_achievement_104"}, {"id": 105, "trigger": {"type": "BUILT_CARRIAGE", "id": 1019}, "priority": 25, "type": "BUILD_TRAINITEM_LEVEL", "name": "name_achievement_105", "simple": "simple_achievement_105"}, {"id": 106, "trigger": {"type": "BUILT_CARRIAGE", "id": 1018}, "priority": 25, "type": "BUILD_TRAINITEM_LEVEL", "name": "name_achievement_106", "simple": "simple_achievement_106"}, {"id": 107, "trigger": {"type": "BUILT_CARRIAGE", "id": 1020}, "priority": 25, "type": "BUILD_TRAINITEM_LEVEL", "name": "name_achievement_107", "simple": "simple_achievement_107"}, {"id": 108, "trigger": {"type": "BUILT_CARRIAGE", "id": 1021}, "priority": 25, "type": "BUILD_TRAINITEM_LEVEL", "name": "name_achievement_108", "simple": "simple_achievement_108"}, {"id": 109, "trigger": {"type": "BUILT_CARRIAGE", "id": 1022}, "priority": 25, "type": "BUILD_TRAINITEM_LEVEL", "name": "name_achievement_109", "simple": "simple_achievement_109"}, {"id": 601, "priority": 40, "type": "CHARACTER_LEVELUP", "name": "name_achievement_601", "simple": "simple_achievement_601"}, {"id": 501, "priority": 30, "type": "GET_CHARACTER_INDEX", "name": "name_achievement_501", "simple": "simple_achievement_501"}, {"id": 801, "priority": 40, "type": "CHARACTER_LEVEL", "name": "name_achievement_801", "simple": "simple_achievement_801"}, {"id": 401, "trigger": {"type": "UNLOCKFUNC", "id": "TOOL"}, "priority": 50, "type": "TOOL_LEVELUP", "name": "name_achievement_401", "simple": "simple_achievement_401"}, {"id": 301, "trigger": {"type": "UNLOCKFUNC", "id": "ENTRUST"}, "priority": 60, "type": "ENTRUST_COMPLETE", "name": "name_achievement_301", "simple": "simple_achievement_301"}, {"id": 1001, "trigger": {"type": "BUILT_CARRIAGE", "id": 1017}, "priority": 60, "type": "GOODS_UNLOCK", "name": "name_achievement_1001", "simple": "simple_achievement_1001"}, {"id": 1002, "trigger": {"type": "BUILT_CARRIAGE", "id": 1020}, "priority": 60, "type": "GOODS_UNLOCK", "name": "name_achievement_1002", "simple": "simple_achievement_1002"}, {"id": 1003, "trigger": {"type": "BUILT_CARRIAGE", "id": 1022}, "priority": 60, "type": "GOODS_UNLOCK", "name": "name_achievement_1003", "simple": "simple_achievement_1003"}, {"id": 1101, "priority": 70, "type": "ORE_DIG", "name": "name_achievement_1101", "simple": "simple_achievement_1101"}, {"id": 1201, "priority": 70, "type": "ORE_KILL_MONSTERS", "name": "name_achievement_1201", "simple": "simple_achievement_1201"}, {"id": 1301, "priority": 70, "type": "ORE_MINE_TIME", "name": "name_achievement_1301", "simple": "simple_achievement_1301"}, {"id": 1401, "priority": 70, "type": "ORE_DEPTH", "name": "name_achievement_1401", "simple": "simple_achievement_1401"}]