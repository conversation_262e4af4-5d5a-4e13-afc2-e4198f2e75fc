[{"id": 1001, "name": "name_planet_1001", "content": "content_planet_1001", "entryName": "name_planet_entry_1001", "icon": "planet_1001", "windName": "schoolPlanet", "entryIcon": "xqty_planet_1001", "games": ["PLAY_COLLECT", "PLAY_ARCHIVES"], "explore": {"time": 3, "reward": [{"type": 2, "base": 250, "rate": 4}, {"type": 11, "id": 26, "num": 3}], "rewardRandom": [{"type": 1, "num": 5, "weight": 1}, {"type": 1, "num": 10, "weight": 1}, {"type": 1, "num": 15, "weight": 1}, {"type": 1, "num": 20, "weight": 1}]}, "iconHide": "planet_black_1001", "profileUnlock": [{"type": 1, "num": 350}]}, {"id": 1005, "lock": {"type": 20, "id": 1001}, "name": "name_planet_1005", "content": "content_planet_1005", "entryName": "name_planet_entry_1005", "icon": "planet_1005", "windName": "eternalGarden", "entryIcon": "xqty_planet_1005", "games": ["PLAY_BLACKHOLE"], "explore": {"time": 4, "reward": [{"type": 2, "base": 600, "rate": 6}, {"type": 11, "id": 26, "num": 4}], "rewardRandom": [{"type": 1, "num": 10, "weight": 1}, {"type": 1, "num": 15, "weight": 1}, {"type": 1, "num": 20, "weight": 1}, {"type": 1, "num": 25, "weight": 1}]}, "iconHide": "planet_black_1005", "profileUnlock": [{"type": 1, "num": 400}]}, {"id": 1006, "lock": {"type": 20, "id": 1005}, "name": "name_planet_1006", "content": "content_planet_1006", "entryName": "name_planet_entry_1006", "icon": "planet_1006", "windName": "machinePlanet", "entryIcon": "xqty_planet_1006", "games": ["PLAY_TOWER"], "explore": {"time": 5, "reward": [{"type": 2, "base": 1200, "rate": 8}, {"type": 11, "id": 26, "num": 5}], "rewardRandom": [{"type": 1, "num": 15, "weight": 1}, {"type": 1, "num": 20, "weight": 1}, {"type": 1, "num": 25, "weight": 1}, {"type": 1, "num": 30, "weight": 1}]}, "iconHide": "planet_black_1006", "profileUnlock": [{"type": 1, "num": 450}]}, {"id": 1009, "lock": {"type": 211, "id": "1006-3-9"}, "name": "name_planet_1009", "content": "content_planet_1009", "entryName": "name_planet_entry_1009", "icon": "planet_1009", "windName": "planet", "entryIcon": "xqty_planet_1009", "games": ["EQUIP_BUY", "EQUIP_MAKE", "PLAY_ORE"], "explore": {"time": 6, "reward": [{"type": 2, "base": 1800, "rate": 9}, {"type": 11, "id": 26, "num": 6}], "rewardRandom": [{"type": 1, "num": 20, "weight": 1}, {"type": 1, "num": 25, "weight": 1}, {"type": 1, "num": 30, "weight": 1}, {"type": 1, "num": 35, "weight": 1}]}, "iconHide": "planet_black_1009", "profileUnlock": [{"type": 1, "num": 500}]}, {"id": 1007, "lock": {"type": 211, "id": "1009-3-9"}, "name": "name_planet_1007", "content": "content_planet_1007", "entryName": "name_planet_entry_1007", "icon": "planet_1007", "windName": "planet", "entryIcon": "xqty_planet_1007", "games": ["PLAY_TRANSPORT", "PLAY_SPACE_STONE"], "explore": {"time": 7, "reward": [{"type": 2, "base": 2400, "rate": 11}, {"type": 11, "id": 26, "num": 7}], "rewardRandom": [{"type": 1, "num": 25, "weight": 1}, {"type": 1, "num": 30, "weight": 1}, {"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}]}, "iconHide": "planet_black_1007", "profileUnlock": [{"type": 1, "num": 550}]}, {"id": 1008, "lock": {"type": 211, "id": "1007-1-15"}, "name": "name_planet_1008", "content": "content_planet_1008", "entryName": "name_planet_entry_1008", "icon": "planet_1008", "windName": "planet", "entryIcon": "xqty_planet_1008", "games": ["PLAY_INSTANCE"], "explore": {"time": 8, "reward": [{"type": 2, "base": 3000, "rate": 12}, {"type": 11, "id": 26, "num": 8}], "rewardRandom": [{"type": 1, "num": 25, "weight": 1}, {"type": 1, "num": 30, "weight": 1}, {"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}]}, "iconHide": "planet_black_1008", "profileUnlock": [{"type": 1, "num": 600}]}, {"id": 1018, "lock": {"type": 211, "id": "1007-3-5"}, "name": "name_planet_1018", "content": "content_planet_1018", "entryName": "name_planet_entry_1018", "icon": "planet_1018", "windName": "planet", "entryIcon": "xqty_planet_1018", "games": ["PLAY_FIELD", "PLAY_SEED"], "explore": {"time": 9, "reward": [{"type": 2, "base": 3600, "rate": 14}, {"type": 11, "id": 26, "num": 9}], "rewardRandom": [{"type": 1, "num": 30, "weight": 1}, {"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}]}, "iconHide": "planet_black_1018", "profileUnlock": [{"type": 1, "num": 650}]}, {"id": 1017, "lock": {"type": 211, "id": "1008-1-10"}, "name": "name_planet_1017", "content": "content_planet_1017", "entryName": "name_planet_entry_1017", "icon": "planet_1017", "windName": "planet", "entryIcon": "xqty_planet_1017", "explore": {"time": 10, "reward": [{"type": 2, "base": 4200, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 30, "weight": 1}, {"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}]}, "iconHide": "planet_black_1017", "profileUnlock": [{"type": 1, "num": 700}]}, {"id": 1014, "lock": {"type": 20, "id": 1017}, "name": "name_planet_1014", "content": "content_planet_1014", "entryName": "name_planet_entry_1014", "icon": "planet_1014", "windName": "planet", "entryIcon": "xqty_planet_1014", "explore": {"time": 10, "reward": [{"type": 2, "base": 4800, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1014", "profileUnlock": [{"type": 1, "num": 900}]}, {"id": 1013, "lock": {"type": 20, "id": 1014}, "name": "name_planet_1013", "content": "content_planet_1013", "entryName": "name_planet_entry_1013", "icon": "planet_1013", "windName": "planet", "entryIcon": "xqty_planet_1013", "explore": {"time": 10, "reward": [{"type": 2, "base": 5400, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1013", "profileUnlock": [{"type": 1, "num": 1000}]}, {"id": 1016, "lock": {"type": 20, "id": 1013}, "name": "name_planet_1016", "content": "content_planet_1016", "entryName": "name_planet_entry_1016", "icon": "planet_1016", "windName": "planet", "entryIcon": "xqty_planet_1016", "explore": {"time": 10, "reward": [{"type": 2, "base": 6000, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1016", "profileUnlock": [{"type": 1, "num": 750}]}, {"id": 1021, "lock": {"type": 20, "id": 1016}, "name": "name_planet_1021", "content": "content_planet_1021", "entryName": "name_planet_entry_1021", "icon": "planet_1021", "windName": "planet", "entryIcon": "xqty_planet_1021", "explore": {"time": 10, "reward": [{"type": 2, "base": 6600, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1021", "profileUnlock": [{"type": 1, "num": 850}]}, {"id": 1020, "lock": {"type": 20, "id": 1021}, "name": "name_planet_1020", "content": "content_planet_1020", "entryName": "name_planet_entry_1020", "icon": "planet_1020", "windName": "planet", "entryIcon": "xqty_planet_1020", "explore": {"time": 10, "reward": [{"type": 2, "base": 7200, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1020", "profileUnlock": [{"type": 1, "num": 800}]}, {"id": 1019, "lock": {"type": 20, "id": 1020}, "name": "name_planet_1019", "content": "content_planet_1019", "entryName": "name_planet_entry_1019", "icon": "planet_1019", "windName": "planet", "entryIcon": "xqty_planet_1019", "explore": {"time": 10, "reward": [{"type": 2, "base": 7800, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1019", "profileUnlock": [{"type": 1, "num": 950}]}, {"id": 1011, "lock": {"type": 20, "id": 1019}, "name": "name_planet_1011", "content": "content_planet_1011", "entryName": "name_planet_entry_1011", "icon": "planet_1011", "windName": "planet", "entryIcon": "xqty_planet_1011", "explore": {"time": 10, "reward": [{"type": 2, "base": 8400, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1011", "profileUnlock": [{"type": 1, "num": 1000}]}, {"id": 1010, "lock": {"type": 20, "id": 1011}, "name": "name_planet_1010", "content": "content_planet_1010", "entryName": "name_planet_entry_1010", "icon": "planet_1010", "windName": "planet", "entryIcon": "xqty_planet_1010", "explore": {"time": 10, "reward": [{"type": 2, "base": 9000, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1010", "profileUnlock": [{"type": 1, "num": 1000}]}, {"id": 1012, "lock": {"type": 20, "id": 1010}, "name": "name_planet_1012", "content": "content_planet_1012", "entryName": "name_planet_entry_1012", "icon": "planet_1012", "windName": "planet", "entryIcon": "xqty_planet_1012", "explore": {"time": 10, "reward": [{"type": 2, "base": 9600, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1012", "profileUnlock": [{"type": 1, "num": 1000}]}, {"id": 1015, "lock": {"type": 20, "id": 1012}, "name": "name_planet_1015", "content": "content_planet_1015", "entryName": "name_planet_entry_1015", "icon": "planet_1015", "windName": "planet", "entryIcon": "xqty_planet_1015", "explore": {"time": 10, "reward": [{"type": 2, "base": 10200, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1015", "profileUnlock": [{"type": 1, "num": 1000}]}, {"id": 1099, "lock": {"type": 20, "id": 1099}, "name": "name_planet_1099", "content": "content_planet_1099", "entryName": "name_planet_entry_1099", "icon": "planet_1099", "windName": "planet", "entryIcon": "xqty_planet_1099", "explore": {"time": 10, "reward": [{"type": 2, "base": 10800, "rate": 16}, {"type": 11, "id": 26, "num": 10}], "rewardRandom": [{"type": 1, "num": 35, "weight": 1}, {"type": 1, "num": 40, "weight": 1}, {"type": 1, "num": 45, "weight": 1}, {"type": 1, "num": 50, "weight": 1}, {"type": 1, "num": 55, "weight": 1}, {"type": 1, "num": 60, "weight": 1}]}, "iconHide": "planet_black_1099", "profileUnlock": [{"type": 1, "num": 1000}]}]