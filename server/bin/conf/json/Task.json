[{"id": "1001_1", "priority": 1001, "simple": "simple_task_GET_ITEM_1", "content": "content_task_GET_ITEM_1", "type": "GET_ITEM", "target": [{"type": 11, "id": 101, "num": 7}, {"type": 11, "id": 102, "num": 2}]}, {"id": "1001_2", "preTask": "1001_1", "priority": 1001, "simple": "simple_task_BUILD_TRAINITEM_1", "content": "content_task_BUILD_TRAINITEM_1", "type": "BUILD_TRAINITEM", "target": [{"id": "1013-1-3"}, {"id": "1013-1-1"}]}, {"id": "1001_3", "preTask": "1001_2", "priority": 1001, "simple": "simple_task_GET_ITEM_2", "content": "content_task_GET_ITEM_2", "type": "GET_ITEM", "target": [{"type": 11, "id": 101, "num": 5}, {"type": 11, "id": 102, "num": 4}]}, {"id": "1001_4", "preTask": "1001_3", "priority": 1001, "simple": "simple_task_GET_ITEM_3", "content": "content_task_GET_ITEM_3", "type": "GET_ITEM", "target": [{"type": 2, "num": 10}]}, {"id": "1001_5", "preTask": "1001_4", "priority": 1001, "simple": "simple_task_BUILD_TRAINITEM_2", "content": "content_task_BUILD_TRAINITEM_2", "type": "BUILD_TRAINITEM", "target": [{"id": "1013-1-4"}], "mark": "HUTAO_BED"}, {"id": "1001_6", "preTask": "1001_5", "priority": 1001, "simple": "simple_task_BUILD_TRAINITEM_3", "content": "content_task_BUILD_TRAINITEM_3", "type": "BUILD_TRAINITEM", "target": [{"id": "1013-1-7"}], "reward": [{"type": 1, "num": 10}]}, {"id": "1001_7", "preTask": "1001_6", "priority": 1001, "simple": "simple_task_BUILD_TRAINITEM_4", "content": "content_task_BUILD_TRAINITEM_4", "type": "BUILD_TRAINITEM", "target": [{"id": "1013-1-5"}, {"id": "1013-1-8"}], "reward": [{"type": 1, "num": 10}]}, {"id": "1001_8", "preTask": "1001_7", "priority": 1001, "simple": "simple_task_BUILD_TRAINITEM_5", "content": "content_task_BUILD_TRAINITEM_5", "type": "BUILD_TRAINITEM", "target": [{"id": "1013-1-9"}], "reward": [{"type": 1, "num": 15}]}, {"id": "1001_9", "preTask": "1001_8", "priority": 1001, "simple": "simple_task_BUILD_TRAINITEM_6", "content": "content_task_BUILD_TRAINITEM_6", "type": "BUILD_TRAINITEM", "target": [{"id": "1013-1-10"}, {"id": "1013-1-6"}], "reward": [{"type": 1, "num": 20}]}, {"id": "1001_10", "preTask": "1001_9", "priority": 1001, "simple": "simple_task_CHARACTER_GETON2_1", "content": "content_task_CHARACTER_GETON2_1", "type": "CHARACTER_GETON2", "target": [{"id": 1007}], "reward": [{"type": 1, "num": 20}]}, {"id": "1001_11", "preTask": "1001_10", "priority": 1001, "simple": "simple_task_UNLOCK_THEME_1", "content": "content_task_UNLOCK_THEME_1", "type": "UNLOCK_THEME", "target": [{"id": "1013-2"}], "reward": [{"type": 1, "num": 20}]}, {"id": "1001_12", "preTask": "1001_11", "priority": 1001, "simple": "simple_task_TRAINITEM_LEVELUP_1", "content": "content_task_TRAINITEM_LEVELUP_1", "type": "TRAINITEM_LEVELUP", "target": [{"id": "1013-2", "num": 5}], "reward": [{"type": 1, "num": 25}]}, {"id": "1001_13", "preTask": "1001_12", "priority": 1001, "simple": "simple_task_CHARACTER_LEVELUP_1", "content": "content_task_CHARACTER_LEVELUP_1", "type": "CHARACTER_LEVELUP", "target": [{"id": "character", "num": 1}, {"id": "level", "num": 5}], "reward": [{"type": 1, "num": 25}]}, {"id": "1001_14", "preTask": "1001_13", "priority": 1001, "simple": "simple_task_TRAINITEM_LEVELUP_2", "content": "content_task_TRAINITEM_LEVELUP_2", "type": "TRAINITEM_LEVELUP", "target": [{"id": "1013-2", "num": 10}], "reward": [{"type": 1, "num": 30}]}, {"id": "1001_15", "preTask": "1001_14", "priority": 1001, "simple": "simple_task_CHARACTER_LEVELUP_2", "content": "content_task_CHARACTER_LEVELUP_2", "type": "CHARACTER_LEVELUP", "target": [{"id": "character", "num": 3}, {"id": "level", "num": 6}], "reward": [{"type": 1, "num": 30}]}, {"id": "2001_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1005-1-4"}, "priority": 2001, "simple": "simple_task_TOOL_LEVELUP2_1", "content": "content_task_TOOL_LEVELUP2_1", "type": "TOOL_LEVELUP2", "target": [{"num": 1}], "reward": [{"type": 1, "num": 15}]}, {"id": "2001_2", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1001-1-20"}, "priority": 2001, "simple": "simple_task_EXPLORE_PLANET_1", "content": "content_task_EXPLORE_PLANET_1", "type": "EXPLORE_PLANET", "target": [{"id": 1001, "num": 100}], "reward": [{"type": 12, "id": "1015-1-1"}], "mark": "HUTAO_TIP"}, {"id": "3001_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1005-1-18"}, "priority": 3001, "simple": "simple_task_BUILD_TRAINITEM_7", "content": "content_task_BUILD_TRAINITEM_7", "type": "BUILD_TRAINITEM", "target": [{"id": "1016-1-3", "num": 1}, {"id": "1016-1-2", "num": 1}], "reward": [{"type": 1, "num": 30}]}, {"id": "3001_2", "preTask": "3001_1", "priority": 3001, "simple": "simple_task_BUILD_TRAINITEM_8", "content": "content_task_BUILD_TRAINITEM_8", "type": "BUILD_TRAINITEM", "target": [{"id": "1016-1-7", "num": 1}], "reward": [{"type": 1, "num": 30}]}, {"id": "3001_3", "preTask": "3001_2", "priority": 3001, "simple": "simple_task_BUILD_TRAINITEM_9", "content": "content_task_BUILD_TRAINITEM_9", "type": "BUILD_TRAINITEM", "target": [{"id": "1016-1-4", "num": 1}], "reward": [{"type": 1, "num": 35}]}, {"id": "3001_4", "preTask": "3001_3", "priority": 3001, "simple": "simple_task_BUILD_TRAINITEM_10", "content": "content_task_BUILD_TRAINITEM_10", "type": "BUILD_TRAINITEM", "target": [{"id": "1016-1-5", "num": 1}], "reward": [{"type": 1, "num": 35}]}, {"id": "3001_5", "preTask": "3001_4", "priority": 3001, "simple": "simple_task_BUILD_TRAINITEM_11", "content": "content_task_BUILD_TRAINITEM_11", "type": "BUILD_TRAINITEM", "target": [{"id": "1016-1-11", "num": 1}, {"id": "1016-1-6", "num": 1}], "reward": [{"type": 1, "num": 35}]}, {"id": "3001_6", "preTask": "3001_5", "priority": 3001, "simple": "simple_task_TRAINITEM_LEVELUP2_1", "content": "content_task_TRAINITEM_LEVELUP2_1", "type": "TRAINITEM_LEVELUP2", "target": [{"id": 1016, "num": 1}], "reward": [{"type": 1, "num": 40}]}, {"id": "3002_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 3002, "simple": "simple_task_EXPLORE_PLANET_AREA_1", "content": "content_task_EXPLORE_PLANET_AREA_1", "type": "EXPLORE_PLANET_AREA", "target": [{"id": "1009-1", "num": 100}], "reward": [{"type": 1, "num": 45}]}, {"id": "3002_2", "preTask": "3002_1", "priority": 3002, "simple": "simple_task_BUILD_TRAIN_1", "content": "content_task_BUILD_TRAIN_1", "type": "BUILD_TRAIN", "target": [{"id": 1014, "num": 1}], "reward": [{"type": 1, "num": 50}]}, {"id": "4001_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 4001, "simple": "simple_task_GOTO_WORK_1", "content": "content_task_GOTO_WORK_1", "type": "GOTO_WORK", "target": [{"id": 1016, "num": 1}], "reward": [{"type": 1, "num": 25}]}, {"id": "5001_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5001, "simple": "simple_task_GOTO_PLANET_1", "content": "content_task_GOTO_PLANET_1", "type": "GOTO_PLANET", "target": [{"id": 1006}]}, {"id": "5002_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5002, "simple": "simple_task_PLANET_BATTLE_ID_1", "content": "content_task_PLANET_BATTLE_ID_1", "type": "PLANET_BATTLE_ID", "target": [{"id": "1006-3-3"}]}, {"id": "5002_2", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5002, "simple": "simple_task_TOWER_1", "content": "content_task_TOWER_1", "type": "TOWER", "target": [{"num": 5}], "reward": [{"type": 1, "num": 30}]}, {"id": "5003_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5003, "simple": "simple_task_TO_DEEP_EXPLORE_1", "content": "content_task_TO_DEEP_EXPLORE_1", "type": "TO_DEEP_EXPLORE"}, {"id": "5003_2", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5003, "simple": "simple_task_DEEP_EXPLORE_1", "content": "content_task_DEEP_EXPLORE_1", "type": "DEEP_EXPLORE", "target": [{"num": 1}]}, {"id": "5003_3", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5003, "simple": "simple_task_PROFILE_1", "content": "content_task_PROFILE_1", "type": "PROFILE", "target": [{"num": 1}], "reward": [{"type": 1, "num": 30}]}, {"id": "5004_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5004, "simple": "simple_task_GOTO_PLANET_ENTRY_1009_1", "content": "content_task_GOTO_PLANET_ENTRY_1009_1", "type": "GOTO_PLANET_ENTRY_1009"}, {"id": "5004_2", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5004, "simple": "simple_task_ORE_PUZZLE_1", "content": "content_task_ORE_PUZZLE_1", "type": "ORE_PUZZLE"}, {"id": "5005_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5005, "simple": "simple_task_EXPLORE_PLANET_AREA_2", "content": "content_task_EXPLORE_PLANET_AREA_2", "type": "EXPLORE_PLANET_AREA", "target": [{"id": "1007-1", "num": 65}]}, {"id": "5005_2", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5005, "simple": "simple_task_TO_TRANSPORT_1", "content": "content_task_TO_TRANSPORT_1", "type": "TO_TRANSPORT"}, {"id": "5005_3", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5005, "simple": "simple_task_TRANSPORT_1", "content": "content_task_TRANSPORT_1", "type": "TRANSPORT", "target": [{"num": 1}], "reward": [{"type": 1, "num": 30}]}, {"id": "5006_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5006, "simple": "simple_task_GOTO_PLANET_ENTRY_1007_1", "content": "content_task_GOTO_PLANET_ENTRY_1007_1", "type": "GOTO_PLANET_ENTRY_1007"}, {"id": "5007_1", "trigger": {"type": "COMPLATE_PLANET_ID", "id": "1017-1-1"}, "priority": 5007, "simple": "simple_task_INSTANCE_PUZZLE_1", "content": "content_task_INSTANCE_PUZZLE_1", "type": "INSTANCE_PUZZLE"}]