[{"id": 1, "point": 1, "needPoint": 0, "weight": 1, "numMax": 6, "costTime": 240, "normal": {"num": 2}, "taskCnt": 6, "addPeople": 1, "rewardRandom": [{"type": 1, "num": 10, "pro": 50}], "reward": [{"type": 2, "rate": 12}, {"type": 3, "rate": 12}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 2, "point": 1, "needPoint": 6, "weight": 1, "numMax": 3, "costTime": 300, "normal": {"num": 3}, "taskCnt": 7, "addPeople": 1, "rewardRandom": [{"type": 1, "num": 20, "pro": 50}], "reward": [{"type": 2, "rate": 14}, {"type": 3, "rate": 14}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 3, "point": 1, "needPoint": 12, "weight": 1, "numMax": 3, "costTime": 360, "normal": {"num": 3}, "taskCnt": 8, "addPeople": 1, "rewardRandom": [{"type": 1, "num": 30, "pro": 50}], "reward": [{"type": 2, "rate": 16}, {"type": 3, "rate": 16}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 4, "point": 1, "needPoint": 24, "weight": 1, "numMax": 3, "costTime": 420, "normal": {"num": 3}, "taskCnt": 9, "addPeople": 0, "rewardRandom": [{"type": 1, "num": 40, "pro": 50}], "reward": [{"type": 2, "rate": 18}, {"type": 3, "rate": 18}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 5, "point": 1, "needPoint": 48, "weight": 1, "numMax": 2, "costTime": 480, "normal": {"num": 3}, "taskCnt": 10, "addPeople": 0, "rewardRandom": [{"type": 1, "num": 50, "pro": 50}], "reward": [{"type": 2, "rate": 20}, {"type": 3, "rate": 20}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 6, "point": 1, "needPoint": 96, "weight": 1, "numMax": 2, "costTime": 540, "normal": {"num": 3}, "taskCnt": 11, "addPeople": 0, "rewardRandom": [{"type": 1, "num": 60, "pro": 50}], "reward": [{"type": 2, "rate": 22}, {"type": 3, "rate": 22}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 7, "point": 1, "needPoint": 192, "weight": 1, "numMax": 1, "costTime": 600, "normal": {"num": 3}, "taskCnt": 12, "addPeople": 0, "rewardRandom": [{"type": 1, "num": 70, "pro": 50}], "reward": [{"type": 2, "rate": 24}, {"type": 3, "rate": 24}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}]