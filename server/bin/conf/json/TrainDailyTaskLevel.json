[{"id": 1, "costTime": 240, "point": 1, "normal": {"num": 2}, "reward": [{"type": 11, "id": 25, "minNum": 35, "maxNum": 45}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 10, "weight": 40}, {"type": 11, "id": 21, "minNum": 1, "maxNum": 1, "weight": 20}, {"type": 11, "id": 7, "num": 10, "weight": 40}], "req": [{"type": 1, "id": 1, "num": -1, "weight": 40}, {"type": 2, "num": -1, "weight": 30}, {"type": 3, "num": -1, "weight": 30}]}, {"id": 2, "costTime": 300, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 45, "maxNum": 55}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 15, "weight": 40}, {"type": 11, "id": 21, "minNum": 1, "maxNum": 1, "weight": 20}, {"type": 11, "id": 7, "num": 15, "weight": 40}], "req": [{"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 20}, {"type": 2, "num": -1, "weight": 30}, {"type": 3, "num": -1, "weight": 30}]}, {"id": 3, "costTime": 360, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 55, "maxNum": 65}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 20, "weight": 39}, {"type": 11, "id": 21, "minNum": 1, "maxNum": 2, "weight": 20}, {"type": 11, "id": 7, "num": 20, "weight": 40}, {"type": 11, "id": 701, "num": 1, "weight": 1}], "req": [{"type": 1, "id": 2, "num": -1, "weight": 40}, {"type": 2, "num": -1, "weight": 30}, {"type": 3, "num": -1, "weight": 30}]}, {"id": 4, "costTime": 420, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 65, "maxNum": 75}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 25, "weight": 38}, {"type": 11, "id": 21, "minNum": 1, "maxNum": 2, "weight": 20}, {"type": 11, "id": 7, "num": 25, "weight": 40}, {"type": 11, "id": 701, "num": 1, "weight": 2}], "req": [{"type": 1, "id": 3, "num": -1, "weight": 20}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 2, "num": -1, "weight": 30}, {"type": 3, "num": -1, "weight": 30}]}, {"id": 5, "costTime": 480, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 75, "maxNum": 85}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 30, "weight": 37}, {"type": 11, "id": 21, "minNum": 2, "maxNum": 2, "weight": 20}, {"type": 11, "id": 7, "num": 30, "weight": 40}, {"type": 11, "id": 701, "num": 1, "weight": 3}], "req": [{"type": 1, "id": 3, "num": -1, "weight": 40}, {"type": 2, "num": -1, "weight": 30}, {"type": 3, "num": -1, "weight": 30}]}]