[{"id": 1, "row": 1, "ceil": 1, "name": "train_tech_name_1", "desc": "train_tech_desc_1", "icon": "kjd_icon_1"}, {"id": 10, "row": 2, "ceil": 1, "pre": [1], "name": "train_tech_name_10", "desc": "train_tech_desc_10", "icon": "kjd_icon_10"}, {"id": 8, "row": 2, "ceil": 2, "pre": [1], "name": "train_tech_name_8", "desc": "train_tech_desc_8", "icon": "kjd_icon_8"}, {"id": 13, "row": 2, "ceil": 3, "pre": [1], "name": "train_tech_name_13", "desc": "train_tech_desc_13", "icon": "kjd_icon_13"}, {"id": 18, "row": 3, "ceil": 1, "pre": [10], "name": "train_tech_name_18", "desc": "train_tech_desc_18", "icon": "kjd_icon_18"}, {"id": 11, "row": 3, "ceil": 2, "pre": [10, 8], "name": "train_tech_name_11", "desc": "train_tech_desc_11", "icon": "kjd_icon_11"}, {"id": 16, "row": 3, "ceil": 3, "pre": [8, 13], "name": "train_tech_name_16", "desc": "train_tech_desc_16", "icon": "kjd_icon_16"}, {"id": 5, "row": 3, "ceil": 4, "pre": [13], "name": "train_tech_name_5", "desc": "train_tech_desc_5", "icon": "kjd_icon_5"}, {"id": 9, "row": 4, "ceil": 1, "pre": [18], "name": "train_tech_name_9", "desc": "train_tech_desc_9", "icon": "kjd_icon_9"}, {"id": 19, "row": 4, "ceil": 2, "pre": [11], "name": "train_tech_name_19", "desc": "train_tech_desc_19", "icon": "kjd_icon_19"}, {"id": 7, "row": 4, "ceil": 3, "pre": [16], "name": "train_tech_name_7", "desc": "train_tech_desc_7", "icon": "kjd_icon_7"}, {"id": 14, "row": 4, "ceil": 4, "pre": [5], "name": "train_tech_name_14", "desc": "train_tech_desc_14", "icon": "kjd_icon_14"}, {"id": 3, "row": 5, "ceil": 1, "pre": [9, 11], "name": "train_tech_name_3", "desc": "train_tech_desc_3", "icon": "kjd_icon_3"}, {"id": 2, "row": 5, "ceil": 2, "pre": [7, 14], "name": "train_tech_name_2", "desc": "train_tech_desc_2", "icon": "kjd_icon_2"}, {"id": 25, "row": 6, "ceil": 1, "pre": [3], "name": "train_tech_name_25", "desc": "train_tech_desc_25", "icon": "kjd_icon_25"}, {"id": 23, "row": 6, "ceil": 2, "pre": [2], "name": "train_tech_name_23", "desc": "train_tech_desc_23", "icon": "kjd_icon_23"}, {"id": 17, "row": 7, "ceil": 1, "pre": [25], "name": "train_tech_name_17", "desc": "train_tech_desc_17", "icon": "kjd_icon_17"}, {"id": 21, "row": 7, "ceil": 2, "pre": [25, 23], "name": "train_tech_name_21", "desc": "train_tech_desc_21", "icon": "kjd_icon_21"}, {"id": 15, "row": 7, "ceil": 3, "pre": [23], "name": "train_tech_name_15", "desc": "train_tech_desc_15", "icon": "kjd_icon_15"}, {"id": 12, "row": 8, "ceil": 1, "pre": [17, 21], "name": "train_tech_name_12", "desc": "train_tech_desc_12", "icon": "kjd_icon_12"}, {"id": 27, "row": 8, "ceil": 2, "pre": [15, 21], "name": "train_tech_name_27", "desc": "train_tech_desc_27", "icon": "kjd_icon_27"}]