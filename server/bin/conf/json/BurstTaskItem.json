[{"id": 1, "name": "burstTaskName_1", "desc": "burstTaskDesc_1", "icon": "rw_tf_ruqin", "reqNum": 3, "costTime": 600, "reward": [{"type": 11, "id": 21, "num": 1}], "train": [1013, 1016, 1014, 1017, 1018, 1020, 1021, 1022], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 2, "name": "burstTaskName_2", "desc": "burstTaskDesc_2", "icon": "rw_tf_dasao", "reqNum": 3, "costTime": 600, "reward": [{"type": 11, "id": 21, "num": 2}], "train": [1013, 1014, 1018, 1021], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 3, "name": "burstTaskName_3", "desc": "burstTaskDesc_3", "icon": "rw_tf_yunshi", "reqNum": 3, "costTime": 600, "reward": [{"type": 11, "id": 21, "num": 3}], "train": [1013, 1016, 1014, 1017, 1018, 1020, 1021, 1022], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 4, "name": "burstTaskName_4", "desc": "burstTaskDesc_4", "icon": "rw_tf_dianli", "reqNum": 3, "costTime": 600, "reward": [{"type": 11, "id": 21, "num": 4}], "train": [1013, 1016, 1014, 1017, 1018, 1020, 1021, 1022], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 5, "name": "burstTaskName_5", "desc": "burstTaskDesc_5", "icon": "rw_tf_dahuo", "reqNum": 3, "costTime": 600, "reward": [{"type": 11, "id": 21, "num": 5}], "train": [1013, 1016, 1014, 1017, 1018, 1020, 1021, 1022], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 6, "name": "burstTaskName_6", "desc": "burstTaskDesc_6", "icon": "rw_tf_yichang", "reqNum": 3, "costTime": 600, "reward": [{"type": 11, "id": 21, "num": 6}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}]