[{"id": 2008, "attack": 3, "hp": 12, "quality": 1, "score": 0, "isBoss": 1, "isSp": 1, "name": "name_monster_2008", "iconBig": "monster_2008_icon_big", "iconCircle": "monster_2008_icon_circle", "spine": "monster_2008", "skillTmpId": 2, "battleSkill": 2008, "reachOffset": {"x": -400, "y": -225}, "battle": {"startPosOffset": 476}, "width": 100}, {"id": 2060, "attack": 9, "hp": 19, "quality": 1, "score": 0, "isBoss": 1, "isSp": 1, "name": "name_monster_2060", "iconBig": "monster_2060_icon_big", "iconCircle": "monster_2060_icon_circle", "spine": "monster_2060", "skillTmpId": 2, "battleSkill": 2060, "reachOffset": {"x": -400, "y": -20}, "width": 100}, {"id": 2061, "attack": 3, "hp": 10, "quality": 1, "score": 0, "isBoss": 1, "isSp": 1, "name": "name_monster_2061", "iconBig": "monster_2061_icon_big", "iconCircle": "monster_2061_icon_circle", "spine": "monster_2061", "skillTmpId": 2, "battleSkill": 2061, "reachOffset": {"x": -400, "y": -2}, "width": 100}, {"id": 2062, "attack": 3, "hp": 5, "quality": 1, "score": 0, "isBoss": 0, "isSp": 1, "name": "name_monster_2062", "iconBig": "monster_2062_icon_big", "iconCircle": "monster_2062_icon_circle", "spine": "monster_2062"}, {"id": 2063, "attack": 5, "hp": 16, "quality": 1, "score": 0, "isBoss": 1, "isSp": 1, "name": "name_monster_2063", "iconBig": "monster_2063_icon_big", "iconCircle": "monster_2063_icon_circle", "spine": "monster_2063", "skillTmpId": 2, "battleSkill": 2063, "reachOffset": {"x": -400, "y": -2}}, {"id": 2163, "attack": 1, "hp": 7, "quality": 1, "score": 0, "isBoss": 0, "isSp": 1, "name": "name_monster_2163", "iconBig": "monster_2163_icon_big", "iconCircle": "monster_2163_icon_circle", "spine": "monster_2063_xueren"}, {"id": 2064, "attack": 5, "hp": 13, "quality": 1, "score": 4, "isBoss": 1, "isSp": 1, "name": "name_monster_2064", "iconBig": "monster_2064_icon_big", "iconCircle": "monster_2064_icon_circle", "spine": "monster_2064", "skillTmpId": 2, "battleSkill": 2064, "reachOffset": {"x": -400, "y": -2}}, {"id": 2100, "attack": 5, "hp": 13, "isBoss": 1, "isSp": 1, "name": "name_monster_2064", "iconBig": "monster_2064_icon_big", "iconCircle": "monster_2064_icon_circle", "spine": "monster_2064", "skin": "baoshi", "skillTmpId": 2, "battleSkill": 2100}, {"id": 2101, "attack": 1, "hp": 20, "isSp": 1, "name": "name_monster_2064", "iconBig": "monster_2101_icon_big", "iconCircle": "monster_2101_icon_circle", "spine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skillTmpId": 2, "battleSkill": 2101}, {"id": 2065, "attack": 8, "hp": 16, "quality": 1, "score": 0, "isBoss": 1, "isSp": 1, "name": "name_monster_2065", "iconBig": "monster_2065_icon_big", "iconCircle": "monster_2065_icon_circle", "spine": "monster_2065", "skillTmpId": 2, "battleSkill": 2065, "reachOffset": {"x": -400, "y": -2}, "width": 100}, {"id": 2066, "attack": 7, "hp": 23, "quality": 1, "score": 0, "isBoss": 1, "isSp": 1, "name": "name_monster_2066", "iconBig": "monster_2066_icon_big", "iconCircle": "monster_2066_icon_circle", "spine": "monster_2066", "skillTmpId": 2, "battleSkill": 2066, "reachOffset": {"x": -400, "y": -2}, "battle": {"startPosOffset": 100}}, {"id": 2067, "attack": 3, "hp": 6, "quality": 1, "score": 0, "isBoss": 0, "isSp": 1, "name": "name_monster_2067", "iconBig": "monster_2067_icon_big", "iconCircle": "monster_2067_icon_circle", "spine": "monster_2067", "skillTmpId": 2, "battleSkill": 2067, "reachOffset": {"x": -400, "y": -2}}, {"id": 2068, "attack": 7, "hp": 23, "quality": 1, "score": 0, "isBoss": 1, "isSp": 1, "name": "name_monster_2068", "iconBig": "monster_2068_icon_big", "iconCircle": "monster_2068_icon_circle", "spine": "monster_2068", "skillTmpId": 2, "battleSkill": 2068, "reachOffset": {"x": -400, "y": -2}, "width": 100}, {"id": 2071, "attack": 1, "hp": 20, "isBoss": 1, "isSp": 1, "name": "name_monster_2071", "iconBig": "monster_2071_icon_big", "iconCircle": "monster_2071_icon_circle", "spine": "monster_2071", "skillTmpId": 2, "battleSkill": 2071, "reachOffset": {"x": -400, "y": -2}, "width": 100}, {"id": 2073, "attack": 2, "hp": 16, "isBoss": 1, "isSp": 1, "name": "name_monster_2073", "iconBig": "monster_2073_icon_big", "iconCircle": "monster_2073_icon_circle", "spine": "monster_2073", "skillTmpId": 2, "battleSkill": 2073, "reachOffset": {"x": -400, "y": -2}}, {"id": 2300, "attack": 3, "hp": 0, "isSp": 1, "spine": "caiji_baoxiang_gw"}, {"id": 2004, "attack": 1, "hp": 5, "quality": 1, "score": 0, "isBoss": 0, "name": "name_monster_2004", "iconBig": "monster_2004_icon_big", "iconCircle": "monster_2004_icon_circle", "spine": "monster_2004", "reachOffset": {"x": -250, "y": -2}}, {"id": 1002004, "attack": 2, "hp": 4, "isBoss": 0, "name": "name_monster_2004", "iconBig": "monster_2004_icon_big", "iconCircle": "monster_2004_icon_circle", "spine": "monster_2004", "reachOffset": {"x": -250, "y": -2}}, {"id": 2001, "attack": 3, "hp": 4, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2001", "iconBig": "monster_2001_icon_big", "iconCircle": "monster_2001_icon_circle", "spine": "monster_2001", "skillTmpId": 4, "battleSkill": 1023, "reachOffset": {"x": -250, "y": -2}}, {"id": 2002, "attack": 4, "hp": 3, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2002", "iconBig": "monster_2002_icon_big", "iconCircle": "monster_2002_icon_circle", "spine": "monster_2002", "skillTmpId": 4, "battleSkill": 1027, "reachOffset": {"x": -250, "y": -2}}, {"id": 2003, "attack": 2, "hp": 3, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2003", "iconBig": "monster_2003_icon_big", "iconCircle": "monster_2003_icon_circle", "spine": "monster_2003", "skillTmpId": 4, "battleSkill": 1029, "reachOffset": {"x": -250, "y": -2}}, {"id": 2005, "attack": 5, "hp": 2, "quality": 2, "score": 4, "isBoss": 0, "name": "name_monster_2005", "iconBig": "monster_2005_icon_big", "iconCircle": "monster_2005_icon_circle", "spine": "monster_2005", "skillTmpId": 4, "battleSkill": 2005, "reachOffset": {"x": -250, "y": -2}}, {"id": 2006, "attack": 4, "hp": 3, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2006", "iconBig": "monster_2006_icon_big", "iconCircle": "monster_2006_icon_circle", "spine": "monster_2006", "skillTmpId": 4, "battleSkill": 1007, "reachOffset": {"x": -250, "y": -2}}, {"id": 2007, "attack": 4, "hp": 4, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2007", "iconBig": "monster_2007_icon_big", "iconCircle": "monster_2007_icon_circle", "spine": "monster_2007", "skillTmpId": 4, "battleSkill": 1038, "reachOffset": {"x": -280, "y": -10}}, {"id": 2009, "attack": 2, "hp": 5, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2009", "iconBig": "monster_2009_icon_big", "iconCircle": "monster_2009_icon_circle", "spine": "monster_2009", "skillTmpId": 4, "battleSkill": 1028, "reachOffset": {"x": -250, "y": -2}}, {"id": 2010, "attack": 4, "hp": 3, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2010", "iconBig": "monster_2010_icon_big", "iconCircle": "monster_2010_icon_circle", "spine": "monster_2010", "skillTmpId": 4, "battleSkill": 2010, "reachOffset": {"x": -250, "y": -2}}, {"id": 2011, "attack": 5, "hp": 2, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2011", "iconBig": "monster_2011_icon_big", "iconCircle": "monster_2011_icon_circle", "spine": "monster_2011", "skillTmpId": 4, "battleSkill": 1022, "reachOffset": {"x": -250, "y": -2}}, {"id": 2012, "attack": 3, "hp": 5, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2012", "iconBig": "monster_2012_icon_big", "iconCircle": "monster_2012_icon_circle", "spine": "monster_2012", "skillTmpId": 4, "battleSkill": 1012, "reachOffset": {"x": -280, "y": -2}}, {"id": 2013, "attack": 3, "hp": 2, "quality": 1, "score": 2, "isBoss": 0, "name": "name_monster_2013", "iconBig": "monster_2013_icon_big", "iconCircle": "monster_2013_icon_circle", "spine": "monster_2013", "skillTmpId": 4, "battleSkill": 1035, "reachOffset": {"x": -250, "y": -2}}, {"id": 2014, "attack": 3, "hp": 4, "quality": 2, "score": 2, "isBoss": 0, "name": "name_monster_2014", "iconBig": "monster_2014_icon_big", "iconCircle": "monster_2014_icon_circle", "spine": "monster_2014", "skillTmpId": 4, "battleSkill": 1040, "reachOffset": {"x": -220, "y": -2}}, {"id": 2015, "attack": 2, "hp": 5, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2015", "iconBig": "monster_2015_icon_big", "iconCircle": "monster_2015_icon_circle", "spine": "monster_2015", "skillTmpId": 4, "battleSkill": 1033, "reachOffset": {"x": -220, "y": -2}}, {"id": 2016, "attack": 2, "hp": 3, "quality": 1, "score": 2, "isBoss": 0, "name": "name_monster_2016", "iconBig": "monster_2016_icon_big", "iconCircle": "monster_2016_icon_circle", "spine": "monster_2016", "skillTmpId": 4, "battleSkill": 1006, "reachOffset": {"x": -250, "y": -2}}, {"id": 2018, "attack": 3, "hp": 3, "quality": 1, "score": 2, "isBoss": 0, "name": "name_monster_2018", "iconBig": "monster_2018_icon_big", "iconCircle": "monster_2018_icon_circle", "spine": "monster_2018", "skillTmpId": 4, "battleSkill": 1024, "reachOffset": {"x": -220, "y": -2}}, {"id": 2019, "attack": 5, "hp": 1, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2019", "iconBig": "monster_2019_icon_big", "iconCircle": "monster_2019_icon_circle", "spine": "monster_2019", "skillTmpId": 4, "battleSkill": 1043, "reachOffset": {"x": -230, "y": -2}}, {"id": 2021, "attack": 3, "hp": 5, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2021", "iconBig": "monster_2021_icon_big", "iconCircle": "monster_2021_icon_circle", "spine": "monster_2021", "skillTmpId": 4, "battleSkill": 1004, "reachOffset": {"x": -300, "y": -2}}, {"id": 2022, "attack": 4, "hp": 4, "quality": 2, "score": 4, "isBoss": 0, "name": "name_monster_2022", "iconBig": "monster_2022_icon_big", "iconCircle": "monster_2022_icon_circle", "spine": "monster_2022", "skillTmpId": 4, "battleSkill": 1011, "reachOffset": {"x": -280, "y": -2}}, {"id": 2023, "attack": 2, "hp": 5, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2023", "iconBig": "monster_2023_icon_big", "iconCircle": "monster_2023_icon_circle", "spine": "monster_2023", "skillTmpId": 4, "battleSkill": 1042, "reachOffset": {"x": -250, "y": -2}}, {"id": 2024, "attack": 3, "hp": 4, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2024", "iconBig": "monster_2024_icon_big", "iconCircle": "monster_2024_icon_circle", "spine": "monster_2024", "skillTmpId": 4, "battleSkill": 1021, "reachOffset": {"x": -300, "y": -2}}, {"id": 2025, "attack": 4, "hp": 3, "quality": 2, "score": 2, "isBoss": 0, "name": "name_monster_2025", "iconBig": "monster_2025_icon_big", "iconCircle": "monster_2025_icon_circle", "spine": "monster_2025", "skillTmpId": 4, "battleSkill": 1020, "reachOffset": {"x": -280, "y": -30}}, {"id": 2026, "attack": 4, "hp": 4, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2026", "iconBig": "monster_2026_icon_big", "iconCircle": "monster_2026_icon_circle", "spine": "monster_2026", "skillTmpId": 4, "battleSkill": 1036, "reachOffset": {"x": -220, "y": -5}}, {"id": 2027, "attack": 4, "hp": 4, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2027", "iconBig": "monster_2027_icon_big", "iconCircle": "monster_2027_icon_circle", "spine": "monster_2027", "skillTmpId": 4, "battleSkill": 1003, "reachOffset": {"x": -250, "y": -2}}, {"id": 2028, "attack": 3, "hp": 3, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2028", "iconBig": "monster_2028_icon_big", "iconCircle": "monster_2028_icon_circle", "spine": "monster_2028", "skillTmpId": 4, "battleSkill": 1039, "reachOffset": {"x": -250, "y": -2}}, {"id": 2029, "attack": 3, "hp": 4, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2029", "iconBig": "monster_2029_icon_big", "iconCircle": "monster_2029_icon_circle", "spine": "monster_2029", "skillTmpId": 4, "battleSkill": 1026, "reachOffset": {"x": -250, "y": -2}}, {"id": 2030, "attack": 3, "hp": 3, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2030", "iconBig": "monster_2030_icon_big", "iconCircle": "monster_2030_icon_circle", "spine": "monster_2030", "skillTmpId": 4, "battleSkill": 1041, "reachOffset": {"x": -250, "y": -2}}, {"id": 2031, "attack": 2, "hp": 4, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2031", "iconBig": "monster_2031_icon_big", "iconCircle": "monster_2031_icon_circle", "spine": "monster_2031", "skillTmpId": 4, "battleSkill": 1008, "reachOffset": {"x": -250, "y": -2}}, {"id": 2032, "attack": 1, "hp": 4, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2032", "iconBig": "monster_2032_icon_big", "iconCircle": "monster_2032_icon_circle", "spine": "monster_2032", "skillTmpId": 4, "battleSkill": 1014, "reachOffset": {"x": -250, "y": -2}}, {"id": 2033, "attack": 4, "hp": 3, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2033", "iconBig": "monster_2033_icon_big", "iconCircle": "monster_2033_icon_circle", "spine": "monster_2033", "skillTmpId": 4, "battleSkill": 1016, "reachOffset": {"x": -250, "y": -2}}, {"id": 2034, "attack": 2, "hp": 5, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2034", "iconBig": "monster_2034_icon_big", "iconCircle": "monster_2034_icon_circle", "spine": "monster_2034", "skillTmpId": 4, "battleSkill": 2034, "reachOffset": {"x": -250, "y": -25}}, {"id": 2035, "attack": 4, "hp": 3, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2035", "iconBig": "monster_2035_icon_big", "iconCircle": "monster_2035_icon_circle", "spine": "monster_2035", "skillTmpId": 4, "battleSkill": 1005, "reachOffset": {"x": -235, "y": -15}}, {"id": 2036, "attack": 3, "hp": 3, "quality": 1, "score": 2, "isBoss": 0, "name": "name_monster_2036", "iconBig": "monster_2036_icon_big", "iconCircle": "monster_2036_icon_circle", "spine": "monster_2036", "skillTmpId": 4, "battleSkill": 1034, "reachOffset": {"x": -250, "y": -2}}, {"id": 2037, "attack": 3, "hp": 3, "quality": 1, "score": 2, "isBoss": 0, "name": "name_monster_2037", "iconBig": "monster_2037_icon_big", "iconCircle": "monster_2037_icon_circle", "spine": "monster_2037", "skillTmpId": 4, "battleSkill": 1013, "reachOffset": {"x": -230, "y": -2}}, {"id": 2038, "attack": 2, "hp": 4, "quality": 1, "score": 2, "isBoss": 0, "name": "name_monster_2038", "iconBig": "monster_2038_icon_big", "iconCircle": "monster_2038_icon_circle", "spine": "monster_2038", "skillTmpId": 4, "battleSkill": 1002, "reachOffset": {"x": -250, "y": -2}}, {"id": 2039, "attack": 3, "hp": 3, "quality": 1, "score": 2, "isBoss": 0, "name": "name_monster_2039", "iconBig": "monster_2039_icon_big", "iconCircle": "monster_2039_icon_circle", "spine": "monster_2039", "skillTmpId": 4, "battleSkill": 1025, "reachOffset": {"x": -250, "y": -2}}, {"id": 2040, "attack": 3, "hp": 3, "quality": 1, "score": 2, "isBoss": 0, "name": "name_monster_2040", "iconBig": "monster_2040_icon_big", "iconCircle": "monster_2040_icon_circle", "spine": "monster_2040", "skillTmpId": 4, "battleSkill": 1015, "reachOffset": {"x": -220, "y": -2}}, {"id": 2041, "attack": 2, "hp": 4, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2041", "iconBig": "monster_2041_icon_big", "iconCircle": "monster_2041_icon_circle", "spine": "monster_2041", "skillTmpId": 4, "battleSkill": 1010, "reachOffset": {"x": -250, "y": -2}}, {"id": 2042, "attack": 4, "hp": 3, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2042", "iconBig": "monster_2042_icon_big", "iconCircle": "monster_2042_icon_circle", "spine": "monster_2042", "skillTmpId": 4, "battleSkill": 1001, "reachOffset": {"x": -250, "y": -2}}, {"id": 2043, "attack": 2, "hp": 4, "quality": 2, "score": 3, "isBoss": 0, "name": "name_monster_2043", "iconBig": "monster_2043_icon_big", "iconCircle": "monster_2043_icon_circle", "spine": "monster_2043", "skillTmpId": 4, "battleSkill": 1031, "reachOffset": {"x": -250, "y": -2}}, {"id": 2045, "attack": 5, "hp": 3, "quality": 3, "score": 4, "isBoss": 0, "name": "name_monster_2045", "iconBig": "monster_2045_icon_big", "iconCircle": "monster_2045_icon_circle", "spine": "monster_2045", "skillTmpId": 4, "battleSkill": 1009, "reachOffset": {"x": -250, "y": -2}}, {"id": 2079, "attack": 3, "hp": 4, "score": 1, "isSp": 1, "name": "name_monster_2079", "iconBig": "monster_2079_icon_big", "iconCircle": "monster_2079_icon_circle", "spine": "monster_2079", "skillTmpId": 4, "battleSkill": 2079, "reachOffset": {"x": -250, "y": -2}, "width": 50}, {"id": 2080, "attack": 2, "hp": 4, "score": 1, "isSp": 1, "name": "name_monster_2080", "iconBig": "monster_2080_icon_big", "iconCircle": "monster_2080_icon_circle", "spine": "monster_2080", "skillTmpId": 4, "battleSkill": 2080, "reachOffset": {"x": -250, "y": -2}}, {"id": 2085, "attack": 3, "hp": 3, "isSp": 1, "name": "name_monster_2085", "iconBig": "monster_2085_icon_big", "iconCircle": "monster_2085_icon_circle", "spine": "monster_2085", "skillTmpId": 4, "battleSkill": 2085, "reachOffset": {"x": -250, "y": -2}}, {"id": 3001, "attack": 4, "hp": 8, "quality": 1, "score": 0, "isBoss": 0, "isSp": 1, "name": "name_monster_3001", "iconBig": "monster_3001_icon_big", "iconCircle": "monster_3001_icon_circle", "spine": "monster_2065_zhao_1", "skillTmpId": 2, "battleSkill": 3001}, {"id": 3002, "attack": 4, "hp": 8, "quality": 1, "score": 0, "isBoss": 0, "isSp": 1, "name": "name_monster_3002", "iconBig": "monster_3002_icon_big", "iconCircle": "monster_3002_icon_circle", "spine": "monster_2065_zhao_2", "skillTmpId": 2, "battleSkill": 3002}, {"id": 3003, "attack": 2, "hp": 4, "quality": 1, "score": 0, "isBoss": 0, "isSp": 1, "name": "name_monster_3003", "iconBig": "monster_3003_icon_big", "iconCircle": "monster_3003_icon_circle", "spine": "monster_2065_zhao_3"}]