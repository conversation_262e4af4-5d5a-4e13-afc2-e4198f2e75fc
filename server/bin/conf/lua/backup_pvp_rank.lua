-- PVP排行榜备份脚本
-- KEYS[1]: 源排行榜key (rankKey)
-- KEYS[2]: 备份排行榜key (backupKey)
-- ARGV[1]: 过期时间(秒)

local rankKey = KEYS[1]
local backupKey = KEYS[2]
local expireSeconds = tonumber(ARGV[1])

-- 检查源排行榜是否存在
if redis.call('EXISTS', rankKey) == 0 then
    return {-1, 0, "源排行榜不存在"}
end

-- 删除旧的备份(如果存在)
redis.call('DEL', backupKey)

-- 复制排行榜数据
local members = redis.call('ZRANGE', rankKey, 0, -1, 'WITHSCORES')
if #members > 0 then
    -- ZRANGE WITHSCORES 返回格式: [member1, score1, member2, score2, ...]
    -- ZADD 需要格式: [score1, member1, score2, member2, ...]
    -- 重新组织数据
    local zaddArgs = {}
    for i = 1, #members, 2 do
        local member = members[i]
        local score = members[i + 1]
        table.insert(zaddArgs, score)
        table.insert(zaddArgs, member)
    end
    -- 批量添加到备份key
    redis.call('ZADD', backupKey, unpack(zaddArgs))
end

-- 设置过期时间
redis.call('EXPIRE', backupKey, expireSeconds)

-- 返回成功结果: {状态码, 成员数量, 消息}
return {0, #members / 2, "备份成功"}
