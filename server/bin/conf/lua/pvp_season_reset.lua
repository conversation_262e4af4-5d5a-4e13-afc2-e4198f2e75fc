-- PVP赛季重置玩家积分脚本
-- KEYS[1]: 玩家数据key (plrDataKey)
-- ARGV[1]: 玩家ID (用于调试日志)

local plrDataKey = KEYS[1]
local playerId = ARGV[1]

-- 获取当前积分
local currentScore = redis.call('HGET', plrDataKey, 'score')

-- 如果积分不存在，返回错误
if currentScore == false then
    return {-1, "玩家积分数据不存在", playerId}
end

-- 转换为数字
local oldScore = math.floor(tonumber(currentScore))
if oldScore == nil then
    return {-2, "积分数据格式错误", playerId}
end

-- 计算新分数: 1000 + (oldScore-1000)*0.2
local newScore = 1000 + (oldScore - 1000) * 0.2

-- 更新积分
redis.call('HSET', plrDataKey, 'score', newScore)

-- 返回成功结果
-- 返回格式: {状态码, 新积分, 原积分, 玩家ID}
return {0, newScore, oldScore, playerId}