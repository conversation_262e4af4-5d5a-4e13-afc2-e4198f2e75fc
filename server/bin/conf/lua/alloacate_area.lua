-- 动态分区分配脚本
local server_areas_key = KEYS[1]      -- "server_areas" 哈希表
local current_server_key = KEYS[2]    -- "server_area_idx" 字符串
local max_players = tonumber(ARGV[1]) -- 最大玩家数限制

-- 获取当前活跃区服ID
local current_server_id = redis.call('GET', current_server_key)
if current_server_id == false then
    -- 首次运行,初始化为区服1
    current_server_id = 1
    redis.call('SET', current_server_key, '1')
else
    current_server_id = tonumber(current_server_id)
end

-- 获取当前区服的玩家数量
local current_count = redis.call('HGET', server_areas_key, tostring(current_server_id))
if current_count == false then
    -- 区服不存在,初始化为0
    current_count = 0
else
    current_count = tonumber(current_count)
end

-- 检查当前区服是否还有空位
if current_count < max_players then
    -- 当前区服未满，分配到此区服
    local new_count = redis.call('HINCRBY', server_areas_key, tostring(current_server_id), 1)
    return {current_server_id, tonumber(new_count), 0}
else
    -- 当前区服已满，创建新区服
    local new_server_id = current_server_id + 1
    -- 更新当前活跃区服ID
    redis.call('SET', current_server_key, tostring(new_server_id))
    -- 初始化新区服,设置玩家数为1
    redis.call('HSET', server_areas_key, tostring(new_server_id), '1')
    return {new_server_id, 1, 1}
end