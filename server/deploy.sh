
project_path="$(cd `dirname $0`; cd ../; pwd)"/
server_path=""$project_path"server/"
pem=""$server_path"tw_train.pem"
remote_server_path=/root/train_test
ip="**************"
port=4653

time=$(date "+%Y%m%d-%H%M%S")
code_name="code_"${time}".tar.gz"
files=(train bin)

cmdRes=""
handleCmd(){
   ip_=$1
   cmd_=$2
   cmd_str="sudo ssh -i "$pem" root@"$ip_" "$cmd_""
   echo $cmd_str
   cmdRes=`$cmd_str`
}


# cmd="lsof -ti:$port"
# handleCmd $ip "$cmd"
#杀掉对应的进程，如果pid不存在，则不执行
# pid=$cmdRes
# if [  -n  "$pid"  ];  then
#     handleCmd $ip "kill $pid"
# fi

#build
GOOS=linux GOARCH=amd64 go build

#压缩上传
tar czvf "$server_path""$code_name" "${files[@]}"
sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_server_path"
rm -rf "$server_path""$code_name"

#停掉进程
handleCmd $ip "pm2 stop train"

#解压
cmd="tar xzvf "$remote_server_path"/"$code_name" -C "$remote_server_path"" 
handleCmd $ip "$cmd"

#运行
cmd=""$remote_server_path"/reload.sh "
handleCmd $ip "$cmd"

