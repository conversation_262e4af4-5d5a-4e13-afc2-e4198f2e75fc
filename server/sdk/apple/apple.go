package apple

import (
	"crypto/ecdsa"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"io"
	"math/big"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"
	comm "train/common"
	ut "train/utils"

	"github.com/dgrijalva/jwt-go"
	"github.com/huyangv/vmqant/log"
)

const (
	APPLE_APPID            = "global.train.twgame"
	APPLE_PRIVATE_KEY_PATH = "bin/conf/AuthKey_T2MP63VN8J.p8"
	APPLE_ROOT_CERT_PATH   = "bin/conf/appleRootCerts"
	APPLE_KID              = "T2MP63VN8J"
	APPLE_TEAM_ID          = "ZM6HGAP5T2"

	APPLE_STORE_PRIVATE_KEY_PATH = "bin/conf/SubscriptionKey_YG65D56U53.p8"
	APPLE_STORE_KID              = "YG65D56U53"

	APPLE_SHARED_SECRET = "83231c7e58bd41e7a5e71058b8aec252"
	APPLE_ISS_USER_ID   = "ef55a6db-ed80-465d-992b-134d0c8f99dc"

	APPLE_URL                       = "https://appleid.apple.com/auth/token"
	APPLE_PUBLICK_KEY_URL           = "https://appleid.apple.com/auth/keys"
	APPLE_ORDER_VERIFY_URL          = "https://buy.itunes.apple.com/verifyReceipt"                   // Appple已弃用
	APPLE_ORDER_VERIFY_URL_SANDBOX  = "https://sandbox.itunes.apple.com/verifyReceipt"               // Appple已弃用
	APPLE_TRANSACTION_URL           = "https://api.storekit.itunes.apple.com/inApps/v1/transactions" // 订单查询 GET
	APPLE_TRANSACTION_URL_SANDBOX   = "https://api.storekit-sandbox.itunes.apple.com/inApps/v1/transactions"
	APPLE_SUBSCRIPTIONS_URL         = "https://api.storekit.itunes.apple.com/inApps/v1/subscriptions" // 订阅查询 GET
	APPLE_SUBSCRIPTIONS_URL_SANDBOX = "https://api.storekit-sandbox.itunes.apple.com/inApps/v1/subscriptions"
)

type AppleVerifyTokenRet struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	IdToken      string `json:"id_token"`
}
type AppleTokenHeader struct {
	Kid string   `json:"kid"`
	Alg string   `json:"alg"`
	X5c []string `json:"x5c"`
}

type ApplePublickKeyRet struct {
	Keys []*ApplePublickKey `json:"keys"`
}
type ApplePublickKey struct {
	Kty string `json:"kty"`
	Kid string `json:"kid"`
	Use string `json:"use"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

var appleLoginPrivateKey *ecdsa.PrivateKey = nil
var appleStorePrivateKey *ecdsa.PrivateKey = nil
var appleRootCertMap map[string]*x509.Certificate = nil

// APPLE登录验证
func AppleLoginCheck(code, token, userId string) (ret *AppleVerifyTokenRet, err error) {
	log.Info("AppleLoginCheck code: %v, token: %v, userId: %v", code, token, userId)
	now := ut.Now() / 1000
	// 生成请求的jwt
	jwtToken := jwt.NewWithClaims(jwt.SigningMethodES256, jwt.MapClaims{
		"iss": APPLE_TEAM_ID,
		"iat": now,
		"exp": now + 60*60*24*30,
		"aud": "https://appleid.apple.com",
		"sub": APPLE_APPID,
	})
	jwtToken.Header["alg"] = "ES256"
	jwtToken.Header["kid"] = APPLE_KID
	err = getAppleLoginPrivateKey()
	if err != nil {
		log.Error("getApplePrivateKey err: %v", err)
		return
	}
	sign, err := jwtToken.SignedString(appleLoginPrivateKey)
	if err != nil {
		log.Error("AppleLoginCheck sign err: %v", err)
		return
	}
	resp, err := http.PostForm(APPLE_URL, url.Values{
		"client_id":     {APPLE_APPID},
		"client_secret": {sign},
		"code":          {code},
		"grant_type":    {"authorization_code"},
	})
	log.Info("AppleLoginCheck sign: %v", sign)
	defer func() {
		_ = resp.Body.Close()
	}()
	if err != nil {
		log.Error("AppleLoginCheck http err:%v", err)
		return
	}
	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		err = errors.New("AppleLoginCheck http resp status err")
		log.Error("AppleLoginCheck http resp status err, status: %v, errStr: %v", resp.Status, string(body))
		return
	}
	err = json.Unmarshal(body, &ret)
	if err != nil {
		log.Error("AppleLoginCheck ret err:%v", err)
		return
	}

	log.Info("AppleLoginCheck ret idtoken: %v", ret.IdToken)
	headerStr := strings.Split(ret.IdToken, ".")[0]
	headerBytes, err := base64.RawStdEncoding.DecodeString(headerStr)
	if err != nil {
		log.Error("AppleLoginCheck resp headerStr decode err: %v", err)
		return
	}
	headerData := &AppleTokenHeader{}
	err = json.Unmarshal(headerBytes, headerData)
	if err != nil {
		log.Error("AppleLoginCheck resp headerStr Unmarshal err: %v", err)
		return
	}
	appleKid := ut.String(headerData.Kid)
	// 获取publickKey
	publicKey, err := getpublicKey(appleKid)
	if publicKey == nil {
		log.Error("AppleLoginCheck get publicKey nil APPLE_KID: %v", APPLE_KID)
		return
	}
	// 解析返回的idToken
	// retToken, _, err := new(jwt.Parser).ParseUnverified(ret.IdToken, jwt.MapClaims{})
	retToken, err := jwt.Parse(ret.IdToken, func(token *jwt.Token) (interface{}, error) {
		return publicKey, nil
	})
	if err != nil {
		log.Error("AppleLoginCheck Parse reponse jwt err: %v", err)
		return
	}

	mapClaims := retToken.Claims.(jwt.MapClaims)
	if mapClaims == nil {
		errStr := "AppleLoginCheck response mapClaims nil"
		log.Error(errStr)
		err = errors.New(errStr)
		return
	}
	// APPid是否一致
	if mapClaims["aud"] != APPLE_APPID {
		log.Error("AppleLoginCheck response appid err: %v", mapClaims["aud"])
		err = errors.New("AppleLoginCheck appid err")
		return
	}
	// openid是否一致
	if mapClaims["sub"] != userId {
		log.Error("AppleLoginCheck response openid err: %v", mapClaims["sub"])
		err = errors.New("AppleLoginCheck openid err")
		return
	}
	return
}

func getpublicKey(kid string) (pubKey *rsa.PublicKey, err error) {
	log.Info("getpublicKey kid: %v", kid)
	publicKeyResp, err := http.Get(APPLE_PUBLICK_KEY_URL)
	defer func() {
		_ = publicKeyResp.Body.Close()
	}()
	if err != nil {
		log.Error("apple publicKeyResp http err: %v", err)
		return
	}
	if publicKeyResp.StatusCode != http.StatusOK {
		err = errors.New("apple publicKeyResp resp status err")
		log.Error("apple publicKeyResp http resp status err, status: %v", publicKeyResp.Status)
		return
	}
	publicKeyRespBody, _ := io.ReadAll(publicKeyResp.Body)
	publickKeyRet := &ApplePublickKeyRet{}
	err = json.Unmarshal(publicKeyRespBody, &publickKeyRet)
	if err != nil {
		log.Error("apple publicKeyResp ret err: %v", err)
		return
	}
	//根据配置的kid返回对应的公钥
	for _, v := range publickKeyRet.Keys {
		if v.Kid == kid {
			n, err := base64.RawURLEncoding.DecodeString(v.N)
			if err != nil {
				return nil, err
			}
			e, err := base64.RawURLEncoding.DecodeString(v.E)
			if err != nil {
				return nil, err
			}
			pubKey = &rsa.PublicKey{N: new(big.Int).SetBytes(n), E: int(new(big.Int).SetBytes(e).Int64())}
		}
	}
	return
}

type AppleOrderVerifyReq struct {
	ReceiptData string `json:"receipt-data"`
	Passward    string `json:"passward"`
}

type AppleOrderVerifyRet struct {
	Environment string                   `json:"environment"` //系统生成收据的环境 可能的值：Sandbox, Production
	Status      int                      `json:"status"`      //如果0收据有效
	Receipt     *AppleOrderVerifyReceipt `json:"receipt"`
}

type AppleOrderVerifyReceipt struct {
	InApp []*AppleOrderVerifyInAppInfo `json:"in_app"`
}
type AppleOrderVerifyInAppInfo struct {
	TransactionId string `json:"transaction_id"`
	ProductId     string `json:"product_id"`
	Quantity      string `json:"quantity"`
}

type AppleTransactionInfoRet struct {
	SignedTransactionInfo string `json:"signedTransactionInfo"` // 已签名的JWS格式交易信息
}

// 订单验证
func AppleOrderVerify(orderId, productId, token string) (err error, quantity int) {
	log.Info("AppleOrderVerify orderId: %v, productId: %v, token: %v", orderId, productId, token)
	url := APPLE_TRANSACTION_URL
	if comm.IsSandBox() {
		url = APPLE_TRANSACTION_URL_SANDBOX
	}
	// 生成签名
	sign, err := generateAppleStoreJWS()
	if err != nil {
		log.Error("AppleOrderVerify sign err: %v", err)
		return
	}
	client := &http.Client{Timeout: 10 * time.Second}
	url = url + "/" + orderId
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Error("AppleOrderVerify http req err: %v", err)
		return
	}
	req.Header.Add("Authorization", "Bearer "+sign)
	resp, err := client.Do(req)
	defer func() {
		_ = resp.Body.Close()
	}()
	if err != nil {
		log.Error("AppleOrderVerify http err:%v", err)
		return
	}
	if resp.StatusCode != http.StatusOK {
		err = errors.New("AppleOrderVerify http resp status err")
		log.Error("AppleOrderVerify http resp status err, status: %v", resp.Status)
		return
	}
	body, _ := io.ReadAll(resp.Body)
	ret := &AppleTransactionInfoRet{}
	err = json.Unmarshal(body, &ret)
	if err != nil {
		log.Error("AppleOrderVerify ret err:%v", err)
		return
	}
	headerStr := strings.Split(ret.SignedTransactionInfo, ".")[0]
	// 解析签名标头
	publicKey, err := ParseAppleJwsHead(headerStr)
	if err != nil {
		log.Error("AppleOrderVerify parseAppleJwsHead err: %v", err)
		return
	}
	// 解析JWS格式的交易信息
	mapClaims, err := ut.ParseJwsToken(ret.SignedTransactionInfo, publicKey)
	// bundleId检测
	if mapClaims["bundleId"] != APPLE_APPID {
		err = errors.New("AppleOrderVerify bundleId err")
		log.Error("AppleOrderVerify bundleId err, bundleId: %v, ret bundleId: %v", APPLE_APPID, mapClaims["bundleId"])
		return
	}
	// productId检测
	if mapClaims["productId"] != productId {
		err = errors.New("AppleOrderVerify productId err")
		log.Error("AppleOrderVerify productId err, db productId: %v, ret productId: %v", productId, mapClaims["productId"])
		return
	}
	quantity = ut.Atoi(mapClaims["quantity"])
	return
}

type AppleSubResponse struct {
	Data        []*SubscriptionGroupIdentifier `json:"data"`        //自动续订订阅的一系列信息
	Environment string                         `json:"environment"` //服务器环境
	AppAppleId  int                            `json:"appAppleId"`  //应用程序的 App Store 标识符
	BundleId    string                         `json:"bundleId"`    //应用程序的捆绑包标识符
}

type SubscriptionGroupIdentifier struct {
	Identifier       string                  `json:"subscriptionGroupIdentifier"` //数组中自动续订订阅的订阅组标识符
	LastTransactions []*LastTransactionsItem `json:"lastTransactions"`            //订阅组中所有自动续订订阅的最新 App Store 签名交易信息和 App Store 签名续订信息的数组
}

type LastTransactionsItem struct {
	OriginalTransactionId string `json:"originalTransactionId"`
	SignedRenewalInfo     string `json:"signedRenewalInfo"`
	SignedTransactionInfo string `json:"signedTransactionInfo"`
	Status                int    `json:"status"` //1自动续订订阅已激活。 2自动续订订阅已过期。3自动续费订阅正处于计费重试期内。4动续订订阅被撤销。
}

// Apple订阅验证
func AppleSubVerify(orderId string) (err error, startTime, endTime int, auto bool, originalId, productId string) {
	log.Info("AppleSubVerify orderId: %v", orderId)
	url := APPLE_SUBSCRIPTIONS_URL
	if comm.IsSandBox() {
		url = APPLE_SUBSCRIPTIONS_URL_SANDBOX
	}
	// 生成签名
	sign, err := generateAppleStoreJWS()
	if err != nil {
		log.Error("AppleSubVerify sign err: %v", err)
		return
	}
	client := &http.Client{Timeout: 10 * time.Second}
	url = url + "/" + orderId
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Error("AppleSubVerify http req err: %v", err)
		return
	}
	req.Header.Add("Authorization", "Bearer "+sign)
	resp, err := client.Do(req)
	defer func() {
		_ = resp.Body.Close()
	}()
	if err != nil {
		log.Error("AppleSubVerify http err:%v", err)
		return
	}
	if resp.StatusCode != http.StatusOK {
		err = errors.New("AppleSubVerify http resp status err")
		log.Error("AppleSubVerify http resp status err, status: %v", resp.Status)
		return
	}
	body, _ := io.ReadAll(resp.Body)
	ret := &AppleSubResponse{}
	err = json.Unmarshal(body, &ret)
	if err != nil {
		log.Error("AppleSubVerify ret err:%v", err)
		return
	}
	if len(ret.Data) == 0 || len(ret.Data[0].LastTransactions) == 0 {
		err = errors.New("AppleSubVerify ret nil")
		log.Error(err.Error())
		return
	}
	lastInfo := ret.Data[0].LastTransactions[0]
	headerStr := strings.Split(lastInfo.SignedTransactionInfo, ".")[0]
	// 解析支付信息签名标头
	publicKey, err := ParseAppleJwsHead(headerStr)
	if err != nil {
		log.Error("AppleSubVerify parseAppleJwsHead err: %v", err)
		return
	}
	// 解析JWS格式的支付信息
	mapClaims, err := ut.ParseJwsToken(lastInfo.SignedTransactionInfo, publicKey)
	startTime = ut.Int(mapClaims["originalPurchaseDate"])
	endTime = ut.Int(mapClaims["expiresDate"])
	// 解析订阅信息签名标头
	subHeaderStr := strings.Split(lastInfo.SignedRenewalInfo, ".")[0]
	subPublicKey, err := ParseAppleJwsHead(subHeaderStr)
	if err != nil {
		log.Error("AppleSubVerify parseSubAppleJwsHead err: %v", err)
		return
	}
	// 解析JWS格式的订阅信息
	subMapClaims, err := ut.ParseJwsToken(lastInfo.SignedRenewalInfo, subPublicKey)
	auto = false
	if ut.Int(subMapClaims["autoRenewStatus"]) == 1 {
		auto = true
	}
	originalId = ut.String(subMapClaims["originalTransactionId"])
	productId = ut.String(subMapClaims["productId"])
	return
}

// Apple推送类型
const (
	APPLE_NOTIFY_TYPE_CONSUMPTION_REQUEST       = "CONSUMPTION_REQUEST"       // 退款请求
	APPLE_NOTIFY_TYPE_DID_CHANGE_RENEWAL_STATUS = "DID_CHANGE_RENEWAL_STATUS" // 续订状态更改
	APPLE_NOTIFY_TYPE_DID_FAIL_TO_RENEW         = "DID_FAIL_TO_RENEW"         // 续订失败
	APPLE_NOTIFY_TYPE_DID_RENEW                 = "DID_RENEW"                 // 续订成功
	APPLE_NOTIFY_TYPE_EXPIRED                   = "EXPIRED"                   // 订阅过期
	APPLE_NOTIFY_TYPE_REFUND                    = "REFUND"                    // 退款成功
	APPLE_NOTIFY_TYPE_REFUND_REVERSED           = "REFUND_REVERSED"           // 撤销退款
	APPLE_NOTIFY_SUB_TYPE_AUTO_RENEW_DISABLED   = "AUTO_RENEW_DISABLED"       // 子类型 取消续订
	APPLE_NOTIFY_SUB_TYPE_AUTO_RENEW_ENABLED    = "AUTO_RENEW_ENABLED"        // 子类型 续订
)

// Apple应用内购买类型
const (
	APPLE_TRANSACTION_TYPE_AUTO_RENEWABLE_SUBSCRIPTION = "Auto-Renewable Subscription" // 自动续订订阅
	APPLE_TRANSACTION_TYPE_NON_CONSUMABLE              = "Non-Consumable"              // 非消耗性应用内购买
	APPLE_TRANSACTION_TYPE_CONSUMABLE                  = "Consumable"                  // 消耗性应用内购买
	APPLE_TRANSACTION_TYPE_NON_RENEWING_SUBSCRIPTION   = "Non-Renewing Subscription"   // 不可续订的订阅
)

// Apple推送数据
type AppleNotifyData struct {
	AppAppleId            int    `json:"appAppleId"`            //应用程序的 App Store 标识符
	BundleId              string `json:"bundleId"`              //应用程序的捆绑包标识符
	Environment           string `json:"environment"`           //服务器环境
	SignedRenewalInfo     string `json:"signedRenewalInfo"`     //JWS格式订阅续订信息
	SignedTransactionInfo string `json:"signedTransactionInfo"` //JWS格式交易信息
	Status                int    `json:"status"`                //自动续订订阅的状态 1已激活 2已过期 3计费重试中 4计费宽限期内 5已撤销
}

// 解析JWS标头
func ParseAppleJwsHead(headerStr string) (pubKey *ecdsa.PublicKey, err error) {
	headerBytes, err := base64.RawStdEncoding.DecodeString(headerStr)
	if err != nil {
		log.Error("parseAppleJwsHead resp headerStr decode err: %v", err)
		return
	}
	headerData := &AppleTokenHeader{}
	err = json.Unmarshal(headerBytes, headerData)
	if err != nil {
		log.Error("parseAppleJwsHead resp headerStr Unmarshal err: %v", err)
		return
	}
	// apple store使用的证书链为 终端证书 中间证书 根证书
	if len(headerData.X5c) < 3 {
		log.Error("parseAppleJwsHead resp headerStr X5c nil")
		return
	}
	certList := []*x509.Certificate{}
	for _, certStr := range headerData.X5c {
		cert, e := ut.GetCertByCertStr(certStr)
		if e != nil {
			err = e
			log.Error("parseAppleJwsHead cert parse certStr: %v err: %v", err)
			return
		}
		certList = append(certList, cert)
	}
	// 验证证书链
	err = ut.VerifyCertChain(certList)
	if err != nil {
		log.Error("parseAppleJwsHead VerifyCertChain headerData.X5c: %v err: %v", headerData.X5c, err)
		return
	}
	// 对比根证书
	trustedRootCert := appleRootCertMap[certList[2].Issuer.CommonName]
	if trustedRootCert == nil || !trustedRootCert.Equal(certList[2]) {
		// 根证书不匹配
		err = errors.New("root cert not match")
		log.Error("parseAppleJwsHead err: %v", err)
		return
	}
	// 从终端证书提取公钥
	pubKey, ok := certList[0].PublicKey.(*ecdsa.PublicKey)
	if !ok {
		log.Error("parseAppleJwsHead get cert not include pubKey")
		return
	}
	return
}

// 退款验证
func AppleOrderRefundVerify(orderId, notifyType, environment, passward string) bool {
	log.Info("AppleOrderRefundVerify orderId: %v, notifyType: %v, environment: %v, passward: %v", orderId, notifyType, environment, passward)
	if notifyType != "REFUND" {
		return false
	}
	if passward != APPLE_SHARED_SECRET {
		log.Error("AppleOrderRefundVerify passward err orderId: %v, passward: %v", orderId, passward)
		return false
	}
	if (environment == "PROD" && comm.IsSandBox()) || (environment == "Sandbox" && !comm.IsSandBox()) {
		// 环境不匹配
		log.Error("AppleOrderRefundVerify environment err orderId: %v, environment: %v, comm.IsSandBox(): %v", orderId, environment, comm.IsSandBox())
		return false
	}
	return true
}

// 生成Apple商店api通用jws
func generateAppleStoreJWS() (sign string, err error) {
	now := ut.Now() / 1000
	// 生成请求的jwt
	jwtToken := jwt.NewWithClaims(jwt.SigningMethodES256, jwt.MapClaims{
		"iss": APPLE_ISS_USER_ID,
		"iat": now,
		"exp": now + 60*10,
		"aud": "appstoreconnect-v1",
		"bid": APPLE_APPID,
	})
	jwtToken.Header["alg"] = "ES256"
	jwtToken.Header["kid"] = APPLE_STORE_KID
	jwtToken.Header["typ"] = "JWT"
	err = getAppleStorePrivateKey()
	if err != nil {
		return
	}
	sign, err = jwtToken.SignedString(appleStorePrivateKey)
	if err != nil {
		log.Error("generateAppleStoreJWS sign err: %v", err)
		return
	}
	return
}

func getAppleLoginPrivateKey() (err error) {
	if appleLoginPrivateKey == nil {
		//读取私钥文件
		privateKeyBytes, e := os.ReadFile(APPLE_PRIVATE_KEY_PATH)
		if e != nil {
			log.Error("getApplePrivateKey read private key err: %v", e)
			err = e
			return
		}
		// 解析私钥
		block, _ := pem.Decode(privateKeyBytes)
		if block == nil {
			err = errors.New("getApplePrivateKey failed to decode PEM block containing private key")
			log.Error(err.Error())
			return
		}
		key, e := x509.ParsePKCS8PrivateKey(block.Bytes)
		if e != nil {
			log.Error("getApplePrivateKey ParsePKCS8PrivateKey err: %v", e)
			err = e
			return
		}
		// 将私钥转换为ECDSA类型
		privateKey, ok := key.(*ecdsa.PrivateKey)
		if !ok {
			err = errors.New("getApplePrivateKey private key is not ECDSA type")
			log.Error(err.Error())
			return
		}
		appleLoginPrivateKey = privateKey
	}
	return err
}

func getAppleStorePrivateKey() (err error) {
	if appleStorePrivateKey == nil {
		//读取私钥文件
		privateKeyBytes, e := os.ReadFile(APPLE_STORE_PRIVATE_KEY_PATH)
		if e != nil {
			log.Error("getAppleStorePrivateKey read private key err: %v", e)
			err = e
			return
		}
		// 解析私钥
		block, _ := pem.Decode(privateKeyBytes)
		if block == nil {
			err = errors.New("getAppleStorePrivateKey failed to decode PEM block containing private key")
			log.Error(err.Error())
			return
		}
		key, e := x509.ParsePKCS8PrivateKey(block.Bytes)
		if e != nil {
			log.Error("getAppleStorePrivateKey ParsePKCS8PrivateKey err: %v", e)
			err = e
			return
		}
		// 将私钥转换为ECDSA类型
		privateKey, ok := key.(*ecdsa.PrivateKey)
		if !ok {
			err = errors.New("getAppleStorePrivateKey private key is not ECDSA type")
			log.Error(err.Error())
			return
		}
		appleStorePrivateKey = privateKey
	}
	return err
}

// 初始化Apple根证书
func InitAppleRootCerts() {
	// 打开文件夹
	dir, err := os.Open(APPLE_ROOT_CERT_PATH)
	if err != nil {
		log.Error("InitAppleRootCerts open folder err: %v", err)
		return
	}
	defer dir.Close()

	// 读取文件夹中的文件
	fileInfos, err := dir.Readdir(-1)
	if err != nil {
		log.Error("InitAppleRootCerts read files err: %v", err)
		return
	}
	appleRootCertMap = map[string]*x509.Certificate{}

	// 遍历文件夹中的文件
	for _, fileInfo := range fileInfos {
		// 检查文件是否为普通文件
		if fileInfo.Mode().IsRegular() {
			// 获取文件路径
			filePath := filepath.Join(APPLE_ROOT_CERT_PATH, fileInfo.Name())
			// 读取文件内容
			rootCertPEM, err := os.ReadFile(filePath)
			if err != nil {
				log.Error("InitAppleRootCerts read files err: %v", err)
				continue
			}
			// 解析X.509根证书
			trustedRootCert, err := x509.ParseCertificate(rootCertPEM)
			if err != nil {
				log.Error("getAppleRootCert ParseCertificate err: %v", err)
				continue
			}
			appleRootCertMap[trustedRootCert.Issuer.CommonName] = trustedRootCert
		}
	}
}
