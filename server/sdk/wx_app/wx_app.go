package wx_app

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
)

const (
	APPID        = "wxf902c10f50dca8bb"
	SECRET       = "851442c542babb5bc6c5bd87dcc108b9"
	CODE_URL     = "https://api.weixin.qq.com/sns/oauth2/access_token?"
	USERINFO_URL = "https://api.weixin.qq.com/sns/userinfo?"
)

type Jscode2sessionRet struct {
	Openid      string `json:"openid"`
	AccessToken string `json:"access_token"`
	// Scope		string `json:"scope"`
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
}

type Token2sessionRet struct {
	Unionid    string `json:"unionid"`
	Headimgurl string `json:"headimgurl"`
	NickName   string `json:"nickname"`
	Errcode    int    `json:"errcode"`
	Errmsg     string `json:"errmsg"`
}

// 获取微信openid根据code
func GetOpenIdByCode(code string) (e int, res Jscode2sessionRet) {
	url := CODE_URL + "appid=" + APPID + "&secret=" + SECRET + "&code=" + code + "&grant_type=authorization_code"
	resp, err := http.Get(url)
	defer func() {
		_ = resp.Body.Close()
	}()
	if err != nil {
		e = -1
		return
	}
	body, _ := ioutil.ReadAll(resp.Body)
	// fmt.Println(string(body))
	_ = json.Unmarshal(body, &res)
	e = res.Errcode
	return
}

func GetUserInfo(access_token string, openid string) (e int, res Token2sessionRet) {
	url := USERINFO_URL + "access_token=" + access_token + "&openid=" + openid

	resp, err := http.Get(url)
	defer func() {
		_ = resp.Body.Close()
	}()
	if err != nil {
		e = -1
		return
	}
	body, _ := ioutil.ReadAll(resp.Body)
	//fmt.Println(string(body))
	_ = json.Unmarshal(body, &res)
	e = res.Errcode
	return
}
