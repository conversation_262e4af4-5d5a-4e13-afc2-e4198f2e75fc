package google

import (
	"context"
	"errors"
	"io/ioutil"
	"net/http"
	"os"
	"time"
	comm "train/common"
	ut "train/utils"
	"train/utils/array"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	o2 "golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/androidpublisher/v3"
	"google.golang.org/api/oauth2/v2"
	"google.golang.org/api/option"
)

const (
	// GOOGLE_APPID                   = "769515196357-r16dvg0m8jscu9lqrmcj053gk572fuba.apps.googleusercontent.com"
	GOOGLE_APPID                   = "769515196357-bshohhtoq1uqe99opg4t071do55d47lb.apps.googleusercontent.com"
	GOOGLE_IOS_APPID               = "769515196357-gbeducfp2r1arkft8d2kq2gvd1oa40rm.apps.googleusercontent.com"
	GOOGLE_PACKAGENAME             = "global.train.twgame"
	GOOGLE_ORDER_VERIFY_URL        = "https://androidpublisher.googleapis.com/androidpublisher/v3/applications/"
	GOOGLE_PAY_KEY_FILE_PATH       = "bin/conf/google_client_secret.json"
	GOOGLE_PROJECT_ID              = "wonder-train"
	FIREBASE_MESSAGE_KEY_FILE_PATH = "bin/conf/google_client_secret.json"
)

// ---------------------- 登录 -------------------------

// Google token验证
func Login(idToken string) (ret *oauth2.Tokeninfo, err error) {
	log.Debug("GoogleLoginCheck tokidTokenenInfo: %v", idToken)
	oauth2Service, err := oauth2.NewService(context.Background(), option.WithHTTPClient(http.DefaultClient))
	if err != nil {
		log.Error("GoogleVerifyToken oauth2 newService err: %v", err)
		return
	}
	tokenInfoCall := oauth2Service.Tokeninfo()
	tokenInfoCall.IdToken(idToken)
	tokenInfo, err := tokenInfoCall.Do()
	if err != nil {
		log.Error("GoogleVerifyToken tokenInfoCall err: %v", err)
		return
	}
	log.Info("GoogleLoginCheck tokenInfo: %v, userId: %v", tokenInfo, tokenInfo.UserId)
	// token到期
	if tokenInfo.ExpiresIn >= int64(ut.Now()) {
		errStr := "GoogleVerifyToken idtoken timeout"
		log.Error(errStr)
		return nil, errors.New(errStr)
	}
	// 防止恶意程序访问同一用户
	if tokenInfo.Audience != GOOGLE_APPID && tokenInfo.Audience != GOOGLE_IOS_APPID {
		errStr := "GoogleVerifyToken idtoken appid err"
		log.Error(errStr)
		return nil, errors.New(errStr)
	}
	return tokenInfo, nil
}

//--------------------- 支付 -------------------------

type GoogleOrderVerifyRet struct {
	OrderId              string `json:"orderId"`              //Google订单号
	PurchaseState        int    `json:"purchaseState"`        //订单的购买状态 0.已购买 1.已取消 2.待处理
	ConsumptionState     int    `json:"consumptionState"`     //商品的消耗状态 0.尚未消耗 1.已使用
	AcknowledgementState int    `json:"acknowledgementState"` //应商品的确认状态 0.待确认 1. 已确认
	Quantity             int    `json:"quantity"`             //数量
}

func GoogleOrderVerify(productId, token string) (err error, quantity int, orderId string) {
	jsonKey, err := os.ReadFile(GOOGLE_PAY_KEY_FILE_PATH)
	if err != nil {
		log.Error("GoogleOrderVerify jsonKey err: %v", err)
		return
	}
	client := &http.Client{Timeout: 10 * time.Second}
	ctx := context.WithValue(context.Background(), o2.HTTPClient, client)
	conf, err := google.JWTConfigFromJSON(jsonKey, androidpublisher.AndroidpublisherScope)
	val := conf.Client(ctx).Transport.(*o2.Transport)
	_, err = val.Source.Token()
	if err != nil {
		log.Error("GoogleOrderVerify token Transport err: %v", err)
		return
	}
	service, err := androidpublisher.NewService(ctx, option.WithHTTPClient(conf.Client(ctx)))
	if err != nil {
		log.Error("GoogleOrderVerify service err: %v", err)
		return
	}
	ps := androidpublisher.NewPurchasesProductsService(service)
	result, err := ps.Get(GOOGLE_PACKAGENAME, productId, token).Context(ctx).Do()
	if err != nil {
		log.Error("GoogleOrderVerify resp err: %v, result: %v", err, result)
		return
	}
	if result.PurchaseType != nil {
		// 0.测试（即从许可测试帐号中购买的服务）1.促销（即使用促销代码购买）2.激励广告
		if !comm.IsSandBox() && *result.PurchaseType == 0 {
			err = errors.New("Test Not In SandBox")
			log.Error("Test Not In SandBox PurchaseType: %v", *result.PurchaseType)
			return
		}
	}
	if result.PurchaseState != 0 {
		//PurchaseState应为0已购买
		err = errors.New("GoogleOrderVerify state err")
		log.Error("GoogleOrderVerify state err PurchaseState: %v, ConsumptionState: %v", result.PurchaseState)
		return
	}
	log.Info("GoogleOrderVerify result.Quantity: %v", result.Quantity)
	quantity = int(result.Quantity)
	orderId = result.OrderId
	return
}

// 获取退款订单列表
func GoogleVoidedOrderCheck() (err error, list []string) {
	if comm.IsDebug() || comm.IsInland() {
		return //国内不用google
	}
	log.Info("GoogleVoidedOrderCheck")
	jsonKey, err := ioutil.ReadFile(GOOGLE_PAY_KEY_FILE_PATH)
	if err != nil {
		log.Error("GoogleVoidedOrderCheck jsonKey err: %v", err)
		return
	}
	client := &http.Client{Timeout: 10 * time.Second}
	ctx := context.WithValue(context.Background(), o2.HTTPClient, client)
	conf, err := google.JWTConfigFromJSON(jsonKey, androidpublisher.AndroidpublisherScope)
	val := conf.Client(ctx).Transport.(*o2.Transport)
	_, err = val.Source.Token()
	if err != nil {
		log.Error("GoogleVoidedOrderCheck token Transport err: %v", err)
		return
	}
	service, err := androidpublisher.NewService(ctx, option.WithHTTPClient(conf.Client(ctx)))
	if err != nil {
		log.Error("GoogleVoidedOrderCheck service err: %v", err)
		return
	}
	ps := androidpublisher.NewPurchasesVoidedpurchasesService(service)
	endTime := time.Now().UnixMilli()
	startTime := endTime - 10*60*1000 // 间隔10min
	result, err := ps.List(GOOGLE_PACKAGENAME).StartTime(startTime).EndTime(endTime).Context(ctx).Do()
	if err != nil {
		log.Error("GoogleVoidedOrderCheck resp err: %v, result: %v", err, result)
		return
	}
	log.Info("GoogleVoidedOrderCheck result: %v", result)
	list = array.Map(result.VoidedPurchases, func(purchase *androidpublisher.VoidedPurchase, _ int) string {
		return purchase.OrderId
	})

	return
}

// ------------------------- FCM消息通知 -----------------------
var FCMApp *firebase.App
var deleteFCMToken deadlock.Map // 失效删除的FCMToken

// 初始化FCM app
func FirebaseCloudMessageInit() {
	opt := option.WithCredentialsFile(FIREBASE_MESSAGE_KEY_FILE_PATH)
	config := &firebase.Config{ProjectID: GOOGLE_PROJECT_ID}
	app, err := firebase.NewApp(context.Background(), config, opt)
	if err != nil {
		log.Error("FirebaseCloudMessageInit err: %v", err)
		return
	}
	FCMApp = app
	deleteFCMToken = deadlock.Map{}
}

// 离线消息通知
func SendOfflineMessge(registrationToken string, title, body string, data map[string]string) {
	go FirebaseSendMessage(registrationToken, title, body, data)
}

// 发送FCM消息
func FirebaseSendMessage(registrationToken string, title, body string, data map[string]string) error {
	if _, ok := deleteFCMToken.Load(registrationToken); ok {
		// 检测令牌是否失效删除
		return errors.New("token invalid")
	}
	// 构建FCM客户端
	ctx := context.Background()
	client, err := FCMApp.Messaging(ctx)
	if err != nil {
		log.Error("FirebaseSendMessage err: %v", err)
	}

	// 设置Android配置
	androidConfig := &messaging.AndroidConfig{
		Notification: &messaging.AndroidNotification{
			DefaultSound: true,
		},
	}

	// 设置APNS配置
	apnsConfig := &messaging.APNSConfig{
		Payload: &messaging.APNSPayload{
			Aps: &messaging.Aps{
				// Alert: &messaging.ApsAlert{
				// 	Title: title,
				// 	Body:  body,
				// },
				Sound: "default", // 设置为 "default" 使用设备的默认通知声音。
			},
		},
	}

	// 构建消息结构体
	message := &messaging.Message{
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Android: androidConfig,
		APNS:    apnsConfig,
		Data:    data,
		Token:   registrationToken,
	}

	// 发送消息
	response, err := client.Send(ctx, message)
	if err != nil {
		if err.Error() == "Requested entity was not found." {
			// 令牌失效 删除令牌
			deleteFCMToken.Store(registrationToken, true)
		}
		log.Error("FirebaseSendMessage err: %v, response: %v", err, response)
		return err
	}
	return nil
}
