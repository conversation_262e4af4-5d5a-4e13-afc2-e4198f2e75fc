package wx

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
)

const (
	APPID        = "wx235b503eeee61e6a"
	SECRET       = "0c68dbd6f8af029e1c7e4173761f43e8"
	USERINFO_URL = "https://api.weixin.qq.com/sns/jscode2session?"
)

type Jscode2sessionRet struct {
	Openid     string `json:"openid"`
	SessionKey string `json:"session_key"`
	Unionid    string `json:"unionid"`
	Errcode    int    `json:"errcode"`
	Errmsg     string `json:"errmsg"`
}

// 获取微信openid根据code
func GetOpenIdByCode(code string) (e int, res Jscode2sessionRet) {
	url := USERINFO_URL + "appid=" + APPID + "&secret=" + SECRET + "&js_code=" + code + "&grant_type=authorization_code"
	resp, err := http.Get(url)
	defer func() {
		_ = resp.Body.Close()
	}()
	if err != nil {
		e = -1
		return
	}
	body, _ := ioutil.ReadAll(resp.Body)
	//fmt.Println(string(body))
	_ = json.Unmarshal(body, &res)
	e = res.Errcode
	return
}
