package fb

import (
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"

	"github.com/huyangv/vmqant/log"
)

const (
	FACEBOOK_APPID            = "178105321860707"
	FACEBOOK_SECRET           = "********************************"
	FACEBOOK_GET_TOKEN_URL    = "https://graph.facebook.com/v16.0/oauth/access_token?"
	FACEBOOK_VERIFY_TOKEN_URL = "https://graph.facebook.com/debug_token?"
	FACEBOOK_REDIRECT_URI     = ""
	FACEBOOK_BASE_URL         = "https://graph.facebook.com/"
)

type FaceBookGetUserInfoRet struct {
	Id      string           `json:"id"`
	Name    string           `json:"name"`
	Picture *FaceBookPicData `json:"picture"`
}
type FaceBookPicData struct {
	Data *FaceBookPic `json:"data"`
}
type FaceBookPic struct {
	Url string `json:"url"`
}

// facebook登录验证
func Login(token, fbId string) (ret *FaceBookGetUserInfoRet, err error) {
	log.Info("FaceBookLoginCheck token: %v, fbId: %v", token, fbId)
	//根据token获取用户信息
	url := FACEBOOK_BASE_URL + fbId + "?fields=id,name,picture&access_token=" + token
	respGetUserInfo, err := http.Get(url)
	defer func() {
		_ = respGetUserInfo.Body.Close()
	}()
	if err != nil {
		log.Error("FaceBookGetUserInfo http err: %v", err)
		return
	}
	if respGetUserInfo.StatusCode != http.StatusOK {
		err = errors.New("FaceBookGetUserInfo http resp status err")
		log.Error("FaceBookGetUserInfo http resp status err, status: %v", respGetUserInfo.Status)
		return
	}
	body, _ := ioutil.ReadAll(respGetUserInfo.Body)
	err = json.Unmarshal(body, &ret)
	if err != nil {
		log.Error("FaceBookGetUserInfo ret err: %v", err)
		return
	}
	return
}
