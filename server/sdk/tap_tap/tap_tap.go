package tap_tap

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/huyangv/vmqant/log"
)

const (
	CLIENT_ID = "6vwlertfoxikme5gdn"
	NONCE     = "8IBTtwOmilesWeKl7plt66=="
)

type TapTapLoginRet struct {
	Code             int    `json:"code"`
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
	Name             string `json:"name"`
	Avatar           string `json:"avatar"`
	Openid           string `json:"openid"`
	Unionid          string `json:"unionid"`
}

type TapTapLoginBody struct {
	Data TapTapLoginRet `json:"data"`
}

func Login(kid, macKey string) (res TapTapLoginRet) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	// 请求 url 相关
	reqHost := "open.tapapis.cn"
	reqURI := "/account/profile/v1?client_id=" + CLIENT_ID
	reqURL := "https://" + reqHost + reqURI

	macStr := timestamp + "\n" + NONCE + "\n" + "GET" + "\n" + reqURI + "\n" + reqHost + "\n" + "443" + "\n\n"
	mac := hmacSha1(macStr, macKey)
	authorization := "MAC id=" + "\"" + kid + "\"" + "," + "ts=" + "\"" + timestamp + "\"" + "," + "nonce=" + "\"" + NONCE + "\"" + "," + "mac=" + "\"" + mac + "\""

	client := http.Client{}
	req, err := http.NewRequest(http.MethodGet, reqURL, nil)
	if err != nil {
		res.Code = 1
		log.Error("TapTapLogin NewRequest err: %v", err)
		return
	}

	// 添加请求头
	req.Header.Add("Authorization", authorization)
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		res.Code = 2
		log.Error("TapTapLogin Do err: %v", err)
		return
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		res.Code = 3
		log.Error("TapTapLogin ReadAll err: %v", err)
		return
	}
	body := &TapTapLoginBody{}
	err = json.Unmarshal(respBody, &body)
	if err != nil {
		res.Code = 4
		log.Error("TapTapLogin Unmarshal err: %v", err)
	}
	res = body.Data
	return
}

/*
HMAC-SHA1 签名
*/
func hmacSha1(valStr, keyStr string) string {
	key := []byte(keyStr)
	mac := hmac.New(sha1.New, key)
	mac.Write([]byte(valStr))

	// 进行 Base64 编码
	return base64.StdEncoding.EncodeToString(mac.Sum(nil))
}
