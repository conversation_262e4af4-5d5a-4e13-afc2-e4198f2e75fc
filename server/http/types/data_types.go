package types

import (
	"strings"
	"train/base/structs"
	ut "train/utils"

	"go.mongodb.org/mongo-driver/bson"
)

// DeletePlayer 被删除的玩家数据
type DeletePlayer struct {
	Mid  string `bson:"-"`
	Id   string `bson:"id"`
	Data string `bson:"data"`
	Time int    `bson:"time"`
	Type int    `bson:"type"` // 1:删除备份 2:主动备份
	Name string `bson:"name"` // 备份名称
}

func RecordPlayer(plr *structs.Player, typ int, name string) *DeletePlayer {
	bytes, _ := bson.Marshal(plr)
	json, _ := ut.BsonToReadableJson(bytes)
	return &DeletePlayer{
		Id:   plr.Id,
		Data: string(json),
		Type: typ,
		Time: ut.Now(),
		Name: name,
	}
}

type GmCmd struct {
	Id     string     `bson:"-"`
	Cmd    string     `bson:"cmd"`
	Desc   string     `bson:"desc"`
	Fields []*GmField `bson:"fields"`
}

// CheckCommand @add-item 2,1
func (g *GmCmd) CheckCommand(command string) bool {
	split := strings.Split(command, " ")
	if len(split) > 2 || len(split) < 1 {
		return false
	}
	if g.Cmd != split[0] {
		return false
	}
	params := strings.Split(split[1], ",")
	for i, field := range g.Fields {
		if !field.Check(i, params) {
			return false
		}
	}
	return true
}

type GmField struct {
	Name  string `json:"name" param:"name" validate:"required,trim,len=1"`
	Type  int    `json:"type" param:"type" validate:"required,in=1|2"`
	Desc  string `json:"desc" param:"desc" validate:"required,trim,len=1"`
	Force bool   `json:"force" param:"force" validate:"required"`
}

func (g *GmField) Check(idx int, params []string) bool {
	if g.Force && len(params) < idx {
		// 要么配置顺序不对，要么传入参数错误
		return false
	}
	return true
}

// Notice 公告
type Notice struct {
	Index    int    `bson:"index"`    // 公告的展示顺序 唯一不可重复
	ShowTime int    `bson:"ShowTime"` // 公告在什么时间点后才可见,不设置则是公告发布时当前时间立即可见
	Title    string `bson:"title"`    // 公告标题
	Content  string `bson:"content"`  // 公告内容
}

type WebItem struct {
	Id   int `json:"id" param:"id" validate:"required,trim"`     // id
	Num  int `json:"num" param:"num" validate:"required,trim"`   // 数量
	Type int `json:"type" param:"type" validate:"required,trim"` // 类型
}

func (w *WebItem) WebItem2Condition() *structs.Condition {
	return &structs.Condition{
		Id:   w.Id,
		Num:  w.Num,
		Type: w.Type,
	}
}

func WebItem2Conditions(items []*WebItem) []*structs.Condition {
	r := make([]*structs.Condition, 0)
	for _, item := range items {
		r = append(r, item.WebItem2Condition())
	}
	return r
}
