package http

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"
	"train/base/structs"
	"train/db"
	"train/http/h"

	com "train/common"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	BACKUP_BATCH_SIZE     = 100 // 可以根据实际情况调整
	BACKUP_BATCH_INTERVAL = 100 // 毫秒
	MAX_BACKUP_TIME       = 240 // 秒，最大允许备份时间
)

type BackupTask struct {
	lastBackupTime int64
	isRunning      atomic.Bool
	currentBatch   int
}

// 任务
type task struct {
	httpModule    *Http
	backupTask    *BackupTask
	onlinePlayers sync.Map // 记录在线玩家 key: playerId, value: lastBackupTime
	runCnt        int
}

func (t *task) Run() {
	if t.backupTask == nil {
		t.backupTask = &BackupTask{}
	}
	t.runCnt += 1
	t.doPlayerDataRecord()
	// if t.runCnt > 10 {
	//t.cleanOldBackups()
	// t.runCnt = 0
	// }
}

// startRecordPlayer 游戏服通知玩家数据需要记录
//
// Parameters:
//   - id string
//
// Returns:
//   - ret map[string]any
//   - err string
func (t *task) startRecordPlayer(id string) (ret map[string]any, err string) {
	if !com.IsBackup() {
		return
	}
	t.onlinePlayers.Store(id, time.Now().UnixMilli())
	log.Debug("开始记录玩家数据 playerId: %s", id)
	return h.ResponseSuccessNoDataNoDesc()
}

// stopRecordPlayer 游戏服通知玩家数据停止记录
//
// Parameters:
//   - id string
//
// Returns:
//   - ret map[string]any
//   - err string
func (t *task) stopRecordPlayer(id string) (ret map[string]any, err string) {
	if !com.IsBackup() {
		return
	}
	t.onlinePlayers.Delete(id)
	record, e := t.backupPlayerData(id)
	if e == nil {
		db.FIX_RECORD.GetCollection().InsertOne(context.TODO(), record)
	}
	log.Debug("停止记录玩家数据 playerId: %s", id)
	return h.ResponseSuccessNoDataNoDesc()
}

// 定时备份玩家数据
func (t *task) doPlayerDataRecord() {
	now := time.Now().UnixMilli()

	// 如果上一次任务还在运行，打印警告并继续执行
	if t.backupTask.isRunning.Load() {
		log.Warning("上次备份任务还未完成，已经运行时间: %d 秒", (now - t.backupTask.lastBackupTime))
		return
	}

	// 设置运行状态
	t.backupTask.isRunning.Store(true)
	defer t.backupTask.isRunning.Store(false)

	// 初始化新的备份任务
	t.backupTask = &BackupTask{
		lastBackupTime: now,
		currentBatch:   0,
	}
	t.backupTask.isRunning.Store(true)

	// 收集当前在线的玩家ID
	playerIds := make([]string, 0)
	t.onlinePlayers.Range(func(key, value interface{}) bool {
		playerIds = append(playerIds, key.(string))
		return true
	})

	if len(playerIds) == 0 {
		t.backupTask.isRunning.Store(false)
		return
	}

	// 启动异步处理
	go t.processBackupPlayers(playerIds)
}

func (t *task) processBackupPlayers(playerIds []string) {
	defer t.backupTask.isRunning.Store(false)
	// 添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*MAX_BACKUP_TIME)
	defer cancel()

	// 分批处理玩家数据
	for i := 0; i < len(playerIds); i += BACKUP_BATCH_SIZE {
		select {
		case <-ctx.Done():
			log.Error("备份任务超时")
			return
		default:
			end := i + BACKUP_BATCH_SIZE
			if end > len(playerIds) {
				end = len(playerIds)
			}

			batch := playerIds[i:end]
			t.processBatch(batch)

			t.backupTask.currentBatch++

			if i+BACKUP_BATCH_SIZE < len(playerIds) {
				time.Sleep(time.Millisecond * BACKUP_BATCH_INTERVAL)
			}
		}
	}
	log.Info("备份任务完成，共处理 %d 批次", t.backupTask.currentBatch)
}

func (t *task) processBatch(batch []string) {
	records := make([]any, 0, len(batch))

	for _, playerId := range batch {
		// 检查玩家是否仍在线
		if _, ok := t.onlinePlayers.Load(playerId); !ok {
			continue
		}

		record, err := t.backupPlayerData(playerId)
		if err != nil {
			log.Error("准备备份数据失败 [playerId=%s]: %v", playerId, err)
			continue
		}
		records = append(records, record)
	}

	if len(records) > 0 {
		_, err := db.FIX_RECORD.GetCollection().InsertMany(context.TODO(), records)
		if err != nil {
			log.Error("写入备份数据失败: %v", err)
		}
	}
}

// backupPlayerData 备份玩家数据
//
// Parameters:
//   - playerId string 玩家id
//
// Returns:
//   - error
func (t *task) backupPlayerData(playerId string) (doc bson.M, err error) {
	// 直接从db拉取玩家存档数据
	startTime := time.Now()
	defer func() {
		if r := recover(); r != nil {
			log.Error("备份玩家数据发生panic [playerId=%s]: %v", playerId, r)
		}
		log.Debug("备份玩家 %s 数据耗时: %v", playerId, time.Since(startTime))
	}()

	plr, err := structs.TryGetPlayerFromDb(playerId, false)
	if err != nil {
		log.Error("获取玩家数据错误: %s %s", playerId, err.Error())

		return nil, err
	}

	// 使用bson直接序列化玩家数据
	bsonData, err := bson.Marshal(plr)
	if err != nil {
		return nil, fmt.Errorf("序列化数据失败: %v", err)
	}

	record := bson.M{
		"id":        playerId,
		"timestamp": time.Now().UnixMilli(),
		"data":      primitive.Binary{Data: bsonData},
	}
	// _, err = db.FIX_RECORD.GetCollection().InsertOne(context.TODO(), &record)

	return record, nil
}

func (t *task) cleanOldBackups() {
	// 保留7天的备份
	deadline := time.Now().AddDate(0, 0, -7).UnixMilli()

	_, err := db.FIX_RECORD.GetCollection().DeleteMany(
		context.Background(),
		bson.M{"timestamp": bson.M{"$lt": deadline}},
	)
	if err != nil {
		log.Error("清理旧备份数据失败: %v", err)
	}
}
