package router

import (
	"net/http"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/huyangv/vmqant/registry"
	mqrpc "github.com/huyangv/vmqant/rpc"
	"github.com/samber/lo"
)

type Router struct {
	SubClass func() module.RPCModule
	Module   func() module.App
	RpcCall  func(w http.ResponseWriter, targetServer string, topic string, params mqrpc.ParamOption) (body map[string]interface{})
}

// GetGameServices
/*
 * @description 获取游戏服的节点
 * @return []*registry.Service
 */
func (r *Router) getGameServices() []*registry.Service {
	service, _ := r.Module().Registry().GetService("game")
	return service
}

// getLoginServices 获取登录服的节点
//
// Returns:
//   - []*registry.Service
func (r *Router) getLoginServices() []*registry.Service {
	service, _ := r.Module().Registry().GetService("login")
	return service
}

func (r *Router) Invoke(moduleType, _func string, args mqrpc.ParamOption) (retVal interface{}, e string) {
	mod := r.Module()
	return mod.Invoke(r.SubClass(), moduleType, _func, args()...)
}

// DoWithPlayer
/*
 * @description 通过玩家uid向游戏服务器进行http 事件 do
 * @param router 路由
 * @param uid 玩家uid
 * @param args 参数
 */

//func (this *Router) DoWithPlayer(route, uid string, args ...any) (body map[string]interface{}) {
//
//	return nil
//}
//
//func (this *Router) DoWith(route string, args ...any) (body map[string]interface{}) {
//	gameServices := this.GetGameServices()
//	for _, service := range gameServices {
//		for _, node := range service.Nodes {
//			this.Invoke(node.Id, route, args)
//		}
//	}
//	return nil
//}

//func (this *Router) DoGameRpcByUid(uid, route string, args map[string]any) (result map[string]interface{}, err string) {
//	if ut.IsEmpty(uid) {
//		return nil, "uid is empty"
//	}
//
//	service, _ := this.Module().Registry().GetService("game")
//	params := make([]interface{}, 0)
//	params = append(params, uid)
//	params = append(params, route)
//	if args != nil {
//		marshal, _ := json.Marshal(args)
//		params = append(params, string(marshal))
//	}
//	for _, s := range service {
//		for _, node := range s.Nodes {
//			resp, e := this.Module().Invoke(this.SubClass(), node.Id, "IsPlayerOnline", params...)
//			if e != "" {
//				log.Error("DoGameRpcByUid error :", e)
//			}
//			data := resp.(map[string]any)
//			exists := data["exists"].(bool)
//			if !exists {
//				continue
//			}
//			if route != "" {
//				result = data["retVal"].(map[string]interface{})
//			}
//			break
//		}
//	}
//	return
//}

// GameRpc
/*
 * @description 获取game服务器的rpc调用
 * @param uid 玩家id
 * @return call
 */
func (r *Router) GameRpc(uid string) (call *RpcCall) {
	service, _ := r.Module().Registry().GetService("game")
	// 保底game节点
	var defaultNode *registry.Node
	for _, s := range service {
		for _, node := range s.Nodes {
			defaultNode = node
			if call == nil {
				// 如果uid为空，直接返回第一个可用的game节点
				if ut.IsEmpty(uid) {
					call = &RpcCall{
						onlineNode:  nil,
						offlineNode: node,
					}
					break
				}
				resp, e := r.Module().Invoke(r.SubClass(), node.Id, "IsPlayerOnline", uid)
				if e != "" {
					log.Error("DoGameRpcByUid error : %s", e)
					continue
				}
				if resp.(bool) {
					call = &RpcCall{
						onlineNode:  node,
						offlineNode: nil,
					}
					defaultNode = nil
					break
				}
			}
		}
	}
	if call == nil {
		call = &RpcCall{
			onlineNode:  nil,
			offlineNode: nil,
		}
		if defaultNode != nil {
			call.offlineNode = defaultNode
		}
	}
	call.router = r
	return
}

type RpcCall struct {
	onlineNode  *registry.Node // 如果玩家在线，onlineNode就是玩家所在节点
	offlineNode *registry.Node // 不论玩家在线/离线，offlineNode都是一个可用的game节点
	execNode    *registry.Node // 执行节点
	router      *Router
}

// Online
/*
 * @description 知道玩家在线的情况下，调用在线节点，不知道也没关系，会切换到可用的离线节点
 * @return *RpcCall
 */
func (r *RpcCall) Online() *RpcCall {
	r.execNode = r.onlineNode
	return r
}

// Offline
/*
 * @description 任意可用game节点
 * @return *RpcCall
 */
func (r *RpcCall) Offline() *RpcCall {
	r.execNode = r.offlineNode
	return r
}

// Call
/*
 * @description rpc 调用 http接口 返回的是 map[string]interface{} 结构
 * @param topic
 * @param params
 * @return body
 * @return err
 */
func (r *RpcCall) Call(topic string, params mqrpc.ParamOption) (body map[string]interface{}, err string) {
	// 不知道玩家在线/离线的情况下，调用可用节点
	if r.execNode == nil {
		r.execNode = lo.If(r.onlineNode != nil, r.onlineNode).Else(r.offlineNode)
	}
	if r.execNode == nil {
		log.Error("Call 错误， execNode是空对象，要检查GameRpc方法")
		return nil, "execNode is nil"
	}

	return r.router.RpcCall(nil, r.execNode.Id, topic, params), err
}

func (r *Router) All(typ string, topic string, params mqrpc.ParamOption) {
	var srv []*registry.Service
	if typ == "game" {
		srv = r.getGameServices()
	}
	if typ == "login" {
		srv = r.getLoginServices()
	}

	for _, sr := range srv {
		if sr == nil {
			continue
		}
		for _, node := range sr.Nodes {
			r.RpcCall(nil, node.Id, topic, mqrpc.Param())
		}
	}
}
