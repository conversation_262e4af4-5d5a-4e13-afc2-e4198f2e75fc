package router

import (
	"train/http/h"

	"github.com/gin-gonic/gin"
)

type Account struct {
	Router
	Login struct {
		UserName string `param:"username" validate:"required,trim,len=4"`
		Password string `param:"password" validate:"required,trim,len=6,md5"`
	} //  PostLogin
	KickPlayer struct {
		ServerId int    `param:"serverId" validate:"required"`
		Uid      string `param:"uid" validate:"required"`
	} `@token`
}

func (act *Account) PostLogin(ctx *gin.Context, username, password string) (body map[string]interface{}, e string) {
	if username != "root" {
		return h.ResponseErrorNoDataWithDesc("??")
	}
	if password != "9d173773473008f95d58347056cab620" {
		return h.ResponseErrorNoDataWithDesc("??")
	}
	return h.ResponseSuccessWithDataWithDesc(map[string]interface{}{
		"token": "DEV-eyJhbGciOiJIUzUxMiIsImtpZCI6IiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2NGVjNjE0OTljOGI2MjdmN2JhNTUxZjAiLCJleHAiOjE3NDcxMDA4MDgsInBlcm1pc3Npb24iOiIxIiwidGltZXN0YW1wIjoxNzQ3MTAwODA4OTMxLCJ1c2VyTmFtZSI6IuiDoeadviJ9.G8VHrCaSG1cKRcorWFaewB0YNamHu4EKdk_AL8b4IqMjqWfYB-9VTXYjr40sHEM4uHz4ca04nW97brqI1vv2Gg",
	}, "!!")
}

func (act *Account) PostKickPlayer(ctx *gin.Context, serverId int, uid string) (code int, data interface{}, notify string) {
	//call := act.RpcCall(ctx.Writer, comm.CallGameServer(serverId), "kickPlayer", mqrpc.Param(uid))
	//fmt.Println(call)
	return
}

/**
 * @document
 * 路由参数列表的第一个必须是是*gin.Context，上下文传递对象.
 * @token 是保留字段,可以使用,但不能自定义,仅在登陆后才拥有.
 * @id 是保留字段,可以使用,但不能自定义,随token解析出现.
 *
 * 参与自动转换的参数有两个tag: param(必选),validate(可选),并且参数必须在结构体中导出,结构体的数据类型都定为基本类型.
 * 参数自动转换列表有顺序限制,例如:
 * Login struct {
 *		UserName string `param:"username" validate:"required"`
 *		Password string `param:"password" validate:"required"`
 *	}
 * 对应的路由方法的参数列表也必须是 func xxxLogin(ctx *gin.Context, username, password string){}
 * 路由方法的参数名称不做限制,只要类型一致即可.
 * param参数需要和前端传递的参数名一致,也就是以后端的为准,不然无法自动填充传递数据
 * 对于参数的校验validate标签,规定以下规则:
 * required : 字段必须存在,前端必须传值,但不会对值进行校验. 例: validate:"required"
 * trim: 字段会被去空格后传递,优先触发trim后才会进行后面的验证.
 * md5 : 优先触发,该值会进行md5加密后传输
 * gt  : 字段必须是数字,且必须大于预设值. 例: validate:"gt=10" (必须大于10)
 * gte : 字段必须是数字,且必须大于等于预设值. 例: validate:"gte=10" (必须大于等于10)
 * lt  : 字段必须是数字,且必须小于于预设值. 例: validate:"lt=10" (必须小于10)
 * lte : 字段必须是数字,且必须小于等于预设值. 例: validate:"lte=10" (必须小于等于10)
 * len : 字段长度至少满足预设值.  例: validate:"len=10" (字段长度必须大于等于10)
 * xlen: 字段长度必须满足预设值.  例: validate:"xlen=10" (字段长度等于10)
 * in  : 字段必须是数字,并且必需在一个类似数组的包含里面. 例: validate:"in=1|2|3" (字段只能是1或者2或者3)
 * deep: 字段必须是结构体切片或者结构体,如果结构体内每一个值也设置了validate标签,则也会触发检查.
 */
