package router

import (
	"context"
	"train/db"
	"train/http/h"
	ut "train/utils"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Invite struct {
	Router
	Create struct {
		Code      string `param:"code" validate:"required,trim,len=1"`
		StartTime int64  `param:"startTime" validate:"required"`
		EndTime   int64  `param:"endTime" validate:"required"`
	}
	List struct{}
	Void struct {
		DocId string `param:"docId" validate:"required,trim,len=1"`
	}
	UnVoid struct {
		DocId string `param:"docId" validate:"required,trim,len=1"`
	}
}

func (i *Invite) PostCreate(ctx *gin.Context, code string, startTime, endTime int64) (body map[string]interface{}, e string) {
	now := int64(ut.Now())
	if startTime > endTime {
		return h.ResponseErrorNoDataWithDesc("邀请码结束时间必须大于开始时间!")
	}
	if endTime < now {
		return h.ResponseErrorNoDataWithDesc("邀请码结束时间必须大于当前时间!")
	}

	r, err := db.INVITE_CODE.GetCollection().InsertOne(context.Background(), bson.M{
		"code":      code,
		"startTime": startTime,
		"endTime":   endTime,
		"bind":      "",
		"state":     0, // 0是正常状态
	})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return h.ResponseErrorNoDataWithDesc("邀请码已存在!")
		}
		return h.ResponseErrorNoDataWithDesc("添加失败：" + err.Error())
	}

	objId := r.InsertedID.(primitive.ObjectID)

	return h.ResponseSuccessWithDataWithDesc(objId.Hex(), "添加成功!")
}

func (i *Invite) PostList(ctx *gin.Context) (body map[string]interface{}, e string) {
	cursor, _ := db.INVITE_CODE.GetCollection().Find(context.Background(), bson.M{})
	data := make([]bson.M, 0)
	_ = cursor.All(context.Background(), &data)
	_ = cursor.Close(context.Background())
	return h.ResponseSuccessWithDataNoDesc(data)
}

func (i *Invite) PostVoid(ctx *gin.Context, _id string) (body map[string]interface{}, e string) {
	oid, err := primitive.ObjectIDFromHex(_id)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("邀请码ID错误!")
	}

	r := db.INVITE_CODE.GetCollection().FindOneAndUpdate(context.Background(), bson.M{"_id": oid}, bson.M{"$set": bson.M{"state": 1}})
	err = r.Err()
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("更新失败：" + err.Error())
	}
	return h.ResponseSuccessNoDataWithDesc("邀请码已作废!")
}

func (i *Invite) PostUnVoid(ctx *gin.Context, _id string) (body map[string]interface{}, e string) {
	oid, err := primitive.ObjectIDFromHex(_id)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("邀请码ID错误!")
	}

	r := db.INVITE_CODE.GetCollection().FindOneAndUpdate(context.Background(), bson.M{"_id": oid}, bson.M{"$set": bson.M{"state": 0}})
	err = r.Err()
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("更新失败：" + err.Error())
	}
	return h.ResponseSuccessNoDataWithDesc("邀请码状态已恢复!")
}
