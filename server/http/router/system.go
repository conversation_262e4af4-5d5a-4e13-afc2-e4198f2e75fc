package router

import (
	"context"
	"fmt"
	"sort"
	"train/db"
	"train/http/h"
	"train/http/types"
	ut "train/utils"

	"github.com/gin-gonic/gin"
	"github.com/huyangv/vmqant/log"
	mqrpc "github.com/huyangv/vmqant/rpc"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
)

// 公告缓存数据
var noticeCache []*types.Notice

type System struct {
	Router
	AddNotice struct {
		Index    int    `param:"index" validate:"required"`
		ShowTime int    `param:"showTime" validate:"required"`
		Title    string `param:"title" validate:"required,trim,len=1"`
		Content  string `param:"content" validate:"required,len=1"`
	}
	DelNotice struct {
		Index int `param:"index" validate:"required"`
	}
	// bi侧获取公告,无需token
	ReqNotice struct {
	}
	// 游戏侧获取公告,无需token
	GameNotice struct {
	}
	// 移动公告顺序
	MoveIndex struct {
		Index int `param:"index" validate:"required"`
		Type  int `param:"type" validate:"required,in=1|2"`
	}
	// 拷贝存档
	DebugCopy struct {
		RecordId string `param:"recordId" validate:"required,trim"`
		PlayerId string `param:"playerId" validate:"required,trim"`
		LockTime bool   `param:"lockTime" validate:"required"`
	}
}

func (sys *System) PostAddNotice(ctx *gin.Context, index, showTime int, title, content string) (body map[string]interface{}, e string) {
	if exists := getNoticeByIndex(index); exists != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("公告序号:%d已经存在", index))
	}
	count := len(noticeCache)
	if count != index-1 {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("公告顺序不正确,应该是%d而不是%d.", count+1, index))
	}
	if showTime <= 0 {
		showTime = ut.Now()
	}
	msg := &types.Notice{
		Index:    count + 1,
		ShowTime: showTime,
		Title:    title,
		Content:  content,
	}
	// 保存
	noticeCache = append(noticeCache, msg)
	return h.ResponseSuccessNoDataWithDesc("添加成功!")
}

func (sys *System) PostDelNotice(ctx *gin.Context, index int) (body map[string]interface{}, e string) {
	count := len(noticeCache)
	noticeCache = lo.Filter(noticeCache, func(notice *types.Notice, i int) bool {
		return notice.Index != index
	})
	if count == len(noticeCache) {
		return h.ResponseErrorNoDataWithDesc("删除失败!")
	}
	noticeCache = lo.Map(noticeCache, func(t *types.Notice, i int) *types.Notice {
		t.Index = i + 1
		return t
	})
	return h.ResponseSuccessNoDataWithDesc("删除成功!")
}

func (sys *System) PostReqNotice(ctx *gin.Context) (body map[string]interface{}, e string) {
	return h.ResponseSuccessWithDataNoDesc(noticeCache)
}

func (sys *System) PostGameNotice(ctx *gin.Context) (body map[string]interface{}, e string) {
	temp := lo.Filter(noticeCache, func(t *types.Notice, i int) bool {
		return t.ShowTime <= ut.Now()
	})
	return h.ResponseSuccessWithDataNoDesc(temp)
}

func (sys *System) PostMoveIndex(ctx *gin.Context, index, typ int) (body map[string]interface{}, e string) {
	// 当前
	notice := getNoticeByIndex(index)
	if notice == nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("公告不存在:%d", index))
	}
	sortF := func() {
		sort.Slice(noticeCache, func(i, j int) bool {
			return noticeCache[i].Index-noticeCache[j].Index < 0
		})
	}
	if typ == 1 {
		prev := getNoticeByIndex(index - 1)
		if prev == nil {
			return h.ResponseErrorNoDataWithDesc("无需上移,已经是第一个了.")
		}
		prev.Index += 1
		notice.Index -= 1
		sortF()
	}
	if typ == 2 {
		next := getNoticeByIndex(index + 1)
		if next == nil {
			return h.ResponseErrorNoDataWithDesc("无需下移,已经是最后一个了.")
		}
		next.Index -= 1
		notice.Index += 1
		sortF()
	}
	return h.ResponseSuccessNoDataWithDesc("操作完成!")
}

func (sys *System) PostDebugCopy(ctx *gin.Context, recordId, playerId string, lockTime bool) (body map[string]interface{}, e string) {
	//return sys.RpcCall(ctx.Writer, comm.CallGameServer(1), "httpDebugCopy", mqrpc.Param(recordId, playerId, lockTime))
	return sys.GameRpc(playerId).Call("httpDebugCopy", mqrpc.Param(recordId, playerId, lockTime))
}

func getNoticeByIndex(index int) *types.Notice {
	find, _ := lo.Find(noticeCache, func(notice *types.Notice) bool {
		return notice.Index == index
	})
	return find
}

func (sys *System) OnLoaded() {
	if noticeCache != nil || len(noticeCache) > 0 {
		return
	}
	noticeCache = make([]*types.Notice, 0)
	cursor, _ := db.GetCollection(db.NOTICE).Find(context.TODO(), &bson.M{})
	_ = cursor.All(context.TODO(), &noticeCache)
	_ = cursor.Close(context.TODO())
	log.Info("系统公告加载完成,共计%d条.", len(noticeCache))
}

func (sys *System) OnClose() {
	if noticeCache == nil || len(noticeCache) == 0 {
		return
	}
	// 先全部删除
	_, err := db.GetCollection(db.NOTICE).DeleteMany(context.TODO(), &bson.M{})
	bsonA := make([]interface{}, 0)
	for _, notice := range noticeCache {
		bsonA = append(bsonA, ut.FieldToBsonAuto(notice))
	}
	if err != nil {
		log.Error("system onClose error1, write failed: %v", bsonA)
		return
	}
	// 全部新增
	_, err = db.GetCollection(db.NOTICE).InsertMany(context.TODO(), bsonA)
	if err != nil {
		log.Error("system onClose error2, write failed: %v", bsonA)
		return
	}
}
