package router

import (
	"context"
	"fmt"
	"train/base/enum"
	"train/db"
	"train/http/h"

	"github.com/gin-gonic/gin"
	mqrpc "github.com/huyangv/vmqant/rpc"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Server struct {
	Router
	AddPlatform struct {
		Platform string `param:"platform" validate:"required,trim,len=1"`
	}
	ListPlatform struct {
		Detail bool `param:"detail" validate:"required"`
	}
	AddBigVersion struct {
		Platform    string `param:"platform" validate:"required,trim,len=1"`
		Version     string `param:"version" validate:"required,trim,len=1"`
		DownloadUrl string `param:"downloadUrl" validate:"required,trim"`
		IsUpdate    bool   `param:"isUpdate" validate:"required"`
		Force       bool   `param:"force" validate:"required"`
	}
	AddHotfixVersion struct {
		Platform    string `param:"platform" validate:"required,trim,len=1"`
		HotVersion  string `param:"hotVersion" validate:"required,trim,len=1"`
		PackageUrl  string `param:"packageUrl" validate:"required,trim,len=1"`
		ManifestUrl string `param:"manifestUrl" validate:"required,trim,len=1"`
	}
	ListServerNodes struct {
	} `@log:"false"`
	OfflineNodes struct {
		Service     string `param:"service" validate:"required,trim"`
		Id          string `param:"id" validate:"required,trim"`
		Before      string `param:"before" validate:"required,trim"`
		SwitchValue bool   `param:"switchValue" validate:"required"`
	}
	GameNodeOnlineCnt struct {
		Id string `param:"id" validate:"required,trim"`
	} `@log:"false"`

	GameNodeKick struct {
		Version string `param:"version" validate:"required,trim"`
	}
}

func (s *Server) PostAddPlatform(ctx *gin.Context, platform string) (body map[string]interface{}, e string) {
	_, err := db.VERSION_UPDATE.GetCollection().InsertOne(context.TODO(), &bson.M{
		"platform": platform,
	})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return h.ResponseErrorNoDataWithDesc("该平台已经存在!")
		}
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("数据库错误:%s", err.Error()))
	}
	return h.ResponseSuccessNoDataWithDesc("添加成功!")
}

func (s *Server) PostListPlatform(ctx *gin.Context, detail bool) (body map[string]interface{}, e string) {
	projection := bson.M{
		"_id":      0,
		"platform": 1,
	}
	if detail {
		projection["version"] = 1
		projection["downloadUrl"] = 1
		projection["manifestUrl"] = 1
		projection["force"] = 1
		projection["isUpdate"] = 1
		projection["history"] = 1
		projection["hotVersion"] = 1
		projection["packageUrl"] = 1
		projection["manifestUrl"] = 1
		projection["hotfixHistory"] = 1
	}

	cursor, err := db.VERSION_UPDATE.GetCollection().Find(context.TODO(), &bson.M{}, options.Find().SetProjection(projection))
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("数据库错误:%s", err.Error()))
	}
	result := lo.Empty[[]bson.M]()
	_ = cursor.All(context.TODO(), &result)
	_ = cursor.Close(context.TODO())
	return h.ResponseSuccessWithDataNoDesc(result)
}

func (s *Server) PostAddBigVersion(ctx *gin.Context, platform, version, downloadUrl string, isUpdate, force bool) (body map[string]interface{}, e string) {
	filter := &bson.M{
		"platform": platform,
	}
	updateInfo := &bson.M{
		"$set": &bson.M{
			"version":     version,
			"downloadUrl": downloadUrl,
			"isUpdate":    isUpdate,
			"force":       force,
		},
		"$push": &bson.M{
			"history": &bson.M{
				"$each":  []string{version},
				"$slice": -5, // 只保留最近5条
			},
		},
	}
	result := db.VERSION_UPDATE.GetCollection().FindOneAndUpdate(context.TODO(), filter, updateInfo)
	err := result.Err()
	if err != nil {
		if mongo.ErrNoDocuments == err {
			return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("平台:%s不存在", platform))
		}
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("数据库错误:%s", err.Error()))
	}
	return h.ResponseSuccessNoDataWithDesc("更新成功!")
}

func (s *Server) PostAddHotfixVersion(ctx *gin.Context, platform, hotVersion, packageUrl, manifestUrl string) (body map[string]interface{}, e string) {
	filter := &bson.M{
		"platform": platform,
	}
	updateInfo := &bson.M{
		"$set": &bson.M{
			"hotVersion":  hotVersion,
			"packageUrl":  packageUrl,
			"manifestUrl": manifestUrl,
		},
		"$push": &bson.M{
			"hotfixHistory": &bson.M{
				"$each":  []string{hotVersion},
				"$slice": -5, // 只保留最近20条
			},
		},
	}
	result := db.VERSION_UPDATE.GetCollection().FindOneAndUpdate(context.TODO(), filter, updateInfo)
	err := result.Err()
	if err != nil {
		if mongo.ErrNoDocuments == err {
			return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("平台:%s不存在", platform))
		}
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("数据库错误:%s", err.Error()))
	}
	return h.ResponseSuccessNoDataWithDesc("更新成功!")
}

func (s *Server) PostListServerNodes(ctx *gin.Context) (body map[string]interface{}, e string) {
	body = make(map[string]interface{})

	each := func(name string) {
		arr := make([]any, 0)
		services, _ := s.Module().Registry().GetService(name)
		for _, service := range services {
			for _, node := range service.Nodes {
				arr = append(arr, node)
			}
		}
		body[name] = arr
	}
	each("gate")
	each("login")
	each("game")
	each("http")
	each("task")

	return h.ResponseSuccessWithDataNoDesc(body)
}

func (s *Server) PostOfflineNodes(ctx *gin.Context, service, id, before string, switchValue bool) (body map[string]interface{}, e string) {
	services, _ := s.Module().Registry().GetService(service)
	if len(services) == 0 {
		return h.ResponseSuccessNoDataWithDesc("服务不存在!")
	}
	for _, service := range services {
		for _, node := range service.Nodes {
			if node != nil && node.Id == id {
				v, ok := node.Metadata[enum.STATE]
				if ok && v != before {
					return h.ResponseErrorNoDataWithDesc("节点状态已经改变,请刷新后重试!")
				}
				s.RpcCall(ctx.Writer, node.Id, "Offline", mqrpc.Param(switchValue))
				return h.ResponseSuccessNoDataWithDesc("请求已经发送!")
			}
		}
	}
	return h.ResponseErrorNoDataWithDesc("找不到节点!")
}

func (s *Server) PostGameNodeOnlineCnt(ctx *gin.Context, id string) (body map[string]interface{}, e string) {
	services, _ := s.Module().Registry().GetService("game")
	if len(services) == 0 {
		return h.ResponseSuccessNoDataWithDesc("服务不存在!")
	}
	for _, service := range services {
		for _, node := range service.Nodes {
			if node != nil && node.Id == id {
				result := s.RpcCall(ctx.Writer, node.Id, "getOnlinePlayerCnt", mqrpc.Param())
				count := 0
				if result != nil {
					count = cast.ToInt(result["count"])
				}
				return h.ResponseSuccessWithDataNoDesc(map[string]any{
					"count": count,
					"node":  node,
				})
			}
		}
	}
	return h.ResponseErrorNoDataWithDesc("找不到节点!")
}

func (s *Server) PostGameNodeKick(ctx *gin.Context, version string) (body map[string]interface{}, e string) {
	// rpc通知游戏节点 检查版本
	for _, service := range s.getGameServices() {
		for _, node := range service.Nodes {
			if node != nil {
				v, ok := node.Metadata[enum.STATE]
				if !ok || v == enum.NORMAL {
					s.RpcCall(ctx.Writer, node.Id, "onClientVersionUpdate", mqrpc.Param(version))
				}
			}
		}
	}
	return h.ResponseSuccessNoDataWithDesc("请求已经发送!")
}
