package router

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"regexp"
	"strings"
	"time"
	"train/base/structs"
	"train/db"
	"train/http/h"
	"train/http/types"
	ut "train/utils"

	"github.com/gin-gonic/gin"
	"github.com/huyangv/vmqant/log"
	mqrpc "github.com/huyangv/vmqant/rpc"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Dev struct {
	Router
	AddGmCmd struct {
		Cmd    string           `param:"cmd" validate:"required,trim,len=1"`
		Desc   string           `param:"desc" validate:"required,len=1"`
		Fields []*types.GmField `param:"fields" validate:"required,deep"`
	}
	ChangeGmCmd struct {
		Id     string           `param:"_id" validate:"required,trim,len=1"`
		Cmd    string           `param:"cmd" validate:"required,trim,len=1"`
		Desc   string           `param:"desc" validate:"required,len=1"`
		Fields []*types.GmField `param:"fields" validate:"required,deep"`
	}
	ListGmCmd struct{}
	DelGmCmd  struct {
		Id string `param:"_id" validate:"required,trim,len=1"`
	}
	ExecGmCmd struct {
		Id      string `param:"_id" validate:"required,trim,len=1"`
		Uid     string `param:"uid" validate:"required,trim,len=1"`
		Command string `param:"command" validate:"required,len=1"` // 无需trim
	}
	// 发送邮件
	SendMail struct {
		Id      string           `param:"id" validate:"required,trim,len=1"`
		Title   string           `param:"title" validate:"required,trim,len=1"`
		Content string           `param:"content" validate:"required"` // 无需trim
		Rewards []*types.WebItem `param:"rewards" validate:"required,deep"`
	}
	// 查询用户邮件
	ListMail struct {
		Id string `param:"id" validate:"required,trim,len=1"`
	}
	ListCdk struct {
	} `@log:"false"`
	CreateCdk struct {
		Code      string           `param:"code" validate:"required,trim"`
		Limit     int              `param:"limit" validate:"required"`
		StartTime int              `param:"startTime" validate:"required"`
		EndTime   int              `param:"endTime" validate:"required"`
		Rewards   []*types.WebItem `param:"rewards" validate:"required,deep"`
	}
	DeleteCdk struct {
		Code string `param:"code" validate:"required,trim,len=1"`
	}
	CheckTransiLogin struct {
		Token      string `param:"token" validate:"required,trim"`
		Permission string `param:"permission" validate:"required,trim"`
		Name       string `param:"name" validate:"required,trim"`
		Id         string `param:"id" validate:"required,trim"`
	}
	ListFixRecord struct {
		Uid string `param:"uid" validate:"trim"`
		St  int64  `param:"st" validate:"required"`
		Et  int64  `param:"et" validate:"required"`
	} `@log:"false"`
	RollbackRecord struct {
		Uid       string `param:"uid" validate:"trim"`    // 回档个人必须传
		RollId    string `param:"rollId" validate:"trim"` // 回档个人必须传
		RollTime  int64  `param:"rollTime"`               // 回档全部-时间
		RollStage string `param:"rollStage"`              // 回档全部-关卡
	}
}

func (d *Dev) PostAddGmCmd(ctx *gin.Context, cmd, desc string, fields []*types.GmField) (body map[string]interface{}, e string) {
	result := db.GetCollection(db.GM_CMD).FindOne(context.TODO(), &bson.M{
		"cmd": cmd,
	})
	obj := &types.GmCmd{}
	err := result.Decode(obj)
	if err != mongo.ErrNoDocuments {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("命令:%s已经存在", cmd))
	}
	obj.Cmd = cmd
	obj.Desc = desc
	obj.Fields = fields
	o, err := db.GetCollection(db.GM_CMD).InsertOne(context.TODO(), &obj)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("保存失败!")
	}
	return h.ResponseSuccessWithDataWithDesc(map[string]interface{}{
		"_id": o.InsertedID,
	}, "保存成功!")
}

func (d *Dev) PostChangeGmCmd(ctx *gin.Context, _id, cmd, desc string, fields []*types.GmField) (body map[string]interface{}, e string) {
	hex, err := primitive.ObjectIDFromHex(_id)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("id错误:%v", err.Error()))
	}
	_, err = db.GetCollection(db.GM_CMD).UpdateOne(context.TODO(), &bson.M{"_id": hex}, &bson.M{
		"$set": &bson.M{
			"cmd":    cmd,
			"desc":   desc,
			"fields": fields,
		},
	}, options.Update().SetUpsert(true))
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("更新命令失败:%v", err.Error()))
	}
	return h.ResponseSuccessNoDataWithDesc("修改成功!")
}

func (d *Dev) PostListGmCmd(ctx *gin.Context) (body map[string]interface{}, e string) {
	cur, err := db.GetCollection(db.GM_CMD).Find(context.TODO(), &bson.M{})
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("数据库查询失败!")
	}
	result := lo.Empty[[]bson.M]()
	_ = cur.All(context.TODO(), &result)

	for _, cmd := range result {
		cmd["_id"] = cmd["_id"].(primitive.ObjectID).Hex()
	}
	return h.ResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": result,
	})
}

func (d *Dev) PostDelGmCmd(ctx *gin.Context, _id string) (body map[string]interface{}, e string) {
	hex, err := primitive.ObjectIDFromHex(_id)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("id错误:%v", err.Error()))
	}
	_, _ = db.GetCollection(db.GM_CMD).DeleteOne(context.TODO(), &bson.M{
		"_id": hex,
	})

	return h.ResponseSuccessNoDataWithDesc("操作完成!")
}

func (d *Dev) PostExecGmCmd(ctx *gin.Context, _id, uid, command string) (body map[string]interface{}, e string) {
	hex, err := primitive.ObjectIDFromHex(_id)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("找不到Gm命令.")
	}
	result := db.GetCollection(db.GM_CMD).FindOne(context.TODO(), &bson.M{
		"_id": hex,
	})
	gm := &types.GmCmd{}
	err = result.Decode(&gm)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("找不到Gm命令.")
	}
	if !gm.CheckCommand(command) {
		return h.ResponseErrorNoDataWithDesc("参数传递不正确,或者配置错误.")
	}
	if !strings.HasPrefix(command, "@") {
		command = fmt.Sprintf("@%s", command)
	}
	// exec
	//return d.RpcCall(ctx.Writer, d.getPlayerRpcName(uid), "execGmCmd", mqrpc.Param(uid, command)), ""
	return d.GameRpc(uid).Online().Call("execGmCmd", mqrpc.Param(uid, command))
}

func (d *Dev) PostSendMail(ctx *gin.Context, id, title, content string, rewards []*types.WebItem) (body map[string]interface{}, e string) {
	rewardsMap := ""
	// 转换为string去传递，那边转condition方便一些
	if rewards != nil && len(rewards) > 0 {
		bytes, _ := json.Marshal(&rewards)
		rewardsMap = string(bytes)
	}
	//return d.RpcCall(ctx.Writer, d.getPlayerRpcName(id), "httpSendMail", mqrpc.Param(id, title, content, rewardsMap)), ""
	return d.GameRpc(id).Online().Call("httpSendMail", mqrpc.Param(id, title, content, rewardsMap))
}

func (d *Dev) PostListMail(ctx *gin.Context, id string) (body map[string]interface{}, e string) {
	//return d.RpcCall(ctx.Writer, d.getPlayerRpcName(id), "httpListMail", mqrpc.Param(id)), ""
	return d.GameRpc(id).Call("httpListMail", mqrpc.Param(id))
}

func (d *Dev) PostListCdk(ctx *gin.Context) (body map[string]interface{}, e string) {
	cursor, err := db.CDK.GetCollection().Find(context.TODO(), bson.M{})
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("数据库错误")
	}
	data := make([]*structs.Cdk, 0)
	_ = cursor.All(context.TODO(), &data)
	_ = cursor.Close(context.TODO())
	return h.ResponseSuccessWithDataNoDesc(data)
}

func (d *Dev) PostCreateCdk(ctx *gin.Context, code string, limit, startTime, endTime int, rewards []*types.WebItem) (body map[string]interface{}, e string) {
	r := make([]*structs.Condition, 0)
	if rewards == nil || len(rewards) == 0 {
		return h.ResponseErrorNoDataWithDesc("奖励数据不存在,创建失败!")
	}
	r = types.WebItem2Conditions(rewards)
	j := &structs.Cdk{
		Limit:     limit,
		StartTime: startTime,
		EndTime:   endTime,
		Rewards:   r,
	}
	// 指定兑换码
	if !ut.IsEmpty(code) {
		j.Code = code
		o := CreateCodeAndWrite2Db(j)
		if o == 1 {
			return h.ResponseErrorNoDataWithDesc("重复的兑换码字符!")
		}
		return h.ResponseSuccessWithDataWithDesc(j, "创建成功!")
	}

	for {
		j.Code = RandomCode()
		if o := CreateCodeAndWrite2Db(j); o == 0 {
			code = j.Code
			break
		}
	}

	return h.ResponseSuccessWithDataWithDesc(j, "创建成功!")
}

func (d *Dev) PostDeleteCdk(ctx *gin.Context, code string) (body map[string]interface{}, e string) {
	_, err := db.CDK.GetCollection().DeleteOne(context.TODO(), bson.M{"code": code})
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("删除失败!")
	}
	return h.ResponseSuccessNoDataWithDesc("删除成功!")
}

func (d *Dev) PostCheckTransiLogin(ctx *gin.Context, token, permission, name, id string) (body map[string]interface{}, e string) {
	claims := h.TryDecodeWebToken(token, false)
	if !claims.VerifyExpiresAt(time.Now().Unix(), true) {
		return h.ResponseErrorNoDataWithDesc("token过期!")
	}
	body = make(map[string]interface{})
	body["token"] = token
	body["name"] = claims["userName"]
	body["permission"] = claims["permission"]
	body["_id"] = claims["_id"]
	return h.ResponseSuccessWithDataNoDesc(body)
}

func (d *Dev) PostListFixRecord(ctx *gin.Context, uid string, st, et int64) (body map[string]interface{}, e string) {
	filter := bson.M{"id": uid}
	// 24小时前 到现在
	if st <= 0 {
		st = time.Now().Add(time.Hour * -24).UnixMilli()
	}
	if et <= 0 {
		et = time.Now().UnixMilli()
	}
	filter["timestamp"] = bson.M{"$gte": st, "$lte": et}

	cursor, err := db.FIX_RECORD.GetCollection().Find(context.TODO(), &filter, &options.FindOptions{
		Projection: bson.M{
			"data": 0,
		},
	})
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("数据库错误")
	}
	data := make([]map[string]interface{}, 0)
	_ = cursor.All(context.TODO(), &data)
	_ = cursor.Close(context.TODO())
	return h.ResponseSuccessWithDataNoDesc(data)
}

func (d *Dev) PostRollbackRecord(ctx *gin.Context, uid, rollId string, rollTime int64, rollStage string) (body map[string]interface{}, e string) {
	if rollId != "" && uid != "" {
		_id, err := primitive.ObjectIDFromHex(rollId)
		if err != nil {
			return h.ResponseErrorNoDataWithDesc("回档个人数据id错误")
		}
		filter := &bson.M{
			"_id": _id,
		}
		res := db.FIX_RECORD.GetCollection().FindOne(context.TODO(), filter)
		if mongo.ErrNoDocuments == res.Err() {
			return h.ResponseErrorNoDataWithDesc("回档个人数据不存在")
		}
		var bs bson.M
		res.Decode(&bs)
		data, ok := bs["data"].(primitive.Binary)
		if !ok {
			return h.ResponseErrorNoDataWithDesc("回档个人数据不存在")
		}
		// 创建gzip reader
		gr, err := gzip.NewReader(bytes.NewReader(data.Data))
		if err != nil {
			return h.ResponseErrorNoDataWithDesc("解压回档数据失败!" + err.Error())
		}
		defer gr.Close()
		var buf bytes.Buffer
		if _, err := io.Copy(&buf, gr); err != nil {
			return h.ResponseErrorNoDataWithDesc("读取解压数据失败!" + err.Error())
		}

		originalJSON := buf.String()

		recordTime := bs["timestamp"].(int64)
		diff := time.Now().UnixMilli() - recordTime
		pattern := `(\b\d{13}\b)`
		regExp, err := regexp.Compile(pattern)
		if err != nil {
			return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("正则表达式编译错误:%v", err.Error()))
		}
		// 替换所有匹配项
		originalJSON = regExp.ReplaceAllStringFunc(originalJSON, func(str string) string {
			return cast.ToString(cast.ToInt64(str) + diff)
		})

		// 解码JSON数据
		var tempPlr *structs.Player
		if err := ut.JsonToBsonUnmarshal([]byte(originalJSON), &tempPlr); err != nil {
			return h.ResponseErrorNoDataWithDesc("回档个人数据出错，存档数据解析失败!" + err.Error())
		}

		// 踢下线
		d.GameRpc(uid).Online().Call("kickPlayer", mqrpc.Param(uid))
		tempPlr.Id = uid

		tempPlr.SaveToDb(ut.FieldToBsonAuto(tempPlr))

		// todo 将回档时间之后的数据全部移到另一个地方
		// 先直接删除
		db.FIX_RECORD.GetCollection().DeleteMany(context.TODO(), &bson.M{"id": uid, "timestamp": bson.M{"$gt": recordTime}})

		return h.ResponseSuccessNoDataWithDesc("个人回档成功!")
	}
	// 全体回滚
	if rollTime > 0 || rollStage != "" {
		// 锁定登录服状态 不允许登录
		d.All("login", "rollbackOffline", mqrpc.Param())
		defer func() {
			// 解除锁定
			d.All("login", "rollbackDone", mqrpc.Param())
		}()

		// 游戏服全踢下线 保存数据
		d.All("game", "kickAllPlayer", mqrpc.Param())

		var limit int64 = 10000
		var lastDocId string = "-1"

		filter := bson.M{}
		list := make(map[string]any)
		for {
			if lastDocId == "" {
				break
			}
			if lastDocId != "-1" {
				_id, _ := primitive.ObjectIDFromHex(lastDocId)
				filter["_id"] = bson.M{"$gt": _id}
				lastDocId = ""
			}

			// 每次无条件查询1000条记录出来 但是不输出data数据
			cursor, err := db.FIX_RECORD.GetCollection().Find(context.TODO(), &filter, &options.FindOptions{
				Limit: &limit,
				Projection: bson.M{
					"data": 0,
				},
			})
			if lastDocId == "-1" {
				lastDocId = ""
			}
			if err != nil {
				return h.ResponseErrorNoDataWithDesc("数据库错误")
			}
			cnt := 0
			for cursor.Next(context.TODO()) {
				cnt++
				var temp bson.M
				err = cursor.Decode(&temp)
				if err != nil {
					break
				}
				if cnt >= int(limit) {
					lastDocId = temp["_id"].(primitive.ObjectID).Hex()
				}

				id, ok := temp["id"]
				if !ok {
					continue
				}

				_id := id.(string)
				if rollTime > 0 {
					timestamp, ok := temp["timestamp"]
					if !ok {
						continue
					}
					_timestamp := timestamp.(int64)
					// 时间必定不会超过rollTime
					if _timestamp > rollTime {
						lastDocId = ""
						break

					}
					val, ok := list[_id]
					if ok && val.(int64) > _timestamp {
						continue
					}
					list[_id] = _timestamp
				} else if rollStage != "" {
					stage, ok := temp["extra"]
					if !ok {
						continue
					}
					_stage := stage.(string)
					if _stage != rollStage {
						continue
					}
					list[_id] = _stage
				}
			}
			_ = cursor.Close(context.TODO())
		}
		if len(list) <= 0 {
			return h.ResponseErrorNoDataWithDesc("回档失败，没有可回档的玩家数据!")
		}
		if rollTime > 0 {
			// todo 将回档时间之后的数据全部移到另一个地方
			// 按时间回档先直接删除
			db.FIX_RECORD.GetCollection().DeleteMany(context.TODO(), &bson.M{"timestamp": bson.M{"$gt": rollTime}})
		}

		// 开始回档
		for id, cond := range list {
			tagIsStage := false
			filter := bson.M{"id": id}
			if v, ok := cond.(string); ok {
				filter["extra"] = v
				tagIsStage = true
			} else if v, ok := cond.(int64); ok {
				filter["timestamp"] = v
			}
			single := db.FIX_RECORD.GetCollection().FindOne(context.TODO(), &filter, &options.FindOneOptions{
				Projection: bson.M{
					"data":      1,
					"timestamp": 1,
				},
			})
			if single.Err() != nil {
				log.Error("回档单个数据失败1: %v", single.Err())
				continue
			}
			var bs bson.M
			single.Decode(&bs)
			data, ok := bs["data"].(primitive.Binary)
			if !ok {
				log.Error("回档单个数据失败2: %v", single.Err())
				continue
			}
			// 创建gzip reader
			gr, err := gzip.NewReader(bytes.NewReader(data.Data))
			if err != nil {
				log.Error("回档单个数据失败3: %v", single.Err())
				continue
			}
			defer gr.Close()

			var buf bytes.Buffer
			if _, err := io.Copy(&buf, gr); err != nil {
				return h.ResponseErrorNoDataWithDesc("读取解压数据失败!" + err.Error())
			}

			recordTime := bs["timestamp"].(int64)
			originalJSON := buf.String()
			diff := time.Now().UnixMilli() - recordTime
			pattern := `(\b\d{13}\b)`
			regExp, err := regexp.Compile(pattern)
			if err != nil {
				return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("正则表达式编译错误:%v", err.Error()))
			}
			// 替换所有匹配项
			originalJSON = regExp.ReplaceAllStringFunc(originalJSON, func(str string) string {
				return cast.ToString(cast.ToInt64(str) + diff)
			})

			var tempPlr *structs.Player
			if err := ut.JsonToBsonUnmarshal([]byte(originalJSON), &tempPlr); err != nil {
				log.Error("回档单个数据失败4: %v", single.Err())
				continue
			}

			tempPlr.Id = id
			tempPlr.SaveToDb(ut.FieldToBsonAuto(tempPlr))
			if tagIsStage && recordTime > 0 {
				oid := bs["_id"].(primitive.ObjectID)
				db.FIX_RECORD.GetCollection().DeleteMany(context.TODO(), &bson.M{"_id": bson.M{"$gte": oid}})
			}
		}

		return h.ResponseSuccessWithDataNoDesc(fmt.Sprintf("回档完成, 回档玩家数量: %d", len(list)))
	}
	return h.ResponseErrorNoDataWithDesc("回档失败，解析类型错误!")
}

func CreateCodeAndWrite2Db(cdk *structs.Cdk) int {
	_, err := db.CDK.GetCollection().InsertOne(context.TODO(), &cdk)
	if err != nil && mongo.IsDuplicateKeyError(err) {
		// 重复的兑换码
		return 1
	}
	return 0
}

func RandomCode() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	strLen := 12
	randomStr := make([]byte, strLen)
	for i := range randomStr {
		randomStr[i] = charset[rand.Intn(len(charset))]
	}
	return string(randomStr)
}
