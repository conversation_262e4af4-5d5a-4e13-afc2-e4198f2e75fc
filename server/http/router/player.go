package router


import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"train/base/structs"
	"train/db"
	"train/http/h"
	"train/http/types"
	ut "train/utils"

	"github.com/gin-gonic/gin"
	mqrpc "github.com/huyangv/vmqant/rpc"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Player struct {
	Router
	KickPlayer struct {
		Uid string `param:"uid" validate:"required,trim"`
	}
	DeletePlayer struct {
		Uid string `param:"uid" validate:"required,trim"`
	}
	Search struct {
		Type int    `param:"type" validate:"required,in=1|2|3"`
		Uid  string `param:"uid" validate:"trim"`
		Size int    `param:"size" validate:"in=10|20|50|2000"`
		Page int    `param:"page" validate:""`
	} `@log:"false"`
	ChangeProp struct {
		Uid string `param:"uid" validate:"required,trim"`
		Num int    `param:"num" validate:"required,gt=0"`
		Typ int    `param:"typ" validate:"required,in=1|2|3"`
	}
	// 记录用户数据存档
	RecordPlayer struct {
		Uid  string `param:"uid" validate:"required,trim"`
		Name string `param:"name" validate:"required"`
	}
	ListRecordPlayer struct {
		Uid string `param:"uid" validate:"required,trim"`
	} `@log:"false"`
	// 获取备份存档
	RecordData struct {
		Id    string `param:"id" validate:"required,trim"`
		Main  bool   `param:"main" validate:"required,trim"` // true表示从玩家表里面取
		Back  bool   `param:"back" validate:"required,trim"` // true表示从回档数据里面查询
		DocId string `param:"docId" validate:"required,trim"`
	} `@log:"false"`
	SetRecordData struct {
		Mid  string `param:"mid" validate:"required,trim"`
		Sid  string `param:"sid" validate:"required,trim"`
		Data string `param:"data" validate:"required"`
		Main bool   `param:"main" validate:"required,trim"` // true表示不是从备份里面取而是从玩家表里面取
		Back bool   `param:"back" validate:"required,trim"` // true表示从回档数据里面查询
	}
	// 删除
	DeleteRecord struct {
		Id string `param:"id" validate:"required,trim"`
	}
}

func (p *Player) PostKickPlayer(ctx *gin.Context, uid string) (body map[string]interface{}, e string) {
	return p.GameRpc(uid).Online().Call("kickPlayer", mqrpc.Param(uid))
}

func (p *Player) PostDeletePlayer(ctx *gin.Context, uid string) (body map[string]interface{}, e string) {
	return p.GameRpc(uid).Online().Call("deletePlayer", mqrpc.Param(uid))
}

func (p *Player) PostSearch(ctx *gin.Context, typ int, uid string, size, page int) (body map[string]interface{}, e string) {
	if !ut.IsEmpty(uid) {
		// 查询指定玩家
		//rpc := p.getPlayerRpcName(uid)
		//return p.RpcCall(ctx.Writer, rpc, "getPlayer", mqrpc.Param(typ, uid, size, page)), ""
		return p.GameRpc(uid).Call("getPlayer", mqrpc.Param(typ, uid, size, page))
	}
	dataArray := make(map[string]any)
	dataArray["offline"] = make([]any, 0)
	dataArray["offlineCnt"] = 0
	dataArray["onlineCnt"] = 0
	services, _ := p.Module().Registry().GetService("game")
	for _, service := range services {
		for _, node := range service.Nodes {
			retVal := p.RpcCall(ctx.Writer, node.Id, "getPlayer", mqrpc.Param(typ, uid, size, page))
			if retVal != nil {
				code := cast.ToInt(retVal["code"])
				data := retVal["data"]
				if code == 0 && data != nil {
					thisData := data.(map[string]any)
					dataArray["offline"] = thisData["offline"]
					dataArray["offlineCnt"] = cast.ToInt(thisData["offlineCnt"])
					dataArray["onlineCnt"] = cast.ToInt(thisData["onlineCnt"]) + cast.ToInt(dataArray["onlineCnt"])
					online := thisData["online"]
					if online != nil {
						onlineData := online.([]any)
						if dataArray["online"] == nil {
							arr := make([]any, 0)
							arr = append(arr, onlineData...)
							dataArray["online"] = arr
						} else {
							arr := dataArray["online"].([]any)
							arr = append(arr, onlineData...)
						}
					}
				}
			}
		}
	}
	return h.ResponseSuccessWithDataNoDesc(dataArray)
}

func (p *Player) PostChangeProp(ctx *gin.Context, uid string, num int, typ int) (body map[string]interface{}, e string) {
	return p.GameRpc(uid).Online().Call("changeProp", mqrpc.Param(uid, num, typ))
}
func (p *Player) PostRecordPlayer(ctx *gin.Context, uid, name string) (body map[string]interface{}, e string) {
	return p.GameRpc(uid).Offline().Call("httpRecordPlayer", mqrpc.Param(uid, name))
}

func (p *Player) PostListRecordPlayer(ctx *gin.Context, uid string) (body map[string]interface{}, e string) {
	filter := bson.M{"type": 2}
	if !ut.IsEmpty(uid) {
		filter["id"] = uid
	}

	cursor, err := db.DELETE_PLAYER.GetCollection().Find(context.TODO(), filter, &options.FindOptions{
		Projection: bson.M{
			"id":   1,
			"time": 1,
			"type": 1,
			"name": 1,
		},
	})
	arr := make([]*types.DeletePlayer, 0)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("查询失败.")
	}
	for cursor.Next(context.Background()) {
		var temp bson.M
		var dp *types.DeletePlayer
		err := cursor.Decode(&temp)
		if err == nil {
			_ = cursor.Decode(&dp)
			dp.Mid = temp["_id"].(primitive.ObjectID).Hex()
			arr = append(arr, dp)
			continue
		}
	}
	return h.ResponseSuccessWithDataNoDesc(arr)
}

func (p *Player) PostRecordData(ctx *gin.Context, id string, main, back bool, docId string) (body map[string]interface{}, e string) {
	hex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("id错误:%v", err.Error()))
	}
	var singleResult *mongo.SingleResult

	if back {
		_id, err := primitive.ObjectIDFromHex(docId)
		if err != nil {
			return h.ResponseErrorNoDataWithDesc("回档个人数据id错误")
		}
		res := db.FIX_RECORD.GetCollection().FindOne(context.TODO(), &bson.M{
			"_id": _id,
		})
		var bs bson.M
		res.Decode(&bs)
		data, ok := bs["data"].(primitive.Binary)
		if !ok {
			return h.ResponseErrorNoDataWithDesc("回档个人数据不存在")
		}
		responseJson := ""
		var tempPlr *structs.Player
		if err := bson.Unmarshal(data.Data, &tempPlr); err != nil {
			if err.Error() == "invalid document length" {
				err = nil
				// 兼容旧版本压缩
				gr, err := gzip.NewReader(bytes.NewReader(data.Data))
				if err != nil {
					return h.ResponseErrorNoDataWithDesc("解压回档数据失败!" + err.Error())
				}
				defer gr.Close()
				var buf bytes.Buffer
				if _, err := io.Copy(&buf, gr); err != nil {
					return h.ResponseErrorNoDataWithDesc("读取解压数据失败!" + err.Error())
				}
				responseJson = buf.String()
				if strings.Contains(responseJson, "NickName") || (strings.Contains(responseJson, "achievement") && strings.Contains(responseJson, "Completes")) {
					err = json.Unmarshal([]byte(responseJson), &tempPlr)
				} else {
					err = ut.JsonToBsonUnmarshal([]byte(responseJson), tempPlr)
				}
				if err != nil {
					return h.ResponseErrorNoDataWithDesc("回档个人数据出错，存档数据解析失败!" + err.Error())
				}
			} else {
				return h.ResponseErrorNoDataWithDesc("回档个人数据出错，存档数据解析失败!" + err.Error())
			}
		} else {
			responseJson, _ = ut.BsonToReadableJson(data.Data)
		}

		tempPlr.Id = id
		plr, _ := structs.TryGetPlayerFromDb(tempPlr.Id, false)
		if plr == nil {
			return h.ResponseErrorNoDataWithDesc("回档个人数据出错，原玩家数据已经不存在!")
		}
		line := &types.DeletePlayer{
			Data: responseJson,
			Id:   tempPlr.Id,
			Mid:  plr.Uid.Get(),
		}

		return h.ResponseSuccessWithDataNoDesc(line)
	}

	if !main {
		singleResult = db.DELETE_PLAYER.GetCollection().FindOne(context.TODO(), bson.M{"_id": hex})
	} else {
		plr, _ := structs.TryGetPlayerFromDb(id, false)
		bytes, _ := bson.Marshal(plr)
		jsonStr, _ := ut.BsonToReadableJson(bytes)
		line := &types.DeletePlayer{
			Data: jsonStr,
			Id:   id,
			Mid:  plr.Uid.Get(),
		}
		return h.ResponseSuccessWithDataNoDesc(line)
	}
	var result bson.M
	_ = singleResult.Decode(&result)
	line := &types.DeletePlayer{}
	err = singleResult.Decode(line)
	if err != nil {
		if mongo.ErrNoDocuments != err {
			return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("查找记录失败:%v", err.Error()))
		}
		// 去玩家表查一次
	}
	line.Mid = result["_id"].(primitive.ObjectID).Hex()
	return h.ResponseSuccessWithDataNoDesc(line)
}

func (p *Player) PostSetRecordData(ctx *gin.Context, mid, sid, data string, main, back bool) (body map[string]interface{}, e string) {
	hex, err := primitive.ObjectIDFromHex(mid)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("id错误:%v", err.Error()))
	}
	// 如果在线就踢掉 保存数据
	p.PostKickPlayer(ctx, sid)

	if !main && !back {
		singleResult := db.DELETE_PLAYER.GetCollection().FindOne(context.TODO(), bson.M{"_id": hex})
		line := &types.DeletePlayer{}
		err = singleResult.Decode(line)
		if err != nil {
			return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("查找记录失败:%v", err.Error()))
		}
		if line.Id != sid {
			return h.ResponseErrorNoDataWithDesc("记录不匹配,无法修改")
		}
	}
	// 把data反序列进plr看看是否正确
	var tempPlr *structs.Player
	if strings.Contains(data, "NickName") || (strings.Contains(data, "achievement") && strings.Contains(data, "Completes")) {
		// 兼容
		err = json.Unmarshal([]byte(data), &tempPlr)
	} else {
		err = ut.JsonToBsonUnmarshal([]byte(data), &tempPlr)
	}
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("反序列化plr数据失败,请检查:%v", err.Error()))
	}
	if !main && !back {
		_, err = db.DELETE_PLAYER.GetCollection().UpdateOne(context.TODO(), bson.M{"_id": hex}, bson.M{"$set": bson.M{"data": data}})
	} else {
		// 保存用户数据
		tempPlr.Id = sid
		tempPlr.Uid, err = ut.NewMongoIdFrom(mid)
		tempPlr.SaveToDb(ut.FieldToBsonAuto(tempPlr))
	}
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("备份数据更新失败,请检查:%v", err.Error()))
	}
	return h.ResponseSuccessNoDataWithDesc("更新成功!")
}

func (p *Player) PostDeleteRecord(ctx *gin.Context, id string) (body map[string]interface{}, e string) {
	hex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("id错误:%v", err.Error()))
	}
	_, _ = db.DELETE_PLAYER.GetCollection().DeleteOne(context.TODO(), &bson.M{
		"_id": hex,
	})

	return h.ResponseSuccessNoDataWithDesc("操作完成!")
}
