package http

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"reflect"
	"strings"
	"time"
	"train/base/enum"
	"train/http/h"
	"train/http/router"
	"train/net"
	ut "train/utils"

	"github.com/bamzi/jobrunner"
	"github.com/gin-gonic/gin"
	"github.com/huyangv/vmqant/conf"
	go_api "github.com/huyangv/vmqant/httpgateway/proto"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	mqrpc "github.com/huyangv/vmqant/rpc"
	"github.com/huyangv/vmqant/server"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

var Module = func() module.Module {
	return new(Http)
}

type Http struct {
	basemodule.BaseModule
	httpClient *Client
	middleware *net.Middleware
	task       *task
}

func (this *Http) GetType() string {
	return "http"
}

func (this *Http) Version() string {
	return "1.0.0"
}

func (this *Http) OnInit(app module.App, settings *conf.ModuleSettings) {
	// 可执行文件所在目录
	eDir, _ := os.Executable()
	// 工作目录
	wDir, _ := os.Getwd()

	this.BaseModule.OnInit(this, app, settings, func(op *server.Options) {
		op.Metadata = map[string]string{
			enum.CODE_VERSION:       this.Version(),
			enum.MIN_CLIENT_VERSION: "",
			enum.EXECUTE_DIR:        eDir,
			enum.WORK_DIR:           wDir,
		}
	})

	this.task = &task{httpModule: this}
	// 中间件
	this.middleware = net.Create(this)
	this.middleware.Wrap("StartRecordPlayer", this.task.startRecordPlayer)
	this.middleware.Wrap("StopRecordPlayer", this.task.stopRecordPlayer)

	gin.DefaultWriter = io.Discard
	this.httpClient = GetHttpClient().setModule(this)
}

func (this *Http) Run(closeSig chan bool) {
	// 运行http任务
	jobrunner.Schedule("@every 30m", this.task)
	// 开始注册路由
	this.require(&router.Account{})
	this.require(&router.Player{})
	this.require(&router.Dev{})
	this.require(&router.Invite{})
	this.require(&router.Server{})
	sys := &router.System{}
	this.require(sys)
	// 运行http client
	port, ok := this.GetModuleSettings().Settings["Port"]
	if !ok {
		port = 8181
	}
	log.Debug("Http模块已经启动,端口:%v", port)

	go func() {
		sys.OnLoaded()
		this.httpClient.Run(fmt.Sprintf(":%v", port))
	}()
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	//stop
	sys.OnClose()
}

func (this *Http) OnDestroy() {
	this.BaseModule.OnDestroy()
}

func (this *Http) NoFoundFunction(fn string) (*mqrpc.FunctionInfo, error) {
	return &mqrpc.FunctionInfo{
		Function:  reflect.ValueOf(this.httpgateway),
		Goroutine: true,
	}, nil
}

func (this *Http) GetModuleServer() server.Server {
	return this.GetServer()
}

func (this *Http) GetRpcServer() mqrpc.RPCServer {
	return this.GetServer().GetRpcServer()
}

func (this *Http) httpgateway(request *go_api.Request) (*go_api.Response, error) {
	log.Error("httpgateway")
	return nil, nil
}

func (this *Http) require(r interface{}) {
	rt := reflect.TypeOf(r)
	rv := reflect.ValueOf(r)
	clazzName := ut.FirstToLower(rt.Elem().Name())
	// 路由基类设置RpcCall
	rpcCall := rv.Elem().FieldByName("RpcCall")
	if rpcCall.IsValid() && rpcCall.Type() == reflect.TypeOf((func(w http.ResponseWriter, targetServer string, topic string, params mqrpc.ParamOption) (body map[string]interface{}))(nil)) {
		rpcCall.Set(reflect.ValueOf(this.httpClient.rpcCall))
	}
	// 获取module
	moduleCall := rv.Elem().FieldByName("Module")
	if moduleCall.IsValid() && moduleCall.Type() == reflect.TypeOf((func() module.App)(nil)) {
		moduleCall.Set(reflect.ValueOf(this.GetApp))
	}
	// 获取subClass
	subClassCall := rv.Elem().FieldByName("SubClass")
	if subClassCall.IsValid() && subClassCall.Type() == reflect.TypeOf((func() module.RPCModule)(nil)) {
		subClassCall.Set(reflect.ValueOf(this.GetSubclass))
	}

	// 遍历方法列表，处理PostXXX和GetXXX方法并代理到路由中
	for i := 0; i < rt.NumMethod(); i++ {
		method := rt.Method(i)
		numParams := method.Type.NumIn()
		methodName := method.Name
		isPost := true
		isHttpFunc := false
		if strings.HasPrefix(methodName, "Get") {
			methodName = methodName[3:]
			isPost = false
			isHttpFunc = true
		}
		if strings.HasPrefix(methodName, "Post") {
			methodName = methodName[4:]
			isHttpFunc = true
		}
		if !isHttpFunc {
			continue
		}
		structFieldName := methodName
		methodName = ut.FirstToLower(methodName)
		// 根据方法名称和struct名称生成路由符号
		path := fmt.Sprintf("/%s/%s", clazzName, methodName)
		field, b := rv.Elem().Type().FieldByName(structFieldName)
		if !b {
			log.Error("wrap中间件处理参数出错,定义了一个多参数handler，但是未配置参数信息:%s", path)
			continue
		}
		paramType := method.Type.In(1)
		// 由于上下文的重要性,所以第一个参数必须是ctx,其包含了很多有用的信息
		if paramType != reflect.TypeOf((*gin.Context)(nil)) {
			log.Warning("[%s]-[%s]:params type not *gin.Context,skip.", clazzName, methodName)
			continue
		}
		var handlerFunc gin.HandlerFunc
		_method := rv.MethodByName(method.Name)

		handlerFunc = func(ctx *gin.Context) {
			// 检查接口权限通过情况 默认情况打开
			get := field.Tag.Get("@auth")
			auth := true
			if !ut.IsEmpty(get) {
				auth = cast.ToBool(get)
			}
			ctx.Set("@auth", auth)
			// 处理log是否打开
			get = field.Tag.Get("@log")
			open := true
			if !ut.IsEmpty(get) {
				open = cast.ToBool(get)
			}
			ctx.Set("@log", open)
			// @LEN 是上层中间件解析参数后获取到的参数个数,可能是0或者不存在
			_, exists := ctx.Get("@LEN")
			lx := 0
			inputs := make([]reflect.Value, numParams-1)
			inputs[lx] = reflect.ValueOf(ctx)
			lx++
			// @token 是上层中间件解析参数请求头后获取到的token,可能不存在
			_token, tokenExists := ctx.Get("@token")
			userId := ""
			__token := cast.ToString(_token)
			devToken := strings.HasPrefix(__token, "DEV-")
			if devToken {
				__token = strings.Replace(__token, "DEV-", "", 1)
			}
			// 优先校验 token字段
			if tokenExists {
				claims := h.TryDecodeWebToken(__token, devToken)
				// token过期
				if !devToken && !claims.VerifyExpiresAt(time.Now().Unix(), true) {
					ctx.JSON(h.OK, map[string]interface{}{
						"code":   300,
						"data":   nil,
						"notify": "token过期",
					})
					ctx.Abort()
					return
				}
				userId = cast.ToString(claims["_id"])
				ctx.Set("@id", userId)
				// 开启了auth的接口必须传递token
				if auth && (!tokenExists || ut.IsEmpty(userId)) {
					ctx.JSON(h.OK, map[string]interface{}{
						"code":   301,
						"data":   nil,
						"notify": "权限不足,无法访问1.",
					})
					ctx.Abort()
					return
				}
			}
			// 开始检查参数的validate 标签
			validate := func() {
				for j := 0; j < field.Type.NumField(); j++ {
					if ctx.IsAborted() {
						break
					}
					structField := field.Type.Field(j)
					// 获取参数tag
					paramTag := structField.Tag.Get("param")
					validateTag := structField.Tag.Get("validate")
					if ut.IsEmpty(paramTag) {
						//log.Debug("router 参数tag不正确:%s", path)
						continue
					}
					if paramTag == "@token" && !tokenExists {
						log.Error("无token访问 但是接口却需要传递@token,请求终止")
						ctx.JSON(h.Error, nil)
						ctx.Abort()
						return
					}
					if paramTag == "@id" {
						_id, idExists := ctx.Get("@id")
						if !idExists || ut.IsEmpty(cast.ToString(_id)) {
							log.Error("无token访问 但是接口却需要传递@id,请求终止")
							ctx.JSON(h.Error, nil)
							ctx.Abort()
							return
						}
					}

					v, e := ctx.Get(paramTag)
					if !e {
						// 赋予类型零值
						v = reflect.Zero(structField.Type).Interface()
					}
					// 将请求传递的字段，转换为handler入参字段类型
					vvv := convert(ctx, structField, reflect.ValueOf(v))
					inputs[lx] = vvv
					lx++
					if !ut.IsEmpty(validateTag) {
						doTagValidate(ctx, v, e, vvv, paramTag, validateTag, func(v reflect.Value) {
							inputs[lx-1] = v
						})
					}
				}
			}
			validate()
			if !exists {
				log.Debug("wrap中间件处理参数出错,request并未发送参数.")
				ctx.Abort()
			}
			if !ctx.IsAborted() {
				// 反射调用用户路由
				// log.Debug("wrapHandlerFunc : %v", inputs)
				returnValues := _method.Call(inputs)
				if len(returnValues) > 0 {
					ctx.JSON(h.OK, returnValues[0].Interface())
				} else {
					ctx.JSON(h.OK, "")
				}
			}
		}

		if isPost {
			GetHttpClient().Post(path, handlerFunc)
			log.Info("POST listen router path:[%s]", path)
		}
		if !isPost {
			GetHttpClient().Get(path, handlerFunc)
			log.Info("GET listen router path:[%s]", path)
		}
	}
}

// 将sourceValue 转换为 targetKind类型
func convert(ctx *gin.Context, targetKind reflect.StructField, sourceValue reflect.Value) reflect.Value {
	if targetKind.Type.Kind() == reflect.Slice {
		// 获取目标切片的类型信息
		sliceType := reflect.TypeOf(reflect.Zero(targetKind.Type).Interface())
		// 创建一个空的切片，类型与目标切片的类型相同
		newSlice := reflect.MakeSlice(sliceType, 0, 0)
		// 切片类型
		// sliceOf := reflect.SliceOf(sliceType)
		// 将传入的参数尝试转换,因为传入的参数已经被转换为map[string]interface{} 所以需要多处理一步判断
		bytes, err := json.Marshal(sourceValue.Interface())
		if err != nil {
			ctx.Abort()
			log.Error("尝试转换切片数据传递出错: before=> %v", err.Error())
			return reflect.Value{}
		}
		// 将newSlice类型原始值转换为指针后使用反序列化
		ptr := reflect.New(sliceType).Interface()
		err = json.Unmarshal(bytes, ptr)
		if err != nil {
			ctx.Abort()
			log.Error("尝试转换切片数据传递出错: after=> %v", err.Error())
			return reflect.Value{}
		}
		// 将解析后的值放回newSlice
		newSlice = reflect.ValueOf(ptr).Elem()
		return newSlice
	}
	// 基本数据类型
	if targetKind.Type.Kind() == sourceValue.Kind() {
		return sourceValue
	}
	switch targetKind.Type.Kind() {
	case reflect.String:
		return reflect.ValueOf(cast.ToString(sourceValue.Interface()))
	case reflect.Int:
		return reflect.ValueOf(cast.ToInt(sourceValue.Interface()))
	case reflect.Int64:
		return reflect.ValueOf(cast.ToInt64(sourceValue.Interface()))
	}
	log.Error("未处理的参数传递类型 >> %v << !", targetKind)

	return reflect.Value{}
}

/**
 *  例： func (d *Dev) PostAddGmCmd(ctx *gin.Context, cmd, desc string, fields []*types.GmField) (body map[string]interface{}, e string)
 *  v => 从ctx中获取到原始值
 *  isGetV => 是否从ctx中获取到v,在上一步执行时,如果isGetV=false,则 v会被设置成反射方法被调用需要的参数类型所对应的zero值, 假设v对应cmd参数,v零值则是""
 *  cv => 将v转换后的值,无论v是不是0值, cv都是反射方法被调用需要的参数类型所对应值, 假设v对应cmd参数,cv则是""
 *  paramTag 从ctx中取出v的key
 *  validateTag v字段所配置的验证标签字符串
 */
func doTagValidate(ctx *gin.Context, v any, isGetV bool, cv reflect.Value, paramTag, validateTag string, output func(v reflect.Value)) {
	quickAbort := func(msg string) {
		if ut.IsEmpty(msg) {
			msg = "参数不正确,请求终止!"
		}
		// 校验失败 返回消息
		ctx.JSON(h.Error, map[string]interface{}{
			"code":   -1023,
			"notify": msg,
		})
		ctx.Abort()
	}
	split := strings.Split(validateTag, ",")
	// 触发优先校验
	lo.ForEach(split, func(vs string, index int) {
		if reflect.TypeOf(v).Kind() == reflect.String {
			if vs == "trim" {
				v = ut.Trim(cast.ToString(v))
				ctx.Set(paramTag, v)
				if output != nil {
					output(reflect.ValueOf(v))
				}
			}
			if vs == "md5" {
				sum := md5.Sum([]byte(cast.ToString(v)))
				v = hex.EncodeToString(sum[:])
				ctx.Set(paramTag, v)
				if output != nil {
					output(reflect.ValueOf(v))
				}
			}
		}
	})
	// tag 遍历校验
	for _, vs := range split {
		if vs == "required" && !isGetV {
			log.Debug("参数[%s]设置了required,但是未能获取到值.", paramTag)
			quickAbort("参数列表不正确!")
			break
		}
		if strings.HasPrefix(vs, "in=") {
			charge := vs[3:]
			after := strings.Split(charge, "|")
			l := len(after)
			if l > 0 {
				tv := cast.ToFloat64(v)
				for _, s := range after {
					if tv == cast.ToFloat64(s) {
						l = 0
						break
					}
				}
				if l > 0 {
					log.Debug("参数[%s]设置了in,但是传值不满足: %s, %f", paramTag, charge, tv)
					quickAbort(fmt.Sprintf("参数:%s必须在列:%v", paramTag, after))
					break
				}
			}
		}
		if strings.HasPrefix(vs, "gt=") {
			charge := cast.ToFloat64(vs[3:])
			tv := cast.ToFloat64(v)
			if tv <= charge {
				log.Debug("参数[%s]设置了gt,但是传值不满足: %f, %f", paramTag, charge, tv)
				quickAbort(fmt.Sprintf("参数:%s必须大于:%f", paramTag, tv))
				break
			}
		}
		if strings.HasPrefix(vs, "gte=") {
			charge := cast.ToFloat64(vs[4:])
			tv := cast.ToFloat64(v)
			if tv < charge {
				log.Debug("参数[%s]设置了gte,但是传值不满足: %f, %f", paramTag, charge, tv)
				quickAbort(fmt.Sprintf("参数:%s必须大于等于:%f", paramTag, tv))
				break
			}
		}
		if strings.HasPrefix(vs, "lt=") {
			charge := cast.ToFloat64(vs[3:])
			tv := cast.ToFloat64(v)
			if charge >= tv {
				log.Debug("参数[%s]设置了lt,但是传值不满足: %f, %f", paramTag, charge, tv)
				quickAbort(fmt.Sprintf("参数:%s必须小于:%f", paramTag, tv))
				break
			}
		}
		if strings.HasPrefix(vs, "lte=") {
			charge := cast.ToFloat64(vs[4:])
			tv := cast.ToFloat64(v)
			if charge > tv {
				log.Debug("参数[%s]设置了lte,但是传值不满足: %f, %f", paramTag, charge, tv)
				quickAbort(fmt.Sprintf("参数:%s必须小于等于:%f", paramTag, tv))
				break
			}
		}
		if strings.HasPrefix(vs, "len=") {
			if reflect.TypeOf(v).Kind() != reflect.String {
				log.Debug("参数[%s]设置了len,但是传值类型不是字符串: %v", paramTag, v)
				quickAbort(fmt.Sprintf("参数:%s必须是字符串.", paramTag))
				break
			}
			charge := cast.ToInt(vs[4:])
			if len(cast.ToString(v)) < charge {
				log.Debug("参数[%s]设置了len,但是传值不满足: %v", paramTag, v)
				quickAbort(fmt.Sprintf("参数:%s长度需要至少等于%d", paramTag, charge))
				break
			}
		}
		if strings.HasPrefix(vs, "xlen=") {
			if reflect.TypeOf(v).Kind() != reflect.String {
				log.Debug("参数[%s]设置了len,但是传值类型不是字符串: %v", paramTag, v)
				quickAbort(fmt.Sprintf("参数:%s必须是字符串.", paramTag))
				break
			}
			charge := cast.ToInt(vs[5:])
			if len(cast.ToString(v)) != charge {
				log.Debug("参数[%s]设置了xlen,但是传值不满足: %v", paramTag, v)
				quickAbort(fmt.Sprintf("参数:%s长度必须等于%d", paramTag, charge))
				break
			}
		}
		if strings.HasPrefix(vs, "deep") {
			kind := reflect.ValueOf(v).Kind()
			// 必须是结构体切片或者结构体
			if kind == reflect.Slice {
				for i := 0; i < cv.Len(); i++ {
					ele := cv.Index(i)
					for j := 0; j < ele.Elem().Type().NumField(); j++ {
						f := ele.Elem().Type().Field(j)
						_param := f.Tag.Get("param")
						_validate := f.Tag.Get("validate")
						inf := ele.Elem().Field(j).Interface()
						inf_cv := convert(ctx, f, ele.Elem().Field(j))
						doTagValidate(ctx, inf, true, inf_cv, _param, _validate, nil)
					}
				}
				continue
			}
			log.Debug("参数[%s]设置了deep,但是类型不匹配: %v, %v", paramTag, v, kind)
			quickAbort(fmt.Sprintf("参数类型验证失败:%s", paramTag))
			break
		}
	}
}
