package task

import (
	"github.com/gin-gonic/gin"
	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
)

var Module = func() module.Module {
	return new(Task)
}

type Task struct {
	basemodule.BaseModule
	httpServer *gin.Engine
}

func (t *Task) GetType() string {
	return "task" //很关键,需要与配置文件中的Module配置对应
}

func (t *Task) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (t *Task) OnAppConfigurationLoaded(app module.App) {
	t.BaseModule.OnAppConfigurationLoaded(app)
}

func (t *Task) OnInit(app module.App, settings *conf.ModuleSettings) {
	t.BaseModule.OnInit(t, app, settings)
	t.InitHttp()
}

func (t *Task) Run(closeSig chan bool) {
	//run
	go func() {
		t.RunHttp()
	}()
	t.RunTick()
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", t.GetType())
	t.StopTick()
	t.StopHttp()
	//stop
}

func (t *Task) OnDestroy() {
	t.BaseModule.OnDestroy()
}
