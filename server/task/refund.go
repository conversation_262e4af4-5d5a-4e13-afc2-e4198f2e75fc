package task

import (
	"time"
	"train/base/enum"
	"train/base/structs"
	"train/sdk/google"

	"github.com/huyangv/vmqant/log"
)

// 谷歌退款订单检测
func (this *Task) GoogleOrderRefundCheck() {
	go func() {
		tiker := time.NewTicker(time.Minute * 10)
		defer tiker.Stop()
		for isRunning {
			Pay := structs.PayModule{}
			if err, googleOrderList := google.GoogleVoidedOrderCheck(); err == nil && googleOrderList != nil {
				for _, orderId := range googleOrderList {
					// 谷歌退款处理
					_, e := Pay.FindByOrderId(orderId, enum.PAY_PLATFORM_GOOGLE)
					if e == "" {
						// 消耗性物品退款
						this.handleOrderRefund(orderId, enum.PAY_PLATFORM_GOOGLE)
					} else {
						// 订阅退款 TODO 验证续订的订单号是否和初始订阅的一样
						// this.handleSubRefund(orderId, enum.PAY_PLATFORM_GOOGLE)
					}
				}
			}
			<-tiker.C
		}
	}()
}

// 退款处理
func (this *Task) handleOrderRefund(orderId, platform string) {
	Pay := structs.PayModule{}
	log.Info("handleOrderRefund orderId: %v, platform: %v", orderId, platform)
	orderInfo, e := Pay.FindByOrderId(orderId, platform)
	if e != "" {
		// 未查询到订单
		log.Error("handleOrderRefund order not find orderId: %v, err: %v", orderId, e)
		return
	}
	if orderInfo.State == structs.ORDER_STATE_REFUND {
		// 已经是已退款状态
		log.Error("handleOrderRefund already refund orderId: %v", orderId)
		return
	}
	if orderInfo.State == structs.ORDER_STATE_FINISH {
		// 已发放奖励 扣除金币
		// productInfo := config.GetJson(slg.PAY_SHOP_GOLD_CONFIG_NAME).Get(slg.PAY_SHOP_GOLD_CONFIG_PRODUCTID, orderInfo.ProductId)
		// if len(productInfo) == 0 {
		// 	// 未找到商品数据
		// 	log.Error("handleOrderRefund productInfo nil uid: %v, itemId: %v, platform: %v", orderInfo.UID, orderInfo.ProductId, orderInfo.Platform)
		// 	return
		// }
		// user := GetUserByDB(orderInfo.UserId)
		// if user == nil {
		// 	// 未找到用户
		// 	log.Error("handleOrderRefund user nil uid: %v, userId: %v", orderInfo.UID, orderInfo.UserId)
		// 	return
		// }
		// // 扣除金币
		// goldCount := -ut.Int(productInfo[0][slg.PAY_SHOP_GOLD_CONFIG_GOLD]) * orderInfo.Quantity
		// user.ChangeGoldByRefund(goldCount, enums.GOLD_CHANGE_REFUND)
		// // 发送邮件
		// content := ut.StringJoin("|", -goldCount, user.Gold)
		// this.SendMailOne(user.SID, user.UID, 100009, "", content, "-1", nil)
		// if user.FlagUpdateDB() {
		// 	this.PutPlayerNotify(user, constant.NQ_UPDATE_ITEMS, &pb.OnUpdatePlayerInfoNotify{Data_41: &pb.UpdateOutPut{Gold: int32(user.Gold), Flag: pb.AddFlags(int32(pb.OutPutFlagEnum_Gold))}})
		// }
		// // 更新订单状态
		// rechargeDb.UpdateRefundOrder(orderInfo.UID)
		// // 数数上报
		// ta.Track(user.SID, user.UID, user.DistinctId, 0, "ta_refund", map[string]interface{}{
		// 	"product_id":    orderInfo.ProductId,
		// 	"currency_type": orderInfo.CurrencyType,
		// 	"pay_amount":    orderInfo.PayAmount,
		// 	"os":            user.GetOs(),
		// 	"platform":      user.Platform,
		// })
	}
}
