package task

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"
	"train/base/enum"
	comm "train/common"
	"train/sdk/apple"
	ut "train/utils"

	"github.com/gin-gonic/gin"
	"github.com/huyangv/vmqant/log"
	"github.com/mitchellh/mapstructure"
)

func (this *Task) InitHttp() {
	gin.SetMode(ut.If(comm.IsDebug(), gin.DebugMode, gin.ReleaseMode))
	s := gin.Default()
	this.httpServer = s

	s.Use(func(ctx *gin.Context) {
		ctx.Header("Access-Control-Allow-Origin", "*")
		ctx.Header("Access-Control-Allow-Methods", "POST, GET")
		ctx.Header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization")
		ctx.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
		ctx.Header("Access-Control-Allow-Credentials", "true")
		if ctx.Request.Method == "OPTIONS" {
			ctx.AbortWithStatus(204)
			return
		}
		ctx.Next()
	})

	s.POST("/notify/appleNotify", this.httpAppleNotify)
}

func (this *Task) RunHttp() {
	port, _ := this.GetModuleSettings().Settings["Port"]
	log.Debug("Task Http模块已经启动,端口:%v", port)
	this.httpServer.Run(fmt.Sprintf(":%v", port))
}

func (this *Task) StopHttp() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	port, _ := this.GetModuleSettings().Settings["Port"]
	//停止HTTP服务器
	server := &http.Server{
		Addr:    fmt.Sprintf(":%v", port),
		Handler: this.httpServer,
	}
	if err := server.Shutdown(ctx); err != nil {
		log.Error("Server Shutdown:", err)
	}
}

// Apple推送
func (this *Task) httpAppleNotify(c *gin.Context) {
	signedPayload := c.PostForm("signedPayload")
	log.Info("httpAppleNotify signedPayload: %v", signedPayload)
	headerStr := strings.Split(signedPayload, ".")[0]
	// 解析签名标头
	publicKey, err := apple.ParseAppleJwsHead(headerStr)
	if err != nil {
		log.Error("AppleNotify parseAppleJwsHead err: %v", err)
		c.String(http.StatusOK, "")
		return
	}
	// 解析JWS格式的推送信息
	mapClaims, err := ut.ParseJwsToken(signedPayload, publicKey)
	// 解析推送数据
	data, ok := mapClaims["data"].(map[string]interface{})
	if !ok {
		log.Error("AppleNotify Parse data err: %v", data)
		c.String(http.StatusOK, "")
		return
	}
	var appleNotifyData apple.AppleNotifyData
	err = mapstructure.Decode(data, &appleNotifyData)
	if err != nil {
		log.Error("AppleNotify Decode data err: %v", data)
		c.String(http.StatusOK, "")
		return
	}
	notifyType := ut.String(mapClaims["notificationType"])
	// notifySubType := ut.String(mapClaims["subtype"])
	switch notifyType {
	case apple.APPLE_NOTIFY_TYPE_DID_RENEW, apple.APPLE_NOTIFY_TYPE_DID_CHANGE_RENEWAL_STATUS:
		log.Error("httpAppleNotify %s not support", notifyType)
		// 解析推送数据中的JWS格式的订阅信息
		// renewalHeaderStr := strings.Split(appleNotifyData.SignedRenewalInfo, ".")[0]
		// // 解析签名标头
		// publicKey, err = apple.ParseAppleJwsHead(renewalHeaderStr)
		// if err != nil {
		// 	log.Error("AppleNotify parseAppleRenewalJwsHead err: %v", err)
		// 	break
		// }
		// // 解析JWS格式的推送信息
		// renewalMapClaims, err := ut.ParseJwsToken(appleNotifyData.SignedRenewalInfo, publicKey)
		// if err != nil {
		// 	log.Error("AppleNotify parseAppleRenewalJws data err: %v", err)
		// 	break
		// }
		// orderId := ut.String(renewalMapClaims["originalTransactionId"])
		// endTime := ut.Int(renewalMapClaims["renewalDate"])
		// if notifyType == apple.APPLE_NOTIFY_TYPE_DID_RENEW {
		// 	// 续订
		// 	this.handleDidRenew(orderId, slg.PAY_PLATFORM_APPLE, endTime)
		// } else if notifyType == apple.APPLE_NOTIFY_TYPE_DID_CHANGE_RENEWAL_STATUS {
		// 	// 更新自动续订状态
		// 	if notifySubType == apple.APPLE_NOTIFY_SUB_TYPE_AUTO_RENEW_ENABLED {
		// 		this.handleRenewStateChange(orderId, slg.PAY_PLATFORM_APPLE, true)
		// 	} else if notifySubType == apple.APPLE_NOTIFY_SUB_TYPE_AUTO_RENEW_DISABLED {
		// 		this.handleRenewStateChange(orderId, slg.PAY_PLATFORM_APPLE, false)
		// 	}
		// }
	case apple.APPLE_NOTIFY_TYPE_REFUND:
		// 解析推送数据中的JWS格式的交易信息
		transHeaderStr := strings.Split(appleNotifyData.SignedTransactionInfo, ".")[0]
		// 解析签名标头
		publicKey, err = apple.ParseAppleJwsHead(transHeaderStr)
		if err != nil {
			log.Error("AppleNotify parseAppleTransJwsHead err: %v", err)
			break
		}
		// 解析JWS格式的推送信息
		transMapClaims, err := ut.ParseJwsToken(appleNotifyData.SignedTransactionInfo, publicKey)
		if err != nil {
			log.Error("AppleNotify parseAppleTransJwsHead data err: %v", err)
			break
		}
		orderId := ut.String(transMapClaims["originalTransactionId"])
		purchaseType := ut.String(transMapClaims["type"])
		// 处理退款
		switch purchaseType {
		case apple.APPLE_TRANSACTION_TYPE_CONSUMABLE:
			// 消耗性应用内购买
			this.handleOrderRefund(orderId, enum.PAY_PLATFORM_APPLE)
		case apple.APPLE_TRANSACTION_TYPE_AUTO_RENEWABLE_SUBSCRIPTION:
		case apple.APPLE_TRANSACTION_TYPE_NON_RENEWING_SUBSCRIPTION:
			// 自动续订和不可续订的订阅
			// this.handleSubRefund(orderId, slg.PAY_PLATFORM_APPLE)
		}

	}
	c.String(http.StatusOK, "")
}

// Google推送
// func (this *Task) httpGoogleNotify(message string) (ret map[string]interface{}, err error) {
// 	log.Info("httpGoogleNotify message: %v", message)
// 	notify := &apple.GoogleNotifyMessage{}
// 	err = json.Unmarshal([]byte(message), notify)
// 	if err != nil {
// 		log.Error("httpGoogleNotify Unmarshal message : %v, err: %v", message, err)
// 		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
// 	}
// 	// 解析base64编码数据
// 	dataBytes, err := base64.StdEncoding.DecodeString(notify.Data)
// 	if err != nil {
// 		log.Error("httpGoogleNotify base64Decode data: %v, err: %v", notify.Data, err)
// 		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
// 	}
// 	notifyData := &apple.DeveloperNotification{}
// 	err = json.Unmarshal(dataBytes, notifyData)
// 	if err != nil {
// 		log.Error("httpGoogleNotify Unmarshal data %v, err: %v", notify.Data, err)
// 		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
// 	}
// 	// 处理订阅通知
// 	if notifyData.Subscription != nil {
// 		this.SubCheckByToken(notifyData.Subscription.PurchaseToken, slg.PAY_PLATFORM_GOOGLE, notifyData.Subscription.NotificationType)
// 	}
// 	return slg.HttpResponseSuccessWithDataNoDesc(""), nil
// }
