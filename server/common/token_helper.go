package comm

import (
	"github.com/golang-jwt/jwt/v4"
	"github.com/huyangv/vmqant/log"
	"train/base/data"
)

// 这个是简单key
const secret = "twomiles@2023&train-better"

// GetTokenByHS256 使用hs256算法生成token
func GetTokenByHS256(v *jwt.MapClaims) string {
	return getToken(v, jwt.SigningMethodHS256, secret)
}

// GetTokenByRsa 使用rsa签名token
func GetTokenByRsa(v *jwt.MapClaims) string {
	key := data.GlobalGetKeyCenter().ParsePrivateKey()
	return getToken(v, jwt.SigningMethodRS256, key)
}

// getToken 生成token
func getToken(v *jwt.MapClaims, method jwt.SigningMethod, secretStr interface{}) string {
	token := jwt.NewWithClaims(method, v)
	signedString, err := token.SignedString(secretStr)
	if err != nil {
		log.Error("GetToken 签名错误:%v", err)
	}
	return signedString
}

// TryDecodeToken 解析token,失败返回nil,成功则返回原始数据,ignoreExpire=true时忽略token过期（如果设置exp则可以使用token.VerifyExpiresAt(time.Now().Unix(), true)判断是否过期）
func TryDecodeToken(token string, ignoreExpire bool) jwt.MapClaims {
	if token == "" {
		return nil
	}
	parse, _ := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); ok {
			return data.GlobalGetKeyCenter().ParsePublicKey(), nil
		}
		return secret, nil
	})
	if parse == nil {
		return nil
	}
	if !ignoreExpire && !parse.Valid {
		return nil
	}
	claims, ok := parse.Claims.(jwt.MapClaims)
	if ok {
		return claims
	}
	return nil
}
