package comm

import (
	"fmt"
	"time"
	ut "train/utils"

	consul "github.com/hashicorp/consul/api"
	"github.com/huyangv/vmqant/module"
	"github.com/spf13/cast"
)

const (
	userLockKey = "USER_LOCK_KEY"
)

// GetConsulClient 获取consul客户端
var GetConsulClient func() *consul.Client

// GetApp 获取mqant app
var GetApp func() module.App

func CreateConsulLock() *ConsulLock {
	return &ConsulLock{
		lockWaitTime: time.Second * 3,
		ttl:          5,
	}
}

func GetConsulValue(key string) (string, error) {
	consulClient := GetConsulClient()
	kvPair, _, err := consulClient.KV().Get(key, nil)
	value := ""
	if kvPair != nil {
		value = cast.ToString(kvPair.Value)
	}
	return value, err
}

func SetConsulValue(key string, value string) error {
	consulClient := GetConsulClient()
	_, err := consulClient.KV().Put(&consul.KVPair{Key: key, Value: []byte(value)}, nil)
	return err
}

func DelConsulValue(key string) error {
	consulClient := GetConsulClient()
	_, err := consulClient.KV().Delete(key, nil)
	return err
}

type ConsulLock struct {
	key          string
	session      string
	ttl          int
	lockWaitTime time.Duration // 等待获取锁的最长时间。
	lock         *consul.Lock
}

func (c *ConsulLock) Key(key string) *ConsulLock {
	c.key = key
	return c
}

func (c *ConsulLock) UidKey(uid string) *ConsulLock {
	c.key = fmt.Sprintf("%s_%s", userLockKey, uid)
	return c
}

//func (c *ConsulLock) Ttl(ttl int) *ConsulLock {
//	c.ttl = ttl
//	return c
//}

// Deprecated: 经过测试，这个session是consul创建的session才有效，无需绑定，用默认的15s过期
/*
 * @description 指定一个Consul会话ID，用于关联锁到特定的会话，确保会话失效时锁会自动释放(锁默认最久持有5s SessionTTL)。
 * @param session
 * @return *ConsulLock
 */
func (c *ConsulLock) BindSession(sessionId string) *ConsulLock {
	c.session = sessionId
	return c
}

// SetLockWaitTime
/*
 * @description 等待获取锁的最长时间,超时默认3秒
 * @param wait
 * @return *ConsulLock
 */
func (c *ConsulLock) SetLockWaitTime(wait time.Duration) *ConsulLock {
	c.lockWaitTime = wait
	return c
}

func (c *ConsulLock) Lock() *ConsulLock {
	client := GetConsulClient()
	opts := &consul.LockOptions{
		Key:         c.key,
		SessionName: c.key,
	}
	if !ut.IsEmpty(c.session) {
		opts.Session = c.session
	}
	if c.lockWaitTime > 0 {
		opts.LockWaitTime = c.lockWaitTime
	}
	//if c.ttl > 0 {
	//	opts.SessionTTL = fmt.Sprintf("%ds", c.ttl)
	//}

	lock, err := client.LockOpts(opts)
	if err != nil {
		panic(err)
	}
	c.lock = lock
	lockChan := make(chan struct{})
	defer close(lockChan)
	_, err = c.lock.Lock(lockChan)
	if err != nil {
		panic(err)
	}
	return c
}

func (c *ConsulLock) Unlock() *ConsulLock {
	if c.lock != nil {
		c.lock.Unlock()
	}
	return c
}
