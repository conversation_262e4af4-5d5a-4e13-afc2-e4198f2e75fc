package ecode

import "strconv"

type ECode int

func (this ECode) String() string {
	return "ecode." + strconv.Itoa(int(this))
}

const (
	UNKNOWN           ECode = 500000 + iota
	NOT_ACCOUNT_TOKEN       //没有token 500001
	TOKEN_INVALID           //token无效
	NOT_BIND_UID            //还未绑定uid
	DB_ERROR                //数据库错误
	PLAYER_NOT_FOUND        //找不到玩家
	NEED_UPDATE             //客户端需要更新
	SERVER_CLOSED           //服务器维护
)
