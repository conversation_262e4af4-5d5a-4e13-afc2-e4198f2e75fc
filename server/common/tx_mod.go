package comm

import (
	"encoding/base64"
	"strconv"
	"strings"
	"sync"

	"github.com/huyangv/vmqant/log"

	cms "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cms/v20190321"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

type TencentMod struct {
	initEnd    bool
	secretId   string
	secretKey  string
	credential *common.Credential
	client     *cms.Client

	chineseCode map[string]string // 中文标点符号
	_l          sync.RWMutex
}

var txMod *TencentMod
var lockObj sync.RWMutex

func GlobalGetTencentMod() *TencentMod {
	if txMod == nil {
		lockObj.Lock()
		if txMod == nil {
			txMod = &TencentMod{
				secretId:  GetSetting()["TxSecretId"].(string),
				secretKey: GetSetting()["TxSecretKey"].(string),
			}
			txMod.init()
		}
		lockObj.Unlock()
	}
	return txMod
}

func (this *TencentMod) init() {
	this._l.Lock()
	defer this._l.Unlock()
	if this.initEnd {
		return
	}
	this.credential = common.NewCredential(
		this.secretId,
		this.secretKey,
	)
	// clientProfile是可选的
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "cms.tencentcloudapi.com"
	this.client, _ = cms.NewClient(this.credential, GetSetting()["TxRegion"].(string), cpf)
	// search website : https://unicode.yunser.com/unicode
	this.chineseCode = map[string]string{
		"8211":  "–",
		"8212":  "—",
		"8216":  "‘",
		"8217":  "’",
		"8220":  "“",
		"8221":  "”",
		"8230":  "…",
		"12289": "、",
		"12290": "。",
		"12296": "〈",
		"12297": "〉",
		"12298": "《",
		"12299": "》",
		"12300": "「",
		"12301": "」",
		"12302": "『",
		"12303": "』",
		"12304": "【",
		"12305": "】",
		"12308": "〔",
		"12309": "〕",
		"65281": "！",
		"65288": "（",
		"65289": "）",
		"65292": "，",
		"65294": "．",
		"65306": "：",
		"65307": "；",
		"65311": "？",
	}
	this.initEnd = true
}

// TextModeration 敏感词检测 返回true为不通过,同时返回会把content中的敏感词击中部分替换成*,如果content本身为空,则返回空字符串并且返回true
func (this *TencentMod) TextModeration(content string) (bool, string) {
	// 如果是空串，没得检查的必要
	if strings.ReplaceAll(content, " ", "") == "" {
		return true, ""
	}
	// 如果字符串用制表符或者空格隔开，腾讯的检测就会失效，所以要先处理这些字符
	temp := content
	// 处理空格
	temp = strings.ReplaceAll(temp, " ", "")
	// 处理除了正常识别字符以外的所有字符
	restoreMap := map[int]string{}
	idx := -1
	for _, t := range temp {
		idx++
		// 中文
		if len(string(t)) == 3 && this.chineseCode[strconv.Itoa(int(t))] == "" {
			continue
		}
		// a-z A-Z
		if (t >= 65 && t <= 90) || (t >= 97 && t <= 127) {
			continue
		}
		restoreMap[idx] = string(t)
		temp = strings.ReplaceAll(temp, string(t), "")
	}

	if temp == "" {
		// 通过
		return false, content
	}

	bs := base64.StdEncoding.EncodeToString([]byte(temp))
	request := cms.NewTextModerationRequest()
	request.Content = &bs
	response, err := this.client.TextModeration(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		//fmt.Println(err)
		log.Error(err.Error(), content)
		return true, content
	}
	//fmt.Println(response.ToJsonString(), err)
	if *response.Response.Data.EvilFlag == 0 {
		return false, content
	}
	// 结果是可疑  不论关键词是否击中，都要处理成*
	if response.Response != nil && response.Response.Data != nil && response.Response.Data.Keywords != nil {
		max := idx
		idx = 0
		newStr := ""
		for _, cur := range response.Response.Data.DetailResult {
			words := cur.Keywords
			// api未击中字符，但是结果是敏感词，则全句替换
			if len(words) == 0 {
				tr := ""
				for range content {
					tr += "*"
				}
				content = tr
				return true, content
			}
			// api未击中字符，可能是 草/泥/马 击中了 草泥马
			for _, keyword := range words {
				cr := ""
				for range *keyword {
					cr += "*"
				}
				temp = strings.ReplaceAll(temp, *keyword, cr)
			}
		}
		for _, v := range temp {
			for restoreMap[idx] != "" {
				newStr += restoreMap[idx]
				idx++
			}
			newStr += string(v)
			idx++
		}
		for ; idx <= max; idx++ {
			if restoreMap[idx] != "" {
				newStr += restoreMap[idx]
			}
		}
		return true, newStr
	}
	return true, content
}
