package comm

import (
	"fmt"
	"github.com/huyangv/vmqant/gate"
	"github.com/spf13/cast"
	"train/base/enum"
)

// GetPlayerSid  获取session中设置的游戏区服号，返回-1是失败的.
func GetPlayerSid(session gate.Session) int {
	result := session.Get(enum.PlayerSid)
	if result != "" {
		return cast.ToInt(result)
	}
	return -1
}

// CallGameServer 防止手动拼写错误 Invoke时用这个来拼
func CallGameServer(sid interface{}, cluster ...interface{}) string {
	append := fmt.Sprintf("game@game&%s", cast.ToString(sid))
	if cluster != nil && len(cluster) > 0 {
		target := cast.ToInt(cluster[0])
		append = fmt.Sprintf("game@game%s%d&%s", enum.CLUSTER, target, cast.ToString(sid))
	}
	return append
}

// CallLoginServer 防止手动拼写错误 Invoke时用这个来拼
func CallLoginServer(sid interface{}, cluster ...interface{}) string {
	append := fmt.Sprintf("login@login&%s", cast.ToString(sid))
	if cluster != nil && len(cluster) > 0 {
		target := cast.ToInt(cluster[0])
		append = fmt.Sprintf("login@login%s%d&%s", enum.CLUSTER, target, cast.ToString(sid))
	}
	return append
}
