// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pvp.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_UpdateFormationMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  PvpType `protobuf:"varint,1,opt,name=type,proto3,enum=proto.PvpType" json:"type,omitempty"` //竞技场类型
	IdAry []int32 `protobuf:"varint,2,rep,packed,name=idAry,proto3" json:"idAry,omitempty"`           //乘客id
}

func (x *C2S_UpdateFormationMessage) Reset() {
	*x = C2S_UpdateFormationMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UpdateFormationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UpdateFormationMessage) ProtoMessage() {}

func (x *C2S_UpdateFormationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UpdateFormationMessage.ProtoReflect.Descriptor instead.
func (*C2S_UpdateFormationMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_UpdateFormationMessage) GetType() PvpType {
	if x != nil {
		return x.Type
	}
	return PvpType_NORMAL
}

func (x *C2S_UpdateFormationMessage) GetIdAry() []int32 {
	if x != nil {
		return x.IdAry
	}
	return nil
}

type S2C_UpdateFormationMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
}

func (x *S2C_UpdateFormationMessage) Reset() {
	*x = S2C_UpdateFormationMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UpdateFormationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UpdateFormationMessage) ProtoMessage() {}

func (x *S2C_UpdateFormationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UpdateFormationMessage.ProtoReflect.Descriptor instead.
func (*S2C_UpdateFormationMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_UpdateFormationMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_GetRankListMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type PvpType `protobuf:"varint,1,opt,name=type,proto3,enum=proto.PvpType" json:"type,omitempty"` //竞技场类型
}

func (x *C2S_GetRankListMessage) Reset() {
	*x = C2S_GetRankListMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetRankListMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetRankListMessage) ProtoMessage() {}

func (x *C2S_GetRankListMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetRankListMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetRankListMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_GetRankListMessage) GetType() PvpType {
	if x != nil {
		return x.Type
	}
	return PvpType_NORMAL
}

type S2C_GetRankListMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`   //code=0代表无错误
	List  []*PvpSimplePlayerData `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`    //数据
	Rank  int32                  `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`   //自己的排名
	Score int32                  `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"` //自己的分数
}

func (x *S2C_GetRankListMessage) Reset() {
	*x = S2C_GetRankListMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetRankListMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetRankListMessage) ProtoMessage() {}

func (x *S2C_GetRankListMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetRankListMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetRankListMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_GetRankListMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetRankListMessage) GetList() []*PvpSimplePlayerData {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *S2C_GetRankListMessage) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *S2C_GetRankListMessage) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type C2S_GetRivalMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    PvpType `protobuf:"varint,1,opt,name=type,proto3,enum=proto.PvpType" json:"type,omitempty"` //竞技场类型
	Refresh bool    `protobuf:"varint,2,opt,name=refresh,proto3" json:"refresh,omitempty"`              //刷新
}

func (x *C2S_GetRivalMessage) Reset() {
	*x = C2S_GetRivalMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetRivalMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetRivalMessage) ProtoMessage() {}

func (x *C2S_GetRivalMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetRivalMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetRivalMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_GetRivalMessage) GetType() PvpType {
	if x != nil {
		return x.Type
	}
	return PvpType_NORMAL
}

func (x *C2S_GetRivalMessage) GetRefresh() bool {
	if x != nil {
		return x.Refresh
	}
	return false
}

type S2C_GetRivalMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
	List []*PvpSimplePlayerData `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`  //数据
}

func (x *S2C_GetRivalMessage) Reset() {
	*x = S2C_GetRivalMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetRivalMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetRivalMessage) ProtoMessage() {}

func (x *S2C_GetRivalMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetRivalMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetRivalMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_GetRivalMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetRivalMessage) GetList() []*PvpSimplePlayerData {
	if x != nil {
		return x.List
	}
	return nil
}

type C2S_PvpFightMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       PvpType `protobuf:"varint,1,opt,name=type,proto3,enum=proto.PvpType" json:"type,omitempty"` //竞技场类型
	RivalIndex int32   `protobuf:"varint,2,opt,name=rivalIndex,proto3" json:"rivalIndex,omitempty"`        //对手序号
	Result     int32   `protobuf:"varint,3,opt,name=result,proto3" json:"result,omitempty"`                //0平局1胜2输
}

func (x *C2S_PvpFightMessage) Reset() {
	*x = C2S_PvpFightMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PvpFightMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PvpFightMessage) ProtoMessage() {}

func (x *C2S_PvpFightMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PvpFightMessage.ProtoReflect.Descriptor instead.
func (*C2S_PvpFightMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_PvpFightMessage) GetType() PvpType {
	if x != nil {
		return x.Type
	}
	return PvpType_NORMAL
}

func (x *C2S_PvpFightMessage) GetRivalIndex() int32 {
	if x != nil {
		return x.RivalIndex
	}
	return 0
}

func (x *C2S_PvpFightMessage) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type S2C_PvpFightMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`   //code=0代表无错误
	Score int32 `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"` //最新分数
	Rank  int32 `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`   //最新排名
}

func (x *S2C_PvpFightMessage) Reset() {
	*x = S2C_PvpFightMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PvpFightMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PvpFightMessage) ProtoMessage() {}

func (x *S2C_PvpFightMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PvpFightMessage.ProtoReflect.Descriptor instead.
func (*S2C_PvpFightMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_PvpFightMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_PvpFightMessage) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *S2C_PvpFightMessage) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

type C2S_PvpBattleRecordListMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type PvpType `protobuf:"varint,1,opt,name=type,proto3,enum=proto.PvpType" json:"type,omitempty"` //竞技场类型
}

func (x *C2S_PvpBattleRecordListMessage) Reset() {
	*x = C2S_PvpBattleRecordListMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PvpBattleRecordListMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PvpBattleRecordListMessage) ProtoMessage() {}

func (x *C2S_PvpBattleRecordListMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PvpBattleRecordListMessage.ProtoReflect.Descriptor instead.
func (*C2S_PvpBattleRecordListMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{8}
}

func (x *C2S_PvpBattleRecordListMessage) GetType() PvpType {
	if x != nil {
		return x.Type
	}
	return PvpType_NORMAL
}

type S2C_PvpBattleRecordListMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
	List []*PvpBattleRecordData `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`  //战绩列表
}

func (x *S2C_PvpBattleRecordListMessage) Reset() {
	*x = S2C_PvpBattleRecordListMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PvpBattleRecordListMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PvpBattleRecordListMessage) ProtoMessage() {}

func (x *S2C_PvpBattleRecordListMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PvpBattleRecordListMessage.ProtoReflect.Descriptor instead.
func (*S2C_PvpBattleRecordListMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_PvpBattleRecordListMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_PvpBattleRecordListMessage) GetList() []*PvpBattleRecordData {
	if x != nil {
		return x.List
	}
	return nil
}

type C2S_PvpBattleReplayMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  PvpType `protobuf:"varint,1,opt,name=type,proto3,enum=proto.PvpType" json:"type,omitempty"` //竞技场类型
	DocId string  `protobuf:"bytes,2,opt,name=docId,proto3" json:"docId,omitempty"`                   //文档id
}

func (x *C2S_PvpBattleReplayMessage) Reset() {
	*x = C2S_PvpBattleReplayMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PvpBattleReplayMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PvpBattleReplayMessage) ProtoMessage() {}

func (x *C2S_PvpBattleReplayMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PvpBattleReplayMessage.ProtoReflect.Descriptor instead.
func (*C2S_PvpBattleReplayMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{10}
}

func (x *C2S_PvpBattleReplayMessage) GetType() PvpType {
	if x != nil {
		return x.Type
	}
	return PvpType_NORMAL
}

func (x *C2S_PvpBattleReplayMessage) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

type S2C_PvpBattleReplayMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`        //code=0代表无错误
	Attacker []*BattleRole `protobuf:"bytes,2,rep,name=attacker,proto3" json:"attacker,omitempty"` //攻击方
	Defender []*BattleRole `protobuf:"bytes,3,rep,name=defender,proto3" json:"defender,omitempty"` //防守方
}

func (x *S2C_PvpBattleReplayMessage) Reset() {
	*x = S2C_PvpBattleReplayMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PvpBattleReplayMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PvpBattleReplayMessage) ProtoMessage() {}

func (x *S2C_PvpBattleReplayMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PvpBattleReplayMessage.ProtoReflect.Descriptor instead.
func (*S2C_PvpBattleReplayMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_PvpBattleReplayMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_PvpBattleReplayMessage) GetAttacker() []*BattleRole {
	if x != nil {
		return x.Attacker
	}
	return nil
}

func (x *S2C_PvpBattleReplayMessage) GetDefender() []*BattleRole {
	if x != nil {
		return x.Defender
	}
	return nil
}

type C2S_PvpModuleDataMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_PvpModuleDataMessage) Reset() {
	*x = C2S_PvpModuleDataMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PvpModuleDataMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PvpModuleDataMessage) ProtoMessage() {}

func (x *C2S_PvpModuleDataMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PvpModuleDataMessage.ProtoReflect.Descriptor instead.
func (*C2S_PvpModuleDataMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{12}
}

type S2C_PvpModuleDataMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Normal *PvpNormalData `protobuf:"bytes,1,opt,name=normal,proto3" json:"normal,omitempty"` //
}

func (x *S2C_PvpModuleDataMessage) Reset() {
	*x = S2C_PvpModuleDataMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pvp_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PvpModuleDataMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PvpModuleDataMessage) ProtoMessage() {}

func (x *S2C_PvpModuleDataMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pvp_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PvpModuleDataMessage.ProtoReflect.Descriptor instead.
func (*S2C_PvpModuleDataMessage) Descriptor() ([]byte, []int) {
	return file_pvp_proto_rawDescGZIP(), []int{13}
}

func (x *S2C_PvpModuleDataMessage) GetNormal() *PvpNormalData {
	if x != nil {
		return x.Normal
	}
	return nil
}

var File_pvp_proto protoreflect.FileDescriptor

var file_pvp_proto_rawDesc = []byte{
	0x0a, 0x09, 0x70, 0x76, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x56, 0x0a, 0x1a,
	0x43, 0x32, 0x53, 0x5f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x76, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x64, 0x41, 0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x69,
	0x64, 0x41, 0x72, 0x79, 0x22, 0x30, 0x0a, 0x1a, 0x53, 0x32, 0x43, 0x5f, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x3c, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65,
	0x74, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74,
	0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x53, 0x0a,
	0x13, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65, 0x74, 0x52, 0x69, 0x76, 0x61, 0x6c, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x22, 0x59, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x52, 0x69, 0x76,
	0x61, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x71, 0x0a,
	0x13, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x76, 0x70, 0x46, 0x69, 0x67, 0x68, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x53, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x76, 0x70, 0x46, 0x69, 0x67, 0x68, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x72, 0x61, 0x6e, 0x6b, 0x22, 0x44, 0x0a, 0x1e, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x76, 0x70,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x64, 0x0a, 0x1e, 0x53,
	0x32, 0x43, 0x5f, 0x50, 0x76, 0x70, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x56, 0x0a, 0x1a, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x76, 0x70, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x22, 0x8e, 0x01, 0x0a, 0x1a, 0x53, 0x32,
	0x43, 0x5f, 0x50, 0x76, 0x70, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x61,
	0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x08,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x08, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x08, 0x64,
	0x65, 0x66, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x08, 0x64, 0x65, 0x66, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22, 0x1a, 0x0a, 0x18, 0x43, 0x32,
	0x53, 0x5f, 0x50, 0x76, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x48, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x76,
	0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2c, 0x0a, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x76, 0x70, 0x4e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pvp_proto_rawDescOnce sync.Once
	file_pvp_proto_rawDescData = file_pvp_proto_rawDesc
)

func file_pvp_proto_rawDescGZIP() []byte {
	file_pvp_proto_rawDescOnce.Do(func() {
		file_pvp_proto_rawDescData = protoimpl.X.CompressGZIP(file_pvp_proto_rawDescData)
	})
	return file_pvp_proto_rawDescData
}

var file_pvp_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_pvp_proto_goTypes = []interface{}{
	(*C2S_UpdateFormationMessage)(nil),     // 0: proto.C2S_UpdateFormationMessage
	(*S2C_UpdateFormationMessage)(nil),     // 1: proto.S2C_UpdateFormationMessage
	(*C2S_GetRankListMessage)(nil),         // 2: proto.C2S_GetRankListMessage
	(*S2C_GetRankListMessage)(nil),         // 3: proto.S2C_GetRankListMessage
	(*C2S_GetRivalMessage)(nil),            // 4: proto.C2S_GetRivalMessage
	(*S2C_GetRivalMessage)(nil),            // 5: proto.S2C_GetRivalMessage
	(*C2S_PvpFightMessage)(nil),            // 6: proto.C2S_PvpFightMessage
	(*S2C_PvpFightMessage)(nil),            // 7: proto.S2C_PvpFightMessage
	(*C2S_PvpBattleRecordListMessage)(nil), // 8: proto.C2S_PvpBattleRecordListMessage
	(*S2C_PvpBattleRecordListMessage)(nil), // 9: proto.S2C_PvpBattleRecordListMessage
	(*C2S_PvpBattleReplayMessage)(nil),     // 10: proto.C2S_PvpBattleReplayMessage
	(*S2C_PvpBattleReplayMessage)(nil),     // 11: proto.S2C_PvpBattleReplayMessage
	(*C2S_PvpModuleDataMessage)(nil),       // 12: proto.C2S_PvpModuleDataMessage
	(*S2C_PvpModuleDataMessage)(nil),       // 13: proto.S2C_PvpModuleDataMessage
	(PvpType)(0),                           // 14: proto.PvpType
	(*PvpSimplePlayerData)(nil),            // 15: proto.PvpSimplePlayerData
	(*PvpBattleRecordData)(nil),            // 16: proto.PvpBattleRecordData
	(*BattleRole)(nil),                     // 17: proto.BattleRole
	(*PvpNormalData)(nil),                  // 18: proto.PvpNormalData
}
var file_pvp_proto_depIdxs = []int32{
	14, // 0: proto.C2S_UpdateFormationMessage.type:type_name -> proto.PvpType
	14, // 1: proto.C2S_GetRankListMessage.type:type_name -> proto.PvpType
	15, // 2: proto.S2C_GetRankListMessage.list:type_name -> proto.PvpSimplePlayerData
	14, // 3: proto.C2S_GetRivalMessage.type:type_name -> proto.PvpType
	15, // 4: proto.S2C_GetRivalMessage.list:type_name -> proto.PvpSimplePlayerData
	14, // 5: proto.C2S_PvpFightMessage.type:type_name -> proto.PvpType
	14, // 6: proto.C2S_PvpBattleRecordListMessage.type:type_name -> proto.PvpType
	16, // 7: proto.S2C_PvpBattleRecordListMessage.list:type_name -> proto.PvpBattleRecordData
	14, // 8: proto.C2S_PvpBattleReplayMessage.type:type_name -> proto.PvpType
	17, // 9: proto.S2C_PvpBattleReplayMessage.attacker:type_name -> proto.BattleRole
	17, // 10: proto.S2C_PvpBattleReplayMessage.defender:type_name -> proto.BattleRole
	18, // 11: proto.S2C_PvpModuleDataMessage.normal:type_name -> proto.PvpNormalData
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_pvp_proto_init() }
func file_pvp_proto_init() {
	if File_pvp_proto != nil {
		return
	}
	file_enum_proto_init()
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pvp_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UpdateFormationMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UpdateFormationMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetRankListMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetRankListMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetRivalMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetRivalMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PvpFightMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PvpFightMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PvpBattleRecordListMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PvpBattleRecordListMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PvpBattleReplayMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PvpBattleReplayMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PvpModuleDataMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pvp_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PvpModuleDataMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pvp_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pvp_proto_goTypes,
		DependencyIndexes: file_pvp_proto_depIdxs,
		MessageInfos:      file_pvp_proto_msgTypes,
	}.Build()
	File_pvp_proto = out.File
	file_pvp_proto_rawDesc = nil
	file_pvp_proto_goTypes = nil
	file_pvp_proto_depIdxs = nil
}
