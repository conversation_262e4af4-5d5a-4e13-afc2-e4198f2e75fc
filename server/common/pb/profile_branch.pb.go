// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: profile_branch.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_ProfileBranchPassNodeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeId int32 `protobuf:"varint,1,opt,name=nodeId,proto3" json:"nodeId,omitempty"` //节点id
}

func (x *C2S_ProfileBranchPassNodeMessage) Reset() {
	*x = C2S_ProfileBranchPassNodeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_profile_branch_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ProfileBranchPassNodeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ProfileBranchPassNodeMessage) ProtoMessage() {}

func (x *C2S_ProfileBranchPassNodeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_profile_branch_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ProfileBranchPassNodeMessage.ProtoReflect.Descriptor instead.
func (*C2S_ProfileBranchPassNodeMessage) Descriptor() ([]byte, []int) {
	return file_profile_branch_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_ProfileBranchPassNodeMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type S2C_ProfileBranchPassNodeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`  //0成功
	Level *ProfileBranchLevel `protobuf:"bytes,2,opt,name=level,proto3" json:"level,omitempty"` //记忆阁当前关卡
}

func (x *S2C_ProfileBranchPassNodeMessage) Reset() {
	*x = S2C_ProfileBranchPassNodeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_profile_branch_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ProfileBranchPassNodeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ProfileBranchPassNodeMessage) ProtoMessage() {}

func (x *S2C_ProfileBranchPassNodeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_profile_branch_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ProfileBranchPassNodeMessage.ProtoReflect.Descriptor instead.
func (*S2C_ProfileBranchPassNodeMessage) Descriptor() ([]byte, []int) {
	return file_profile_branch_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_ProfileBranchPassNodeMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ProfileBranchPassNodeMessage) GetLevel() *ProfileBranchLevel {
	if x != nil {
		return x.Level
	}
	return nil
}

type C2S_ProfileBranchQuestionMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_ProfileBranchQuestionMessage) Reset() {
	*x = C2S_ProfileBranchQuestionMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_profile_branch_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ProfileBranchQuestionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ProfileBranchQuestionMessage) ProtoMessage() {}

func (x *C2S_ProfileBranchQuestionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_profile_branch_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ProfileBranchQuestionMessage.ProtoReflect.Descriptor instead.
func (*C2S_ProfileBranchQuestionMessage) Descriptor() ([]byte, []int) {
	return file_profile_branch_proto_rawDescGZIP(), []int{2}
}

type S2C_ProfileBranchQuestionMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`               //0成功
	Energy      int32 `protobuf:"varint,2,opt,name=energy,proto3" json:"energy,omitempty"`           //体力
	SurplusTime int32 `protobuf:"varint,3,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
}

func (x *S2C_ProfileBranchQuestionMessage) Reset() {
	*x = S2C_ProfileBranchQuestionMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_profile_branch_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ProfileBranchQuestionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ProfileBranchQuestionMessage) ProtoMessage() {}

func (x *S2C_ProfileBranchQuestionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_profile_branch_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ProfileBranchQuestionMessage.ProtoReflect.Descriptor instead.
func (*S2C_ProfileBranchQuestionMessage) Descriptor() ([]byte, []int) {
	return file_profile_branch_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_ProfileBranchQuestionMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ProfileBranchQuestionMessage) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

func (x *S2C_ProfileBranchQuestionMessage) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

type C2S_ProfileBranchSyncEnergyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_ProfileBranchSyncEnergyMessage) Reset() {
	*x = C2S_ProfileBranchSyncEnergyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_profile_branch_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ProfileBranchSyncEnergyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ProfileBranchSyncEnergyMessage) ProtoMessage() {}

func (x *C2S_ProfileBranchSyncEnergyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_profile_branch_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ProfileBranchSyncEnergyMessage.ProtoReflect.Descriptor instead.
func (*C2S_ProfileBranchSyncEnergyMessage) Descriptor() ([]byte, []int) {
	return file_profile_branch_proto_rawDescGZIP(), []int{4}
}

type S2C_ProfileBranchSyncEnergyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`               //0成功
	Energy      int32 `protobuf:"varint,2,opt,name=energy,proto3" json:"energy,omitempty"`           //体力
	SurplusTime int32 `protobuf:"varint,3,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
}

func (x *S2C_ProfileBranchSyncEnergyMessage) Reset() {
	*x = S2C_ProfileBranchSyncEnergyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_profile_branch_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ProfileBranchSyncEnergyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ProfileBranchSyncEnergyMessage) ProtoMessage() {}

func (x *S2C_ProfileBranchSyncEnergyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_profile_branch_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ProfileBranchSyncEnergyMessage.ProtoReflect.Descriptor instead.
func (*S2C_ProfileBranchSyncEnergyMessage) Descriptor() ([]byte, []int) {
	return file_profile_branch_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_ProfileBranchSyncEnergyMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ProfileBranchSyncEnergyMessage) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

func (x *S2C_ProfileBranchSyncEnergyMessage) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

type C2S_ProfileBranchUnlockMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //记忆阁id
}

func (x *C2S_ProfileBranchUnlockMessage) Reset() {
	*x = C2S_ProfileBranchUnlockMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_profile_branch_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ProfileBranchUnlockMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ProfileBranchUnlockMessage) ProtoMessage() {}

func (x *C2S_ProfileBranchUnlockMessage) ProtoReflect() protoreflect.Message {
	mi := &file_profile_branch_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ProfileBranchUnlockMessage.ProtoReflect.Descriptor instead.
func (*C2S_ProfileBranchUnlockMessage) Descriptor() ([]byte, []int) {
	return file_profile_branch_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_ProfileBranchUnlockMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_ProfileBranchUnlockMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0成功
}

func (x *S2C_ProfileBranchUnlockMessage) Reset() {
	*x = S2C_ProfileBranchUnlockMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_profile_branch_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ProfileBranchUnlockMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ProfileBranchUnlockMessage) ProtoMessage() {}

func (x *S2C_ProfileBranchUnlockMessage) ProtoReflect() protoreflect.Message {
	mi := &file_profile_branch_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ProfileBranchUnlockMessage.ProtoReflect.Descriptor instead.
func (*S2C_ProfileBranchUnlockMessage) Descriptor() ([]byte, []int) {
	return file_profile_branch_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_ProfileBranchUnlockMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_profile_branch_proto protoreflect.FileDescriptor

var file_profile_branch_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3a, 0x0a, 0x20, 0x43,
	0x32, 0x53, 0x5f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x50, 0x61, 0x73, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x67, 0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x50, 0x61, 0x73, 0x73,
	0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x2f, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x22, 0x22, 0x0a, 0x20, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x70, 0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x6e,
	0x65, 0x72, 0x67, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c,
	0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x24, 0x0a, 0x22, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x53, 0x79, 0x6e, 0x63, 0x45,
	0x6e, 0x65, 0x72, 0x67, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x72, 0x0a, 0x22,
	0x53, 0x32, 0x43, 0x5f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x53, 0x79, 0x6e, 0x63, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x12, 0x20,
	0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x30, 0x0a, 0x1e, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x34, 0x0a, 0x1e, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_profile_branch_proto_rawDescOnce sync.Once
	file_profile_branch_proto_rawDescData = file_profile_branch_proto_rawDesc
)

func file_profile_branch_proto_rawDescGZIP() []byte {
	file_profile_branch_proto_rawDescOnce.Do(func() {
		file_profile_branch_proto_rawDescData = protoimpl.X.CompressGZIP(file_profile_branch_proto_rawDescData)
	})
	return file_profile_branch_proto_rawDescData
}

var file_profile_branch_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_profile_branch_proto_goTypes = []interface{}{
	(*C2S_ProfileBranchPassNodeMessage)(nil),   // 0: proto.C2S_ProfileBranchPassNodeMessage
	(*S2C_ProfileBranchPassNodeMessage)(nil),   // 1: proto.S2C_ProfileBranchPassNodeMessage
	(*C2S_ProfileBranchQuestionMessage)(nil),   // 2: proto.C2S_ProfileBranchQuestionMessage
	(*S2C_ProfileBranchQuestionMessage)(nil),   // 3: proto.S2C_ProfileBranchQuestionMessage
	(*C2S_ProfileBranchSyncEnergyMessage)(nil), // 4: proto.C2S_ProfileBranchSyncEnergyMessage
	(*S2C_ProfileBranchSyncEnergyMessage)(nil), // 5: proto.S2C_ProfileBranchSyncEnergyMessage
	(*C2S_ProfileBranchUnlockMessage)(nil),     // 6: proto.C2S_ProfileBranchUnlockMessage
	(*S2C_ProfileBranchUnlockMessage)(nil),     // 7: proto.S2C_ProfileBranchUnlockMessage
	(*ProfileBranchLevel)(nil),                 // 8: proto.ProfileBranchLevel
}
var file_profile_branch_proto_depIdxs = []int32{
	8, // 0: proto.S2C_ProfileBranchPassNodeMessage.level:type_name -> proto.ProfileBranchLevel
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_profile_branch_proto_init() }
func file_profile_branch_proto_init() {
	if File_profile_branch_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_profile_branch_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ProfileBranchPassNodeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_profile_branch_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ProfileBranchPassNodeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_profile_branch_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ProfileBranchQuestionMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_profile_branch_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ProfileBranchQuestionMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_profile_branch_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ProfileBranchSyncEnergyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_profile_branch_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ProfileBranchSyncEnergyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_profile_branch_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ProfileBranchUnlockMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_profile_branch_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ProfileBranchUnlockMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_profile_branch_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_profile_branch_proto_goTypes,
		DependencyIndexes: file_profile_branch_proto_depIdxs,
		MessageInfos:      file_profile_branch_proto_msgTypes,
	}.Build()
	File_profile_branch_proto = out.File
	file_profile_branch_proto_rawDesc = nil
	file_profile_branch_proto_goTypes = nil
	file_profile_branch_proto_depIdxs = nil
}
