// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: daily_task.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_FinishDailyTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`        //任务id -1是大奖
	Extras []string `protobuf:"bytes,2,rep,name=extras,proto3" json:"extras,omitempty"` //提交的物品的扩展
}

func (x *C2S_FinishDailyTaskMessage) Reset() {
	*x = C2S_FinishDailyTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_daily_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_FinishDailyTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_FinishDailyTaskMessage) ProtoMessage() {}

func (x *C2S_FinishDailyTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_daily_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_FinishDailyTaskMessage.ProtoReflect.Descriptor instead.
func (*C2S_FinishDailyTaskMessage) Descriptor() ([]byte, []int) {
	return file_daily_task_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_FinishDailyTaskMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_FinishDailyTaskMessage) GetExtras() []string {
	if x != nil {
		return x.Extras
	}
	return nil
}

type S2C_FinishDailyTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code
	Task *DailyTask `protobuf:"bytes,2,opt,name=task,proto3" json:"task,omitempty"`  //下一个任务
}

func (x *S2C_FinishDailyTaskMessage) Reset() {
	*x = S2C_FinishDailyTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_daily_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_FinishDailyTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_FinishDailyTaskMessage) ProtoMessage() {}

func (x *S2C_FinishDailyTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_daily_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_FinishDailyTaskMessage.ProtoReflect.Descriptor instead.
func (*S2C_FinishDailyTaskMessage) Descriptor() ([]byte, []int) {
	return file_daily_task_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_FinishDailyTaskMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_FinishDailyTaskMessage) GetTask() *DailyTask {
	if x != nil {
		return x.Task
	}
	return nil
}

type C2S_SyncDailyTaskInfoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SyncDailyTaskInfoMessage) Reset() {
	*x = C2S_SyncDailyTaskInfoMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_daily_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncDailyTaskInfoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncDailyTaskInfoMessage) ProtoMessage() {}

func (x *C2S_SyncDailyTaskInfoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_daily_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncDailyTaskInfoMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncDailyTaskInfoMessage) Descriptor() ([]byte, []int) {
	return file_daily_task_proto_rawDescGZIP(), []int{2}
}

type S2C_SyncDailyTaskInfoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code
	Info *DailyTaskInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`  //0
}

func (x *S2C_SyncDailyTaskInfoMessage) Reset() {
	*x = S2C_SyncDailyTaskInfoMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_daily_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncDailyTaskInfoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncDailyTaskInfoMessage) ProtoMessage() {}

func (x *S2C_SyncDailyTaskInfoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_daily_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncDailyTaskInfoMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncDailyTaskInfoMessage) Descriptor() ([]byte, []int) {
	return file_daily_task_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_SyncDailyTaskInfoMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncDailyTaskInfoMessage) GetInfo() *DailyTaskInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type C2S_DialogTaskDoneMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index       int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`             //任务序号
	DialogIndex int32 `protobuf:"varint,2,opt,name=dialogIndex,proto3" json:"dialogIndex,omitempty"` //对话序号
}

func (x *C2S_DialogTaskDoneMessage) Reset() {
	*x = C2S_DialogTaskDoneMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_daily_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_DialogTaskDoneMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_DialogTaskDoneMessage) ProtoMessage() {}

func (x *C2S_DialogTaskDoneMessage) ProtoReflect() protoreflect.Message {
	mi := &file_daily_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_DialogTaskDoneMessage.ProtoReflect.Descriptor instead.
func (*C2S_DialogTaskDoneMessage) Descriptor() ([]byte, []int) {
	return file_daily_task_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_DialogTaskDoneMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *C2S_DialogTaskDoneMessage) GetDialogIndex() int32 {
	if x != nil {
		return x.DialogIndex
	}
	return 0
}

type S2C_DialogTaskDoneMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code
}

func (x *S2C_DialogTaskDoneMessage) Reset() {
	*x = S2C_DialogTaskDoneMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_daily_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_DialogTaskDoneMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_DialogTaskDoneMessage) ProtoMessage() {}

func (x *S2C_DialogTaskDoneMessage) ProtoReflect() protoreflect.Message {
	mi := &file_daily_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_DialogTaskDoneMessage.ProtoReflect.Descriptor instead.
func (*S2C_DialogTaskDoneMessage) Descriptor() ([]byte, []int) {
	return file_daily_task_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_DialogTaskDoneMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_BattleTaskDoneTestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` //任务序号
}

func (x *C2S_BattleTaskDoneTestMessage) Reset() {
	*x = C2S_BattleTaskDoneTestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_daily_task_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BattleTaskDoneTestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BattleTaskDoneTestMessage) ProtoMessage() {}

func (x *C2S_BattleTaskDoneTestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_daily_task_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BattleTaskDoneTestMessage.ProtoReflect.Descriptor instead.
func (*C2S_BattleTaskDoneTestMessage) Descriptor() ([]byte, []int) {
	return file_daily_task_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_BattleTaskDoneTestMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type S2C_BattleTaskDoneTestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code
}

func (x *S2C_BattleTaskDoneTestMessage) Reset() {
	*x = S2C_BattleTaskDoneTestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_daily_task_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BattleTaskDoneTestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BattleTaskDoneTestMessage) ProtoMessage() {}

func (x *S2C_BattleTaskDoneTestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_daily_task_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BattleTaskDoneTestMessage.ProtoReflect.Descriptor instead.
func (*S2C_BattleTaskDoneTestMessage) Descriptor() ([]byte, []int) {
	return file_daily_task_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_BattleTaskDoneTestMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_daily_task_proto protoreflect.FileDescriptor

var file_daily_task_proto_rawDesc = []byte{
	0x0a, 0x10, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x44, 0x0a, 0x1a, 0x43, 0x32, 0x53, 0x5f, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x22, 0x56, 0x0a,
	0x1a, 0x53, 0x32, 0x43, 0x5f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x24, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x04, 0x74, 0x61, 0x73, 0x6b, 0x22, 0x1e, 0x0a, 0x1c, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x79, 0x6e,
	0x63, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x5c, 0x0a, 0x1c, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e,
	0x63, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x22, 0x53, 0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x44, 0x69, 0x61, 0x6c, 0x6f,
	0x67, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x6f, 0x6e, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x69, 0x61,
	0x6c, 0x6f, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x2f, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f,
	0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x6f, 0x6e, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x35, 0x0a, 0x1d, 0x43, 0x32, 0x53,
	0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x6f, 0x6e, 0x65, 0x54,
	0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x22, 0x33, 0x0a, 0x1d, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x44, 0x6f, 0x6e, 0x65, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_daily_task_proto_rawDescOnce sync.Once
	file_daily_task_proto_rawDescData = file_daily_task_proto_rawDesc
)

func file_daily_task_proto_rawDescGZIP() []byte {
	file_daily_task_proto_rawDescOnce.Do(func() {
		file_daily_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_daily_task_proto_rawDescData)
	})
	return file_daily_task_proto_rawDescData
}

var file_daily_task_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_daily_task_proto_goTypes = []interface{}{
	(*C2S_FinishDailyTaskMessage)(nil),    // 0: proto.C2S_FinishDailyTaskMessage
	(*S2C_FinishDailyTaskMessage)(nil),    // 1: proto.S2C_FinishDailyTaskMessage
	(*C2S_SyncDailyTaskInfoMessage)(nil),  // 2: proto.C2S_SyncDailyTaskInfoMessage
	(*S2C_SyncDailyTaskInfoMessage)(nil),  // 3: proto.S2C_SyncDailyTaskInfoMessage
	(*C2S_DialogTaskDoneMessage)(nil),     // 4: proto.C2S_DialogTaskDoneMessage
	(*S2C_DialogTaskDoneMessage)(nil),     // 5: proto.S2C_DialogTaskDoneMessage
	(*C2S_BattleTaskDoneTestMessage)(nil), // 6: proto.C2S_BattleTaskDoneTestMessage
	(*S2C_BattleTaskDoneTestMessage)(nil), // 7: proto.S2C_BattleTaskDoneTestMessage
	(*DailyTask)(nil),                     // 8: proto.DailyTask
	(*DailyTaskInfo)(nil),                 // 9: proto.DailyTaskInfo
}
var file_daily_task_proto_depIdxs = []int32{
	8, // 0: proto.S2C_FinishDailyTaskMessage.task:type_name -> proto.DailyTask
	9, // 1: proto.S2C_SyncDailyTaskInfoMessage.info:type_name -> proto.DailyTaskInfo
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_daily_task_proto_init() }
func file_daily_task_proto_init() {
	if File_daily_task_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_daily_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_FinishDailyTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_daily_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_FinishDailyTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_daily_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncDailyTaskInfoMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_daily_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncDailyTaskInfoMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_daily_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_DialogTaskDoneMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_daily_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_DialogTaskDoneMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_daily_task_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BattleTaskDoneTestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_daily_task_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BattleTaskDoneTestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_daily_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_daily_task_proto_goTypes,
		DependencyIndexes: file_daily_task_proto_depIdxs,
		MessageInfos:      file_daily_task_proto_msgTypes,
	}.Build()
	File_daily_task_proto = out.File
	file_daily_task_proto_rawDesc = nil
	file_daily_task_proto_goTypes = nil
	file_daily_task_proto_depIdxs = nil
}
