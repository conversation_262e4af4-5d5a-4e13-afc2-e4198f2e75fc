// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: black_hole.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_ReadyStartBlackHoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level int32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"` //难度
}

func (x *C2S_ReadyStartBlackHoleMessage) Reset() {
	*x = C2S_ReadyStartBlackHoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ReadyStartBlackHoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ReadyStartBlackHoleMessage) ProtoMessage() {}

func (x *C2S_ReadyStartBlackHoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ReadyStartBlackHoleMessage.ProtoReflect.Descriptor instead.
func (*C2S_ReadyStartBlackHoleMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_ReadyStartBlackHoleMessage) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type S2C_ReadyStartBlackHoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code
	Boss []*BattleRole `protobuf:"bytes,2,rep,name=boss,proto3" json:"boss,omitempty"`  //当前难度下的boss阵容
}

func (x *S2C_ReadyStartBlackHoleMessage) Reset() {
	*x = S2C_ReadyStartBlackHoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ReadyStartBlackHoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ReadyStartBlackHoleMessage) ProtoMessage() {}

func (x *S2C_ReadyStartBlackHoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ReadyStartBlackHoleMessage.ProtoReflect.Descriptor instead.
func (*S2C_ReadyStartBlackHoleMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_ReadyStartBlackHoleMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ReadyStartBlackHoleMessage) GetBoss() []*BattleRole {
	if x != nil {
		return x.Boss
	}
	return nil
}

type C2S_StartBlackHoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level int32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`        //难度
	Roles []int32 `protobuf:"varint,2,rep,packed,name=roles,proto3" json:"roles,omitempty"` //选择的乘客
}

func (x *C2S_StartBlackHoleMessage) Reset() {
	*x = C2S_StartBlackHoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_StartBlackHoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_StartBlackHoleMessage) ProtoMessage() {}

func (x *C2S_StartBlackHoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_StartBlackHoleMessage.ProtoReflect.Descriptor instead.
func (*C2S_StartBlackHoleMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_StartBlackHoleMessage) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *C2S_StartBlackHoleMessage) GetRoles() []int32 {
	if x != nil {
		return x.Roles
	}
	return nil
}

type S2C_StartBlackHoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`          //code
	BlackHole *BlackHole `protobuf:"bytes,2,opt,name=blackHole,proto3" json:"blackHole,omitempty"` //黑洞数据
}

func (x *S2C_StartBlackHoleMessage) Reset() {
	*x = S2C_StartBlackHoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_StartBlackHoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_StartBlackHoleMessage) ProtoMessage() {}

func (x *S2C_StartBlackHoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_StartBlackHoleMessage.ProtoReflect.Descriptor instead.
func (*S2C_StartBlackHoleMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_StartBlackHoleMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_StartBlackHoleMessage) GetBlackHole() *BlackHole {
	if x != nil {
		return x.BlackHole
	}
	return nil
}

type C2S_SelectBlackHoleNodeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeId string          `protobuf:"bytes,1,opt,name=nodeId,proto3" json:"nodeId,omitempty"` //节点id
	Buff   *BlackHoleBuff  `protobuf:"bytes,2,opt,name=buff,proto3" json:"buff,omitempty"`     //选择的buff
	Equip  *BlackHoleEquip `protobuf:"bytes,3,opt,name=equip,proto3" json:"equip,omitempty"`   //选择的装备
	Aid    string          `protobuf:"bytes,4,opt,name=aid,proto3" json:"aid,omitempty"`       //选择援助的角色uid
	Battle *BattleResult   `protobuf:"bytes,5,opt,name=battle,proto3" json:"battle,omitempty"` //战斗结果
}

func (x *C2S_SelectBlackHoleNodeMessage) Reset() {
	*x = C2S_SelectBlackHoleNodeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SelectBlackHoleNodeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SelectBlackHoleNodeMessage) ProtoMessage() {}

func (x *C2S_SelectBlackHoleNodeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SelectBlackHoleNodeMessage.ProtoReflect.Descriptor instead.
func (*C2S_SelectBlackHoleNodeMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_SelectBlackHoleNodeMessage) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *C2S_SelectBlackHoleNodeMessage) GetBuff() *BlackHoleBuff {
	if x != nil {
		return x.Buff
	}
	return nil
}

func (x *C2S_SelectBlackHoleNodeMessage) GetEquip() *BlackHoleEquip {
	if x != nil {
		return x.Equip
	}
	return nil
}

func (x *C2S_SelectBlackHoleNodeMessage) GetAid() string {
	if x != nil {
		return x.Aid
	}
	return ""
}

func (x *C2S_SelectBlackHoleNodeMessage) GetBattle() *BattleResult {
	if x != nil {
		return x.Battle
	}
	return nil
}

type S2C_SelectBlackHoleNodeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //0
	CurId   string            `protobuf:"bytes,2,opt,name=curId,proto3" json:"curId,omitempty"`     //当前id
	NextId  string            `protobuf:"bytes,3,opt,name=nextId,proto3" json:"nextId,omitempty"`   //下一步id
	Buff    *BlackHoleBuff    `protobuf:"bytes,4,opt,name=buff,proto3" json:"buff,omitempty"`       //最终生成的buff
	Rebirth string            `protobuf:"bytes,5,opt,name=rebirth,proto3" json:"rebirth,omitempty"` //复活的角色uid
	Battle  *BattleResult     `protobuf:"bytes,6,opt,name=battle,proto3" json:"battle,omitempty"`   //战斗结果
	Equips  []*BlackHoleEquip `protobuf:"bytes,7,rep,name=equips,proto3" json:"equips,omitempty"`   //装备
}

func (x *S2C_SelectBlackHoleNodeMessage) Reset() {
	*x = S2C_SelectBlackHoleNodeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SelectBlackHoleNodeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SelectBlackHoleNodeMessage) ProtoMessage() {}

func (x *S2C_SelectBlackHoleNodeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SelectBlackHoleNodeMessage.ProtoReflect.Descriptor instead.
func (*S2C_SelectBlackHoleNodeMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_SelectBlackHoleNodeMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SelectBlackHoleNodeMessage) GetCurId() string {
	if x != nil {
		return x.CurId
	}
	return ""
}

func (x *S2C_SelectBlackHoleNodeMessage) GetNextId() string {
	if x != nil {
		return x.NextId
	}
	return ""
}

func (x *S2C_SelectBlackHoleNodeMessage) GetBuff() *BlackHoleBuff {
	if x != nil {
		return x.Buff
	}
	return nil
}

func (x *S2C_SelectBlackHoleNodeMessage) GetRebirth() string {
	if x != nil {
		return x.Rebirth
	}
	return ""
}

func (x *S2C_SelectBlackHoleNodeMessage) GetBattle() *BattleResult {
	if x != nil {
		return x.Battle
	}
	return nil
}

func (x *S2C_SelectBlackHoleNodeMessage) GetEquips() []*BlackHoleEquip {
	if x != nil {
		return x.Equips
	}
	return nil
}

type C2S_SyncBlackHoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SyncBlackHoleMessage) Reset() {
	*x = C2S_SyncBlackHoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncBlackHoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncBlackHoleMessage) ProtoMessage() {}

func (x *C2S_SyncBlackHoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncBlackHoleMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncBlackHoleMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{6}
}

type S2C_SyncBlackHoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`          //
	BlackHole *BlackHole `protobuf:"bytes,2,opt,name=blackHole,proto3" json:"blackHole,omitempty"` //
}

func (x *S2C_SyncBlackHoleMessage) Reset() {
	*x = S2C_SyncBlackHoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncBlackHoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncBlackHoleMessage) ProtoMessage() {}

func (x *S2C_SyncBlackHoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncBlackHoleMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncBlackHoleMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_SyncBlackHoleMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncBlackHoleMessage) GetBlackHole() *BlackHole {
	if x != nil {
		return x.BlackHole
	}
	return nil
}

type C2S_UnlockBlackHoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_UnlockBlackHoleMessage) Reset() {
	*x = C2S_UnlockBlackHoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UnlockBlackHoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UnlockBlackHoleMessage) ProtoMessage() {}

func (x *C2S_UnlockBlackHoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UnlockBlackHoleMessage.ProtoReflect.Descriptor instead.
func (*C2S_UnlockBlackHoleMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{8}
}

type S2C_UnlockBlackHoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`          //
	BlackHole *BlackHole `protobuf:"bytes,2,opt,name=blackHole,proto3" json:"blackHole,omitempty"` //
}

func (x *S2C_UnlockBlackHoleMessage) Reset() {
	*x = S2C_UnlockBlackHoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_black_hole_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UnlockBlackHoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UnlockBlackHoleMessage) ProtoMessage() {}

func (x *S2C_UnlockBlackHoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_black_hole_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UnlockBlackHoleMessage.ProtoReflect.Descriptor instead.
func (*S2C_UnlockBlackHoleMessage) Descriptor() ([]byte, []int) {
	return file_black_hole_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_UnlockBlackHoleMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_UnlockBlackHoleMessage) GetBlackHole() *BlackHole {
	if x != nil {
		return x.BlackHole
	}
	return nil
}

var File_black_hole_proto protoreflect.FileDescriptor

var file_black_hole_proto_rawDesc = []byte{
	0x0a, 0x10, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x68, 0x6f, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x36, 0x0a, 0x1e, 0x43, 0x32, 0x53, 0x5f, 0x52,
	0x65, 0x61, 0x64, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f,
	0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22,
	0x5b, 0x0a, 0x1e, 0x53, 0x32, 0x43, 0x5f, 0x52, 0x65, 0x61, 0x64, 0x79, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x62, 0x6f, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x04, 0x62, 0x6f, 0x73, 0x73, 0x22, 0x47, 0x0a, 0x19,
	0x43, 0x32, 0x53, 0x5f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f,
	0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05,
	0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x5f, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x48,
	0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x52, 0x09, 0x62, 0x6c, 0x61,
	0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x1e, 0x43, 0x32, 0x53, 0x5f, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x04, 0x62, 0x75, 0x66, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c,
	0x65, 0x42, 0x75, 0x66, 0x66, 0x52, 0x04, 0x62, 0x75, 0x66, 0x66, 0x12, 0x2b, 0x0a, 0x05, 0x65,
	0x71, 0x75, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x52, 0x05, 0x65, 0x71, 0x75, 0x69, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x06, 0x62, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x22, 0x82, 0x02, 0x0a, 0x1e, 0x53, 0x32, 0x43, 0x5f,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x75, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x75, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x04,
	0x62, 0x75, 0x66, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x42, 0x75, 0x66, 0x66,
	0x52, 0x04, 0x62, 0x75, 0x66, 0x66, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x62, 0x69, 0x72, 0x74,
	0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x62, 0x69, 0x72, 0x74, 0x68,
	0x12, 0x2b, 0x0a, 0x06, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x2d, 0x0a,
	0x06, 0x65, 0x71, 0x75, 0x69, 0x70, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x45,
	0x71, 0x75, 0x69, 0x70, 0x52, 0x06, 0x65, 0x71, 0x75, 0x69, 0x70, 0x73, 0x22, 0x1a, 0x0a, 0x18,
	0x43, 0x32, 0x53, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x5e, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f,
	0x53, 0x79, 0x6e, 0x63, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x62, 0x6c, 0x61, 0x63,
	0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x52, 0x09, 0x62,
	0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x22, 0x1c, 0x0a, 0x1a, 0x43, 0x32, 0x53, 0x5f,
	0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x60, 0x0a, 0x1a, 0x53, 0x32, 0x43, 0x5f, 0x55, 0x6e,
	0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x62, 0x6c, 0x61, 0x63,
	0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x52, 0x09, 0x62,
	0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_black_hole_proto_rawDescOnce sync.Once
	file_black_hole_proto_rawDescData = file_black_hole_proto_rawDesc
)

func file_black_hole_proto_rawDescGZIP() []byte {
	file_black_hole_proto_rawDescOnce.Do(func() {
		file_black_hole_proto_rawDescData = protoimpl.X.CompressGZIP(file_black_hole_proto_rawDescData)
	})
	return file_black_hole_proto_rawDescData
}

var file_black_hole_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_black_hole_proto_goTypes = []interface{}{
	(*C2S_ReadyStartBlackHoleMessage)(nil), // 0: proto.C2S_ReadyStartBlackHoleMessage
	(*S2C_ReadyStartBlackHoleMessage)(nil), // 1: proto.S2C_ReadyStartBlackHoleMessage
	(*C2S_StartBlackHoleMessage)(nil),      // 2: proto.C2S_StartBlackHoleMessage
	(*S2C_StartBlackHoleMessage)(nil),      // 3: proto.S2C_StartBlackHoleMessage
	(*C2S_SelectBlackHoleNodeMessage)(nil), // 4: proto.C2S_SelectBlackHoleNodeMessage
	(*S2C_SelectBlackHoleNodeMessage)(nil), // 5: proto.S2C_SelectBlackHoleNodeMessage
	(*C2S_SyncBlackHoleMessage)(nil),       // 6: proto.C2S_SyncBlackHoleMessage
	(*S2C_SyncBlackHoleMessage)(nil),       // 7: proto.S2C_SyncBlackHoleMessage
	(*C2S_UnlockBlackHoleMessage)(nil),     // 8: proto.C2S_UnlockBlackHoleMessage
	(*S2C_UnlockBlackHoleMessage)(nil),     // 9: proto.S2C_UnlockBlackHoleMessage
	(*BattleRole)(nil),                     // 10: proto.BattleRole
	(*BlackHole)(nil),                      // 11: proto.BlackHole
	(*BlackHoleBuff)(nil),                  // 12: proto.BlackHoleBuff
	(*BlackHoleEquip)(nil),                 // 13: proto.BlackHoleEquip
	(*BattleResult)(nil),                   // 14: proto.BattleResult
}
var file_black_hole_proto_depIdxs = []int32{
	10, // 0: proto.S2C_ReadyStartBlackHoleMessage.boss:type_name -> proto.BattleRole
	11, // 1: proto.S2C_StartBlackHoleMessage.blackHole:type_name -> proto.BlackHole
	12, // 2: proto.C2S_SelectBlackHoleNodeMessage.buff:type_name -> proto.BlackHoleBuff
	13, // 3: proto.C2S_SelectBlackHoleNodeMessage.equip:type_name -> proto.BlackHoleEquip
	14, // 4: proto.C2S_SelectBlackHoleNodeMessage.battle:type_name -> proto.BattleResult
	12, // 5: proto.S2C_SelectBlackHoleNodeMessage.buff:type_name -> proto.BlackHoleBuff
	14, // 6: proto.S2C_SelectBlackHoleNodeMessage.battle:type_name -> proto.BattleResult
	13, // 7: proto.S2C_SelectBlackHoleNodeMessage.equips:type_name -> proto.BlackHoleEquip
	11, // 8: proto.S2C_SyncBlackHoleMessage.blackHole:type_name -> proto.BlackHole
	11, // 9: proto.S2C_UnlockBlackHoleMessage.blackHole:type_name -> proto.BlackHole
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_black_hole_proto_init() }
func file_black_hole_proto_init() {
	if File_black_hole_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_black_hole_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ReadyStartBlackHoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_black_hole_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ReadyStartBlackHoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_black_hole_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_StartBlackHoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_black_hole_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_StartBlackHoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_black_hole_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SelectBlackHoleNodeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_black_hole_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SelectBlackHoleNodeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_black_hole_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncBlackHoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_black_hole_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncBlackHoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_black_hole_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UnlockBlackHoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_black_hole_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UnlockBlackHoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_black_hole_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_black_hole_proto_goTypes,
		DependencyIndexes: file_black_hole_proto_depIdxs,
		MessageInfos:      file_black_hole_proto_msgTypes,
	}.Build()
	File_black_hole_proto = out.File
	file_black_hole_proto_rawDesc = nil
	file_black_hole_proto_goTypes = nil
	file_black_hole_proto_depIdxs = nil
}
