// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: arrest.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_AcceptArrestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` //通缉令id
}

func (x *C2S_AcceptArrestMessage) Reset() {
	*x = C2S_AcceptArrestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_AcceptArrestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_AcceptArrestMessage) ProtoMessage() {}

func (x *C2S_AcceptArrestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_AcceptArrestMessage.ProtoReflect.Descriptor instead.
func (*C2S_AcceptArrestMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_AcceptArrestMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type S2C_AcceptArrestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_AcceptArrestMessage) Reset() {
	*x = S2C_AcceptArrestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_AcceptArrestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_AcceptArrestMessage) ProtoMessage() {}

func (x *S2C_AcceptArrestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_AcceptArrestMessage.ProtoReflect.Descriptor instead.
func (*S2C_AcceptArrestMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_AcceptArrestMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_SetArrestBattleResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`         //通缉令id
	Result *BattleResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"` //战斗结果，非0失败
}

func (x *C2S_SetArrestBattleResultMessage) Reset() {
	*x = C2S_SetArrestBattleResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SetArrestBattleResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SetArrestBattleResultMessage) ProtoMessage() {}

func (x *C2S_SetArrestBattleResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SetArrestBattleResultMessage.ProtoReflect.Descriptor instead.
func (*C2S_SetArrestBattleResultMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_SetArrestBattleResultMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *C2S_SetArrestBattleResultMessage) GetResult() *BattleResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type S2C_SetArrestBattleResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Data *Arrest `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`  //触发逃跑后的通缉令信息
}

func (x *S2C_SetArrestBattleResultMessage) Reset() {
	*x = S2C_SetArrestBattleResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SetArrestBattleResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SetArrestBattleResultMessage) ProtoMessage() {}

func (x *S2C_SetArrestBattleResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SetArrestBattleResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_SetArrestBattleResultMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_SetArrestBattleResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SetArrestBattleResultMessage) GetData() *Arrest {
	if x != nil {
		return x.Data
	}
	return nil
}

type C2S_FinishArrestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` //通缉令id
}

func (x *C2S_FinishArrestMessage) Reset() {
	*x = C2S_FinishArrestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_FinishArrestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_FinishArrestMessage) ProtoMessage() {}

func (x *C2S_FinishArrestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_FinishArrestMessage.ProtoReflect.Descriptor instead.
func (*C2S_FinishArrestMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_FinishArrestMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type S2C_FinishArrestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_FinishArrestMessage) Reset() {
	*x = S2C_FinishArrestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_FinishArrestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_FinishArrestMessage) ProtoMessage() {}

func (x *S2C_FinishArrestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_FinishArrestMessage.ProtoReflect.Descriptor instead.
func (*S2C_FinishArrestMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_FinishArrestMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_DestroyArrestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` //通缉令id
}

func (x *C2S_DestroyArrestMessage) Reset() {
	*x = C2S_DestroyArrestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_DestroyArrestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_DestroyArrestMessage) ProtoMessage() {}

func (x *C2S_DestroyArrestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_DestroyArrestMessage.ProtoReflect.Descriptor instead.
func (*C2S_DestroyArrestMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_DestroyArrestMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type S2C_DestroyArrestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_DestroyArrestMessage) Reset() {
	*x = S2C_DestroyArrestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_DestroyArrestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_DestroyArrestMessage) ProtoMessage() {}

func (x *S2C_DestroyArrestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_DestroyArrestMessage.ProtoReflect.Descriptor instead.
func (*S2C_DestroyArrestMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_DestroyArrestMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_RefreshAllArrestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_RefreshAllArrestMessage) Reset() {
	*x = C2S_RefreshAllArrestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_RefreshAllArrestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_RefreshAllArrestMessage) ProtoMessage() {}

func (x *C2S_RefreshAllArrestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_RefreshAllArrestMessage.ProtoReflect.Descriptor instead.
func (*C2S_RefreshAllArrestMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{8}
}

type S2C_RefreshAllArrestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*Arrest `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"` //列表
}

func (x *S2C_RefreshAllArrestMessage) Reset() {
	*x = S2C_RefreshAllArrestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_RefreshAllArrestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_RefreshAllArrestMessage) ProtoMessage() {}

func (x *S2C_RefreshAllArrestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_RefreshAllArrestMessage.ProtoReflect.Descriptor instead.
func (*S2C_RefreshAllArrestMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_RefreshAllArrestMessage) GetData() []*Arrest {
	if x != nil {
		return x.Data
	}
	return nil
}

type C2S_ShowArrestResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_ShowArrestResultMessage) Reset() {
	*x = C2S_ShowArrestResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ShowArrestResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ShowArrestResultMessage) ProtoMessage() {}

func (x *C2S_ShowArrestResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ShowArrestResultMessage.ProtoReflect.Descriptor instead.
func (*C2S_ShowArrestResultMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{10}
}

type S2C_ShowArrestResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_ShowArrestResultMessage) Reset() {
	*x = S2C_ShowArrestResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arrest_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ShowArrestResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ShowArrestResultMessage) ProtoMessage() {}

func (x *S2C_ShowArrestResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_arrest_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ShowArrestResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_ShowArrestResultMessage) Descriptor() ([]byte, []int) {
	return file_arrest_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_ShowArrestResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_arrest_proto protoreflect.FileDescriptor

var file_arrest_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x61, 0x72, 0x72, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x29, 0x0a, 0x17, 0x43, 0x32, 0x53, 0x5f, 0x41, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2d,
	0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x41, 0x72, 0x72, 0x65,
	0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x5f, 0x0a,
	0x20, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x65, 0x74, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x2b, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x59,
	0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x65, 0x74, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x72, 0x72,
	0x65, 0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x29, 0x0a, 0x17, 0x43, 0x32, 0x53,
	0x5f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x2d, 0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x46, 0x69, 0x6e, 0x69,
	0x73, 0x68, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x22, 0x2a, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f, 0x44, 0x65, 0x73, 0x74, 0x72,
	0x6f, 0x79, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x2e, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x44, 0x65, 0x73, 0x74, 0x72, 0x6f, 0x79, 0x41, 0x72,
	0x72, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22,
	0x1d, 0x0a, 0x1b, 0x43, 0x32, 0x53, 0x5f, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x6c,
	0x6c, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x40,
	0x0a, 0x1b, 0x53, 0x32, 0x43, 0x5f, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x6c, 0x6c,
	0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x1d, 0x0a, 0x1b, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x68, 0x6f, 0x77, 0x41, 0x72, 0x72, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x31, 0x0a, 0x1b, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x68, 0x6f, 0x77, 0x41, 0x72, 0x72, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_arrest_proto_rawDescOnce sync.Once
	file_arrest_proto_rawDescData = file_arrest_proto_rawDesc
)

func file_arrest_proto_rawDescGZIP() []byte {
	file_arrest_proto_rawDescOnce.Do(func() {
		file_arrest_proto_rawDescData = protoimpl.X.CompressGZIP(file_arrest_proto_rawDescData)
	})
	return file_arrest_proto_rawDescData
}

var file_arrest_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_arrest_proto_goTypes = []interface{}{
	(*C2S_AcceptArrestMessage)(nil),          // 0: proto.C2S_AcceptArrestMessage
	(*S2C_AcceptArrestMessage)(nil),          // 1: proto.S2C_AcceptArrestMessage
	(*C2S_SetArrestBattleResultMessage)(nil), // 2: proto.C2S_SetArrestBattleResultMessage
	(*S2C_SetArrestBattleResultMessage)(nil), // 3: proto.S2C_SetArrestBattleResultMessage
	(*C2S_FinishArrestMessage)(nil),          // 4: proto.C2S_FinishArrestMessage
	(*S2C_FinishArrestMessage)(nil),          // 5: proto.S2C_FinishArrestMessage
	(*C2S_DestroyArrestMessage)(nil),         // 6: proto.C2S_DestroyArrestMessage
	(*S2C_DestroyArrestMessage)(nil),         // 7: proto.S2C_DestroyArrestMessage
	(*C2S_RefreshAllArrestMessage)(nil),      // 8: proto.C2S_RefreshAllArrestMessage
	(*S2C_RefreshAllArrestMessage)(nil),      // 9: proto.S2C_RefreshAllArrestMessage
	(*C2S_ShowArrestResultMessage)(nil),      // 10: proto.C2S_ShowArrestResultMessage
	(*S2C_ShowArrestResultMessage)(nil),      // 11: proto.S2C_ShowArrestResultMessage
	(*BattleResult)(nil),                     // 12: proto.BattleResult
	(*Arrest)(nil),                           // 13: proto.Arrest
}
var file_arrest_proto_depIdxs = []int32{
	12, // 0: proto.C2S_SetArrestBattleResultMessage.result:type_name -> proto.BattleResult
	13, // 1: proto.S2C_SetArrestBattleResultMessage.data:type_name -> proto.Arrest
	13, // 2: proto.S2C_RefreshAllArrestMessage.data:type_name -> proto.Arrest
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_arrest_proto_init() }
func file_arrest_proto_init() {
	if File_arrest_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_arrest_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_AcceptArrestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_AcceptArrestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SetArrestBattleResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SetArrestBattleResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_FinishArrestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_FinishArrestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_DestroyArrestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_DestroyArrestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_RefreshAllArrestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_RefreshAllArrestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ShowArrestResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arrest_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ShowArrestResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_arrest_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_arrest_proto_goTypes,
		DependencyIndexes: file_arrest_proto_depIdxs,
		MessageInfos:      file_arrest_proto_msgTypes,
	}.Build()
	File_arrest_proto = out.File
	file_arrest_proto_rawDesc = nil
	file_arrest_proto_goTypes = nil
	file_arrest_proto_depIdxs = nil
}
