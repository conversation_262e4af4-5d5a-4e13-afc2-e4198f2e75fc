// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: tower.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_TowerBattleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_TowerBattleMessage) Reset() {
	*x = C2S_TowerBattleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tower_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_TowerBattleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_TowerBattleMessage) ProtoMessage() {}

func (x *C2S_TowerBattleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_tower_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_TowerBattleMessage.ProtoReflect.Descriptor instead.
func (*C2S_TowerBattleMessage) Descriptor() ([]byte, []int) {
	return file_tower_proto_rawDescGZIP(), []int{0}
}

type S2C_TowerBattleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_TowerBattleMessage) Reset() {
	*x = S2C_TowerBattleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tower_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_TowerBattleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_TowerBattleMessage) ProtoMessage() {}

func (x *S2C_TowerBattleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_tower_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_TowerBattleMessage.ProtoReflect.Descriptor instead.
func (*S2C_TowerBattleMessage) Descriptor() ([]byte, []int) {
	return file_tower_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_TowerBattleMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_TowerBattleWinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_TowerBattleWinMessage) Reset() {
	*x = C2S_TowerBattleWinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tower_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_TowerBattleWinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_TowerBattleWinMessage) ProtoMessage() {}

func (x *C2S_TowerBattleWinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_tower_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_TowerBattleWinMessage.ProtoReflect.Descriptor instead.
func (*C2S_TowerBattleWinMessage) Descriptor() ([]byte, []int) {
	return file_tower_proto_rawDescGZIP(), []int{2}
}

type S2C_TowerBattleWinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`  //0
	Tower *Tower `protobuf:"bytes,2,opt,name=tower,proto3" json:"tower,omitempty"` //爬塔数据
}

func (x *S2C_TowerBattleWinMessage) Reset() {
	*x = S2C_TowerBattleWinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tower_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_TowerBattleWinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_TowerBattleWinMessage) ProtoMessage() {}

func (x *S2C_TowerBattleWinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_tower_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_TowerBattleWinMessage.ProtoReflect.Descriptor instead.
func (*S2C_TowerBattleWinMessage) Descriptor() ([]byte, []int) {
	return file_tower_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_TowerBattleWinMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_TowerBattleWinMessage) GetTower() *Tower {
	if x != nil {
		return x.Tower
	}
	return nil
}

var File_tower_proto protoreflect.FileDescriptor

var file_tower_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x74, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x18, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x54, 0x6f, 0x77, 0x65, 0x72, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2c, 0x0a, 0x16,
	0x53, 0x32, 0x43, 0x5f, 0x54, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x1b, 0x0a, 0x19, 0x43, 0x32,
	0x53, 0x5f, 0x54, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x57, 0x69, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x53, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x54,
	0x6f, 0x77, 0x65, 0x72, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x57, 0x69, 0x6e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x05, 0x74, 0x6f, 0x77, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x54, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x05, 0x74, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x06, 0x5a, 0x04,
	0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tower_proto_rawDescOnce sync.Once
	file_tower_proto_rawDescData = file_tower_proto_rawDesc
)

func file_tower_proto_rawDescGZIP() []byte {
	file_tower_proto_rawDescOnce.Do(func() {
		file_tower_proto_rawDescData = protoimpl.X.CompressGZIP(file_tower_proto_rawDescData)
	})
	return file_tower_proto_rawDescData
}

var file_tower_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_tower_proto_goTypes = []interface{}{
	(*C2S_TowerBattleMessage)(nil),    // 0: proto.C2S_TowerBattleMessage
	(*S2C_TowerBattleMessage)(nil),    // 1: proto.S2C_TowerBattleMessage
	(*C2S_TowerBattleWinMessage)(nil), // 2: proto.C2S_TowerBattleWinMessage
	(*S2C_TowerBattleWinMessage)(nil), // 3: proto.S2C_TowerBattleWinMessage
	(*Tower)(nil),                     // 4: proto.Tower
}
var file_tower_proto_depIdxs = []int32{
	4, // 0: proto.S2C_TowerBattleWinMessage.tower:type_name -> proto.Tower
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tower_proto_init() }
func file_tower_proto_init() {
	if File_tower_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tower_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_TowerBattleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tower_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_TowerBattleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tower_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_TowerBattleWinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tower_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_TowerBattleWinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tower_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tower_proto_goTypes,
		DependencyIndexes: file_tower_proto_depIdxs,
		MessageInfos:      file_tower_proto_msgTypes,
	}.Build()
	File_tower_proto = out.File
	file_tower_proto_rawDesc = nil
	file_tower_proto_goTypes = nil
	file_tower_proto_depIdxs = nil
}
