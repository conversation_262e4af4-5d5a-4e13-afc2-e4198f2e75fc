// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: ore.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_OreActionMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X     int32             `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`                                    //行
	Y     int32             `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"`                                    //列
	Type  OreCeilActionType `protobuf:"varint,3,opt,name=type,proto3,enum=proto.OreCeilActionType" json:"type,omitempty"` //操作类型
	Level int32             `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`                            //难度
	Extra int32             `protobuf:"varint,5,opt,name=extra,proto3" json:"extra,omitempty"`                            //奖励计算参数
}

func (x *C2S_OreActionMessage) Reset() {
	*x = C2S_OreActionMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_OreActionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_OreActionMessage) ProtoMessage() {}

func (x *C2S_OreActionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_OreActionMessage.ProtoReflect.Descriptor instead.
func (*C2S_OreActionMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_OreActionMessage) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *C2S_OreActionMessage) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *C2S_OreActionMessage) GetType() OreCeilActionType {
	if x != nil {
		return x.Type
	}
	return OreCeilActionType_BreakNormal
}

func (x *C2S_OreActionMessage) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *C2S_OreActionMessage) GetExtra() int32 {
	if x != nil {
		return x.Extra
	}
	return 0
}

type S2C_OreActionMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    OreCeilActionCode `protobuf:"varint,1,opt,name=code,proto3,enum=proto.OreCeilActionCode" json:"code,omitempty"` //
	Data    []*OreRowData     `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`                               //增加的数据，可能有
	Rewards []*Condition      `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`                         //奖励数据，可能有
}

func (x *S2C_OreActionMessage) Reset() {
	*x = S2C_OreActionMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_OreActionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_OreActionMessage) ProtoMessage() {}

func (x *S2C_OreActionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_OreActionMessage.ProtoReflect.Descriptor instead.
func (*S2C_OreActionMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_OreActionMessage) GetCode() OreCeilActionCode {
	if x != nil {
		return x.Code
	}
	return OreCeilActionCode_OnSuccess
}

func (x *S2C_OreActionMessage) GetData() []*OreRowData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *S2C_OreActionMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_OreSyncBreakItemTimeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_OreSyncBreakItemTimeMessage) Reset() {
	*x = C2S_OreSyncBreakItemTimeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_OreSyncBreakItemTimeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_OreSyncBreakItemTimeMessage) ProtoMessage() {}

func (x *C2S_OreSyncBreakItemTimeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_OreSyncBreakItemTimeMessage.ProtoReflect.Descriptor instead.
func (*C2S_OreSyncBreakItemTimeMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{2}
}

type S2C_OreSyncBreakItemTimeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Item *ItemInfo `protobuf:"bytes,2,opt,name=item,proto3" json:"item,omitempty"`  //镐子数据
	Time int32     `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"` //下一次恢复倒计时
}

func (x *S2C_OreSyncBreakItemTimeMessage) Reset() {
	*x = S2C_OreSyncBreakItemTimeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_OreSyncBreakItemTimeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_OreSyncBreakItemTimeMessage) ProtoMessage() {}

func (x *S2C_OreSyncBreakItemTimeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_OreSyncBreakItemTimeMessage.ProtoReflect.Descriptor instead.
func (*S2C_OreSyncBreakItemTimeMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_OreSyncBreakItemTimeMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_OreSyncBreakItemTimeMessage) GetItem() *ItemInfo {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *S2C_OreSyncBreakItemTimeMessage) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

type C2S_OreLevelFightMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level int32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"` //挑战的等级
}

func (x *C2S_OreLevelFightMessage) Reset() {
	*x = C2S_OreLevelFightMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_OreLevelFightMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_OreLevelFightMessage) ProtoMessage() {}

func (x *C2S_OreLevelFightMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_OreLevelFightMessage.ProtoReflect.Descriptor instead.
func (*C2S_OreLevelFightMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_OreLevelFightMessage) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type S2C_OreLevelFightMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //1已经解锁难度,2不存在的难度配置
}

func (x *S2C_OreLevelFightMessage) Reset() {
	*x = S2C_OreLevelFightMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_OreLevelFightMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_OreLevelFightMessage) ProtoMessage() {}

func (x *S2C_OreLevelFightMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_OreLevelFightMessage.ProtoReflect.Descriptor instead.
func (*S2C_OreLevelFightMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_OreLevelFightMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_GetOreLevelDataMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level int32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"` //等级
}

func (x *C2S_GetOreLevelDataMessage) Reset() {
	*x = C2S_GetOreLevelDataMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetOreLevelDataMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetOreLevelDataMessage) ProtoMessage() {}

func (x *C2S_GetOreLevelDataMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetOreLevelDataMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetOreLevelDataMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_GetOreLevelDataMessage) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type S2C_GetOreLevelDataMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Data *OreLevelData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`  //矿洞数据
}

func (x *S2C_GetOreLevelDataMessage) Reset() {
	*x = S2C_GetOreLevelDataMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetOreLevelDataMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetOreLevelDataMessage) ProtoMessage() {}

func (x *S2C_GetOreLevelDataMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetOreLevelDataMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetOreLevelDataMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_GetOreLevelDataMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetOreLevelDataMessage) GetData() *OreLevelData {
	if x != nil {
		return x.Data
	}
	return nil
}

type C2S_UnlockOreMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_UnlockOreMessage) Reset() {
	*x = C2S_UnlockOreMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UnlockOreMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UnlockOreMessage) ProtoMessage() {}

func (x *C2S_UnlockOreMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UnlockOreMessage.ProtoReflect.Descriptor instead.
func (*C2S_UnlockOreMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{8}
}

type S2C_UnlockOreMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Ore  *Ore  `protobuf:"bytes,2,opt,name=ore,proto3" json:"ore,omitempty"`    //
}

func (x *S2C_UnlockOreMessage) Reset() {
	*x = S2C_UnlockOreMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ore_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UnlockOreMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UnlockOreMessage) ProtoMessage() {}

func (x *S2C_UnlockOreMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ore_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UnlockOreMessage.ProtoReflect.Descriptor instead.
func (*S2C_UnlockOreMessage) Descriptor() ([]byte, []int) {
	return file_ore_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_UnlockOreMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_UnlockOreMessage) GetOre() *Ore {
	if x != nil {
		return x.Ore
	}
	return nil
}

var File_ore_proto protoreflect.FileDescriptor

var file_ore_proto_rawDesc = []byte{
	0x0a, 0x09, 0x6f, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x01, 0x0a,
	0x14, 0x43, 0x32, 0x53, 0x5f, 0x4f, 0x72, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01,
	0x79, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x43, 0x65, 0x69, 0x6c, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x97, 0x01, 0x0a, 0x14,
	0x53, 0x32, 0x43, 0x5f, 0x4f, 0x72, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x43, 0x65,
	0x69, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x52, 0x6f, 0x77, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x21, 0x0a, 0x1f, 0x43, 0x32, 0x53, 0x5f, 0x4f, 0x72, 0x65,
	0x53, 0x79, 0x6e, 0x63, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x69, 0x6d,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x6e, 0x0a, 0x1f, 0x53, 0x32, 0x43, 0x5f,
	0x4f, 0x72, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x69, 0x6d, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x23, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04,
	0x69, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x30, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f,
	0x4f, 0x72, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x46, 0x69, 0x67, 0x68, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x2e, 0x0a, 0x18, 0x53, 0x32,
	0x43, 0x5f, 0x4f, 0x72, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x46, 0x69, 0x67, 0x68, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x32, 0x0a, 0x1a, 0x43, 0x32,
	0x53, 0x5f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x59,
	0x0a, 0x1a, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x65, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x27, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x32, 0x53,
	0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4f, 0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x48, 0x0a, 0x14, 0x53, 0x32, 0x43, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4f,
	0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a,
	0x03, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x65, 0x52, 0x03, 0x6f, 0x72, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e,
	0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ore_proto_rawDescOnce sync.Once
	file_ore_proto_rawDescData = file_ore_proto_rawDesc
)

func file_ore_proto_rawDescGZIP() []byte {
	file_ore_proto_rawDescOnce.Do(func() {
		file_ore_proto_rawDescData = protoimpl.X.CompressGZIP(file_ore_proto_rawDescData)
	})
	return file_ore_proto_rawDescData
}

var file_ore_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_ore_proto_goTypes = []interface{}{
	(*C2S_OreActionMessage)(nil),            // 0: proto.C2S_OreActionMessage
	(*S2C_OreActionMessage)(nil),            // 1: proto.S2C_OreActionMessage
	(*C2S_OreSyncBreakItemTimeMessage)(nil), // 2: proto.C2S_OreSyncBreakItemTimeMessage
	(*S2C_OreSyncBreakItemTimeMessage)(nil), // 3: proto.S2C_OreSyncBreakItemTimeMessage
	(*C2S_OreLevelFightMessage)(nil),        // 4: proto.C2S_OreLevelFightMessage
	(*S2C_OreLevelFightMessage)(nil),        // 5: proto.S2C_OreLevelFightMessage
	(*C2S_GetOreLevelDataMessage)(nil),      // 6: proto.C2S_GetOreLevelDataMessage
	(*S2C_GetOreLevelDataMessage)(nil),      // 7: proto.S2C_GetOreLevelDataMessage
	(*C2S_UnlockOreMessage)(nil),            // 8: proto.C2S_UnlockOreMessage
	(*S2C_UnlockOreMessage)(nil),            // 9: proto.S2C_UnlockOreMessage
	(OreCeilActionType)(0),                  // 10: proto.OreCeilActionType
	(OreCeilActionCode)(0),                  // 11: proto.OreCeilActionCode
	(*OreRowData)(nil),                      // 12: proto.OreRowData
	(*Condition)(nil),                       // 13: proto.Condition
	(*ItemInfo)(nil),                        // 14: proto.ItemInfo
	(*OreLevelData)(nil),                    // 15: proto.OreLevelData
	(*Ore)(nil),                             // 16: proto.Ore
}
var file_ore_proto_depIdxs = []int32{
	10, // 0: proto.C2S_OreActionMessage.type:type_name -> proto.OreCeilActionType
	11, // 1: proto.S2C_OreActionMessage.code:type_name -> proto.OreCeilActionCode
	12, // 2: proto.S2C_OreActionMessage.data:type_name -> proto.OreRowData
	13, // 3: proto.S2C_OreActionMessage.rewards:type_name -> proto.Condition
	14, // 4: proto.S2C_OreSyncBreakItemTimeMessage.item:type_name -> proto.ItemInfo
	15, // 5: proto.S2C_GetOreLevelDataMessage.data:type_name -> proto.OreLevelData
	16, // 6: proto.S2C_UnlockOreMessage.ore:type_name -> proto.Ore
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_ore_proto_init() }
func file_ore_proto_init() {
	if File_ore_proto != nil {
		return
	}
	file_enum_proto_init()
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_ore_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_OreActionMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ore_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_OreActionMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ore_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_OreSyncBreakItemTimeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ore_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_OreSyncBreakItemTimeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ore_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_OreLevelFightMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ore_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_OreLevelFightMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ore_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetOreLevelDataMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ore_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetOreLevelDataMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ore_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UnlockOreMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ore_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UnlockOreMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ore_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ore_proto_goTypes,
		DependencyIndexes: file_ore_proto_depIdxs,
		MessageInfos:      file_ore_proto_msgTypes,
	}.Build()
	File_ore_proto = out.File
	file_ore_proto_rawDesc = nil
	file_ore_proto_goTypes = nil
	file_ore_proto_depIdxs = nil
}
