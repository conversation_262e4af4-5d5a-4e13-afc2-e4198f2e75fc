// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: passenger.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_ChangePassengerDormMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`         //乘客id
	Index  int32 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`   //位置
	DormId int32 `protobuf:"varint,3,opt,name=dormId,proto3" json:"dormId,omitempty"` //车厢id, null为离开
}

func (x *C2S_ChangePassengerDormMessage) Reset() {
	*x = C2S_ChangePassengerDormMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChangePassengerDormMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChangePassengerDormMessage) ProtoMessage() {}

func (x *C2S_ChangePassengerDormMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChangePassengerDormMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChangePassengerDormMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_ChangePassengerDormMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_ChangePassengerDormMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *C2S_ChangePassengerDormMessage) GetDormId() int32 {
	if x != nil {
		return x.DormId
	}
	return 0
}

type S2C_ChangePassengerDormRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误，1车厢满了
}

func (x *S2C_ChangePassengerDormRespMessage) Reset() {
	*x = S2C_ChangePassengerDormRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChangePassengerDormRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChangePassengerDormRespMessage) ProtoMessage() {}

func (x *S2C_ChangePassengerDormRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChangePassengerDormRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChangePassengerDormRespMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_ChangePassengerDormRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_PassengerLevelUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` //操作类型，1升级2升星
	Id   int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`     //乘客id
}

func (x *C2S_PassengerLevelUpMessage) Reset() {
	*x = C2S_PassengerLevelUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PassengerLevelUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PassengerLevelUpMessage) ProtoMessage() {}

func (x *C2S_PassengerLevelUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PassengerLevelUpMessage.ProtoReflect.Descriptor instead.
func (*C2S_PassengerLevelUpMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_PassengerLevelUpMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *C2S_PassengerLevelUpMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_PassengerLevelUpResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
}

func (x *S2C_PassengerLevelUpResultMessage) Reset() {
	*x = S2C_PassengerLevelUpResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PassengerLevelUpResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PassengerLevelUpResultMessage) ProtoMessage() {}

func (x *S2C_PassengerLevelUpResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PassengerLevelUpResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_PassengerLevelUpResultMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_PassengerLevelUpResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ChangePassengerWorkMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`               //乘客id
	WorkId    int32 `protobuf:"varint,2,opt,name=workId,proto3" json:"workId,omitempty"`       //车厢id, null为离开
	WorkIndex int32 `protobuf:"varint,3,opt,name=workIndex,proto3" json:"workIndex,omitempty"` //车厢工位下标, null为离开
}

func (x *C2S_ChangePassengerWorkMessage) Reset() {
	*x = C2S_ChangePassengerWorkMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChangePassengerWorkMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChangePassengerWorkMessage) ProtoMessage() {}

func (x *C2S_ChangePassengerWorkMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChangePassengerWorkMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChangePassengerWorkMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_ChangePassengerWorkMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_ChangePassengerWorkMessage) GetWorkId() int32 {
	if x != nil {
		return x.WorkId
	}
	return 0
}

func (x *C2S_ChangePassengerWorkMessage) GetWorkIndex() int32 {
	if x != nil {
		return x.WorkIndex
	}
	return 0
}

type S2C_ChangePassengerWorkMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
}

func (x *S2C_ChangePassengerWorkMessage) Reset() {
	*x = S2C_ChangePassengerWorkMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChangePassengerWorkMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChangePassengerWorkMessage) ProtoMessage() {}

func (x *S2C_ChangePassengerWorkMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChangePassengerWorkMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChangePassengerWorkMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_ChangePassengerWorkMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_CompletePassengerPlotMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` //剧情id
}

func (x *C2S_CompletePassengerPlotMessage) Reset() {
	*x = C2S_CompletePassengerPlotMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CompletePassengerPlotMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CompletePassengerPlotMessage) ProtoMessage() {}

func (x *C2S_CompletePassengerPlotMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CompletePassengerPlotMessage.ProtoReflect.Descriptor instead.
func (*C2S_CompletePassengerPlotMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_CompletePassengerPlotMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type S2C_CompletePassengerPlotMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
}

func (x *S2C_CompletePassengerPlotMessage) Reset() {
	*x = S2C_CompletePassengerPlotMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CompletePassengerPlotMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CompletePassengerPlotMessage) ProtoMessage() {}

func (x *S2C_CompletePassengerPlotMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CompletePassengerPlotMessage.ProtoReflect.Descriptor instead.
func (*S2C_CompletePassengerPlotMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_CompletePassengerPlotMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_UnlockSkinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` //皮肤id
}

func (x *C2S_UnlockSkinMessage) Reset() {
	*x = C2S_UnlockSkinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UnlockSkinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UnlockSkinMessage) ProtoMessage() {}

func (x *C2S_UnlockSkinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UnlockSkinMessage.ProtoReflect.Descriptor instead.
func (*C2S_UnlockSkinMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{8}
}

func (x *C2S_UnlockSkinMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type S2C_UnlockSkinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //1皮肤不存在2解锁消耗不足
}

func (x *S2C_UnlockSkinMessage) Reset() {
	*x = S2C_UnlockSkinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UnlockSkinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UnlockSkinMessage) ProtoMessage() {}

func (x *S2C_UnlockSkinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UnlockSkinMessage.ProtoReflect.Descriptor instead.
func (*S2C_UnlockSkinMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_UnlockSkinMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ChangeSkinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PassengerId int32 `protobuf:"varint,1,opt,name=passengerId,proto3" json:"passengerId,omitempty"` //乘客id
	SkinIndex   int32 `protobuf:"varint,2,opt,name=skinIndex,proto3" json:"skinIndex,omitempty"`     //皮肤序号
}

func (x *C2S_ChangeSkinMessage) Reset() {
	*x = C2S_ChangeSkinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChangeSkinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChangeSkinMessage) ProtoMessage() {}

func (x *C2S_ChangeSkinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChangeSkinMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChangeSkinMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{10}
}

func (x *C2S_ChangeSkinMessage) GetPassengerId() int32 {
	if x != nil {
		return x.PassengerId
	}
	return 0
}

func (x *C2S_ChangeSkinMessage) GetSkinIndex() int32 {
	if x != nil {
		return x.SkinIndex
	}
	return 0
}

type S2C_ChangeSkinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //1皮肤不存在
}

func (x *S2C_ChangeSkinMessage) Reset() {
	*x = S2C_ChangeSkinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChangeSkinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChangeSkinMessage) ProtoMessage() {}

func (x *S2C_ChangeSkinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChangeSkinMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChangeSkinMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_ChangeSkinMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_TalentLevelUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PassengerId int32 `protobuf:"varint,1,opt,name=passengerId,proto3" json:"passengerId,omitempty"` //乘客id
	Id          int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                   //天赋id
}

func (x *C2S_TalentLevelUpMessage) Reset() {
	*x = C2S_TalentLevelUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_TalentLevelUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_TalentLevelUpMessage) ProtoMessage() {}

func (x *C2S_TalentLevelUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_TalentLevelUpMessage.ProtoReflect.Descriptor instead.
func (*C2S_TalentLevelUpMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{12}
}

func (x *C2S_TalentLevelUpMessage) GetPassengerId() int32 {
	if x != nil {
		return x.PassengerId
	}
	return 0
}

func (x *C2S_TalentLevelUpMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_TalentLevelUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_TalentLevelUpMessage) Reset() {
	*x = S2C_TalentLevelUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_TalentLevelUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_TalentLevelUpMessage) ProtoMessage() {}

func (x *S2C_TalentLevelUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_TalentLevelUpMessage.ProtoReflect.Descriptor instead.
func (*S2C_TalentLevelUpMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{13}
}

func (x *S2C_TalentLevelUpMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_FragMergeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`   //投影id
	Num int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"` //合成数量
}

func (x *C2S_FragMergeMessage) Reset() {
	*x = C2S_FragMergeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_FragMergeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_FragMergeMessage) ProtoMessage() {}

func (x *C2S_FragMergeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_FragMergeMessage.ProtoReflect.Descriptor instead.
func (*C2S_FragMergeMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{14}
}

func (x *C2S_FragMergeMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_FragMergeMessage) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type S2C_FragMergeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //
	Rewards []*Condition `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"` //
}

func (x *S2C_FragMergeMessage) Reset() {
	*x = S2C_FragMergeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_FragMergeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_FragMergeMessage) ProtoMessage() {}

func (x *S2C_FragMergeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_FragMergeMessage.ProtoReflect.Descriptor instead.
func (*S2C_FragMergeMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{15}
}

func (x *S2C_FragMergeMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_FragMergeMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_PassengerUnlockProfileMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProfileId   int32 `protobuf:"varint,1,opt,name=profileId,proto3" json:"profileId,omitempty"`     //资料id
	PassengerId int32 `protobuf:"varint,2,opt,name=passengerId,proto3" json:"passengerId,omitempty"` //乘客id
	Position    int32 `protobuf:"varint,3,opt,name=position,proto3" json:"position,omitempty"`       //位置
}

func (x *C2S_PassengerUnlockProfileMessage) Reset() {
	*x = C2S_PassengerUnlockProfileMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PassengerUnlockProfileMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PassengerUnlockProfileMessage) ProtoMessage() {}

func (x *C2S_PassengerUnlockProfileMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PassengerUnlockProfileMessage.ProtoReflect.Descriptor instead.
func (*C2S_PassengerUnlockProfileMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{16}
}

func (x *C2S_PassengerUnlockProfileMessage) GetProfileId() int32 {
	if x != nil {
		return x.ProfileId
	}
	return 0
}

func (x *C2S_PassengerUnlockProfileMessage) GetPassengerId() int32 {
	if x != nil {
		return x.PassengerId
	}
	return 0
}

func (x *C2S_PassengerUnlockProfileMessage) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

type S2C_PassengerUnlockProfileMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
}

func (x *S2C_PassengerUnlockProfileMessage) Reset() {
	*x = S2C_PassengerUnlockProfileMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PassengerUnlockProfileMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PassengerUnlockProfileMessage) ProtoMessage() {}

func (x *S2C_PassengerUnlockProfileMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PassengerUnlockProfileMessage.ProtoReflect.Descriptor instead.
func (*S2C_PassengerUnlockProfileMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{17}
}

func (x *S2C_PassengerUnlockProfileMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_TransPassengerMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromId int32 `protobuf:"varint,1,opt,name=fromId,proto3" json:"fromId,omitempty"` //被转换乘客id
	ToId   int32 `protobuf:"varint,2,opt,name=toId,proto3" json:"toId,omitempty"`     //乘客id
}

func (x *C2S_TransPassengerMessage) Reset() {
	*x = C2S_TransPassengerMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_TransPassengerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_TransPassengerMessage) ProtoMessage() {}

func (x *C2S_TransPassengerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_TransPassengerMessage.ProtoReflect.Descriptor instead.
func (*C2S_TransPassengerMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{18}
}

func (x *C2S_TransPassengerMessage) GetFromId() int32 {
	if x != nil {
		return x.FromId
	}
	return 0
}

func (x *C2S_TransPassengerMessage) GetToId() int32 {
	if x != nil {
		return x.ToId
	}
	return 0
}

type S2C_TransPassengerMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
}

func (x *S2C_TransPassengerMessage) Reset() {
	*x = S2C_TransPassengerMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_TransPassengerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_TransPassengerMessage) ProtoMessage() {}

func (x *S2C_TransPassengerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_TransPassengerMessage.ProtoReflect.Descriptor instead.
func (*S2C_TransPassengerMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{19}
}

func (x *S2C_TransPassengerMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_PassengerProfileSortChangeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PassengerId int32           `protobuf:"varint,1,opt,name=passengerId,proto3" json:"passengerId,omitempty"`                                                                            //乘客id
	Sort        map[int32]int32 `protobuf:"bytes,2,rep,name=sort,proto3" json:"sort,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //排序数据
}

func (x *C2S_PassengerProfileSortChangeMessage) Reset() {
	*x = C2S_PassengerProfileSortChangeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PassengerProfileSortChangeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PassengerProfileSortChangeMessage) ProtoMessage() {}

func (x *C2S_PassengerProfileSortChangeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PassengerProfileSortChangeMessage.ProtoReflect.Descriptor instead.
func (*C2S_PassengerProfileSortChangeMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{20}
}

func (x *C2S_PassengerProfileSortChangeMessage) GetPassengerId() int32 {
	if x != nil {
		return x.PassengerId
	}
	return 0
}

func (x *C2S_PassengerProfileSortChangeMessage) GetSort() map[int32]int32 {
	if x != nil {
		return x.Sort
	}
	return nil
}

type S2C_PassengerProfileSortChangeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
}

func (x *S2C_PassengerProfileSortChangeMessage) Reset() {
	*x = S2C_PassengerProfileSortChangeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_passenger_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PassengerProfileSortChangeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PassengerProfileSortChangeMessage) ProtoMessage() {}

func (x *S2C_PassengerProfileSortChangeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_passenger_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PassengerProfileSortChangeMessage.ProtoReflect.Descriptor instead.
func (*S2C_PassengerProfileSortChangeMessage) Descriptor() ([]byte, []int) {
	return file_passenger_proto_rawDescGZIP(), []int{21}
}

func (x *S2C_PassengerProfileSortChangeMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_passenger_proto protoreflect.FileDescriptor

var file_passenger_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5e, 0x0a, 0x1e, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x44, 0x6f, 0x72,
	0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x64, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x22, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x44, 0x6f, 0x72,
	0x6d, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x41, 0x0a, 0x1b, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65,
	0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x37, 0x0a, 0x21, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x65,
	0x6e, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x66, 0x0a, 0x1e,
	0x43, 0x32, 0x53, 0x5f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e,
	0x67, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x22, 0x34, 0x0a, 0x1e, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x32, 0x0a, 0x20, 0x43, 0x32,
	0x53, 0x5f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e,
	0x67, 0x65, 0x72, 0x50, 0x6c, 0x6f, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x36,
	0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61,
	0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x50, 0x6c, 0x6f, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x27, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x55, 0x6e,
	0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x6b, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x2b, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x6b, 0x69,
	0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x57, 0x0a, 0x15,
	0x43, 0x32, 0x53, 0x5f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x6b, 0x69, 0x6e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x61, 0x73, 0x73,
	0x65, 0x6e, 0x67, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x6b, 0x69, 0x6e, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x6b, 0x69, 0x6e,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x2b, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x53, 0x6b, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x4c, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x2e, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x38, 0x0a, 0x14, 0x43, 0x32, 0x53, 0x5f, 0x46, 0x72, 0x61, 0x67, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x56, 0x0a, 0x14, 0x53, 0x32,
	0x43, 0x5f, 0x46, 0x72, 0x61, 0x67, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x22, 0x7f, 0x0a, 0x21, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e,
	0x67, 0x65, 0x72, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x61, 0x73, 0x73,
	0x65, 0x6e, 0x67, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x37, 0x0a, 0x21, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x65,
	0x6e, 0x67, 0x65, 0x72, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x47, 0x0a, 0x19,
	0x43, 0x32, 0x53, 0x5f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67,
	0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x72, 0x6f,
	0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x66, 0x72, 0x6f, 0x6d, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x6f, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x74, 0x6f, 0x49, 0x64, 0x22, 0x2f, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x25, 0x43, 0x32, 0x53, 0x5f, 0x50,
	0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x4a, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x61, 0x73,
	0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x72,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x53,
	0x6f, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x1a, 0x37,
	0x0a, 0x09, 0x53, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3b, 0x0a, 0x25, 0x53, 0x32, 0x43, 0x5f, 0x50,
	0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_passenger_proto_rawDescOnce sync.Once
	file_passenger_proto_rawDescData = file_passenger_proto_rawDesc
)

func file_passenger_proto_rawDescGZIP() []byte {
	file_passenger_proto_rawDescOnce.Do(func() {
		file_passenger_proto_rawDescData = protoimpl.X.CompressGZIP(file_passenger_proto_rawDescData)
	})
	return file_passenger_proto_rawDescData
}

var file_passenger_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_passenger_proto_goTypes = []interface{}{
	(*C2S_ChangePassengerDormMessage)(nil),        // 0: proto.C2S_ChangePassengerDormMessage
	(*S2C_ChangePassengerDormRespMessage)(nil),    // 1: proto.S2C_ChangePassengerDormRespMessage
	(*C2S_PassengerLevelUpMessage)(nil),           // 2: proto.C2S_PassengerLevelUpMessage
	(*S2C_PassengerLevelUpResultMessage)(nil),     // 3: proto.S2C_PassengerLevelUpResultMessage
	(*C2S_ChangePassengerWorkMessage)(nil),        // 4: proto.C2S_ChangePassengerWorkMessage
	(*S2C_ChangePassengerWorkMessage)(nil),        // 5: proto.S2C_ChangePassengerWorkMessage
	(*C2S_CompletePassengerPlotMessage)(nil),      // 6: proto.C2S_CompletePassengerPlotMessage
	(*S2C_CompletePassengerPlotMessage)(nil),      // 7: proto.S2C_CompletePassengerPlotMessage
	(*C2S_UnlockSkinMessage)(nil),                 // 8: proto.C2S_UnlockSkinMessage
	(*S2C_UnlockSkinMessage)(nil),                 // 9: proto.S2C_UnlockSkinMessage
	(*C2S_ChangeSkinMessage)(nil),                 // 10: proto.C2S_ChangeSkinMessage
	(*S2C_ChangeSkinMessage)(nil),                 // 11: proto.S2C_ChangeSkinMessage
	(*C2S_TalentLevelUpMessage)(nil),              // 12: proto.C2S_TalentLevelUpMessage
	(*S2C_TalentLevelUpMessage)(nil),              // 13: proto.S2C_TalentLevelUpMessage
	(*C2S_FragMergeMessage)(nil),                  // 14: proto.C2S_FragMergeMessage
	(*S2C_FragMergeMessage)(nil),                  // 15: proto.S2C_FragMergeMessage
	(*C2S_PassengerUnlockProfileMessage)(nil),     // 16: proto.C2S_PassengerUnlockProfileMessage
	(*S2C_PassengerUnlockProfileMessage)(nil),     // 17: proto.S2C_PassengerUnlockProfileMessage
	(*C2S_TransPassengerMessage)(nil),             // 18: proto.C2S_TransPassengerMessage
	(*S2C_TransPassengerMessage)(nil),             // 19: proto.S2C_TransPassengerMessage
	(*C2S_PassengerProfileSortChangeMessage)(nil), // 20: proto.C2S_PassengerProfileSortChangeMessage
	(*S2C_PassengerProfileSortChangeMessage)(nil), // 21: proto.S2C_PassengerProfileSortChangeMessage
	nil,               // 22: proto.C2S_PassengerProfileSortChangeMessage.SortEntry
	(*Condition)(nil), // 23: proto.Condition
}
var file_passenger_proto_depIdxs = []int32{
	23, // 0: proto.S2C_FragMergeMessage.rewards:type_name -> proto.Condition
	22, // 1: proto.C2S_PassengerProfileSortChangeMessage.sort:type_name -> proto.C2S_PassengerProfileSortChangeMessage.SortEntry
	2,  // [2:2] is the sub-list for method output_type
	2,  // [2:2] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_passenger_proto_init() }
func file_passenger_proto_init() {
	if File_passenger_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_passenger_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChangePassengerDormMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChangePassengerDormRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PassengerLevelUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PassengerLevelUpResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChangePassengerWorkMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChangePassengerWorkMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CompletePassengerPlotMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CompletePassengerPlotMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UnlockSkinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UnlockSkinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChangeSkinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChangeSkinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_TalentLevelUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_TalentLevelUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_FragMergeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_FragMergeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PassengerUnlockProfileMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PassengerUnlockProfileMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_TransPassengerMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_TransPassengerMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PassengerProfileSortChangeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_passenger_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PassengerProfileSortChangeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_passenger_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_passenger_proto_goTypes,
		DependencyIndexes: file_passenger_proto_depIdxs,
		MessageInfos:      file_passenger_proto_msgTypes,
	}.Build()
	File_passenger_proto = out.File
	file_passenger_proto_rawDesc = nil
	file_passenger_proto_goTypes = nil
	file_passenger_proto_depIdxs = nil
}
