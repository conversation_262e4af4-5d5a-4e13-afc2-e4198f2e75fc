// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: field.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 操作类型
type CeilOperationType int32

const (
	CeilOperationType_Invalid    CeilOperationType = 0 //无效操作，因为枚举必须存在0值，可以不用但一定要有
	CeilOperationType_Unlock     CeilOperationType = 1 //解锁格子
	CeilOperationType_Sow        CeilOperationType = 2 //播种
	CeilOperationType_Water      CeilOperationType = 3 //浇水
	CeilOperationType_Fertilizer CeilOperationType = 4 //施肥
	CeilOperationType_Harvest    CeilOperationType = 5 //收获
)

// Enum value maps for CeilOperationType.
var (
	CeilOperationType_name = map[int32]string{
		0: "Invalid",
		1: "Unlock",
		2: "Sow",
		3: "Water",
		4: "Fertilizer",
		5: "Harvest",
	}
	CeilOperationType_value = map[string]int32{
		"Invalid":    0,
		"Unlock":     1,
		"Sow":        2,
		"Water":      3,
		"Fertilizer": 4,
		"Harvest":    5,
	}
)

func (x CeilOperationType) Enum() *CeilOperationType {
	p := new(CeilOperationType)
	*p = x
	return p
}

func (x CeilOperationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CeilOperationType) Descriptor() protoreflect.EnumDescriptor {
	return file_field_proto_enumTypes[0].Descriptor()
}

func (CeilOperationType) Type() protoreflect.EnumType {
	return &file_field_proto_enumTypes[0]
}

func (x CeilOperationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CeilOperationType.Descriptor instead.
func (CeilOperationType) EnumDescriptor() ([]byte, []int) {
	return file_field_proto_rawDescGZIP(), []int{0}
}

// 操作结果
type CeilOperationCode int32

const (
	CeilOperationCode_Success                    CeilOperationCode = 0  //操作成功
	CeilOperationCode_CeilAlreadyUnlock          CeilOperationCode = 1  //格子已经被解锁
	CeilOperationCode_NotEnoughCostForUnlock     CeilOperationCode = 2  //解锁格子消耗不足
	CeilOperationCode_CeilNotUnlock              CeilOperationCode = 3  //格子未被解锁
	CeilOperationCode_CeilAlreadyPlant           CeilOperationCode = 4  //格子已经有作物
	CeilOperationCode_CeilPlantIdError           CeilOperationCode = 5  //格子类型与作物类型不匹配
	CeilOperationCode_CeilNeedNotWater           CeilOperationCode = 6  //格子不需要浇水(没有作物or作物成熟)
	CeilOperationCode_NotEnoughCostForWater      CeilOperationCode = 7  //水能不足，无法浇水
	CeilOperationCode_CeilNeedNotFertilizer      CeilOperationCode = 8  //格子不需要施肥(没有作物or作物成熟)
	CeilOperationCode_NotEnoughCostForFertilizer CeilOperationCode = 9  //肥料不足
	CeilOperationCode_CeilNotHasPlanet           CeilOperationCode = 10 //格子没有作物，无法收获
	CeilOperationCode_CeilCanNotHarvest          CeilOperationCode = 11 //作物未成熟，无法收获
	CeilOperationCode_NotEnoughCostForPlant      CeilOperationCode = 12 //种子不足，种植失败
	CeilOperationCode_CeilMax                    CeilOperationCode = 13 //格子数量达到上限，解锁失败
	CeilOperationCode_SureCeilSort               CeilOperationCode = 14 //格子数量解锁顺序不对
)

// Enum value maps for CeilOperationCode.
var (
	CeilOperationCode_name = map[int32]string{
		0:  "Success",
		1:  "CeilAlreadyUnlock",
		2:  "NotEnoughCostForUnlock",
		3:  "CeilNotUnlock",
		4:  "CeilAlreadyPlant",
		5:  "CeilPlantIdError",
		6:  "CeilNeedNotWater",
		7:  "NotEnoughCostForWater",
		8:  "CeilNeedNotFertilizer",
		9:  "NotEnoughCostForFertilizer",
		10: "CeilNotHasPlanet",
		11: "CeilCanNotHarvest",
		12: "NotEnoughCostForPlant",
		13: "CeilMax",
		14: "SureCeilSort",
	}
	CeilOperationCode_value = map[string]int32{
		"Success":                    0,
		"CeilAlreadyUnlock":          1,
		"NotEnoughCostForUnlock":     2,
		"CeilNotUnlock":              3,
		"CeilAlreadyPlant":           4,
		"CeilPlantIdError":           5,
		"CeilNeedNotWater":           6,
		"NotEnoughCostForWater":      7,
		"CeilNeedNotFertilizer":      8,
		"NotEnoughCostForFertilizer": 9,
		"CeilNotHasPlanet":           10,
		"CeilCanNotHarvest":          11,
		"NotEnoughCostForPlant":      12,
		"CeilMax":                    13,
		"SureCeilSort":               14,
	}
)

func (x CeilOperationCode) Enum() *CeilOperationCode {
	p := new(CeilOperationCode)
	*p = x
	return p
}

func (x CeilOperationCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CeilOperationCode) Descriptor() protoreflect.EnumDescriptor {
	return file_field_proto_enumTypes[1].Descriptor()
}

func (CeilOperationCode) Type() protoreflect.EnumType {
	return &file_field_proto_enumTypes[1]
}

func (x CeilOperationCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CeilOperationCode.Descriptor instead.
func (CeilOperationCode) EnumDescriptor() ([]byte, []int) {
	return file_field_proto_rawDescGZIP(), []int{1}
}

// After are structs.
// After are messages.
type C2S_CeilOperationMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                  //顺位id
	Type       CeilOperationType `protobuf:"varint,2,opt,name=type,proto3,enum=proto.CeilOperationType" json:"type,omitempty"` //操作类型
	PowerValue int32             `protobuf:"varint,3,opt,name=powerValue,proto3" json:"powerValue,omitempty"`                  //
}

func (x *C2S_CeilOperationMessage) Reset() {
	*x = C2S_CeilOperationMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_field_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CeilOperationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CeilOperationMessage) ProtoMessage() {}

func (x *C2S_CeilOperationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_field_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CeilOperationMessage.ProtoReflect.Descriptor instead.
func (*C2S_CeilOperationMessage) Descriptor() ([]byte, []int) {
	return file_field_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_CeilOperationMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_CeilOperationMessage) GetType() CeilOperationType {
	if x != nil {
		return x.Type
	}
	return CeilOperationType_Invalid
}

func (x *C2S_CeilOperationMessage) GetPowerValue() int32 {
	if x != nil {
		return x.PowerValue
	}
	return 0
}

type S2C_CeilOperationMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    CeilOperationCode `protobuf:"varint,1,opt,name=code,proto3,enum=proto.CeilOperationCode" json:"code,omitempty"` //
	Data    *FieldCeil        `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`                               //操作之后的格子数据
	Rewards []*Condition      `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`                         //收获奖励数据
}

func (x *S2C_CeilOperationMessage) Reset() {
	*x = S2C_CeilOperationMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_field_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CeilOperationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CeilOperationMessage) ProtoMessage() {}

func (x *S2C_CeilOperationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_field_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CeilOperationMessage.ProtoReflect.Descriptor instead.
func (*S2C_CeilOperationMessage) Descriptor() ([]byte, []int) {
	return file_field_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_CeilOperationMessage) GetCode() CeilOperationCode {
	if x != nil {
		return x.Code
	}
	return CeilOperationCode_Success
}

func (x *S2C_CeilOperationMessage) GetData() *FieldCeil {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *S2C_CeilOperationMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_CeilSyncMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //顺位id
}

func (x *C2S_CeilSyncMessage) Reset() {
	*x = C2S_CeilSyncMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_field_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CeilSyncMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CeilSyncMessage) ProtoMessage() {}

func (x *C2S_CeilSyncMessage) ProtoReflect() protoreflect.Message {
	mi := &file_field_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CeilSyncMessage.ProtoReflect.Descriptor instead.
func (*C2S_CeilSyncMessage) Descriptor() ([]byte, []int) {
	return file_field_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_CeilSyncMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_CeilSyncMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Data *FieldCeil `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`  //格子数据
}

func (x *S2C_CeilSyncMessage) Reset() {
	*x = S2C_CeilSyncMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_field_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CeilSyncMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CeilSyncMessage) ProtoMessage() {}

func (x *S2C_CeilSyncMessage) ProtoReflect() protoreflect.Message {
	mi := &file_field_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CeilSyncMessage.ProtoReflect.Descriptor instead.
func (*S2C_CeilSyncMessage) Descriptor() ([]byte, []int) {
	return file_field_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_CeilSyncMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_CeilSyncMessage) GetData() *FieldCeil {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_field_proto protoreflect.FileDescriptor

var file_field_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x78, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x65, 0x69, 0x6c, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x65, 0x69, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x9a, 0x01, 0x0a,
	0x18, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x65, 0x69, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x65, 0x69, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x25, 0x0a, 0x13, 0x43, 0x32, 0x53,
	0x5f, 0x43, 0x65, 0x69, 0x6c, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x4f, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x65, 0x69, 0x6c, 0x53, 0x79, 0x6e, 0x63,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x65, 0x69, 0x6c, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x2a, 0x5d, 0x0a, 0x11, 0x43, 0x65, 0x69, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x10, 0x01, 0x12,
	0x07, 0x0a, 0x03, 0x53, 0x6f, 0x77, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x57, 0x61, 0x74, 0x65,
	0x72, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x65, 0x72, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65,
	0x72, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x48, 0x61, 0x72, 0x76, 0x65, 0x73, 0x74, 0x10, 0x05,
	0x2a, 0xe5, 0x02, 0x0a, 0x11, 0x43, 0x65, 0x69, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x65, 0x69, 0x6c, 0x41, 0x6c, 0x72, 0x65, 0x61,
	0x64, 0x79, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4e, 0x6f,
	0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x43, 0x6f, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x55, 0x6e,
	0x6c, 0x6f, 0x63, 0x6b, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x65, 0x69, 0x6c, 0x4e, 0x6f,
	0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x65, 0x69,
	0x6c, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x10, 0x04, 0x12,
	0x14, 0x0a, 0x10, 0x43, 0x65, 0x69, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x65, 0x69, 0x6c, 0x4e, 0x65, 0x65,
	0x64, 0x4e, 0x6f, 0x74, 0x57, 0x61, 0x74, 0x65, 0x72, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x4e,
	0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x43, 0x6f, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x57,
	0x61, 0x74, 0x65, 0x72, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x65, 0x69, 0x6c, 0x4e, 0x65,
	0x65, 0x64, 0x4e, 0x6f, 0x74, 0x46, 0x65, 0x72, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65, 0x72, 0x10,
	0x08, 0x12, 0x1e, 0x0a, 0x1a, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x43, 0x6f,
	0x73, 0x74, 0x46, 0x6f, 0x72, 0x46, 0x65, 0x72, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65, 0x72, 0x10,
	0x09, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x65, 0x69, 0x6c, 0x4e, 0x6f, 0x74, 0x48, 0x61, 0x73, 0x50,
	0x6c, 0x61, 0x6e, 0x65, 0x74, 0x10, 0x0a, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x65, 0x69, 0x6c, 0x43,
	0x61, 0x6e, 0x4e, 0x6f, 0x74, 0x48, 0x61, 0x72, 0x76, 0x65, 0x73, 0x74, 0x10, 0x0b, 0x12, 0x19,
	0x0a, 0x15, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x43, 0x6f, 0x73, 0x74, 0x46,
	0x6f, 0x72, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x10, 0x0c, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x65, 0x69,
	0x6c, 0x4d, 0x61, 0x78, 0x10, 0x0d, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x65, 0x43, 0x65,
	0x69, 0x6c, 0x53, 0x6f, 0x72, 0x74, 0x10, 0x0e, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_field_proto_rawDescOnce sync.Once
	file_field_proto_rawDescData = file_field_proto_rawDesc
)

func file_field_proto_rawDescGZIP() []byte {
	file_field_proto_rawDescOnce.Do(func() {
		file_field_proto_rawDescData = protoimpl.X.CompressGZIP(file_field_proto_rawDescData)
	})
	return file_field_proto_rawDescData
}

var file_field_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_field_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_field_proto_goTypes = []interface{}{
	(CeilOperationType)(0),           // 0: proto.CeilOperationType
	(CeilOperationCode)(0),           // 1: proto.CeilOperationCode
	(*C2S_CeilOperationMessage)(nil), // 2: proto.C2S_CeilOperationMessage
	(*S2C_CeilOperationMessage)(nil), // 3: proto.S2C_CeilOperationMessage
	(*C2S_CeilSyncMessage)(nil),      // 4: proto.C2S_CeilSyncMessage
	(*S2C_CeilSyncMessage)(nil),      // 5: proto.S2C_CeilSyncMessage
	(*FieldCeil)(nil),                // 6: proto.FieldCeil
	(*Condition)(nil),                // 7: proto.Condition
}
var file_field_proto_depIdxs = []int32{
	0, // 0: proto.C2S_CeilOperationMessage.type:type_name -> proto.CeilOperationType
	1, // 1: proto.S2C_CeilOperationMessage.code:type_name -> proto.CeilOperationCode
	6, // 2: proto.S2C_CeilOperationMessage.data:type_name -> proto.FieldCeil
	7, // 3: proto.S2C_CeilOperationMessage.rewards:type_name -> proto.Condition
	6, // 4: proto.S2C_CeilSyncMessage.data:type_name -> proto.FieldCeil
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_field_proto_init() }
func file_field_proto_init() {
	if File_field_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_field_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CeilOperationMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_field_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CeilOperationMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_field_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CeilSyncMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_field_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CeilSyncMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_field_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_field_proto_goTypes,
		DependencyIndexes: file_field_proto_depIdxs,
		EnumInfos:         file_field_proto_enumTypes,
		MessageInfos:      file_field_proto_msgTypes,
	}.Build()
	File_field_proto = out.File
	file_field_proto_rawDesc = nil
	file_field_proto_goTypes = nil
	file_field_proto_depIdxs = nil
}
