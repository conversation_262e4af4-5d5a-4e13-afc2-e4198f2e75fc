// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: instance.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_InstanceFightMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level int32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"` //副本id
}

func (x *C2S_InstanceFightMessage) Reset() {
	*x = C2S_InstanceFightMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_instance_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_InstanceFightMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_InstanceFightMessage) ProtoMessage() {}

func (x *C2S_InstanceFightMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instance_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_InstanceFightMessage.ProtoReflect.Descriptor instead.
func (*C2S_InstanceFightMessage) Descriptor() ([]byte, []int) {
	return file_instance_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_InstanceFightMessage) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type S2C_InstanceFightMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //1要挑战的不是最新关卡,2要挑战的关卡不存在
	Rewards []*Condition `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"` //奖励
}

func (x *S2C_InstanceFightMessage) Reset() {
	*x = S2C_InstanceFightMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_instance_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_InstanceFightMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_InstanceFightMessage) ProtoMessage() {}

func (x *S2C_InstanceFightMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instance_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_InstanceFightMessage.ProtoReflect.Descriptor instead.
func (*S2C_InstanceFightMessage) Descriptor() ([]byte, []int) {
	return file_instance_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_InstanceFightMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_InstanceFightMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_SyncInstanceMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SyncInstanceMessage) Reset() {
	*x = C2S_SyncInstanceMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_instance_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncInstanceMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncInstanceMessage) ProtoMessage() {}

func (x *C2S_SyncInstanceMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instance_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncInstanceMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncInstanceMessage) Descriptor() ([]byte, []int) {
	return file_instance_proto_rawDescGZIP(), []int{2}
}

type S2C_SyncInstanceMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Data *Instance `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`  //
}

func (x *S2C_SyncInstanceMessage) Reset() {
	*x = S2C_SyncInstanceMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_instance_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncInstanceMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncInstanceMessage) ProtoMessage() {}

func (x *S2C_SyncInstanceMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instance_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncInstanceMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncInstanceMessage) Descriptor() ([]byte, []int) {
	return file_instance_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_SyncInstanceMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncInstanceMessage) GetData() *Instance {
	if x != nil {
		return x.Data
	}
	return nil
}

type C2S_UnlockInstanceMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_UnlockInstanceMessage) Reset() {
	*x = C2S_UnlockInstanceMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_instance_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UnlockInstanceMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UnlockInstanceMessage) ProtoMessage() {}

func (x *C2S_UnlockInstanceMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instance_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UnlockInstanceMessage.ProtoReflect.Descriptor instead.
func (*C2S_UnlockInstanceMessage) Descriptor() ([]byte, []int) {
	return file_instance_proto_rawDescGZIP(), []int{4}
}

type S2C_UnlockInstanceMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_UnlockInstanceMessage) Reset() {
	*x = S2C_UnlockInstanceMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_instance_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UnlockInstanceMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UnlockInstanceMessage) ProtoMessage() {}

func (x *S2C_UnlockInstanceMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instance_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UnlockInstanceMessage.ProtoReflect.Descriptor instead.
func (*S2C_UnlockInstanceMessage) Descriptor() ([]byte, []int) {
	return file_instance_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_UnlockInstanceMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_CompleteInstancePuzzleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_CompleteInstancePuzzleMessage) Reset() {
	*x = C2S_CompleteInstancePuzzleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_instance_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CompleteInstancePuzzleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CompleteInstancePuzzleMessage) ProtoMessage() {}

func (x *C2S_CompleteInstancePuzzleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instance_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CompleteInstancePuzzleMessage.ProtoReflect.Descriptor instead.
func (*C2S_CompleteInstancePuzzleMessage) Descriptor() ([]byte, []int) {
	return file_instance_proto_rawDescGZIP(), []int{6}
}

type S2C_CompleteInstancePuzzleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_CompleteInstancePuzzleMessage) Reset() {
	*x = S2C_CompleteInstancePuzzleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_instance_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CompleteInstancePuzzleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CompleteInstancePuzzleMessage) ProtoMessage() {}

func (x *S2C_CompleteInstancePuzzleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instance_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CompleteInstancePuzzleMessage.ProtoReflect.Descriptor instead.
func (*S2C_CompleteInstancePuzzleMessage) Descriptor() ([]byte, []int) {
	return file_instance_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_CompleteInstancePuzzleMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_instance_proto protoreflect.FileDescriptor

var file_instance_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x30, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x67, 0x68, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x5a, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x67, 0x68, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x52,
	0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x1b, 0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x2f, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x23, 0x0a, 0x21, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x75, 0x7a, 0x7a, 0x6c, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x37, 0x0a, 0x21, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x75, 0x7a,
	0x7a, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06,
	0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_instance_proto_rawDescOnce sync.Once
	file_instance_proto_rawDescData = file_instance_proto_rawDesc
)

func file_instance_proto_rawDescGZIP() []byte {
	file_instance_proto_rawDescOnce.Do(func() {
		file_instance_proto_rawDescData = protoimpl.X.CompressGZIP(file_instance_proto_rawDescData)
	})
	return file_instance_proto_rawDescData
}

var file_instance_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_instance_proto_goTypes = []interface{}{
	(*C2S_InstanceFightMessage)(nil),          // 0: proto.C2S_InstanceFightMessage
	(*S2C_InstanceFightMessage)(nil),          // 1: proto.S2C_InstanceFightMessage
	(*C2S_SyncInstanceMessage)(nil),           // 2: proto.C2S_SyncInstanceMessage
	(*S2C_SyncInstanceMessage)(nil),           // 3: proto.S2C_SyncInstanceMessage
	(*C2S_UnlockInstanceMessage)(nil),         // 4: proto.C2S_UnlockInstanceMessage
	(*S2C_UnlockInstanceMessage)(nil),         // 5: proto.S2C_UnlockInstanceMessage
	(*C2S_CompleteInstancePuzzleMessage)(nil), // 6: proto.C2S_CompleteInstancePuzzleMessage
	(*S2C_CompleteInstancePuzzleMessage)(nil), // 7: proto.S2C_CompleteInstancePuzzleMessage
	(*Condition)(nil),                         // 8: proto.Condition
	(*Instance)(nil),                          // 9: proto.Instance
}
var file_instance_proto_depIdxs = []int32{
	8, // 0: proto.S2C_InstanceFightMessage.rewards:type_name -> proto.Condition
	9, // 1: proto.S2C_SyncInstanceMessage.data:type_name -> proto.Instance
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_instance_proto_init() }
func file_instance_proto_init() {
	if File_instance_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_instance_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_InstanceFightMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_instance_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_InstanceFightMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_instance_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncInstanceMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_instance_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncInstanceMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_instance_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UnlockInstanceMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_instance_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UnlockInstanceMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_instance_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CompleteInstancePuzzleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_instance_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CompleteInstancePuzzleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_instance_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instance_proto_goTypes,
		DependencyIndexes: file_instance_proto_depIdxs,
		MessageInfos:      file_instance_proto_msgTypes,
	}.Build()
	File_instance_proto = out.File
	file_instance_proto_rawDesc = nil
	file_instance_proto_goTypes = nil
	file_instance_proto_depIdxs = nil
}
