// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: planet.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_TrainNavigationMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	Plus     bool  `protobuf:"varint,2,opt,name=plus,proto3" json:"plus,omitempty"`         //使用次元晶石
}

func (x *C2S_TrainNavigationMessage) Reset() {
	*x = C2S_TrainNavigationMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_TrainNavigationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_TrainNavigationMessage) ProtoMessage() {}

func (x *C2S_TrainNavigationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_TrainNavigationMessage.ProtoReflect.Descriptor instead.
func (*C2S_TrainNavigationMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_TrainNavigationMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_TrainNavigationMessage) GetPlus() bool {
	if x != nil {
		return x.Plus
	}
	return false
}

type S2C_TrainNavigationResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //1当前正在航行,2该星球已经探索完毕,3星球id配置读取错误,4星球前置条件未达到,5次元晶石不足
	Time int32 `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"` //航行结束剩余时间>=0
}

func (x *S2C_TrainNavigationResultMessage) Reset() {
	*x = S2C_TrainNavigationResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_TrainNavigationResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_TrainNavigationResultMessage) ProtoMessage() {}

func (x *S2C_TrainNavigationResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_TrainNavigationResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_TrainNavigationResultMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_TrainNavigationResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_TrainNavigationResultMessage) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

type C2S_GetPlanetMoveSurplusTimeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_GetPlanetMoveSurplusTimeMessage) Reset() {
	*x = C2S_GetPlanetMoveSurplusTimeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetPlanetMoveSurplusTimeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetPlanetMoveSurplusTimeMessage) ProtoMessage() {}

func (x *C2S_GetPlanetMoveSurplusTimeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetPlanetMoveSurplusTimeMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetPlanetMoveSurplusTimeMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{2}
}

type S2C_GetPlanetMoveSurplusTimeRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
	Time int32 `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"` //剩余时间
}

func (x *S2C_GetPlanetMoveSurplusTimeRespMessage) Reset() {
	*x = S2C_GetPlanetMoveSurplusTimeRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetPlanetMoveSurplusTimeRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetPlanetMoveSurplusTimeRespMessage) ProtoMessage() {}

func (x *S2C_GetPlanetMoveSurplusTimeRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetPlanetMoveSurplusTimeRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetPlanetMoveSurplusTimeRespMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_GetPlanetMoveSurplusTimeRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetPlanetMoveSurplusTimeRespMessage) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

type C2S_LandPlanetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
}

func (x *C2S_LandPlanetMessage) Reset() {
	*x = C2S_LandPlanetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LandPlanetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LandPlanetMessage) ProtoMessage() {}

func (x *C2S_LandPlanetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LandPlanetMessage.ProtoReflect.Descriptor instead.
func (*C2S_LandPlanetMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_LandPlanetMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type S2C_LandPlanetRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //1正在航行中,时间没到
}

func (x *S2C_LandPlanetRespMessage) Reset() {
	*x = S2C_LandPlanetRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_LandPlanetRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_LandPlanetRespMessage) ProtoMessage() {}

func (x *S2C_LandPlanetRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_LandPlanetRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_LandPlanetRespMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_LandPlanetRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ChapterPassMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	MapId    int32 `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`       //地图id
	NodeId   int32 `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`     //节点id
}

func (x *C2S_ChapterPassMessage) Reset() {
	*x = C2S_ChapterPassMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChapterPassMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChapterPassMessage) ProtoMessage() {}

func (x *C2S_ChapterPassMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChapterPassMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChapterPassMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_ChapterPassMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_ChapterPassMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_ChapterPassMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type S2C_ChapterPassResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //
	MapId   int32        `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`    //当前地图id,如果是-1就代表星球探索完毕
	NextId  int32        `protobuf:"varint,3,opt,name=nextId,proto3" json:"nextId,omitempty"`  //下一个节点的id,可能为空(可能会涉及到当前地图上限，需要切换地图)
	Rewards []*Condition `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"` //奖励
}

func (x *S2C_ChapterPassResultMessage) Reset() {
	*x = S2C_ChapterPassResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChapterPassResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChapterPassResultMessage) ProtoMessage() {}

func (x *S2C_ChapterPassResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChapterPassResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChapterPassResultMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_ChapterPassResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ChapterPassResultMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2C_ChapterPassResultMessage) GetNextId() int32 {
	if x != nil {
		return x.NextId
	}
	return 0
}

func (x *S2C_ChapterPassResultMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_PassBranchPlanetNodeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BranchId string `protobuf:"bytes,1,opt,name=branchId,proto3" json:"branchId,omitempty"` //支线id
	MapId    int32  `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`      //地图id
	NodeId   int32  `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`    //节点id
}

func (x *C2S_PassBranchPlanetNodeMessage) Reset() {
	*x = C2S_PassBranchPlanetNodeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PassBranchPlanetNodeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PassBranchPlanetNodeMessage) ProtoMessage() {}

func (x *C2S_PassBranchPlanetNodeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PassBranchPlanetNodeMessage.ProtoReflect.Descriptor instead.
func (*C2S_PassBranchPlanetNodeMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{8}
}

func (x *C2S_PassBranchPlanetNodeMessage) GetBranchId() string {
	if x != nil {
		return x.BranchId
	}
	return ""
}

func (x *C2S_PassBranchPlanetNodeMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_PassBranchPlanetNodeMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type S2C_PassBranchPlanetNodeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //
	MapId   int32        `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`    //当前地图id,如果是-1就代表星球探索完毕
	NextId  int32        `protobuf:"varint,3,opt,name=nextId,proto3" json:"nextId,omitempty"`  //下一个节点的id,可能为空(可能会涉及到当前地图上限，需要切换地图)
	Rewards []*Condition `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"` //奖励
}

func (x *S2C_PassBranchPlanetNodeMessage) Reset() {
	*x = S2C_PassBranchPlanetNodeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PassBranchPlanetNodeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PassBranchPlanetNodeMessage) ProtoMessage() {}

func (x *S2C_PassBranchPlanetNodeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PassBranchPlanetNodeMessage.ProtoReflect.Descriptor instead.
func (*S2C_PassBranchPlanetNodeMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_PassBranchPlanetNodeMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_PassBranchPlanetNodeMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2C_PassBranchPlanetNodeMessage) GetNextId() int32 {
	if x != nil {
		return x.NextId
	}
	return 0
}

func (x *S2C_PassBranchPlanetNodeMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_ClaimBranchPlanetNodeRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BranchId string `protobuf:"bytes,1,opt,name=branchId,proto3" json:"branchId,omitempty"` //支线id
	MapId    int32  `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`      //地图id
	NodeId   int32  `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`    //节点id
	Index    int32  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`      //奖励下标
}

func (x *C2S_ClaimBranchPlanetNodeRewardMessage) Reset() {
	*x = C2S_ClaimBranchPlanetNodeRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ClaimBranchPlanetNodeRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ClaimBranchPlanetNodeRewardMessage) ProtoMessage() {}

func (x *C2S_ClaimBranchPlanetNodeRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ClaimBranchPlanetNodeRewardMessage.ProtoReflect.Descriptor instead.
func (*C2S_ClaimBranchPlanetNodeRewardMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{10}
}

func (x *C2S_ClaimBranchPlanetNodeRewardMessage) GetBranchId() string {
	if x != nil {
		return x.BranchId
	}
	return ""
}

func (x *C2S_ClaimBranchPlanetNodeRewardMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_ClaimBranchPlanetNodeRewardMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *C2S_ClaimBranchPlanetNodeRewardMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type S2C_ClaimBranchPlanetNodeRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_ClaimBranchPlanetNodeRewardMessage) Reset() {
	*x = S2C_ClaimBranchPlanetNodeRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ClaimBranchPlanetNodeRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ClaimBranchPlanetNodeRewardMessage) ProtoMessage() {}

func (x *S2C_ClaimBranchPlanetNodeRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ClaimBranchPlanetNodeRewardMessage.ProtoReflect.Descriptor instead.
func (*S2C_ClaimBranchPlanetNodeRewardMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_ClaimBranchPlanetNodeRewardMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_CancelMoveToPlanetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_CancelMoveToPlanetMessage) Reset() {
	*x = C2S_CancelMoveToPlanetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CancelMoveToPlanetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CancelMoveToPlanetMessage) ProtoMessage() {}

func (x *C2S_CancelMoveToPlanetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CancelMoveToPlanetMessage.ProtoReflect.Descriptor instead.
func (*C2S_CancelMoveToPlanetMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{12}
}

type S2C_CancelMoveToPlanetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_CancelMoveToPlanetMessage) Reset() {
	*x = S2C_CancelMoveToPlanetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CancelMoveToPlanetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CancelMoveToPlanetMessage) ProtoMessage() {}

func (x *S2C_CancelMoveToPlanetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CancelMoveToPlanetMessage.ProtoReflect.Descriptor instead.
func (*S2C_CancelMoveToPlanetMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{13}
}

func (x *S2C_CancelMoveToPlanetMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_UnlockProfileMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	Id       int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`             //贴纸id
	Index    int32 `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`       //位置
}

func (x *C2S_UnlockProfileMessage) Reset() {
	*x = C2S_UnlockProfileMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UnlockProfileMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UnlockProfileMessage) ProtoMessage() {}

func (x *C2S_UnlockProfileMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UnlockProfileMessage.ProtoReflect.Descriptor instead.
func (*C2S_UnlockProfileMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{14}
}

func (x *C2S_UnlockProfileMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_UnlockProfileMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_UnlockProfileMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type S2C_UnlockProfileMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_UnlockProfileMessage) Reset() {
	*x = S2C_UnlockProfileMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UnlockProfileMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UnlockProfileMessage) ProtoMessage() {}

func (x *S2C_UnlockProfileMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UnlockProfileMessage.ProtoReflect.Descriptor instead.
func (*S2C_UnlockProfileMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{15}
}

func (x *S2C_UnlockProfileMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ProfileCollectRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	Area     int32 `protobuf:"varint,2,opt,name=area,proto3" json:"area,omitempty"`         //星球区域
	Step     int32 `protobuf:"varint,3,opt,name=step,proto3" json:"step,omitempty"`         //进度
}

func (x *C2S_ProfileCollectRewardMessage) Reset() {
	*x = C2S_ProfileCollectRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ProfileCollectRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ProfileCollectRewardMessage) ProtoMessage() {}

func (x *C2S_ProfileCollectRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ProfileCollectRewardMessage.ProtoReflect.Descriptor instead.
func (*C2S_ProfileCollectRewardMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{16}
}

func (x *C2S_ProfileCollectRewardMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_ProfileCollectRewardMessage) GetArea() int32 {
	if x != nil {
		return x.Area
	}
	return 0
}

func (x *C2S_ProfileCollectRewardMessage) GetStep() int32 {
	if x != nil {
		return x.Step
	}
	return 0
}

type S2C_ProfileCollectRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_ProfileCollectRewardMessage) Reset() {
	*x = S2C_ProfileCollectRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ProfileCollectRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ProfileCollectRewardMessage) ProtoMessage() {}

func (x *S2C_ProfileCollectRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ProfileCollectRewardMessage.ProtoReflect.Descriptor instead.
func (*S2C_ProfileCollectRewardMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{17}
}

func (x *S2C_ProfileCollectRewardMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_PlanetProfileSortChangeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32           `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"`                                                                                  //星球id
	Sort     map[int32]int32 `protobuf:"bytes,2,rep,name=sort,proto3" json:"sort,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //排序数据
}

func (x *C2S_PlanetProfileSortChangeMessage) Reset() {
	*x = C2S_PlanetProfileSortChangeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PlanetProfileSortChangeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PlanetProfileSortChangeMessage) ProtoMessage() {}

func (x *C2S_PlanetProfileSortChangeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PlanetProfileSortChangeMessage.ProtoReflect.Descriptor instead.
func (*C2S_PlanetProfileSortChangeMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{18}
}

func (x *C2S_PlanetProfileSortChangeMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_PlanetProfileSortChangeMessage) GetSort() map[int32]int32 {
	if x != nil {
		return x.Sort
	}
	return nil
}

type S2C_PlanetProfileSortChangeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
}

func (x *S2C_PlanetProfileSortChangeMessage) Reset() {
	*x = S2C_PlanetProfileSortChangeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PlanetProfileSortChangeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PlanetProfileSortChangeMessage) ProtoMessage() {}

func (x *S2C_PlanetProfileSortChangeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PlanetProfileSortChangeMessage.ProtoReflect.Descriptor instead.
func (*S2C_PlanetProfileSortChangeMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{19}
}

func (x *S2C_PlanetProfileSortChangeMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ChapterPassRandomBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	MapId    int32 `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`       //地图id
	NodeId   int32 `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`     //节点id
}

func (x *C2S_ChapterPassRandomBoxMessage) Reset() {
	*x = C2S_ChapterPassRandomBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChapterPassRandomBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChapterPassRandomBoxMessage) ProtoMessage() {}

func (x *C2S_ChapterPassRandomBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChapterPassRandomBoxMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChapterPassRandomBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{20}
}

func (x *C2S_ChapterPassRandomBoxMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_ChapterPassRandomBoxMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_ChapterPassRandomBoxMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type S2C_ChapterPassRandomBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //
	MapId   int32        `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`    //当前地图id,如果是-1就代表星球探索完毕
	NextId  int32        `protobuf:"varint,3,opt,name=nextId,proto3" json:"nextId,omitempty"`  //下一个节点的id,可能为空(可能会涉及到当前地图上限，需要切换地图)
	Rewards []*Condition `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"` //奖励
}

func (x *S2C_ChapterPassRandomBoxMessage) Reset() {
	*x = S2C_ChapterPassRandomBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChapterPassRandomBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChapterPassRandomBoxMessage) ProtoMessage() {}

func (x *S2C_ChapterPassRandomBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChapterPassRandomBoxMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChapterPassRandomBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{21}
}

func (x *S2C_ChapterPassRandomBoxMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ChapterPassRandomBoxMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2C_ChapterPassRandomBoxMessage) GetNextId() int32 {
	if x != nil {
		return x.NextId
	}
	return 0
}

func (x *S2C_ChapterPassRandomBoxMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_ChapterStartTimeLimitBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	MapId    int32 `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`       //地图id
	NodeId   int32 `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`     //节点id
}

func (x *C2S_ChapterStartTimeLimitBoxMessage) Reset() {
	*x = C2S_ChapterStartTimeLimitBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChapterStartTimeLimitBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChapterStartTimeLimitBoxMessage) ProtoMessage() {}

func (x *C2S_ChapterStartTimeLimitBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChapterStartTimeLimitBoxMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChapterStartTimeLimitBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{22}
}

func (x *C2S_ChapterStartTimeLimitBoxMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_ChapterStartTimeLimitBoxMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_ChapterStartTimeLimitBoxMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type S2C_ChapterStartTimeLimitBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`               //
	SurplusTime int32 `protobuf:"varint,2,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
}

func (x *S2C_ChapterStartTimeLimitBoxMessage) Reset() {
	*x = S2C_ChapterStartTimeLimitBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChapterStartTimeLimitBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChapterStartTimeLimitBoxMessage) ProtoMessage() {}

func (x *S2C_ChapterStartTimeLimitBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChapterStartTimeLimitBoxMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChapterStartTimeLimitBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{23}
}

func (x *S2C_ChapterStartTimeLimitBoxMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ChapterStartTimeLimitBoxMessage) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

type C2S_ChapterSyncTimeLimitBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	MapId    int32 `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`       //地图id
	NodeId   int32 `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`     //节点id
	Clicks   int32 `protobuf:"varint,4,opt,name=clicks,proto3" json:"clicks,omitempty"`     //点击次数
}

func (x *C2S_ChapterSyncTimeLimitBoxMessage) Reset() {
	*x = C2S_ChapterSyncTimeLimitBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChapterSyncTimeLimitBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChapterSyncTimeLimitBoxMessage) ProtoMessage() {}

func (x *C2S_ChapterSyncTimeLimitBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChapterSyncTimeLimitBoxMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChapterSyncTimeLimitBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{24}
}

func (x *C2S_ChapterSyncTimeLimitBoxMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_ChapterSyncTimeLimitBoxMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_ChapterSyncTimeLimitBoxMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *C2S_ChapterSyncTimeLimitBoxMessage) GetClicks() int32 {
	if x != nil {
		return x.Clicks
	}
	return 0
}

type S2C_ChapterSyncTimeLimitBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`               //
	State       int32        `protobuf:"varint,2,opt,name=state,proto3" json:"state,omitempty"`             //状态
	SurplusTime int32        `protobuf:"varint,3,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
	Rewards     []*Condition `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"`          //奖励
}

func (x *S2C_ChapterSyncTimeLimitBoxMessage) Reset() {
	*x = S2C_ChapterSyncTimeLimitBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChapterSyncTimeLimitBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChapterSyncTimeLimitBoxMessage) ProtoMessage() {}

func (x *S2C_ChapterSyncTimeLimitBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChapterSyncTimeLimitBoxMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChapterSyncTimeLimitBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{25}
}

func (x *S2C_ChapterSyncTimeLimitBoxMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ChapterSyncTimeLimitBoxMessage) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *S2C_ChapterSyncTimeLimitBoxMessage) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

func (x *S2C_ChapterSyncTimeLimitBoxMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_ChapterPassTimeLimitBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	MapId    int32 `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`       //地图id
	NodeId   int32 `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`     //节点id
}

func (x *C2S_ChapterPassTimeLimitBoxMessage) Reset() {
	*x = C2S_ChapterPassTimeLimitBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChapterPassTimeLimitBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChapterPassTimeLimitBoxMessage) ProtoMessage() {}

func (x *C2S_ChapterPassTimeLimitBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChapterPassTimeLimitBoxMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChapterPassTimeLimitBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{26}
}

func (x *C2S_ChapterPassTimeLimitBoxMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_ChapterPassTimeLimitBoxMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_ChapterPassTimeLimitBoxMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type S2C_ChapterPassTimeLimitBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_ChapterPassTimeLimitBoxMessage) Reset() {
	*x = S2C_ChapterPassTimeLimitBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChapterPassTimeLimitBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChapterPassTimeLimitBoxMessage) ProtoMessage() {}

func (x *S2C_ChapterPassTimeLimitBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChapterPassTimeLimitBoxMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChapterPassTimeLimitBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{27}
}

func (x *S2C_ChapterPassTimeLimitBoxMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ChapterPassMonsterBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	MapId    int32 `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`       //地图id
	NodeId   int32 `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`     //节点id
	Num      int32 `protobuf:"varint,4,opt,name=num,proto3" json:"num,omitempty"`           //受击次数
}

func (x *C2S_ChapterPassMonsterBoxMessage) Reset() {
	*x = C2S_ChapterPassMonsterBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChapterPassMonsterBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChapterPassMonsterBoxMessage) ProtoMessage() {}

func (x *C2S_ChapterPassMonsterBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChapterPassMonsterBoxMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChapterPassMonsterBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{28}
}

func (x *C2S_ChapterPassMonsterBoxMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_ChapterPassMonsterBoxMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_ChapterPassMonsterBoxMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *C2S_ChapterPassMonsterBoxMessage) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type S2C_ChapterPassMonsterBoxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //
	Rewards []*Condition `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"` //奖励
}

func (x *S2C_ChapterPassMonsterBoxMessage) Reset() {
	*x = S2C_ChapterPassMonsterBoxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChapterPassMonsterBoxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChapterPassMonsterBoxMessage) ProtoMessage() {}

func (x *S2C_ChapterPassMonsterBoxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChapterPassMonsterBoxMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChapterPassMonsterBoxMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{29}
}

func (x *S2C_ChapterPassMonsterBoxMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ChapterPassMonsterBoxMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_ChapterPassToolBlessMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	MapId    int32 `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`       //地图id
	NodeId   int32 `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`     //节点id
}

func (x *C2S_ChapterPassToolBlessMessage) Reset() {
	*x = C2S_ChapterPassToolBlessMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChapterPassToolBlessMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChapterPassToolBlessMessage) ProtoMessage() {}

func (x *C2S_ChapterPassToolBlessMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChapterPassToolBlessMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChapterPassToolBlessMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{30}
}

func (x *C2S_ChapterPassToolBlessMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_ChapterPassToolBlessMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_ChapterPassToolBlessMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type S2C_ChapterPassToolBlessMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_ChapterPassToolBlessMessage) Reset() {
	*x = S2C_ChapterPassToolBlessMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChapterPassToolBlessMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChapterPassToolBlessMessage) ProtoMessage() {}

func (x *S2C_ChapterPassToolBlessMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChapterPassToolBlessMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChapterPassToolBlessMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{31}
}

func (x *S2C_ChapterPassToolBlessMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ChapterPassRageModeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
	MapId    int32 `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`       //地图id
	NodeId   int32 `protobuf:"varint,3,opt,name=nodeId,proto3" json:"nodeId,omitempty"`     //节点id
}

func (x *C2S_ChapterPassRageModeMessage) Reset() {
	*x = C2S_ChapterPassRageModeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChapterPassRageModeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChapterPassRageModeMessage) ProtoMessage() {}

func (x *C2S_ChapterPassRageModeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChapterPassRageModeMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChapterPassRageModeMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{32}
}

func (x *C2S_ChapterPassRageModeMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_ChapterPassRageModeMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *C2S_ChapterPassRageModeMessage) GetNodeId() int32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type S2C_ChapterPassRageModeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_ChapterPassRageModeMessage) Reset() {
	*x = S2C_ChapterPassRageModeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChapterPassRageModeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChapterPassRageModeMessage) ProtoMessage() {}

func (x *S2C_ChapterPassRageModeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChapterPassRageModeMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChapterPassRageModeMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{33}
}

func (x *S2C_ChapterPassRageModeMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_DoPublicityMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id
}

func (x *C2S_DoPublicityMessage) Reset() {
	*x = C2S_DoPublicityMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_DoPublicityMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_DoPublicityMessage) ProtoMessage() {}

func (x *C2S_DoPublicityMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_DoPublicityMessage.ProtoReflect.Descriptor instead.
func (*C2S_DoPublicityMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{34}
}

func (x *C2S_DoPublicityMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type S2C_DoPublicityMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code                     int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                                         //
	Num                      int32        `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`                                           //招募人口数
	Rewards                  []*Condition `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`                                    //奖励列表
	PublicityUnGetOutputTime int32        `protobuf:"varint,4,opt,name=publicityUnGetOutputTime,proto3" json:"publicityUnGetOutputTime,omitempty"` //未领取的产出时长
}

func (x *S2C_DoPublicityMessage) Reset() {
	*x = S2C_DoPublicityMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_DoPublicityMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_DoPublicityMessage) ProtoMessage() {}

func (x *S2C_DoPublicityMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_DoPublicityMessage.ProtoReflect.Descriptor instead.
func (*S2C_DoPublicityMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{35}
}

func (x *S2C_DoPublicityMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_DoPublicityMessage) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *S2C_DoPublicityMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *S2C_DoPublicityMessage) GetPublicityUnGetOutputTime() int32 {
	if x != nil {
		return x.PublicityUnGetOutputTime
	}
	return 0
}

type C2S_GetPublicityRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球id,-1是全部
}

func (x *C2S_GetPublicityRewardMessage) Reset() {
	*x = C2S_GetPublicityRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetPublicityRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetPublicityRewardMessage) ProtoMessage() {}

func (x *C2S_GetPublicityRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetPublicityRewardMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetPublicityRewardMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{36}
}

func (x *C2S_GetPublicityRewardMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type S2C_GetPublicityRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                                                                                                //
	Rewards []*Condition    `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`                                                                                           //奖励列表
	TimeMap map[int32]int32 `protobuf:"bytes,3,rep,name=timeMap,proto3" json:"timeMap,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //时间数据
}

func (x *S2C_GetPublicityRewardMessage) Reset() {
	*x = S2C_GetPublicityRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_planet_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetPublicityRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetPublicityRewardMessage) ProtoMessage() {}

func (x *S2C_GetPublicityRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_planet_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetPublicityRewardMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetPublicityRewardMessage) Descriptor() ([]byte, []int) {
	return file_planet_proto_rawDescGZIP(), []int{37}
}

func (x *S2C_GetPublicityRewardMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetPublicityRewardMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *S2C_GetPublicityRewardMessage) GetTimeMap() map[int32]int32 {
	if x != nil {
		return x.TimeMap
	}
	return nil
}

var File_planet_proto protoreflect.FileDescriptor

var file_planet_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x4c, 0x0a, 0x1a, 0x43, 0x32, 0x53, 0x5f, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x6c, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x70, 0x6c, 0x75,
	0x73, 0x22, 0x4a, 0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x4e, 0x61,
	0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x25, 0x0a,
	0x23, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x4d, 0x6f,
	0x76, 0x65, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x51, 0x0a, 0x27, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x50,
	0x6c, 0x61, 0x6e, 0x65, 0x74, 0x4d, 0x6f, 0x76, 0x65, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x33, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x4c,
	0x61, 0x6e, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x22, 0x2f, 0x0a, 0x19,
	0x53, 0x32, 0x43, 0x5f, 0x4c, 0x61, 0x6e, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x62, 0x0a,
	0x16, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65,
	0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x22, 0x8c, 0x01, 0x0a, 0x1c, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65,
	0x72, 0x50, 0x61, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6e, 0x65, 0x78, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x65,
	0x78, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x22, 0x6b, 0x0a, 0x1f, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x42, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x8f, 0x01,
	0x0a, 0x1f, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e,
	0x65, 0x78, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x65, 0x78,
	0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22,
	0x88, 0x01, 0x0a, 0x26, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x42, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x3c, 0x0a, 0x26, 0x53, 0x32,
	0x43, 0x5f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x50, 0x6c, 0x61,
	0x6e, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x1f, 0x0a, 0x1d, 0x43, 0x32, 0x53, 0x5f,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x6f, 0x76, 0x65, 0x54, 0x6f, 0x50, 0x6c, 0x61, 0x6e,
	0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x33, 0x0a, 0x1d, 0x53, 0x32, 0x43,
	0x5f, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x6f, 0x76, 0x65, 0x54, 0x6f, 0x50, 0x6c, 0x61,
	0x6e, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x5c,
	0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x2e, 0x0a, 0x18,
	0x53, 0x32, 0x43, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x65, 0x0a, 0x1f,
	0x43, 0x32, 0x53, 0x5f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x61,
	0x72, 0x65, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x61, 0x72, 0x65, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73,
	0x74, 0x65, 0x70, 0x22, 0x35, 0x0a, 0x1f, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xc2, 0x01, 0x0a, 0x22, 0x43,
	0x32, 0x53, 0x5f, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x53, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x47, 0x0a,
	0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x1a, 0x37, 0x0a, 0x09, 0x53, 0x6f, 0x72, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x38, 0x0a, 0x22, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x6b, 0x0a, 0x1f, 0x43, 0x32, 0x53,
	0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x52, 0x61, 0x6e, 0x64,
	0x6f, 0x6d, 0x42, 0x6f, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x8f, 0x01, 0x0a, 0x1f, 0x53, 0x32, 0x43, 0x5f, 0x43,
	0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x52, 0x61, 0x6e, 0x64, 0x6f, 0x6d,
	0x42, 0x6f, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d,
	0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x6f, 0x0a, 0x23, 0x43, 0x32, 0x53, 0x5f,
	0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d,
	0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x23, 0x53, 0x32, 0x43,
	0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c,
	0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x22, 0x43, 0x32, 0x53, 0x5f, 0x43,
	0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x42, 0x6f, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x73, 0x22,
	0x9c, 0x01, 0x0a, 0x22, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x53,
	0x79, 0x6e, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x78, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x6e,
	0x0a, 0x22, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x78, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x38,
	0x0a, 0x22, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x78, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x7e, 0x0a, 0x20, 0x43, 0x32, 0x53, 0x5f,
	0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x4d, 0x6f, 0x6e, 0x73, 0x74,
	0x65, 0x72, 0x42, 0x6f, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x62, 0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f,
	0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x4d, 0x6f, 0x6e, 0x73, 0x74,
	0x65, 0x72, 0x42, 0x6f, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x6b, 0x0a, 0x1f,
	0x43, 0x32, 0x53, 0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x54,
	0x6f, 0x6f, 0x6c, 0x42, 0x6c, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d,
	0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x1f, 0x53, 0x32, 0x43,
	0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x54, 0x6f, 0x6f, 0x6c,
	0x42, 0x6c, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x6a, 0x0a, 0x1e, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50,
	0x61, 0x73, 0x73, 0x52, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d,
	0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x1e,
	0x53, 0x32, 0x43, 0x5f, 0x43, 0x68, 0x61, 0x70, 0x74, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x52,
	0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x34, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x44, 0x6f, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x22, 0xa6, 0x01, 0x0a, 0x16, 0x53, 0x32, 0x43,
	0x5f, 0x44, 0x6f, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x3a, 0x0a, 0x18, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x69,
	0x74, 0x79, 0x55, 0x6e, 0x47, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x69,
	0x74, 0x79, 0x55, 0x6e, 0x47, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x3b, 0x0a, 0x1d, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65, 0x74, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x22, 0xe8,
	0x01, 0x0a, 0x1d, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x12, 0x4b, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65,
	0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x1a, 0x3a, 0x0a,
	0x0c, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_planet_proto_rawDescOnce sync.Once
	file_planet_proto_rawDescData = file_planet_proto_rawDesc
)

func file_planet_proto_rawDescGZIP() []byte {
	file_planet_proto_rawDescOnce.Do(func() {
		file_planet_proto_rawDescData = protoimpl.X.CompressGZIP(file_planet_proto_rawDescData)
	})
	return file_planet_proto_rawDescData
}

var file_planet_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_planet_proto_goTypes = []interface{}{
	(*C2S_TrainNavigationMessage)(nil),              // 0: proto.C2S_TrainNavigationMessage
	(*S2C_TrainNavigationResultMessage)(nil),        // 1: proto.S2C_TrainNavigationResultMessage
	(*C2S_GetPlanetMoveSurplusTimeMessage)(nil),     // 2: proto.C2S_GetPlanetMoveSurplusTimeMessage
	(*S2C_GetPlanetMoveSurplusTimeRespMessage)(nil), // 3: proto.S2C_GetPlanetMoveSurplusTimeRespMessage
	(*C2S_LandPlanetMessage)(nil),                   // 4: proto.C2S_LandPlanetMessage
	(*S2C_LandPlanetRespMessage)(nil),               // 5: proto.S2C_LandPlanetRespMessage
	(*C2S_ChapterPassMessage)(nil),                  // 6: proto.C2S_ChapterPassMessage
	(*S2C_ChapterPassResultMessage)(nil),            // 7: proto.S2C_ChapterPassResultMessage
	(*C2S_PassBranchPlanetNodeMessage)(nil),         // 8: proto.C2S_PassBranchPlanetNodeMessage
	(*S2C_PassBranchPlanetNodeMessage)(nil),         // 9: proto.S2C_PassBranchPlanetNodeMessage
	(*C2S_ClaimBranchPlanetNodeRewardMessage)(nil),  // 10: proto.C2S_ClaimBranchPlanetNodeRewardMessage
	(*S2C_ClaimBranchPlanetNodeRewardMessage)(nil),  // 11: proto.S2C_ClaimBranchPlanetNodeRewardMessage
	(*C2S_CancelMoveToPlanetMessage)(nil),           // 12: proto.C2S_CancelMoveToPlanetMessage
	(*S2C_CancelMoveToPlanetMessage)(nil),           // 13: proto.S2C_CancelMoveToPlanetMessage
	(*C2S_UnlockProfileMessage)(nil),                // 14: proto.C2S_UnlockProfileMessage
	(*S2C_UnlockProfileMessage)(nil),                // 15: proto.S2C_UnlockProfileMessage
	(*C2S_ProfileCollectRewardMessage)(nil),         // 16: proto.C2S_ProfileCollectRewardMessage
	(*S2C_ProfileCollectRewardMessage)(nil),         // 17: proto.S2C_ProfileCollectRewardMessage
	(*C2S_PlanetProfileSortChangeMessage)(nil),      // 18: proto.C2S_PlanetProfileSortChangeMessage
	(*S2C_PlanetProfileSortChangeMessage)(nil),      // 19: proto.S2C_PlanetProfileSortChangeMessage
	(*C2S_ChapterPassRandomBoxMessage)(nil),         // 20: proto.C2S_ChapterPassRandomBoxMessage
	(*S2C_ChapterPassRandomBoxMessage)(nil),         // 21: proto.S2C_ChapterPassRandomBoxMessage
	(*C2S_ChapterStartTimeLimitBoxMessage)(nil),     // 22: proto.C2S_ChapterStartTimeLimitBoxMessage
	(*S2C_ChapterStartTimeLimitBoxMessage)(nil),     // 23: proto.S2C_ChapterStartTimeLimitBoxMessage
	(*C2S_ChapterSyncTimeLimitBoxMessage)(nil),      // 24: proto.C2S_ChapterSyncTimeLimitBoxMessage
	(*S2C_ChapterSyncTimeLimitBoxMessage)(nil),      // 25: proto.S2C_ChapterSyncTimeLimitBoxMessage
	(*C2S_ChapterPassTimeLimitBoxMessage)(nil),      // 26: proto.C2S_ChapterPassTimeLimitBoxMessage
	(*S2C_ChapterPassTimeLimitBoxMessage)(nil),      // 27: proto.S2C_ChapterPassTimeLimitBoxMessage
	(*C2S_ChapterPassMonsterBoxMessage)(nil),        // 28: proto.C2S_ChapterPassMonsterBoxMessage
	(*S2C_ChapterPassMonsterBoxMessage)(nil),        // 29: proto.S2C_ChapterPassMonsterBoxMessage
	(*C2S_ChapterPassToolBlessMessage)(nil),         // 30: proto.C2S_ChapterPassToolBlessMessage
	(*S2C_ChapterPassToolBlessMessage)(nil),         // 31: proto.S2C_ChapterPassToolBlessMessage
	(*C2S_ChapterPassRageModeMessage)(nil),          // 32: proto.C2S_ChapterPassRageModeMessage
	(*S2C_ChapterPassRageModeMessage)(nil),          // 33: proto.S2C_ChapterPassRageModeMessage
	(*C2S_DoPublicityMessage)(nil),                  // 34: proto.C2S_DoPublicityMessage
	(*S2C_DoPublicityMessage)(nil),                  // 35: proto.S2C_DoPublicityMessage
	(*C2S_GetPublicityRewardMessage)(nil),           // 36: proto.C2S_GetPublicityRewardMessage
	(*S2C_GetPublicityRewardMessage)(nil),           // 37: proto.S2C_GetPublicityRewardMessage
	nil,                                             // 38: proto.C2S_PlanetProfileSortChangeMessage.SortEntry
	nil,                                             // 39: proto.S2C_GetPublicityRewardMessage.TimeMapEntry
	(*Condition)(nil),                               // 40: proto.Condition
}
var file_planet_proto_depIdxs = []int32{
	40, // 0: proto.S2C_ChapterPassResultMessage.rewards:type_name -> proto.Condition
	40, // 1: proto.S2C_PassBranchPlanetNodeMessage.rewards:type_name -> proto.Condition
	38, // 2: proto.C2S_PlanetProfileSortChangeMessage.sort:type_name -> proto.C2S_PlanetProfileSortChangeMessage.SortEntry
	40, // 3: proto.S2C_ChapterPassRandomBoxMessage.rewards:type_name -> proto.Condition
	40, // 4: proto.S2C_ChapterSyncTimeLimitBoxMessage.rewards:type_name -> proto.Condition
	40, // 5: proto.S2C_ChapterPassMonsterBoxMessage.rewards:type_name -> proto.Condition
	40, // 6: proto.S2C_DoPublicityMessage.rewards:type_name -> proto.Condition
	40, // 7: proto.S2C_GetPublicityRewardMessage.rewards:type_name -> proto.Condition
	39, // 8: proto.S2C_GetPublicityRewardMessage.timeMap:type_name -> proto.S2C_GetPublicityRewardMessage.TimeMapEntry
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_planet_proto_init() }
func file_planet_proto_init() {
	if File_planet_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_planet_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_TrainNavigationMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_TrainNavigationResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetPlanetMoveSurplusTimeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetPlanetMoveSurplusTimeRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LandPlanetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_LandPlanetRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChapterPassMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChapterPassResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PassBranchPlanetNodeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PassBranchPlanetNodeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ClaimBranchPlanetNodeRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ClaimBranchPlanetNodeRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CancelMoveToPlanetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CancelMoveToPlanetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UnlockProfileMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UnlockProfileMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ProfileCollectRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ProfileCollectRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PlanetProfileSortChangeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PlanetProfileSortChangeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChapterPassRandomBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChapterPassRandomBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChapterStartTimeLimitBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChapterStartTimeLimitBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChapterSyncTimeLimitBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChapterSyncTimeLimitBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChapterPassTimeLimitBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChapterPassTimeLimitBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChapterPassMonsterBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChapterPassMonsterBoxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChapterPassToolBlessMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChapterPassToolBlessMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChapterPassRageModeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChapterPassRageModeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_DoPublicityMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_DoPublicityMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetPublicityRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_planet_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetPublicityRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_planet_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_planet_proto_goTypes,
		DependencyIndexes: file_planet_proto_depIdxs,
		MessageInfos:      file_planet_proto_msgTypes,
	}.Build()
	File_planet_proto = out.File
	file_planet_proto_rawDesc = nil
	file_planet_proto_goTypes = nil
	file_planet_proto_depIdxs = nil
}
