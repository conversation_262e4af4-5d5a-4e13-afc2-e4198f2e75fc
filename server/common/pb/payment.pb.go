// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: payment.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_CreatePayOrderMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId string `protobuf:"bytes,1,opt,name=productId,proto3" json:"productId,omitempty"` //商品id
	Platform  string `protobuf:"bytes,2,opt,name=platform,proto3" json:"platform,omitempty"`   //平台
}

func (x *C2S_CreatePayOrderMessage) Reset() {
	*x = C2S_CreatePayOrderMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CreatePayOrderMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CreatePayOrderMessage) ProtoMessage() {}

func (x *C2S_CreatePayOrderMessage) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CreatePayOrderMessage.ProtoReflect.Descriptor instead.
func (*C2S_CreatePayOrderMessage) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_CreatePayOrderMessage) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *C2S_CreatePayOrderMessage) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type S2C_CreatePayOrderMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
	Uid  string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`    //内部订单号
}

func (x *S2C_CreatePayOrderMessage) Reset() {
	*x = S2C_CreatePayOrderMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CreatePayOrderMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CreatePayOrderMessage) ProtoMessage() {}

func (x *S2C_CreatePayOrderMessage) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CreatePayOrderMessage.ProtoReflect.Descriptor instead.
func (*S2C_CreatePayOrderMessage) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_CreatePayOrderMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_CreatePayOrderMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type C2S_VerifyPayOrderMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId    string  `protobuf:"bytes,1,opt,name=productId,proto3" json:"productId,omitempty"`        //商品id
	OrderId      string  `protobuf:"bytes,2,opt,name=orderId,proto3" json:"orderId,omitempty"`            //外部订单号
	CpOrderId    string  `protobuf:"bytes,3,opt,name=cpOrderId,proto3" json:"cpOrderId,omitempty"`        //内部订单号
	Token        string  `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`                //验证数据
	Platform     string  `protobuf:"bytes,5,opt,name=platform,proto3" json:"platform,omitempty"`          //支付方式
	PurchaseTime int64   `protobuf:"varint,6,opt,name=purchaseTime,proto3" json:"purchaseTime,omitempty"` //支付时间
	CurrencyType string  `protobuf:"bytes,7,opt,name=currencyType,proto3" json:"currencyType,omitempty"`  //支付币种
	PayAmount    float64 `protobuf:"fixed64,8,opt,name=payAmount,proto3" json:"payAmount,omitempty"`      //支付金额
	Quantity     int32   `protobuf:"varint,9,opt,name=quantity,proto3" json:"quantity,omitempty"`         //数量
}

func (x *C2S_VerifyPayOrderMessage) Reset() {
	*x = C2S_VerifyPayOrderMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_VerifyPayOrderMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_VerifyPayOrderMessage) ProtoMessage() {}

func (x *C2S_VerifyPayOrderMessage) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_VerifyPayOrderMessage.ProtoReflect.Descriptor instead.
func (*C2S_VerifyPayOrderMessage) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_VerifyPayOrderMessage) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *C2S_VerifyPayOrderMessage) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *C2S_VerifyPayOrderMessage) GetCpOrderId() string {
	if x != nil {
		return x.CpOrderId
	}
	return ""
}

func (x *C2S_VerifyPayOrderMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *C2S_VerifyPayOrderMessage) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *C2S_VerifyPayOrderMessage) GetPurchaseTime() int64 {
	if x != nil {
		return x.PurchaseTime
	}
	return 0
}

func (x *C2S_VerifyPayOrderMessage) GetCurrencyType() string {
	if x != nil {
		return x.CurrencyType
	}
	return ""
}

func (x *C2S_VerifyPayOrderMessage) GetPayAmount() float64 {
	if x != nil {
		return x.PayAmount
	}
	return 0
}

func (x *C2S_VerifyPayOrderMessage) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

type S2C_VerifyPayOrderMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //1货币不足2物品库存不足3商店数据不存在
	Uid  string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`    //内部订单号
}

func (x *S2C_VerifyPayOrderMessage) Reset() {
	*x = S2C_VerifyPayOrderMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_VerifyPayOrderMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_VerifyPayOrderMessage) ProtoMessage() {}

func (x *S2C_VerifyPayOrderMessage) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_VerifyPayOrderMessage.ProtoReflect.Descriptor instead.
func (*S2C_VerifyPayOrderMessage) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_VerifyPayOrderMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_VerifyPayOrderMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type C2S_GetPayRewardsMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` //订单id
}

func (x *C2S_GetPayRewardsMessage) Reset() {
	*x = C2S_GetPayRewardsMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetPayRewardsMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetPayRewardsMessage) ProtoMessage() {}

func (x *C2S_GetPayRewardsMessage) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetPayRewardsMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetPayRewardsMessage) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_GetPayRewardsMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type S2C_GetPayRewardsMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_GetPayRewardsMessage) Reset() {
	*x = S2C_GetPayRewardsMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetPayRewardsMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetPayRewardsMessage) ProtoMessage() {}

func (x *S2C_GetPayRewardsMessage) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetPayRewardsMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetPayRewardsMessage) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_GetPayRewardsMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_payment_proto protoreflect.FileDescriptor

var file_payment_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x55, 0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x41, 0x0a,
	0x19, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x22, 0xa5, 0x02, 0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50,
	0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x70, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x70, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x70, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0x41, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x2c, 0x0a, 0x18, 0x43,
	0x32, 0x53, 0x5f, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x2e, 0x0a, 0x18, 0x53, 0x32, 0x43,
	0x5f, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_payment_proto_rawDescOnce sync.Once
	file_payment_proto_rawDescData = file_payment_proto_rawDesc
)

func file_payment_proto_rawDescGZIP() []byte {
	file_payment_proto_rawDescOnce.Do(func() {
		file_payment_proto_rawDescData = protoimpl.X.CompressGZIP(file_payment_proto_rawDescData)
	})
	return file_payment_proto_rawDescData
}

var file_payment_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_payment_proto_goTypes = []interface{}{
	(*C2S_CreatePayOrderMessage)(nil), // 0: proto.C2S_CreatePayOrderMessage
	(*S2C_CreatePayOrderMessage)(nil), // 1: proto.S2C_CreatePayOrderMessage
	(*C2S_VerifyPayOrderMessage)(nil), // 2: proto.C2S_VerifyPayOrderMessage
	(*S2C_VerifyPayOrderMessage)(nil), // 3: proto.S2C_VerifyPayOrderMessage
	(*C2S_GetPayRewardsMessage)(nil),  // 4: proto.C2S_GetPayRewardsMessage
	(*S2C_GetPayRewardsMessage)(nil),  // 5: proto.S2C_GetPayRewardsMessage
}
var file_payment_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_payment_proto_init() }
func file_payment_proto_init() {
	if File_payment_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_payment_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CreatePayOrderMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CreatePayOrderMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_VerifyPayOrderMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_VerifyPayOrderMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetPayRewardsMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetPayRewardsMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_payment_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_payment_proto_goTypes,
		DependencyIndexes: file_payment_proto_depIdxs,
		MessageInfos:      file_payment_proto_msgTypes,
	}.Build()
	File_payment_proto = out.File
	file_payment_proto_rawDesc = nil
	file_payment_proto_goTypes = nil
	file_payment_proto_depIdxs = nil
}
