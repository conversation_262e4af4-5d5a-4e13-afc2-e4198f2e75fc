// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: equip.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_WearEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` //装备唯一id
}

func (x *C2S_WearEquipMessage) Reset() {
	*x = C2S_WearEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_WearEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_WearEquipMessage) ProtoMessage() {}

func (x *C2S_WearEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_WearEquipMessage.ProtoReflect.Descriptor instead.
func (*C2S_WearEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_WearEquipMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type S2C_WearEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //1找不到装备
}

func (x *S2C_WearEquipMessage) Reset() {
	*x = S2C_WearEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_WearEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_WearEquipMessage) ProtoMessage() {}

func (x *S2C_WearEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_WearEquipMessage.ProtoReflect.Descriptor instead.
func (*S2C_WearEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_WearEquipMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_UnWearEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` //装备唯一id
}

func (x *C2S_UnWearEquipMessage) Reset() {
	*x = C2S_UnWearEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UnWearEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UnWearEquipMessage) ProtoMessage() {}

func (x *C2S_UnWearEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UnWearEquipMessage.ProtoReflect.Descriptor instead.
func (*C2S_UnWearEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_UnWearEquipMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type S2C_UnWearEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //1找不到穿戴中的装备
}

func (x *S2C_UnWearEquipMessage) Reset() {
	*x = S2C_UnWearEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UnWearEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UnWearEquipMessage) ProtoMessage() {}

func (x *S2C_UnWearEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UnWearEquipMessage.ProtoReflect.Descriptor instead.
func (*S2C_UnWearEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_UnWearEquipMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_MakeEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`           //装备id
	TableId int32 `protobuf:"varint,2,opt,name=tableId,proto3" json:"tableId,omitempty"` //打造台id
}

func (x *C2S_MakeEquipMessage) Reset() {
	*x = C2S_MakeEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MakeEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MakeEquipMessage) ProtoMessage() {}

func (x *C2S_MakeEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MakeEquipMessage.ProtoReflect.Descriptor instead.
func (*C2S_MakeEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_MakeEquipMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_MakeEquipMessage) GetTableId() int32 {
	if x != nil {
		return x.TableId
	}
	return 0
}

type S2C_MakeEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`  //0
	Equip *EquipItem `protobuf:"bytes,2,opt,name=equip,proto3" json:"equip,omitempty"` //装备
}

func (x *S2C_MakeEquipMessage) Reset() {
	*x = S2C_MakeEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MakeEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MakeEquipMessage) ProtoMessage() {}

func (x *S2C_MakeEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MakeEquipMessage.ProtoReflect.Descriptor instead.
func (*S2C_MakeEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_MakeEquipMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_MakeEquipMessage) GetEquip() *EquipItem {
	if x != nil {
		return x.Equip
	}
	return nil
}

type C2S_BuyEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`       //装备id
	Level int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"` //装备等级
}

func (x *C2S_BuyEquipMessage) Reset() {
	*x = C2S_BuyEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BuyEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BuyEquipMessage) ProtoMessage() {}

func (x *C2S_BuyEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BuyEquipMessage.ProtoReflect.Descriptor instead.
func (*C2S_BuyEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_BuyEquipMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_BuyEquipMessage) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type S2C_BuyEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`  //0
	Equip *EquipItem `protobuf:"bytes,2,opt,name=equip,proto3" json:"equip,omitempty"` //装备
}

func (x *S2C_BuyEquipMessage) Reset() {
	*x = S2C_BuyEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BuyEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BuyEquipMessage) ProtoMessage() {}

func (x *S2C_BuyEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BuyEquipMessage.ProtoReflect.Descriptor instead.
func (*S2C_BuyEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_BuyEquipMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_BuyEquipMessage) GetEquip() *EquipItem {
	if x != nil {
		return x.Equip
	}
	return nil
}

type C2S_SellEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid []string `protobuf:"bytes,1,rep,name=uid,proto3" json:"uid,omitempty"` //装备id
}

func (x *C2S_SellEquipMessage) Reset() {
	*x = C2S_SellEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SellEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SellEquipMessage) ProtoMessage() {}

func (x *C2S_SellEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SellEquipMessage.ProtoReflect.Descriptor instead.
func (*C2S_SellEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{8}
}

func (x *C2S_SellEquipMessage) GetUid() []string {
	if x != nil {
		return x.Uid
	}
	return nil
}

type S2C_SellEquipMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_SellEquipMessage) Reset() {
	*x = S2C_SellEquipMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_equip_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SellEquipMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SellEquipMessage) ProtoMessage() {}

func (x *S2C_SellEquipMessage) ProtoReflect() protoreflect.Message {
	mi := &file_equip_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SellEquipMessage.ProtoReflect.Descriptor instead.
func (*S2C_SellEquipMessage) Descriptor() ([]byte, []int) {
	return file_equip_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_SellEquipMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_equip_proto protoreflect.FileDescriptor

var file_equip_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x65, 0x71, 0x75, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x28, 0x0a, 0x14, 0x43, 0x32, 0x53, 0x5f, 0x57, 0x65, 0x61, 0x72, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x2a, 0x0a, 0x14,
	0x53, 0x32, 0x43, 0x5f, 0x57, 0x65, 0x61, 0x72, 0x45, 0x71, 0x75, 0x69, 0x70, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x2a, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f,
	0x55, 0x6e, 0x57, 0x65, 0x61, 0x72, 0x45, 0x71, 0x75, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x22, 0x2c, 0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x55, 0x6e, 0x57, 0x65,
	0x61, 0x72, 0x45, 0x71, 0x75, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x40, 0x0a, 0x14, 0x43, 0x32, 0x53, 0x5f, 0x4d, 0x61, 0x6b, 0x65, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x49, 0x64, 0x22, 0x52, 0x0a, 0x14, 0x53, 0x32, 0x43, 0x5f, 0x4d, 0x61, 0x6b, 0x65,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x26, 0x0a, 0x05, 0x65, 0x71, 0x75, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x65, 0x71, 0x75, 0x69, 0x70, 0x22, 0x3b, 0x0a, 0x13, 0x43, 0x32, 0x53, 0x5f,
	0x42, 0x75, 0x79, 0x45, 0x71, 0x75, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x51, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x75, 0x79,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x26, 0x0a, 0x05, 0x65, 0x71, 0x75, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x65, 0x71, 0x75, 0x69, 0x70, 0x22, 0x28, 0x0a, 0x14, 0x43, 0x32, 0x53, 0x5f,
	0x53, 0x65, 0x6c, 0x6c, 0x45, 0x71, 0x75, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x22, 0x2a, 0x0a, 0x14, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x65, 0x6c, 0x6c, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06,
	0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_equip_proto_rawDescOnce sync.Once
	file_equip_proto_rawDescData = file_equip_proto_rawDesc
)

func file_equip_proto_rawDescGZIP() []byte {
	file_equip_proto_rawDescOnce.Do(func() {
		file_equip_proto_rawDescData = protoimpl.X.CompressGZIP(file_equip_proto_rawDescData)
	})
	return file_equip_proto_rawDescData
}

var file_equip_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_equip_proto_goTypes = []interface{}{
	(*C2S_WearEquipMessage)(nil),   // 0: proto.C2S_WearEquipMessage
	(*S2C_WearEquipMessage)(nil),   // 1: proto.S2C_WearEquipMessage
	(*C2S_UnWearEquipMessage)(nil), // 2: proto.C2S_UnWearEquipMessage
	(*S2C_UnWearEquipMessage)(nil), // 3: proto.S2C_UnWearEquipMessage
	(*C2S_MakeEquipMessage)(nil),   // 4: proto.C2S_MakeEquipMessage
	(*S2C_MakeEquipMessage)(nil),   // 5: proto.S2C_MakeEquipMessage
	(*C2S_BuyEquipMessage)(nil),    // 6: proto.C2S_BuyEquipMessage
	(*S2C_BuyEquipMessage)(nil),    // 7: proto.S2C_BuyEquipMessage
	(*C2S_SellEquipMessage)(nil),   // 8: proto.C2S_SellEquipMessage
	(*S2C_SellEquipMessage)(nil),   // 9: proto.S2C_SellEquipMessage
	(*EquipItem)(nil),              // 10: proto.EquipItem
}
var file_equip_proto_depIdxs = []int32{
	10, // 0: proto.S2C_MakeEquipMessage.equip:type_name -> proto.EquipItem
	10, // 1: proto.S2C_BuyEquipMessage.equip:type_name -> proto.EquipItem
	2,  // [2:2] is the sub-list for method output_type
	2,  // [2:2] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_equip_proto_init() }
func file_equip_proto_init() {
	if File_equip_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_equip_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_WearEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_equip_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_WearEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_equip_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UnWearEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_equip_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UnWearEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_equip_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MakeEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_equip_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MakeEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_equip_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BuyEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_equip_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BuyEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_equip_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SellEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_equip_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SellEquipMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_equip_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_equip_proto_goTypes,
		DependencyIndexes: file_equip_proto_depIdxs,
		MessageInfos:      file_equip_proto_msgTypes,
	}.Build()
	File_equip_proto = out.File
	file_equip_proto_rawDesc = nil
	file_equip_proto_goTypes = nil
	file_equip_proto_depIdxs = nil
}
