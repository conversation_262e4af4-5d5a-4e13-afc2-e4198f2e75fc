// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: msg.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type S2C_ErrorResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_ErrorResultMessage) Reset() {
	*x = S2C_ErrorResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ErrorResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ErrorResultMessage) ProtoMessage() {}

func (x *S2C_ErrorResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ErrorResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_ErrorResultMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{0}
}

func (x *S2C_ErrorResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_RegisterMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username   string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`     //用户名
	Password   string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`     //密码
	Platform   string `protobuf:"bytes,3,opt,name=platform,proto3" json:"platform,omitempty"`     //平台
	Ver        string `protobuf:"bytes,4,opt,name=ver,proto3" json:"ver,omitempty"`               //客户端当前版本
	DistanceId string `protobuf:"bytes,5,opt,name=distanceId,proto3" json:"distanceId,omitempty"` //ta访客id
	Os         string `protobuf:"bytes,6,opt,name=os,proto3" json:"os,omitempty"`                 //系统
}

func (x *C2S_RegisterMessage) Reset() {
	*x = C2S_RegisterMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_RegisterMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_RegisterMessage) ProtoMessage() {}

func (x *C2S_RegisterMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_RegisterMessage.ProtoReflect.Descriptor instead.
func (*C2S_RegisterMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{1}
}

func (x *C2S_RegisterMessage) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *C2S_RegisterMessage) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *C2S_RegisterMessage) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *C2S_RegisterMessage) GetVer() string {
	if x != nil {
		return x.Ver
	}
	return ""
}

func (x *C2S_RegisterMessage) GetDistanceId() string {
	if x != nil {
		return x.DistanceId
	}
	return ""
}

func (x *C2S_RegisterMessage) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

type S2C_RegisterResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`  //注册结果，0成功，1用户名存在， 2用户名或密码不规范
	Id    string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`       //用户id
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"` //免密token
}

func (x *S2C_RegisterResultMessage) Reset() {
	*x = S2C_RegisterResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_RegisterResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_RegisterResultMessage) ProtoMessage() {}

func (x *S2C_RegisterResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_RegisterResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_RegisterResultMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{2}
}

func (x *S2C_RegisterResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_RegisterResultMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *S2C_RegisterResultMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type C2S_LoginAccountMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string       `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"` //用户名,游客不需要携带
	Password string       `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"` //密码,游客不需要携带
	Common   *LoginCommon `protobuf:"bytes,3,opt,name=common,proto3" json:"common,omitempty"`     //公共参数
}

func (x *C2S_LoginAccountMessage) Reset() {
	*x = C2S_LoginAccountMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginAccountMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginAccountMessage) ProtoMessage() {}

func (x *C2S_LoginAccountMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginAccountMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginAccountMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{3}
}

func (x *C2S_LoginAccountMessage) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *C2S_LoginAccountMessage) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *C2S_LoginAccountMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

type C2S_LoginByTokenMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                    //用户id
	Token       string       `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`              //免密登录
	IsReconnect bool         `protobuf:"varint,3,opt,name=isReconnect,proto3" json:"isReconnect,omitempty"` //是不是重连登陆请求
	Common      *LoginCommon `protobuf:"bytes,4,opt,name=common,proto3" json:"common,omitempty"`            //公共参数
}

func (x *C2S_LoginByTokenMessage) Reset() {
	*x = C2S_LoginByTokenMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginByTokenMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginByTokenMessage) ProtoMessage() {}

func (x *C2S_LoginByTokenMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginByTokenMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginByTokenMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_LoginByTokenMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *C2S_LoginByTokenMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *C2S_LoginByTokenMessage) GetIsReconnect() bool {
	if x != nil {
		return x.IsReconnect
	}
	return false
}

func (x *C2S_LoginByTokenMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

type C2S_LoginGuestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"` //公共参数
	UserId string       `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"` //游客 id
}

func (x *C2S_LoginGuestMessage) Reset() {
	*x = C2S_LoginGuestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginGuestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginGuestMessage) ProtoMessage() {}

func (x *C2S_LoginGuestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginGuestMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginGuestMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{5}
}

func (x *C2S_LoginGuestMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_LoginGuestMessage) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type C2S_LoginFBMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"` //公共参数
	UserId string       `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"` //facebook id
	Token  string       `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`   //token
}

func (x *C2S_LoginFBMessage) Reset() {
	*x = C2S_LoginFBMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginFBMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginFBMessage) ProtoMessage() {}

func (x *C2S_LoginFBMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginFBMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginFBMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_LoginFBMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_LoginFBMessage) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *C2S_LoginFBMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type C2S_BindFBMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"` //公共参数
	UserId string       `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"` //facebook id
	Token  string       `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`   //token
}

func (x *C2S_BindFBMessage) Reset() {
	*x = C2S_BindFBMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BindFBMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BindFBMessage) ProtoMessage() {}

func (x *C2S_BindFBMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BindFBMessage.ProtoReflect.Descriptor instead.
func (*C2S_BindFBMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{7}
}

func (x *C2S_BindFBMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_BindFBMessage) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *C2S_BindFBMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type C2S_LoginAppleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common   *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`     //公共参数
	UserId   string       `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`     //用户id
	NickName string       `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"` //用户名字
	Token    string       `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`       //token
	Code     string       `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`         //code
}

func (x *C2S_LoginAppleMessage) Reset() {
	*x = C2S_LoginAppleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginAppleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginAppleMessage) ProtoMessage() {}

func (x *C2S_LoginAppleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginAppleMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginAppleMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{8}
}

func (x *C2S_LoginAppleMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_LoginAppleMessage) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *C2S_LoginAppleMessage) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *C2S_LoginAppleMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *C2S_LoginAppleMessage) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type C2S_BindAppleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common   *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`     //公共参数
	UserId   string       `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`     //用户id
	NickName string       `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"` //用户名字
	Token    string       `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`       //token
	Code     string       `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`         //code
}

func (x *C2S_BindAppleMessage) Reset() {
	*x = C2S_BindAppleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BindAppleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BindAppleMessage) ProtoMessage() {}

func (x *C2S_BindAppleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BindAppleMessage.ProtoReflect.Descriptor instead.
func (*C2S_BindAppleMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{9}
}

func (x *C2S_BindAppleMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_BindAppleMessage) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *C2S_BindAppleMessage) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *C2S_BindAppleMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *C2S_BindAppleMessage) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type C2S_LoginGoogleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common    *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`       //公共参数
	Token     string       `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`         //token
	NickName  string       `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`   //用户名字
	AvatarUrl string       `protobuf:"bytes,4,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"` //用户头像
}

func (x *C2S_LoginGoogleMessage) Reset() {
	*x = C2S_LoginGoogleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginGoogleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginGoogleMessage) ProtoMessage() {}

func (x *C2S_LoginGoogleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginGoogleMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginGoogleMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{10}
}

func (x *C2S_LoginGoogleMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_LoginGoogleMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *C2S_LoginGoogleMessage) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *C2S_LoginGoogleMessage) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

type C2S_BindGoogleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common    *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`       //公共参数
	Token     string       `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`         //token
	NickName  string       `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`   //用户名字
	AvatarUrl string       `protobuf:"bytes,4,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"` //用户头像
}

func (x *C2S_BindGoogleMessage) Reset() {
	*x = C2S_BindGoogleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BindGoogleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BindGoogleMessage) ProtoMessage() {}

func (x *C2S_BindGoogleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BindGoogleMessage.ProtoReflect.Descriptor instead.
func (*C2S_BindGoogleMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{11}
}

func (x *C2S_BindGoogleMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_BindGoogleMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *C2S_BindGoogleMessage) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *C2S_BindGoogleMessage) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

type C2S_LoginWxMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common    *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`       //公共参数
	Code      string       `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`           //用户id
	NickName  string       `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`   //用户名字
	AvatarUrl string       `protobuf:"bytes,4,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"` //用户头像
}

func (x *C2S_LoginWxMessage) Reset() {
	*x = C2S_LoginWxMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginWxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginWxMessage) ProtoMessage() {}

func (x *C2S_LoginWxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginWxMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginWxMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{12}
}

func (x *C2S_LoginWxMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_LoginWxMessage) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *C2S_LoginWxMessage) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *C2S_LoginWxMessage) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

type C2S_LoginWxAppMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"` //公共参数
	Code   string       `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`     //用户id
}

func (x *C2S_LoginWxAppMessage) Reset() {
	*x = C2S_LoginWxAppMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginWxAppMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginWxAppMessage) ProtoMessage() {}

func (x *C2S_LoginWxAppMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginWxAppMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginWxAppMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{13}
}

func (x *C2S_LoginWxAppMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_LoginWxAppMessage) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type C2S_LoginTapTapMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common   *LoginCommon `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`               //公共参数
	UserId   string       `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`               //用户id
	NickName string       `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`           //用户名字
	Kid      string       `protobuf:"bytes,4,opt,name=kid,proto3" json:"kid,omitempty"`                     //kid
	MacKey   string       `protobuf:"bytes,5,opt,name=mac_key,json=macKey,proto3" json:"mac_key,omitempty"` //mac_key
}

func (x *C2S_LoginTapTapMessage) Reset() {
	*x = C2S_LoginTapTapMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginTapTapMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginTapTapMessage) ProtoMessage() {}

func (x *C2S_LoginTapTapMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginTapTapMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginTapTapMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{14}
}

func (x *C2S_LoginTapTapMessage) GetCommon() *LoginCommon {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *C2S_LoginTapTapMessage) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *C2S_LoginTapTapMessage) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *C2S_LoginTapTapMessage) GetKid() string {
	if x != nil {
		return x.Kid
	}
	return ""
}

func (x *C2S_LoginTapTapMessage) GetMacKey() string {
	if x != nil {
		return x.MacKey
	}
	return ""
}

type C2S_CertificationMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RealName string `protobuf:"bytes,1,opt,name=realName,proto3" json:"realName,omitempty"` //真实姓名
	IdCard   string `protobuf:"bytes,2,opt,name=idCard,proto3" json:"idCard,omitempty"`     //身份证号
}

func (x *C2S_CertificationMessage) Reset() {
	*x = C2S_CertificationMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CertificationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CertificationMessage) ProtoMessage() {}

func (x *C2S_CertificationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CertificationMessage.ProtoReflect.Descriptor instead.
func (*C2S_CertificationMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{15}
}

func (x *C2S_CertificationMessage) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *C2S_CertificationMessage) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

type S2C_CertificationResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //验证结果（0：成功，1：身份证姓名不匹配）
	Age  int32 `protobuf:"varint,2,opt,name=age,proto3" json:"age,omitempty"`   //年龄
}

func (x *S2C_CertificationResultMessage) Reset() {
	*x = S2C_CertificationResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CertificationResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CertificationResultMessage) ProtoMessage() {}

func (x *S2C_CertificationResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CertificationResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_CertificationResultMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{16}
}

func (x *S2C_CertificationResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_CertificationResultMessage) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

type S2C_LoginResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`        //登录结果（0：登录成功，此时userInfo是有效数据，1：用户名或密码错误，2：免密登录token失效，需要重新登录），3：客户端版本需要更新
	UserInfo *UserInfo `protobuf:"bytes,2,opt,name=userInfo,proto3" json:"userInfo,omitempty"` //用户数据信息
}

func (x *S2C_LoginResultMessage) Reset() {
	*x = S2C_LoginResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_LoginResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_LoginResultMessage) ProtoMessage() {}

func (x *S2C_LoginResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_LoginResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_LoginResultMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{17}
}

func (x *S2C_LoginResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_LoginResultMessage) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type S2C_BindResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`          //0
	UserType  string `protobuf:"bytes,2,opt,name=userType,proto3" json:"userType,omitempty"`   //用户类型
	NickName  string `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`   //昵称
	AvatarUrl string `protobuf:"bytes,4,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"` //头像
}

func (x *S2C_BindResultMessage) Reset() {
	*x = S2C_BindResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BindResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BindResultMessage) ProtoMessage() {}

func (x *S2C_BindResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BindResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_BindResultMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{18}
}

func (x *S2C_BindResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_BindResultMessage) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

func (x *S2C_BindResultMessage) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *S2C_BindResultMessage) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

type S2C_NoticeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"` //测试通知客户端
}

func (x *S2C_NoticeMessage) Reset() {
	*x = S2C_NoticeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_NoticeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_NoticeMessage) ProtoMessage() {}

func (x *S2C_NoticeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_NoticeMessage.ProtoReflect.Descriptor instead.
func (*S2C_NoticeMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{19}
}

func (x *S2C_NoticeMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type C2S_EnterGameServerMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid        int32  `protobuf:"varint,1,opt,name=sid,proto3" json:"sid,omitempty"`               //服务器区号
	Token      string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`            //登录服获取到的token
	CloseGuide bool   `protobuf:"varint,3,opt,name=closeGuide,proto3" json:"closeGuide,omitempty"` //是否关闭新手教程
}

func (x *C2S_EnterGameServerMessage) Reset() {
	*x = C2S_EnterGameServerMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_EnterGameServerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_EnterGameServerMessage) ProtoMessage() {}

func (x *C2S_EnterGameServerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_EnterGameServerMessage.ProtoReflect.Descriptor instead.
func (*C2S_EnterGameServerMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{20}
}

func (x *C2S_EnterGameServerMessage) GetSid() int32 {
	if x != nil {
		return x.Sid
	}
	return 0
}

func (x *C2S_EnterGameServerMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *C2S_EnterGameServerMessage) GetCloseGuide() bool {
	if x != nil {
		return x.CloseGuide
	}
	return false
}

type S2C_EnterGameServerResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //选区结果,前端没做选区,游戏服启动的情况下,暂时都是成功
}

func (x *S2C_EnterGameServerResultMessage) Reset() {
	*x = S2C_EnterGameServerResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_EnterGameServerResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_EnterGameServerResultMessage) ProtoMessage() {}

func (x *S2C_EnterGameServerResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_EnterGameServerResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_EnterGameServerResultMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{21}
}

func (x *S2C_EnterGameServerResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_GetPlayerInfoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_GetPlayerInfoMessage) Reset() {
	*x = C2S_GetPlayerInfoMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetPlayerInfoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetPlayerInfoMessage) ProtoMessage() {}

func (x *C2S_GetPlayerInfoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetPlayerInfoMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetPlayerInfoMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{22}
}

type S2C_GetPlayerInfoResMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   int32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`    //
	Player *Player `protobuf:"bytes,2,opt,name=player,proto3" json:"player,omitempty"` //玩家基础数据
}

func (x *S2C_GetPlayerInfoResMessage) Reset() {
	*x = S2C_GetPlayerInfoResMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetPlayerInfoResMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetPlayerInfoResMessage) ProtoMessage() {}

func (x *S2C_GetPlayerInfoResMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetPlayerInfoResMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetPlayerInfoResMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{23}
}

func (x *S2C_GetPlayerInfoResMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetPlayerInfoResMessage) GetPlayer() *Player {
	if x != nil {
		return x.Player
	}
	return nil
}

type S2C_CurrencyChangeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Currency *CurrencyInfo `protobuf:"bytes,1,opt,name=currency,proto3" json:"currency,omitempty"` //货币实体
}

func (x *S2C_CurrencyChangeMessage) Reset() {
	*x = S2C_CurrencyChangeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CurrencyChangeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CurrencyChangeMessage) ProtoMessage() {}

func (x *S2C_CurrencyChangeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CurrencyChangeMessage.ProtoReflect.Descriptor instead.
func (*S2C_CurrencyChangeMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{24}
}

func (x *S2C_CurrencyChangeMessage) GetCurrency() *CurrencyInfo {
	if x != nil {
		return x.Currency
	}
	return nil
}

type S2C_BagItemChangeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ItemInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"` //道具列表
}

func (x *S2C_BagItemChangeMessage) Reset() {
	*x = S2C_BagItemChangeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagItemChangeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagItemChangeMessage) ProtoMessage() {}

func (x *S2C_BagItemChangeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagItemChangeMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagItemChangeMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{25}
}

func (x *S2C_BagItemChangeMessage) GetItems() []*ItemInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type C2S_GmExecuteMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cmd string `protobuf:"bytes,1,opt,name=cmd,proto3" json:"cmd,omitempty"` //gm命令
}

func (x *C2S_GmExecuteMessage) Reset() {
	*x = C2S_GmExecuteMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GmExecuteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GmExecuteMessage) ProtoMessage() {}

func (x *C2S_GmExecuteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GmExecuteMessage.ProtoReflect.Descriptor instead.
func (*C2S_GmExecuteMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{26}
}

func (x *C2S_GmExecuteMessage) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

type S2C_GmExecuteRspMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"` //
}

func (x *S2C_GmExecuteRspMessage) Reset() {
	*x = S2C_GmExecuteRspMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GmExecuteRspMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GmExecuteRspMessage) ProtoMessage() {}

func (x *S2C_GmExecuteRspMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GmExecuteRspMessage.ProtoReflect.Descriptor instead.
func (*S2C_GmExecuteRspMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{27}
}

func (x *S2C_GmExecuteRspMessage) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

type C2S_JackpotReqMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgId string `protobuf:"bytes,1,opt,name=msgId,proto3" json:"msgId,omitempty"` //消息号
	Type  int32  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`  //1单抽2十连抽
}

func (x *C2S_JackpotReqMessage) Reset() {
	*x = C2S_JackpotReqMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_JackpotReqMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_JackpotReqMessage) ProtoMessage() {}

func (x *C2S_JackpotReqMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_JackpotReqMessage.ProtoReflect.Descriptor instead.
func (*C2S_JackpotReqMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{28}
}

func (x *C2S_JackpotReqMessage) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *C2S_JackpotReqMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type S2C_JackpotRspMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0代表无错误
	List []*Condition `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`  //抽卡结果列表
}

func (x *S2C_JackpotRspMessage) Reset() {
	*x = S2C_JackpotRspMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_JackpotRspMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_JackpotRspMessage) ProtoMessage() {}

func (x *S2C_JackpotRspMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_JackpotRspMessage.ProtoReflect.Descriptor instead.
func (*S2C_JackpotRspMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{29}
}

func (x *S2C_JackpotRspMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_JackpotRspMessage) GetList() []*Condition {
	if x != nil {
		return x.List
	}
	return nil
}

type C2S_CollectItemMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Carriages     []*Output `protobuf:"bytes,1,rep,name=carriages,proto3" json:"carriages,omitempty"`          //车厢产出
	Time          int32     `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"`                   //加速时间
	PassengerStar int32     `protobuf:"varint,3,opt,name=passengerStar,proto3" json:"passengerStar,omitempty"` //乘客星尘
	Heart         int32     `protobuf:"varint,4,opt,name=heart,proto3" json:"heart,omitempty"`                 //爱心
}

func (x *C2S_CollectItemMessage) Reset() {
	*x = C2S_CollectItemMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CollectItemMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CollectItemMessage) ProtoMessage() {}

func (x *C2S_CollectItemMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CollectItemMessage.ProtoReflect.Descriptor instead.
func (*C2S_CollectItemMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{30}
}

func (x *C2S_CollectItemMessage) GetCarriages() []*Output {
	if x != nil {
		return x.Carriages
	}
	return nil
}

func (x *C2S_CollectItemMessage) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *C2S_CollectItemMessage) GetPassengerStar() int32 {
	if x != nil {
		return x.PassengerStar
	}
	return 0
}

func (x *C2S_CollectItemMessage) GetHeart() int32 {
	if x != nil {
		return x.Heart
	}
	return 0
}

type S2C_CollectItemRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code          int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                   //code=0代表无错误
	PassengerStar int32     `protobuf:"varint,2,opt,name=passengerStar,proto3" json:"passengerStar,omitempty"` //乘客星尘
	Heart         int32     `protobuf:"varint,3,opt,name=heart,proto3" json:"heart,omitempty"`                 //爱心
	Carriages     []*Output `protobuf:"bytes,4,rep,name=carriages,proto3" json:"carriages,omitempty"`          //车厢产出
}

func (x *S2C_CollectItemRespMessage) Reset() {
	*x = S2C_CollectItemRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CollectItemRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CollectItemRespMessage) ProtoMessage() {}

func (x *S2C_CollectItemRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CollectItemRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_CollectItemRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{31}
}

func (x *S2C_CollectItemRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_CollectItemRespMessage) GetPassengerStar() int32 {
	if x != nil {
		return x.PassengerStar
	}
	return 0
}

func (x *S2C_CollectItemRespMessage) GetHeart() int32 {
	if x != nil {
		return x.Heart
	}
	return 0
}

func (x *S2C_CollectItemRespMessage) GetCarriages() []*Output {
	if x != nil {
		return x.Carriages
	}
	return nil
}

type C2S_SpeedUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SpeedUpMessage) Reset() {
	*x = C2S_SpeedUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SpeedUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SpeedUpMessage) ProtoMessage() {}

func (x *C2S_SpeedUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SpeedUpMessage.ProtoReflect.Descriptor instead.
func (*C2S_SpeedUpMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{32}
}

type S2C_SpeedUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`     //code=0代表无错误, 1表示玩家未处于加速中
	Time   uint64 `protobuf:"fixed64,2,opt,name=time,proto3" json:"time,omitempty"`    //游戏模拟时间
	Energy int32  `protobuf:"varint,3,opt,name=energy,proto3" json:"energy,omitempty"` //剩余体力
}

func (x *S2C_SpeedUpMessage) Reset() {
	*x = S2C_SpeedUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SpeedUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SpeedUpMessage) ProtoMessage() {}

func (x *S2C_SpeedUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SpeedUpMessage.ProtoReflect.Descriptor instead.
func (*S2C_SpeedUpMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{33}
}

func (x *S2C_SpeedUpMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SpeedUpMessage) GetTime() uint64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *S2C_SpeedUpMessage) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

type C2S_StopSpeedUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_StopSpeedUpMessage) Reset() {
	*x = C2S_StopSpeedUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_StopSpeedUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_StopSpeedUpMessage) ProtoMessage() {}

func (x *C2S_StopSpeedUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_StopSpeedUpMessage.ProtoReflect.Descriptor instead.
func (*C2S_StopSpeedUpMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{34}
}

type S2C_StopSpeedUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`     //code=0代表无错误, 1表示玩家未处于加速中
	Time   uint64 `protobuf:"fixed64,2,opt,name=time,proto3" json:"time,omitempty"`    //游戏模拟时间
	Energy int32  `protobuf:"varint,3,opt,name=energy,proto3" json:"energy,omitempty"` //剩余体力
}

func (x *S2C_StopSpeedUpMessage) Reset() {
	*x = S2C_StopSpeedUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_StopSpeedUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_StopSpeedUpMessage) ProtoMessage() {}

func (x *S2C_StopSpeedUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_StopSpeedUpMessage.ProtoReflect.Descriptor instead.
func (*S2C_StopSpeedUpMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{35}
}

func (x *S2C_StopSpeedUpMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_StopSpeedUpMessage) GetTime() uint64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *S2C_StopSpeedUpMessage) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

type C2S_SyncSpeedUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SyncSpeedUpMessage) Reset() {
	*x = C2S_SyncSpeedUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncSpeedUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncSpeedUpMessage) ProtoMessage() {}

func (x *C2S_SyncSpeedUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncSpeedUpMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncSpeedUpMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{36}
}

type S2C_SyncSpeedUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`     //code=0代表无错误, 1表示玩家未处于加速中
	Time   uint64 `protobuf:"fixed64,2,opt,name=time,proto3" json:"time,omitempty"`    //游戏模拟时间
	Energy int32  `protobuf:"varint,3,opt,name=energy,proto3" json:"energy,omitempty"` //剩余体力
}

func (x *S2C_SyncSpeedUpMessage) Reset() {
	*x = S2C_SyncSpeedUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncSpeedUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncSpeedUpMessage) ProtoMessage() {}

func (x *S2C_SyncSpeedUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncSpeedUpMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncSpeedUpMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{37}
}

func (x *S2C_SyncSpeedUpMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncSpeedUpMessage) GetTime() uint64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *S2C_SyncSpeedUpMessage) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

type C2S_RecoverEnergyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` //1免费2扣钻石
}

func (x *C2S_RecoverEnergyMessage) Reset() {
	*x = C2S_RecoverEnergyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_RecoverEnergyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_RecoverEnergyMessage) ProtoMessage() {}

func (x *C2S_RecoverEnergyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_RecoverEnergyMessage.ProtoReflect.Descriptor instead.
func (*C2S_RecoverEnergyMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{38}
}

func (x *C2S_RecoverEnergyMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type S2C_RecoverEnergyRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   int32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`    //0成功
	Energy *Energy `protobuf:"bytes,2,opt,name=energy,proto3" json:"energy,omitempty"` //探索数据
}

func (x *S2C_RecoverEnergyRespMessage) Reset() {
	*x = S2C_RecoverEnergyRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_RecoverEnergyRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_RecoverEnergyRespMessage) ProtoMessage() {}

func (x *S2C_RecoverEnergyRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_RecoverEnergyRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_RecoverEnergyRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{39}
}

func (x *S2C_RecoverEnergyRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_RecoverEnergyRespMessage) GetEnergy() *Energy {
	if x != nil {
		return x.Energy
	}
	return nil
}

type C2S_SyncMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SyncMessage) Reset() {
	*x = C2S_SyncMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncMessage) ProtoMessage() {}

func (x *C2S_SyncMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{40}
}

type S2C_SyncMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time         uint64  `protobuf:"fixed64,1,opt,name=time,proto3" json:"time,omitempty"`                //游戏时长
	Energy       float64 `protobuf:"fixed64,2,opt,name=energy,proto3" json:"energy,omitempty"`            //剩余体力
	ElectricTime int32   `protobuf:"varint,3,opt,name=electricTime,proto3" json:"electricTime,omitempty"` //电力加速剩余时间
}

func (x *S2C_SyncMessage) Reset() {
	*x = S2C_SyncMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncMessage) ProtoMessage() {}

func (x *S2C_SyncMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{41}
}

func (x *S2C_SyncMessage) GetTime() uint64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *S2C_SyncMessage) GetEnergy() float64 {
	if x != nil {
		return x.Energy
	}
	return 0
}

func (x *S2C_SyncMessage) GetElectricTime() int32 {
	if x != nil {
		return x.ElectricTime
	}
	return 0
}

type S2C_LogoutMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reason int32 `protobuf:"varint,1,opt,name=reason,proto3" json:"reason,omitempty"` //0角色在其他地方登录
}

func (x *S2C_LogoutMessage) Reset() {
	*x = S2C_LogoutMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_LogoutMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_LogoutMessage) ProtoMessage() {}

func (x *S2C_LogoutMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_LogoutMessage.ProtoReflect.Descriptor instead.
func (*S2C_LogoutMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{42}
}

func (x *S2C_LogoutMessage) GetReason() int32 {
	if x != nil {
		return x.Reason
	}
	return 0
}

type C2S_RecordGuideStepMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuideId int32 `protobuf:"varint,1,opt,name=guideId,proto3" json:"guideId,omitempty"` //当前引导模块id
	StepId  int32 `protobuf:"varint,2,opt,name=stepId,proto3" json:"stepId,omitempty"`   //当前步骤id
}

func (x *C2S_RecordGuideStepMessage) Reset() {
	*x = C2S_RecordGuideStepMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_RecordGuideStepMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_RecordGuideStepMessage) ProtoMessage() {}

func (x *C2S_RecordGuideStepMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_RecordGuideStepMessage.ProtoReflect.Descriptor instead.
func (*C2S_RecordGuideStepMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{43}
}

func (x *C2S_RecordGuideStepMessage) GetGuideId() int32 {
	if x != nil {
		return x.GuideId
	}
	return 0
}

func (x *C2S_RecordGuideStepMessage) GetStepId() int32 {
	if x != nil {
		return x.StepId
	}
	return 0
}

type S2C_RecordGuideStepResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0成功1数据不对2步骤id不对
}

func (x *S2C_RecordGuideStepResultMessage) Reset() {
	*x = S2C_RecordGuideStepResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_RecordGuideStepResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_RecordGuideStepResultMessage) ProtoMessage() {}

func (x *S2C_RecordGuideStepResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_RecordGuideStepResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_RecordGuideStepResultMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{44}
}

func (x *S2C_RecordGuideStepResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ClaimTaskRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` //任务id
}

func (x *C2S_ClaimTaskRewardMessage) Reset() {
	*x = C2S_ClaimTaskRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ClaimTaskRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ClaimTaskRewardMessage) ProtoMessage() {}

func (x *C2S_ClaimTaskRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ClaimTaskRewardMessage.ProtoReflect.Descriptor instead.
func (*C2S_ClaimTaskRewardMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{45}
}

func (x *C2S_ClaimTaskRewardMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type S2C_ClaimTaskRewardResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0成功
}

func (x *S2C_ClaimTaskRewardResultMessage) Reset() {
	*x = S2C_ClaimTaskRewardResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ClaimTaskRewardResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ClaimTaskRewardResultMessage) ProtoMessage() {}

func (x *S2C_ClaimTaskRewardResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ClaimTaskRewardResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_ClaimTaskRewardResultMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{46}
}

func (x *S2C_ClaimTaskRewardResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_SyncPlanetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SyncPlanetMessage) Reset() {
	*x = C2S_SyncPlanetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncPlanetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncPlanetMessage) ProtoMessage() {}

func (x *C2S_SyncPlanetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncPlanetMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncPlanetMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{47}
}

type S2C_SyncPlanetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   int32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`    //0成功
	Planet *Planet `protobuf:"bytes,2,opt,name=planet,proto3" json:"planet,omitempty"` //当前星球数据
}

func (x *S2C_SyncPlanetMessage) Reset() {
	*x = S2C_SyncPlanetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncPlanetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncPlanetMessage) ProtoMessage() {}

func (x *S2C_SyncPlanetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncPlanetMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncPlanetMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{48}
}

func (x *S2C_SyncPlanetMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncPlanetMessage) GetPlanet() *Planet {
	if x != nil {
		return x.Planet
	}
	return nil
}

type C2S_SyncDailyInfoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SyncDailyInfoMessage) Reset() {
	*x = C2S_SyncDailyInfoMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncDailyInfoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncDailyInfoMessage) ProtoMessage() {}

func (x *C2S_SyncDailyInfoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncDailyInfoMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncDailyInfoMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{49}
}

type S2C_SyncDailyInfoRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code                int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                               //
	JackpotDailyNum     int32          `protobuf:"varint,2,opt,name=jackpotDailyNum,proto3" json:"jackpotDailyNum,omitempty"`         //每日抽卡次数
	Wanted              *Wanted        `protobuf:"bytes,3,opt,name=wanted,proto3" json:"wanted,omitempty"`                            //悬赏
	Energy              *Energy        `protobuf:"bytes,4,opt,name=energy,proto3" json:"energy,omitempty"`                            //加速能量
	BlackHole           *BlackHole     `protobuf:"bytes,5,opt,name=blackHole,proto3" json:"blackHole,omitempty"`                      //黑洞
	Instance            *Instance      `protobuf:"bytes,6,opt,name=instance,proto3" json:"instance,omitempty"`                        //每日副本数据
	Store               *Store         `protobuf:"bytes,7,opt,name=store,proto3" json:"store,omitempty"`                              //商店数据
	NextDaySurpluTime   int32          `protobuf:"varint,8,opt,name=nextDaySurpluTime,proto3" json:"nextDaySurpluTime,omitempty"`     //下次每日刷新剩余时间
	NextWeekSurplusTime int32          `protobuf:"varint,9,opt,name=nextWeekSurplusTime,proto3" json:"nextWeekSurplusTime,omitempty"` //下次周刷新时间
	ArrestData          *ArrestModule  `protobuf:"bytes,10,opt,name=arrestData,proto3" json:"arrestData,omitempty"`                   //通缉令数据
	DailyTask           *DailyTaskInfo `protobuf:"bytes,11,opt,name=dailyTask,proto3" json:"dailyTask,omitempty"`                     //每日任务数据
	Transport           *Transport     `protobuf:"bytes,12,opt,name=transport,proto3" json:"transport,omitempty"`                     //运送任务
	SpaceStone          *SpaceStone    `protobuf:"bytes,13,opt,name=spaceStone,proto3" json:"spaceStone,omitempty"`                   //空间宝石
	OfflineRewardTime   int32          `protobuf:"varint,14,opt,name=offlineRewardTime,proto3" json:"offlineRewardTime,omitempty"`    //离线奖励时长
}

func (x *S2C_SyncDailyInfoRespMessage) Reset() {
	*x = S2C_SyncDailyInfoRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncDailyInfoRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncDailyInfoRespMessage) ProtoMessage() {}

func (x *S2C_SyncDailyInfoRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncDailyInfoRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncDailyInfoRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{50}
}

func (x *S2C_SyncDailyInfoRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncDailyInfoRespMessage) GetJackpotDailyNum() int32 {
	if x != nil {
		return x.JackpotDailyNum
	}
	return 0
}

func (x *S2C_SyncDailyInfoRespMessage) GetWanted() *Wanted {
	if x != nil {
		return x.Wanted
	}
	return nil
}

func (x *S2C_SyncDailyInfoRespMessage) GetEnergy() *Energy {
	if x != nil {
		return x.Energy
	}
	return nil
}

func (x *S2C_SyncDailyInfoRespMessage) GetBlackHole() *BlackHole {
	if x != nil {
		return x.BlackHole
	}
	return nil
}

func (x *S2C_SyncDailyInfoRespMessage) GetInstance() *Instance {
	if x != nil {
		return x.Instance
	}
	return nil
}

func (x *S2C_SyncDailyInfoRespMessage) GetStore() *Store {
	if x != nil {
		return x.Store
	}
	return nil
}

func (x *S2C_SyncDailyInfoRespMessage) GetNextDaySurpluTime() int32 {
	if x != nil {
		return x.NextDaySurpluTime
	}
	return 0
}

func (x *S2C_SyncDailyInfoRespMessage) GetNextWeekSurplusTime() int32 {
	if x != nil {
		return x.NextWeekSurplusTime
	}
	return 0
}

func (x *S2C_SyncDailyInfoRespMessage) GetArrestData() *ArrestModule {
	if x != nil {
		return x.ArrestData
	}
	return nil
}

func (x *S2C_SyncDailyInfoRespMessage) GetDailyTask() *DailyTaskInfo {
	if x != nil {
		return x.DailyTask
	}
	return nil
}

func (x *S2C_SyncDailyInfoRespMessage) GetTransport() *Transport {
	if x != nil {
		return x.Transport
	}
	return nil
}

func (x *S2C_SyncDailyInfoRespMessage) GetSpaceStone() *SpaceStone {
	if x != nil {
		return x.SpaceStone
	}
	return nil
}

func (x *S2C_SyncDailyInfoRespMessage) GetOfflineRewardTime() int32 {
	if x != nil {
		return x.OfflineRewardTime
	}
	return 0
}

type C2S_MailDetailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MailId string `protobuf:"bytes,1,opt,name=mailId,proto3" json:"mailId,omitempty"` //邮件id
}

func (x *C2S_MailDetailMessage) Reset() {
	*x = C2S_MailDetailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MailDetailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MailDetailMessage) ProtoMessage() {}

func (x *C2S_MailDetailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MailDetailMessage.ProtoReflect.Descriptor instead.
func (*C2S_MailDetailMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{51}
}

func (x *C2S_MailDetailMessage) GetMailId() string {
	if x != nil {
		return x.MailId
	}
	return ""
}

type S2C_MailDetailRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //非0则有错
	Mail *MailInfo `protobuf:"bytes,2,opt,name=mail,proto3" json:"mail,omitempty"`  //邮件详情
}

func (x *S2C_MailDetailRespMessage) Reset() {
	*x = S2C_MailDetailRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MailDetailRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MailDetailRespMessage) ProtoMessage() {}

func (x *S2C_MailDetailRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MailDetailRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_MailDetailRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{52}
}

func (x *S2C_MailDetailRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_MailDetailRespMessage) GetMail() *MailInfo {
	if x != nil {
		return x.Mail
	}
	return nil
}

type S2C_OnNewMailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mail *MailInfo `protobuf:"bytes,1,opt,name=mail,proto3" json:"mail,omitempty"` //邮件信息
}

func (x *S2C_OnNewMailMessage) Reset() {
	*x = S2C_OnNewMailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_OnNewMailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_OnNewMailMessage) ProtoMessage() {}

func (x *S2C_OnNewMailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_OnNewMailMessage.ProtoReflect.Descriptor instead.
func (*S2C_OnNewMailMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{53}
}

func (x *S2C_OnNewMailMessage) GetMail() *MailInfo {
	if x != nil {
		return x.Mail
	}
	return nil
}

type C2S_DeleteReadMailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_DeleteReadMailMessage) Reset() {
	*x = C2S_DeleteReadMailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_DeleteReadMailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_DeleteReadMailMessage) ProtoMessage() {}

func (x *C2S_DeleteReadMailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_DeleteReadMailMessage.ProtoReflect.Descriptor instead.
func (*C2S_DeleteReadMailMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{54}
}

type S2C_DeleteReadMailRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //无需判断
}

func (x *S2C_DeleteReadMailRespMessage) Reset() {
	*x = S2C_DeleteReadMailRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_DeleteReadMailRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_DeleteReadMailRespMessage) ProtoMessage() {}

func (x *S2C_DeleteReadMailRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_DeleteReadMailRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_DeleteReadMailRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{55}
}

func (x *S2C_DeleteReadMailRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_AttachMailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MailId string `protobuf:"bytes,1,opt,name=mailId,proto3" json:"mailId,omitempty"` //-1代表全部
}

func (x *C2S_AttachMailMessage) Reset() {
	*x = C2S_AttachMailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_AttachMailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_AttachMailMessage) ProtoMessage() {}

func (x *C2S_AttachMailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_AttachMailMessage.ProtoReflect.Descriptor instead.
func (*C2S_AttachMailMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{56}
}

func (x *C2S_AttachMailMessage) GetMailId() string {
	if x != nil {
		return x.MailId
	}
	return ""
}

type S2C_AttachMailRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //0成功
	Rewards []*Condition `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"` //奖励列表,针对全部领取
	Ids     []string     `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`         //需要更新的邮件列表
}

func (x *S2C_AttachMailRespMessage) Reset() {
	*x = S2C_AttachMailRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_AttachMailRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_AttachMailRespMessage) ProtoMessage() {}

func (x *S2C_AttachMailRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_AttachMailRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_AttachMailRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{57}
}

func (x *S2C_AttachMailRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_AttachMailRespMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *S2C_AttachMailRespMessage) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type C2S_MailListMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` //0是获取邮件列表1是获取红点状态
}

func (x *C2S_MailListMessage) Reset() {
	*x = C2S_MailListMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MailListMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MailListMessage) ProtoMessage() {}

func (x *C2S_MailListMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MailListMessage.ProtoReflect.Descriptor instead.
func (*C2S_MailListMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{58}
}

func (x *C2S_MailListMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type S2C_MailListRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mail []*MailInfo `protobuf:"bytes,1,rep,name=mail,proto3" json:"mail,omitempty"` //邮件信息列表
}

func (x *S2C_MailListRespMessage) Reset() {
	*x = S2C_MailListRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MailListRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MailListRespMessage) ProtoMessage() {}

func (x *S2C_MailListRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MailListRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_MailListRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{59}
}

func (x *S2C_MailListRespMessage) GetMail() []*MailInfo {
	if x != nil {
		return x.Mail
	}
	return nil
}

type C2S_CheckCdkMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cdk string `protobuf:"bytes,1,opt,name=cdk,proto3" json:"cdk,omitempty"` //兑换码
}

func (x *C2S_CheckCdkMessage) Reset() {
	*x = C2S_CheckCdkMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CheckCdkMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CheckCdkMessage) ProtoMessage() {}

func (x *C2S_CheckCdkMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CheckCdkMessage.ProtoReflect.Descriptor instead.
func (*C2S_CheckCdkMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{60}
}

func (x *C2S_CheckCdkMessage) GetCdk() string {
	if x != nil {
		return x.Cdk
	}
	return ""
}

type S2C_CheckCdkMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //非0则失败
	Rewards []*Condition `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"` //奖励列表
}

func (x *S2C_CheckCdkMessage) Reset() {
	*x = S2C_CheckCdkMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CheckCdkMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CheckCdkMessage) ProtoMessage() {}

func (x *S2C_CheckCdkMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CheckCdkMessage.ProtoReflect.Descriptor instead.
func (*S2C_CheckCdkMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{61}
}

func (x *S2C_CheckCdkMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_CheckCdkMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_DiyPlayerInfoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NickName string `protobuf:"bytes,1,opt,name=nickName,proto3" json:"nickName,omitempty"` //玩家名称
}

func (x *C2S_DiyPlayerInfoMessage) Reset() {
	*x = C2S_DiyPlayerInfoMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_DiyPlayerInfoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_DiyPlayerInfoMessage) ProtoMessage() {}

func (x *C2S_DiyPlayerInfoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_DiyPlayerInfoMessage.ProtoReflect.Descriptor instead.
func (*C2S_DiyPlayerInfoMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{62}
}

func (x *C2S_DiyPlayerInfoMessage) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

type S2C_DiyPlayerInfoRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //非0则失败
}

func (x *S2C_DiyPlayerInfoRespMessage) Reset() {
	*x = S2C_DiyPlayerInfoRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_DiyPlayerInfoRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_DiyPlayerInfoRespMessage) ProtoMessage() {}

func (x *S2C_DiyPlayerInfoRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_DiyPlayerInfoRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_DiyPlayerInfoRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{63}
}

func (x *S2C_DiyPlayerInfoRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_SignOutMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthField1 string `protobuf:"bytes,1,opt,name=authField1,proto3" json:"authField1,omitempty"` //认证信息1
	AuthField2 string `protobuf:"bytes,2,opt,name=authField2,proto3" json:"authField2,omitempty"` //认证信息2
}

func (x *C2S_SignOutMessage) Reset() {
	*x = C2S_SignOutMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SignOutMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SignOutMessage) ProtoMessage() {}

func (x *C2S_SignOutMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SignOutMessage.ProtoReflect.Descriptor instead.
func (*C2S_SignOutMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{64}
}

func (x *C2S_SignOutMessage) GetAuthField1() string {
	if x != nil {
		return x.AuthField1
	}
	return ""
}

func (x *C2S_SignOutMessage) GetAuthField2() string {
	if x != nil {
		return x.AuthField2
	}
	return ""
}

type S2C_SignOutRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //1游客不能注销2认证信息匹配失败
}

func (x *S2C_SignOutRespMessage) Reset() {
	*x = S2C_SignOutRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SignOutRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SignOutRespMessage) ProtoMessage() {}

func (x *S2C_SignOutRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SignOutRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_SignOutRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{65}
}

func (x *S2C_SignOutRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_CancelSignOutMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_CancelSignOutMessage) Reset() {
	*x = C2S_CancelSignOutMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CancelSignOutMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CancelSignOutMessage) ProtoMessage() {}

func (x *C2S_CancelSignOutMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CancelSignOutMessage.ProtoReflect.Descriptor instead.
func (*C2S_CancelSignOutMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{66}
}

type S2C_CancelSignOutRespMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //非0失败,1账号信息不匹配,2账号未处于注销,3账号已经走完注销流程,无法恢复
}

func (x *S2C_CancelSignOutRespMessage) Reset() {
	*x = S2C_CancelSignOutRespMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CancelSignOutRespMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CancelSignOutRespMessage) ProtoMessage() {}

func (x *S2C_CancelSignOutRespMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CancelSignOutRespMessage.ProtoReflect.Descriptor instead.
func (*S2C_CancelSignOutRespMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{67}
}

func (x *S2C_CancelSignOutRespMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_RemoveNewMarkMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TypeId int32   `protobuf:"varint,1,opt,name=typeId,proto3" json:"typeId,omitempty"`        //new标签类型
	AryVal []int32 `protobuf:"varint,2,rep,packed,name=aryVal,proto3" json:"aryVal,omitempty"` //new标签数据
}

func (x *C2S_RemoveNewMarkMessage) Reset() {
	*x = C2S_RemoveNewMarkMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_RemoveNewMarkMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_RemoveNewMarkMessage) ProtoMessage() {}

func (x *C2S_RemoveNewMarkMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_RemoveNewMarkMessage.ProtoReflect.Descriptor instead.
func (*C2S_RemoveNewMarkMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{68}
}

func (x *C2S_RemoveNewMarkMessage) GetTypeId() int32 {
	if x != nil {
		return x.TypeId
	}
	return 0
}

func (x *C2S_RemoveNewMarkMessage) GetAryVal() []int32 {
	if x != nil {
		return x.AryVal
	}
	return nil
}

type S2C_RemoveNewMarkMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0成功
}

func (x *S2C_RemoveNewMarkMessage) Reset() {
	*x = S2C_RemoveNewMarkMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_RemoveNewMarkMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_RemoveNewMarkMessage) ProtoMessage() {}

func (x *S2C_RemoveNewMarkMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_RemoveNewMarkMessage.ProtoReflect.Descriptor instead.
func (*S2C_RemoveNewMarkMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{69}
}

func (x *S2C_RemoveNewMarkMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_BuyBatteryMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Num int32 `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"` //买多少电池
}

func (x *C2S_BuyBatteryMessage) Reset() {
	*x = C2S_BuyBatteryMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BuyBatteryMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BuyBatteryMessage) ProtoMessage() {}

func (x *C2S_BuyBatteryMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BuyBatteryMessage.ProtoReflect.Descriptor instead.
func (*C2S_BuyBatteryMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{70}
}

func (x *C2S_BuyBatteryMessage) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type S2C_BuyBatteryMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_BuyBatteryMessage) Reset() {
	*x = S2C_BuyBatteryMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BuyBatteryMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BuyBatteryMessage) ProtoMessage() {}

func (x *S2C_BuyBatteryMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BuyBatteryMessage.ProtoReflect.Descriptor instead.
func (*S2C_BuyBatteryMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{71}
}

func (x *S2C_BuyBatteryMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_SetBattleTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Team *BattleTeam `protobuf:"bytes,1,opt,name=team,proto3" json:"team,omitempty"` //编队
}

func (x *C2S_SetBattleTeamMessage) Reset() {
	*x = C2S_SetBattleTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SetBattleTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SetBattleTeamMessage) ProtoMessage() {}

func (x *C2S_SetBattleTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SetBattleTeamMessage.ProtoReflect.Descriptor instead.
func (*C2S_SetBattleTeamMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{72}
}

func (x *C2S_SetBattleTeamMessage) GetTeam() *BattleTeam {
	if x != nil {
		return x.Team
	}
	return nil
}

type S2C_SetBattleTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_SetBattleTeamMessage) Reset() {
	*x = S2C_SetBattleTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SetBattleTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SetBattleTeamMessage) ProtoMessage() {}

func (x *S2C_SetBattleTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SetBattleTeamMessage.ProtoReflect.Descriptor instead.
func (*S2C_SetBattleTeamMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{73}
}

func (x *S2C_SetBattleTeamMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_JackpotPointsGetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_JackpotPointsGetMessage) Reset() {
	*x = C2S_JackpotPointsGetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_JackpotPointsGetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_JackpotPointsGetMessage) ProtoMessage() {}

func (x *C2S_JackpotPointsGetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_JackpotPointsGetMessage.ProtoReflect.Descriptor instead.
func (*C2S_JackpotPointsGetMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{74}
}

type S2C_JackpotPointsGetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //非0积分不足
	List []*Condition `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`  //结果列表
}

func (x *S2C_JackpotPointsGetMessage) Reset() {
	*x = S2C_JackpotPointsGetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_JackpotPointsGetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_JackpotPointsGetMessage) ProtoMessage() {}

func (x *S2C_JackpotPointsGetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_JackpotPointsGetMessage.ProtoReflect.Descriptor instead.
func (*S2C_JackpotPointsGetMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{75}
}

func (x *S2C_JackpotPointsGetMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_JackpotPointsGetMessage) GetList() []*Condition {
	if x != nil {
		return x.List
	}
	return nil
}

type C2S_UnlockSpeedUpAutoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_UnlockSpeedUpAutoMessage) Reset() {
	*x = C2S_UnlockSpeedUpAutoMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UnlockSpeedUpAutoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UnlockSpeedUpAutoMessage) ProtoMessage() {}

func (x *C2S_UnlockSpeedUpAutoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UnlockSpeedUpAutoMessage.ProtoReflect.Descriptor instead.
func (*C2S_UnlockSpeedUpAutoMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{76}
}

type S2C_UnlockSpeedUpAutoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //非0消耗不足
}

func (x *S2C_UnlockSpeedUpAutoMessage) Reset() {
	*x = S2C_UnlockSpeedUpAutoMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UnlockSpeedUpAutoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UnlockSpeedUpAutoMessage) ProtoMessage() {}

func (x *S2C_UnlockSpeedUpAutoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UnlockSpeedUpAutoMessage.ProtoReflect.Descriptor instead.
func (*S2C_UnlockSpeedUpAutoMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{77}
}

func (x *S2C_UnlockSpeedUpAutoMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_SyncTimeStoneRecordDataMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SyncTimeStoneRecordDataMessage) Reset() {
	*x = C2S_SyncTimeStoneRecordDataMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncTimeStoneRecordDataMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncTimeStoneRecordDataMessage) ProtoMessage() {}

func (x *C2S_SyncTimeStoneRecordDataMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncTimeStoneRecordDataMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncTimeStoneRecordDataMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{78}
}

type S2C_SyncTimeStoneRecordDataMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	List *TimeStoneRecord `protobuf:"bytes,2,opt,name=list,proto3" json:"list,omitempty"`  //
}

func (x *S2C_SyncTimeStoneRecordDataMessage) Reset() {
	*x = S2C_SyncTimeStoneRecordDataMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncTimeStoneRecordDataMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncTimeStoneRecordDataMessage) ProtoMessage() {}

func (x *S2C_SyncTimeStoneRecordDataMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncTimeStoneRecordDataMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncTimeStoneRecordDataMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{79}
}

func (x *S2C_SyncTimeStoneRecordDataMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncTimeStoneRecordDataMessage) GetList() *TimeStoneRecord {
	if x != nil {
		return x.List
	}
	return nil
}

type C2S_UseTimeStoneMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` //记录id
}

func (x *C2S_UseTimeStoneMessage) Reset() {
	*x = C2S_UseTimeStoneMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UseTimeStoneMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UseTimeStoneMessage) ProtoMessage() {}

func (x *C2S_UseTimeStoneMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UseTimeStoneMessage.ProtoReflect.Descriptor instead.
func (*C2S_UseTimeStoneMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{80}
}

func (x *C2S_UseTimeStoneMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type S2C_UseTimeStoneMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_UseTimeStoneMessage) Reset() {
	*x = S2C_UseTimeStoneMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UseTimeStoneMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UseTimeStoneMessage) ProtoMessage() {}

func (x *S2C_UseTimeStoneMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UseTimeStoneMessage.ProtoReflect.Descriptor instead.
func (*S2C_UseTimeStoneMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{81}
}

func (x *S2C_UseTimeStoneMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_GetAdRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type AdType `protobuf:"varint,1,opt,name=type,proto3,enum=proto.AdType" json:"type,omitempty"` //类型
}

func (x *C2S_GetAdRewardMessage) Reset() {
	*x = C2S_GetAdRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetAdRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetAdRewardMessage) ProtoMessage() {}

func (x *C2S_GetAdRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetAdRewardMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetAdRewardMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{82}
}

func (x *C2S_GetAdRewardMessage) GetType() AdType {
	if x != nil {
		return x.Type
	}
	return AdType_RecoveryTrainEnergy
}

type S2C_GetAdRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Data *anypb.Any `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`  //返回的数据
}

func (x *S2C_GetAdRewardMessage) Reset() {
	*x = S2C_GetAdRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetAdRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetAdRewardMessage) ProtoMessage() {}

func (x *S2C_GetAdRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetAdRewardMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetAdRewardMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{83}
}

func (x *S2C_GetAdRewardMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetAdRewardMessage) GetData() *anypb.Any {
	if x != nil {
		return x.Data
	}
	return nil
}

type S2C_OnGetBurstTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *BurstTaskItem `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` //
}

func (x *S2C_OnGetBurstTaskMessage) Reset() {
	*x = S2C_OnGetBurstTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_msg_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_OnGetBurstTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_OnGetBurstTaskMessage) ProtoMessage() {}

func (x *S2C_OnGetBurstTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_msg_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_OnGetBurstTaskMessage.ProtoReflect.Descriptor instead.
func (*S2C_OnGetBurstTaskMessage) Descriptor() ([]byte, []int) {
	return file_msg_proto_rawDescGZIP(), []int{84}
}

func (x *S2C_OnGetBurstTaskMessage) GetData() *BurstTaskItem {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_msg_proto protoreflect.FileDescriptor

var file_msg_proto_rawDesc = []byte{
	0x0a, 0x09, 0x6d, 0x73, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2c, 0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xab, 0x01, 0x0a, 0x13, 0x43, 0x32, 0x53, 0x5f, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x76, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x6f, 0x73, 0x22, 0x55, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x7d, 0x0a, 0x17, 0x43, 0x32,
	0x53, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x2a, 0x0a,
	0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x8d, 0x01, 0x0a, 0x17, 0x43, 0x32,
	0x53, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x42, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x69,
	0x73, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x69, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x12, 0x2a, 0x0a,
	0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x5b, 0x0a, 0x15, 0x43, 0x32, 0x53,
	0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x6e, 0x0a, 0x12, 0x43, 0x32, 0x53, 0x5f, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x46, 0x42, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x06,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x6d, 0x0a, 0x11, 0x43, 0x32, 0x53, 0x5f, 0x42, 0x69,
	0x6e, 0x64, 0x46, 0x42, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa1, 0x01, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x2a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xa0, 0x01, 0x0a, 0x14, 0x43, 0x32,
	0x53, 0x5f, 0x42, 0x69, 0x6e, 0x64, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x94, 0x01, 0x0a,
	0x16, 0x43, 0x32, 0x53, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55,
	0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x55, 0x72, 0x6c, 0x22, 0x93, 0x01, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x42, 0x69, 0x6e, 0x64,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a,
	0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x22, 0x8e, 0x01, 0x0a, 0x12, 0x43, 0x32,
	0x53, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x57, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x22, 0x57, 0x0a, 0x15, 0x43, 0x32,
	0x53, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x57, 0x78, 0x41, 0x70, 0x70, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x54, 0x61, 0x70, 0x54, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a,
	0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x69, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x61, 0x63, 0x4b, 0x65, 0x79, 0x22, 0x4e, 0x0a, 0x18, 0x43, 0x32, 0x53,
	0x5f, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x22, 0x46, 0x0a, 0x1e, 0x53, 0x32, 0x43,
	0x5f, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67,
	0x65, 0x22, 0x59, 0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x2b, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x81, 0x01, 0x0a,
	0x15, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c,
	0x22, 0x2d, 0x0a, 0x11, 0x53, 0x32, 0x43, 0x5f, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22,
	0x64, 0x0a, 0x1a, 0x43, 0x32, 0x53, 0x5f, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x47, 0x75,
	0x69, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65,
	0x47, 0x75, 0x69, 0x64, 0x65, 0x22, 0x36, 0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x1a, 0x0a,
	0x18, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x58, 0x0a, 0x1b, 0x53, 0x32, 0x43,
	0x5f, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x06,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x06, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x22, 0x4c, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x2f, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x22, 0x41, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0x28, 0x0a, 0x14, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x6d, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x63, 0x6d, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x22, 0x2f,
	0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x6d, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x70,
	0x6c, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x41, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x4a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x52, 0x65,
	0x71, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x51, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x4a, 0x61, 0x63, 0x6b, 0x70, 0x6f,
	0x74, 0x52, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x24, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x95, 0x01, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x2b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x52, 0x09, 0x63, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e,
	0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x68, 0x65, 0x61, 0x72, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x68, 0x65, 0x61, 0x72, 0x74, 0x22, 0x99, 0x01,
	0x0a, 0x1a, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x68, 0x65, 0x61, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x68, 0x65, 0x61, 0x72, 0x74, 0x12, 0x2b, 0x0a, 0x09,
	0x63, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x09,
	0x63, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x43, 0x32, 0x53,
	0x5f, 0x53, 0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x54, 0x0a, 0x12, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65,
	0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x18, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x74, 0x6f,
	0x70, 0x53, 0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x58, 0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x18, 0x0a, 0x16, 0x43, 0x32, 0x53,
	0x5f, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0x58, 0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x53,
	0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x2e, 0x0a,
	0x18, 0x43, 0x32, 0x53, 0x5f, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x45, 0x6e, 0x65, 0x72,
	0x67, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x59, 0x0a,
	0x1c, 0x53, 0x32, 0x43, 0x5f, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x45, 0x6e, 0x65, 0x72,
	0x67, 0x79, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x25, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79,
	0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x11, 0x0a, 0x0f, 0x43, 0x32, 0x53, 0x5f,
	0x53, 0x79, 0x6e, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x61, 0x0a, 0x0f, 0x53,
	0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x52, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x72, 0x69, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x72, 0x69, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x2b,
	0x0a, 0x11, 0x53, 0x32, 0x43, 0x5f, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x4e, 0x0a, 0x1a, 0x43,
	0x32, 0x53, 0x5f, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x47, 0x75, 0x69, 0x64, 0x65, 0x53, 0x74,
	0x65, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69,
	0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x67, 0x75, 0x69, 0x64,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x65, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x65, 0x70, 0x49, 0x64, 0x22, 0x36, 0x0a, 0x20, 0x53,
	0x32, 0x43, 0x5f, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x47, 0x75, 0x69, 0x64, 0x65, 0x53, 0x74,
	0x65, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x22, 0x2c, 0x0a, 0x1a, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x36, 0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x17, 0x0a, 0x15, 0x43, 0x32, 0x53,
	0x5f, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x52, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x6c,
	0x61, 0x6e, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x25, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x52, 0x06,
	0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x22, 0x1a, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x79,
	0x6e, 0x63, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x85, 0x05, 0x0a, 0x1c, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6a, 0x61, 0x63, 0x6b, 0x70,
	0x6f, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4e, 0x75,
	0x6d, 0x12, 0x25, 0x0a, 0x06, 0x77, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64,
	0x52, 0x06, 0x77, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72,
	0x67, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x12,
	0x2e, 0x0a, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b,
	0x48, 0x6f, 0x6c, 0x65, 0x52, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x12,
	0x2b, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x05,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x05, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x12, 0x2c, 0x0a, 0x11, 0x6e, 0x65, 0x78, 0x74, 0x44, 0x61, 0x79, 0x53, 0x75, 0x72, 0x70, 0x6c,
	0x75, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6e, 0x65, 0x78,
	0x74, 0x44, 0x61, 0x79, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30,
	0x0a, 0x13, 0x6e, 0x65, 0x78, 0x74, 0x57, 0x65, 0x65, 0x6b, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x6e, 0x65, 0x78,
	0x74, 0x57, 0x65, 0x65, 0x6b, 0x53, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x33, 0x0a, 0x0a, 0x61, 0x72, 0x72, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x72, 0x72,
	0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x0a, 0x61, 0x72, 0x72, 0x65, 0x73,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32, 0x0a, 0x09, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61,
	0x73, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2e, 0x0a, 0x09, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x09,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65,
	0x52, 0x0a, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x12, 0x2c, 0x0a, 0x11,
	0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x2f, 0x0a, 0x15, 0x43, 0x32,
	0x53, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x22, 0x54, 0x0a, 0x19, 0x53,
	0x32, 0x43, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x04,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6d, 0x61, 0x69,
	0x6c, 0x22, 0x3b, 0x0a, 0x14, 0x53, 0x32, 0x43, 0x5f, 0x4f, 0x6e, 0x4e, 0x65, 0x77, 0x4d, 0x61,
	0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x1b,
	0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x61, 0x64,
	0x4d, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x33, 0x0a, 0x1d, 0x53,
	0x32, 0x43, 0x5f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x2f, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x4d, 0x61,
	0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x69,
	0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49,
	0x64, 0x22, 0x6d, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x4d,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x22, 0x29, 0x0a, 0x13, 0x43, 0x32, 0x53, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x3e, 0x0a, 0x17, 0x53,
	0x32, 0x43, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x27, 0x0a, 0x13, 0x43,
	0x32, 0x53, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x64, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x64, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x63, 0x64, 0x6b, 0x22, 0x55, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x43, 0x64, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x36, 0x0a, 0x18, 0x43,
	0x32, 0x53, 0x5f, 0x44, 0x69, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x32, 0x0a, 0x1c, 0x53, 0x32, 0x43, 0x5f, 0x44, 0x69, 0x79, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x54, 0x0a, 0x12, 0x43, 0x32, 0x53, 0x5f, 0x53,
	0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x61, 0x75, 0x74, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x31, 0x12, 0x1e, 0x0a,
	0x0a, 0x61, 0x75, 0x74, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x32, 0x22, 0x2c, 0x0a,
	0x16, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x1a, 0x0a, 0x18, 0x43,
	0x32, 0x53, 0x5f, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x32, 0x0a, 0x1c, 0x53, 0x32, 0x43, 0x5f, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x4a, 0x0a, 0x18, 0x43,
	0x32, 0x53, 0x5f, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4e, 0x65, 0x77, 0x4d, 0x61, 0x72, 0x6b,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x72, 0x79, 0x56, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x06, 0x61, 0x72, 0x79, 0x56, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4e, 0x65, 0x77, 0x4d, 0x61, 0x72, 0x6b, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x29, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x42,
	0x75, 0x79, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e,
	0x75, 0x6d, 0x22, 0x2b, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x75, 0x79, 0x42, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22,
	0x41, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x65, 0x74, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x74,
	0x65, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x04, 0x74, 0x65,
	0x61, 0x6d, 0x22, 0x2e, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x65, 0x74, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x1d, 0x0a, 0x1b, 0x43, 0x32, 0x53, 0x5f, 0x4a, 0x61, 0x63, 0x6b, 0x70, 0x6f,
	0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x57, 0x0a, 0x1b, 0x53, 0x32, 0x43, 0x5f, 0x4a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x1e, 0x0a, 0x1c, 0x43, 0x32,
	0x53, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x41,
	0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x32, 0x0a, 0x1c, 0x53, 0x32,
	0x43, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x55, 0x70, 0x41,
	0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x24,
	0x0a, 0x22, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74,
	0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x64, 0x0a, 0x22, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2a,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x29, 0x0a, 0x17, 0x43, 0x32,
	0x53, 0x5f, 0x55, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2d, 0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x55, 0x73, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x22, 0x3b, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65, 0x74, 0x41,
	0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x21,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x22, 0x56, 0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x41, 0x64, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x41, 0x6e, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x45, 0x0a, 0x19, 0x53, 0x32, 0x43,
	0x5f, 0x4f, 0x6e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x72, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x75, 0x72,
	0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_msg_proto_rawDescOnce sync.Once
	file_msg_proto_rawDescData = file_msg_proto_rawDesc
)

func file_msg_proto_rawDescGZIP() []byte {
	file_msg_proto_rawDescOnce.Do(func() {
		file_msg_proto_rawDescData = protoimpl.X.CompressGZIP(file_msg_proto_rawDescData)
	})
	return file_msg_proto_rawDescData
}

var file_msg_proto_msgTypes = make([]protoimpl.MessageInfo, 85)
var file_msg_proto_goTypes = []interface{}{
	(*S2C_ErrorResultMessage)(nil),             // 0: proto.S2C_ErrorResultMessage
	(*C2S_RegisterMessage)(nil),                // 1: proto.C2S_RegisterMessage
	(*S2C_RegisterResultMessage)(nil),          // 2: proto.S2C_RegisterResultMessage
	(*C2S_LoginAccountMessage)(nil),            // 3: proto.C2S_LoginAccountMessage
	(*C2S_LoginByTokenMessage)(nil),            // 4: proto.C2S_LoginByTokenMessage
	(*C2S_LoginGuestMessage)(nil),              // 5: proto.C2S_LoginGuestMessage
	(*C2S_LoginFBMessage)(nil),                 // 6: proto.C2S_LoginFBMessage
	(*C2S_BindFBMessage)(nil),                  // 7: proto.C2S_BindFBMessage
	(*C2S_LoginAppleMessage)(nil),              // 8: proto.C2S_LoginAppleMessage
	(*C2S_BindAppleMessage)(nil),               // 9: proto.C2S_BindAppleMessage
	(*C2S_LoginGoogleMessage)(nil),             // 10: proto.C2S_LoginGoogleMessage
	(*C2S_BindGoogleMessage)(nil),              // 11: proto.C2S_BindGoogleMessage
	(*C2S_LoginWxMessage)(nil),                 // 12: proto.C2S_LoginWxMessage
	(*C2S_LoginWxAppMessage)(nil),              // 13: proto.C2S_LoginWxAppMessage
	(*C2S_LoginTapTapMessage)(nil),             // 14: proto.C2S_LoginTapTapMessage
	(*C2S_CertificationMessage)(nil),           // 15: proto.C2S_CertificationMessage
	(*S2C_CertificationResultMessage)(nil),     // 16: proto.S2C_CertificationResultMessage
	(*S2C_LoginResultMessage)(nil),             // 17: proto.S2C_LoginResultMessage
	(*S2C_BindResultMessage)(nil),              // 18: proto.S2C_BindResultMessage
	(*S2C_NoticeMessage)(nil),                  // 19: proto.S2C_NoticeMessage
	(*C2S_EnterGameServerMessage)(nil),         // 20: proto.C2S_EnterGameServerMessage
	(*S2C_EnterGameServerResultMessage)(nil),   // 21: proto.S2C_EnterGameServerResultMessage
	(*C2S_GetPlayerInfoMessage)(nil),           // 22: proto.C2S_GetPlayerInfoMessage
	(*S2C_GetPlayerInfoResMessage)(nil),        // 23: proto.S2C_GetPlayerInfoResMessage
	(*S2C_CurrencyChangeMessage)(nil),          // 24: proto.S2C_CurrencyChangeMessage
	(*S2C_BagItemChangeMessage)(nil),           // 25: proto.S2C_BagItemChangeMessage
	(*C2S_GmExecuteMessage)(nil),               // 26: proto.C2S_GmExecuteMessage
	(*S2C_GmExecuteRspMessage)(nil),            // 27: proto.S2C_GmExecuteRspMessage
	(*C2S_JackpotReqMessage)(nil),              // 28: proto.C2S_JackpotReqMessage
	(*S2C_JackpotRspMessage)(nil),              // 29: proto.S2C_JackpotRspMessage
	(*C2S_CollectItemMessage)(nil),             // 30: proto.C2S_CollectItemMessage
	(*S2C_CollectItemRespMessage)(nil),         // 31: proto.S2C_CollectItemRespMessage
	(*C2S_SpeedUpMessage)(nil),                 // 32: proto.C2S_SpeedUpMessage
	(*S2C_SpeedUpMessage)(nil),                 // 33: proto.S2C_SpeedUpMessage
	(*C2S_StopSpeedUpMessage)(nil),             // 34: proto.C2S_StopSpeedUpMessage
	(*S2C_StopSpeedUpMessage)(nil),             // 35: proto.S2C_StopSpeedUpMessage
	(*C2S_SyncSpeedUpMessage)(nil),             // 36: proto.C2S_SyncSpeedUpMessage
	(*S2C_SyncSpeedUpMessage)(nil),             // 37: proto.S2C_SyncSpeedUpMessage
	(*C2S_RecoverEnergyMessage)(nil),           // 38: proto.C2S_RecoverEnergyMessage
	(*S2C_RecoverEnergyRespMessage)(nil),       // 39: proto.S2C_RecoverEnergyRespMessage
	(*C2S_SyncMessage)(nil),                    // 40: proto.C2S_SyncMessage
	(*S2C_SyncMessage)(nil),                    // 41: proto.S2C_SyncMessage
	(*S2C_LogoutMessage)(nil),                  // 42: proto.S2C_LogoutMessage
	(*C2S_RecordGuideStepMessage)(nil),         // 43: proto.C2S_RecordGuideStepMessage
	(*S2C_RecordGuideStepResultMessage)(nil),   // 44: proto.S2C_RecordGuideStepResultMessage
	(*C2S_ClaimTaskRewardMessage)(nil),         // 45: proto.C2S_ClaimTaskRewardMessage
	(*S2C_ClaimTaskRewardResultMessage)(nil),   // 46: proto.S2C_ClaimTaskRewardResultMessage
	(*C2S_SyncPlanetMessage)(nil),              // 47: proto.C2S_SyncPlanetMessage
	(*S2C_SyncPlanetMessage)(nil),              // 48: proto.S2C_SyncPlanetMessage
	(*C2S_SyncDailyInfoMessage)(nil),           // 49: proto.C2S_SyncDailyInfoMessage
	(*S2C_SyncDailyInfoRespMessage)(nil),       // 50: proto.S2C_SyncDailyInfoRespMessage
	(*C2S_MailDetailMessage)(nil),              // 51: proto.C2S_MailDetailMessage
	(*S2C_MailDetailRespMessage)(nil),          // 52: proto.S2C_MailDetailRespMessage
	(*S2C_OnNewMailMessage)(nil),               // 53: proto.S2C_OnNewMailMessage
	(*C2S_DeleteReadMailMessage)(nil),          // 54: proto.C2S_DeleteReadMailMessage
	(*S2C_DeleteReadMailRespMessage)(nil),      // 55: proto.S2C_DeleteReadMailRespMessage
	(*C2S_AttachMailMessage)(nil),              // 56: proto.C2S_AttachMailMessage
	(*S2C_AttachMailRespMessage)(nil),          // 57: proto.S2C_AttachMailRespMessage
	(*C2S_MailListMessage)(nil),                // 58: proto.C2S_MailListMessage
	(*S2C_MailListRespMessage)(nil),            // 59: proto.S2C_MailListRespMessage
	(*C2S_CheckCdkMessage)(nil),                // 60: proto.C2S_CheckCdkMessage
	(*S2C_CheckCdkMessage)(nil),                // 61: proto.S2C_CheckCdkMessage
	(*C2S_DiyPlayerInfoMessage)(nil),           // 62: proto.C2S_DiyPlayerInfoMessage
	(*S2C_DiyPlayerInfoRespMessage)(nil),       // 63: proto.S2C_DiyPlayerInfoRespMessage
	(*C2S_SignOutMessage)(nil),                 // 64: proto.C2S_SignOutMessage
	(*S2C_SignOutRespMessage)(nil),             // 65: proto.S2C_SignOutRespMessage
	(*C2S_CancelSignOutMessage)(nil),           // 66: proto.C2S_CancelSignOutMessage
	(*S2C_CancelSignOutRespMessage)(nil),       // 67: proto.S2C_CancelSignOutRespMessage
	(*C2S_RemoveNewMarkMessage)(nil),           // 68: proto.C2S_RemoveNewMarkMessage
	(*S2C_RemoveNewMarkMessage)(nil),           // 69: proto.S2C_RemoveNewMarkMessage
	(*C2S_BuyBatteryMessage)(nil),              // 70: proto.C2S_BuyBatteryMessage
	(*S2C_BuyBatteryMessage)(nil),              // 71: proto.S2C_BuyBatteryMessage
	(*C2S_SetBattleTeamMessage)(nil),           // 72: proto.C2S_SetBattleTeamMessage
	(*S2C_SetBattleTeamMessage)(nil),           // 73: proto.S2C_SetBattleTeamMessage
	(*C2S_JackpotPointsGetMessage)(nil),        // 74: proto.C2S_JackpotPointsGetMessage
	(*S2C_JackpotPointsGetMessage)(nil),        // 75: proto.S2C_JackpotPointsGetMessage
	(*C2S_UnlockSpeedUpAutoMessage)(nil),       // 76: proto.C2S_UnlockSpeedUpAutoMessage
	(*S2C_UnlockSpeedUpAutoMessage)(nil),       // 77: proto.S2C_UnlockSpeedUpAutoMessage
	(*C2S_SyncTimeStoneRecordDataMessage)(nil), // 78: proto.C2S_SyncTimeStoneRecordDataMessage
	(*S2C_SyncTimeStoneRecordDataMessage)(nil), // 79: proto.S2C_SyncTimeStoneRecordDataMessage
	(*C2S_UseTimeStoneMessage)(nil),            // 80: proto.C2S_UseTimeStoneMessage
	(*S2C_UseTimeStoneMessage)(nil),            // 81: proto.S2C_UseTimeStoneMessage
	(*C2S_GetAdRewardMessage)(nil),             // 82: proto.C2S_GetAdRewardMessage
	(*S2C_GetAdRewardMessage)(nil),             // 83: proto.S2C_GetAdRewardMessage
	(*S2C_OnGetBurstTaskMessage)(nil),          // 84: proto.S2C_OnGetBurstTaskMessage
	(*LoginCommon)(nil),                        // 85: proto.LoginCommon
	(*UserInfo)(nil),                           // 86: proto.UserInfo
	(*Player)(nil),                             // 87: proto.Player
	(*CurrencyInfo)(nil),                       // 88: proto.CurrencyInfo
	(*ItemInfo)(nil),                           // 89: proto.ItemInfo
	(*Condition)(nil),                          // 90: proto.Condition
	(*Output)(nil),                             // 91: proto.Output
	(*Energy)(nil),                             // 92: proto.Energy
	(*Planet)(nil),                             // 93: proto.Planet
	(*Wanted)(nil),                             // 94: proto.Wanted
	(*BlackHole)(nil),                          // 95: proto.BlackHole
	(*Instance)(nil),                           // 96: proto.Instance
	(*Store)(nil),                              // 97: proto.Store
	(*ArrestModule)(nil),                       // 98: proto.ArrestModule
	(*DailyTaskInfo)(nil),                      // 99: proto.DailyTaskInfo
	(*Transport)(nil),                          // 100: proto.Transport
	(*SpaceStone)(nil),                         // 101: proto.SpaceStone
	(*MailInfo)(nil),                           // 102: proto.MailInfo
	(*BattleTeam)(nil),                         // 103: proto.BattleTeam
	(*TimeStoneRecord)(nil),                    // 104: proto.TimeStoneRecord
	(AdType)(0),                                // 105: proto.AdType
	(*anypb.Any)(nil),                          // 106: google.protobuf.Any
	(*BurstTaskItem)(nil),                      // 107: proto.BurstTaskItem
}
var file_msg_proto_depIdxs = []int32{
	85,  // 0: proto.C2S_LoginAccountMessage.common:type_name -> proto.LoginCommon
	85,  // 1: proto.C2S_LoginByTokenMessage.common:type_name -> proto.LoginCommon
	85,  // 2: proto.C2S_LoginGuestMessage.common:type_name -> proto.LoginCommon
	85,  // 3: proto.C2S_LoginFBMessage.common:type_name -> proto.LoginCommon
	85,  // 4: proto.C2S_BindFBMessage.common:type_name -> proto.LoginCommon
	85,  // 5: proto.C2S_LoginAppleMessage.common:type_name -> proto.LoginCommon
	85,  // 6: proto.C2S_BindAppleMessage.common:type_name -> proto.LoginCommon
	85,  // 7: proto.C2S_LoginGoogleMessage.common:type_name -> proto.LoginCommon
	85,  // 8: proto.C2S_BindGoogleMessage.common:type_name -> proto.LoginCommon
	85,  // 9: proto.C2S_LoginWxMessage.common:type_name -> proto.LoginCommon
	85,  // 10: proto.C2S_LoginWxAppMessage.common:type_name -> proto.LoginCommon
	85,  // 11: proto.C2S_LoginTapTapMessage.common:type_name -> proto.LoginCommon
	86,  // 12: proto.S2C_LoginResultMessage.userInfo:type_name -> proto.UserInfo
	87,  // 13: proto.S2C_GetPlayerInfoResMessage.player:type_name -> proto.Player
	88,  // 14: proto.S2C_CurrencyChangeMessage.currency:type_name -> proto.CurrencyInfo
	89,  // 15: proto.S2C_BagItemChangeMessage.items:type_name -> proto.ItemInfo
	90,  // 16: proto.S2C_JackpotRspMessage.list:type_name -> proto.Condition
	91,  // 17: proto.C2S_CollectItemMessage.carriages:type_name -> proto.Output
	91,  // 18: proto.S2C_CollectItemRespMessage.carriages:type_name -> proto.Output
	92,  // 19: proto.S2C_RecoverEnergyRespMessage.energy:type_name -> proto.Energy
	93,  // 20: proto.S2C_SyncPlanetMessage.planet:type_name -> proto.Planet
	94,  // 21: proto.S2C_SyncDailyInfoRespMessage.wanted:type_name -> proto.Wanted
	92,  // 22: proto.S2C_SyncDailyInfoRespMessage.energy:type_name -> proto.Energy
	95,  // 23: proto.S2C_SyncDailyInfoRespMessage.blackHole:type_name -> proto.BlackHole
	96,  // 24: proto.S2C_SyncDailyInfoRespMessage.instance:type_name -> proto.Instance
	97,  // 25: proto.S2C_SyncDailyInfoRespMessage.store:type_name -> proto.Store
	98,  // 26: proto.S2C_SyncDailyInfoRespMessage.arrestData:type_name -> proto.ArrestModule
	99,  // 27: proto.S2C_SyncDailyInfoRespMessage.dailyTask:type_name -> proto.DailyTaskInfo
	100, // 28: proto.S2C_SyncDailyInfoRespMessage.transport:type_name -> proto.Transport
	101, // 29: proto.S2C_SyncDailyInfoRespMessage.spaceStone:type_name -> proto.SpaceStone
	102, // 30: proto.S2C_MailDetailRespMessage.mail:type_name -> proto.MailInfo
	102, // 31: proto.S2C_OnNewMailMessage.mail:type_name -> proto.MailInfo
	90,  // 32: proto.S2C_AttachMailRespMessage.rewards:type_name -> proto.Condition
	102, // 33: proto.S2C_MailListRespMessage.mail:type_name -> proto.MailInfo
	90,  // 34: proto.S2C_CheckCdkMessage.rewards:type_name -> proto.Condition
	103, // 35: proto.C2S_SetBattleTeamMessage.team:type_name -> proto.BattleTeam
	90,  // 36: proto.S2C_JackpotPointsGetMessage.list:type_name -> proto.Condition
	104, // 37: proto.S2C_SyncTimeStoneRecordDataMessage.list:type_name -> proto.TimeStoneRecord
	105, // 38: proto.C2S_GetAdRewardMessage.type:type_name -> proto.AdType
	106, // 39: proto.S2C_GetAdRewardMessage.data:type_name -> google.protobuf.Any
	107, // 40: proto.S2C_OnGetBurstTaskMessage.data:type_name -> proto.BurstTaskItem
	41,  // [41:41] is the sub-list for method output_type
	41,  // [41:41] is the sub-list for method input_type
	41,  // [41:41] is the sub-list for extension type_name
	41,  // [41:41] is the sub-list for extension extendee
	0,   // [0:41] is the sub-list for field type_name
}

func init() { file_msg_proto_init() }
func file_msg_proto_init() {
	if File_msg_proto != nil {
		return
	}
	file_struct_proto_init()
	file_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_msg_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ErrorResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_RegisterMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_RegisterResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginAccountMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginByTokenMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginGuestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginFBMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BindFBMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginAppleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BindAppleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginGoogleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BindGoogleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginWxMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginWxAppMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginTapTapMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CertificationMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CertificationResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_LoginResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BindResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_NoticeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_EnterGameServerMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_EnterGameServerResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetPlayerInfoMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetPlayerInfoResMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CurrencyChangeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagItemChangeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GmExecuteMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GmExecuteRspMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_JackpotReqMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_JackpotRspMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CollectItemMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CollectItemRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SpeedUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SpeedUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_StopSpeedUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_StopSpeedUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncSpeedUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncSpeedUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_RecoverEnergyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_RecoverEnergyRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_LogoutMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_RecordGuideStepMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_RecordGuideStepResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ClaimTaskRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ClaimTaskRewardResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncPlanetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncPlanetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncDailyInfoMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncDailyInfoRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MailDetailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MailDetailRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_OnNewMailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_DeleteReadMailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_DeleteReadMailRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_AttachMailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_AttachMailRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MailListMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MailListRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CheckCdkMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CheckCdkMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_DiyPlayerInfoMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_DiyPlayerInfoRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SignOutMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SignOutRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CancelSignOutMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CancelSignOutRespMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_RemoveNewMarkMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_RemoveNewMarkMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BuyBatteryMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BuyBatteryMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SetBattleTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SetBattleTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_JackpotPointsGetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_JackpotPointsGetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UnlockSpeedUpAutoMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UnlockSpeedUpAutoMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncTimeStoneRecordDataMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncTimeStoneRecordDataMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UseTimeStoneMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UseTimeStoneMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetAdRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetAdRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_msg_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_OnGetBurstTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_msg_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   85,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_msg_proto_goTypes,
		DependencyIndexes: file_msg_proto_depIdxs,
		MessageInfos:      file_msg_proto_msgTypes,
	}.Build()
	File_msg_proto = out.File
	file_msg_proto_rawDesc = nil
	file_msg_proto_goTypes = nil
	file_msg_proto_depIdxs = nil
}
