// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: train.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_BuyCarriageMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //车厢配置id
}

func (x *C2S_BuyCarriageMessage) Reset() {
	*x = C2S_BuyCarriageMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BuyCarriageMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BuyCarriageMessage) ProtoMessage() {}

func (x *C2S_BuyCarriageMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BuyCarriageMessage.ProtoReflect.Descriptor instead.
func (*C2S_BuyCarriageMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_BuyCarriageMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_BuyCarriageResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`        //code=0代表无错误,否则就去读failList,里面存检查失败的条件列表
	FailList []*Condition  `protobuf:"bytes,2,rep,name=failList,proto3" json:"failList,omitempty"` //条件check失败的列表
	Carriage *CarriageInfo `protobuf:"bytes,3,opt,name=carriage,proto3" json:"carriage,omitempty"` //新增的车厢
}

func (x *S2C_BuyCarriageResultMessage) Reset() {
	*x = S2C_BuyCarriageResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BuyCarriageResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BuyCarriageResultMessage) ProtoMessage() {}

func (x *S2C_BuyCarriageResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BuyCarriageResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_BuyCarriageResultMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_BuyCarriageResultMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_BuyCarriageResultMessage) GetFailList() []*Condition {
	if x != nil {
		return x.FailList
	}
	return nil
}

func (x *S2C_BuyCarriageResultMessage) GetCarriage() *CarriageInfo {
	if x != nil {
		return x.Carriage
	}
	return nil
}

type C2S_GetCarriageBuildInfoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //车厢id
}

func (x *C2S_GetCarriageBuildInfoMessage) Reset() {
	*x = C2S_GetCarriageBuildInfoMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetCarriageBuildInfoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetCarriageBuildInfoMessage) ProtoMessage() {}

func (x *C2S_GetCarriageBuildInfoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetCarriageBuildInfoMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetCarriageBuildInfoMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_GetCarriageBuildInfoMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_GetCarriageBuildInfoResMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`           //code=0
	BuildTime int32 `protobuf:"varint,2,opt,name=buildTime,proto3" json:"buildTime,omitempty"` //建造剩余时间
	OpenDoor  bool  `protobuf:"varint,3,opt,name=openDoor,proto3" json:"openDoor,omitempty"`   //是否打开猫猫门
}

func (x *S2C_GetCarriageBuildInfoResMessage) Reset() {
	*x = S2C_GetCarriageBuildInfoResMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetCarriageBuildInfoResMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetCarriageBuildInfoResMessage) ProtoMessage() {}

func (x *S2C_GetCarriageBuildInfoResMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetCarriageBuildInfoResMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetCarriageBuildInfoResMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_GetCarriageBuildInfoResMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetCarriageBuildInfoResMessage) GetBuildTime() int32 {
	if x != nil {
		return x.BuildTime
	}
	return 0
}

func (x *S2C_GetCarriageBuildInfoResMessage) GetOpenDoor() bool {
	if x != nil {
		return x.OpenDoor
	}
	return false
}

type C2S_OpenCarriageDoorMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //车厢id
}

func (x *C2S_OpenCarriageDoorMessage) Reset() {
	*x = C2S_OpenCarriageDoorMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_OpenCarriageDoorMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_OpenCarriageDoorMessage) ProtoMessage() {}

func (x *C2S_OpenCarriageDoorMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_OpenCarriageDoorMessage.ProtoReflect.Descriptor instead.
func (*C2S_OpenCarriageDoorMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_OpenCarriageDoorMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_OpenCarriageDoorResMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0
}

func (x *S2C_OpenCarriageDoorResMessage) Reset() {
	*x = S2C_OpenCarriageDoorResMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_OpenCarriageDoorResMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_OpenCarriageDoorResMessage) ProtoMessage() {}

func (x *S2C_OpenCarriageDoorResMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_OpenCarriageDoorResMessage.ProtoReflect.Descriptor instead.
func (*S2C_OpenCarriageDoorResMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_OpenCarriageDoorResMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_BuildLevelUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CarriageId int32 `protobuf:"varint,1,opt,name=carriageId,proto3" json:"carriageId,omitempty"` //车厢id
	Order      int32 `protobuf:"varint,2,opt,name=order,proto3" json:"order,omitempty"`           //设施序号
}

func (x *C2S_BuildLevelUpMessage) Reset() {
	*x = C2S_BuildLevelUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BuildLevelUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BuildLevelUpMessage) ProtoMessage() {}

func (x *C2S_BuildLevelUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BuildLevelUpMessage.ProtoReflect.Descriptor instead.
func (*C2S_BuildLevelUpMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_BuildLevelUpMessage) GetCarriageId() int32 {
	if x != nil {
		return x.CarriageId
	}
	return 0
}

func (x *C2S_BuildLevelUpMessage) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

type S2C_BuildLevelUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0
}

func (x *S2C_BuildLevelUpMessage) Reset() {
	*x = S2C_BuildLevelUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BuildLevelUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BuildLevelUpMessage) ProtoMessage() {}

func (x *S2C_BuildLevelUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BuildLevelUpMessage.ProtoReflect.Descriptor instead.
func (*S2C_BuildLevelUpMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_BuildLevelUpMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ChangeBuildSkinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CarriageId int32 `protobuf:"varint,1,opt,name=carriageId,proto3" json:"carriageId,omitempty"` //车厢id
	Order      int32 `protobuf:"varint,2,opt,name=order,proto3" json:"order,omitempty"`           //设施序号
	Skin       int32 `protobuf:"varint,3,opt,name=skin,proto3" json:"skin,omitempty"`             //设施皮肤
}

func (x *C2S_ChangeBuildSkinMessage) Reset() {
	*x = C2S_ChangeBuildSkinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChangeBuildSkinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChangeBuildSkinMessage) ProtoMessage() {}

func (x *C2S_ChangeBuildSkinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChangeBuildSkinMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChangeBuildSkinMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{8}
}

func (x *C2S_ChangeBuildSkinMessage) GetCarriageId() int32 {
	if x != nil {
		return x.CarriageId
	}
	return 0
}

func (x *C2S_ChangeBuildSkinMessage) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *C2S_ChangeBuildSkinMessage) GetSkin() int32 {
	if x != nil {
		return x.Skin
	}
	return 0
}

type S2C_ChangeBuildSkinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0
}

func (x *S2C_ChangeBuildSkinMessage) Reset() {
	*x = S2C_ChangeBuildSkinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChangeBuildSkinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChangeBuildSkinMessage) ProtoMessage() {}

func (x *S2C_ChangeBuildSkinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChangeBuildSkinMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChangeBuildSkinMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_ChangeBuildSkinMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_CarriageThemeLvUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CarriageId int32 `protobuf:"varint,1,opt,name=carriageId,proto3" json:"carriageId,omitempty"` //车厢id
}

func (x *C2S_CarriageThemeLvUpMessage) Reset() {
	*x = C2S_CarriageThemeLvUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CarriageThemeLvUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CarriageThemeLvUpMessage) ProtoMessage() {}

func (x *C2S_CarriageThemeLvUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CarriageThemeLvUpMessage.ProtoReflect.Descriptor instead.
func (*C2S_CarriageThemeLvUpMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{10}
}

func (x *C2S_CarriageThemeLvUpMessage) GetCarriageId() int32 {
	if x != nil {
		return x.CarriageId
	}
	return 0
}

type S2C_CarriageThemeLvUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //code=0
}

func (x *S2C_CarriageThemeLvUpMessage) Reset() {
	*x = S2C_CarriageThemeLvUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CarriageThemeLvUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CarriageThemeLvUpMessage) ProtoMessage() {}

func (x *S2C_CarriageThemeLvUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CarriageThemeLvUpMessage.ProtoReflect.Descriptor instead.
func (*S2C_CarriageThemeLvUpMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_CarriageThemeLvUpMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_UnlockGoodsMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`       //id
	Extra []*Condition `protobuf:"bytes,2,rep,name=extra,proto3" json:"extra,omitempty"` //扩展列表
}

func (x *C2S_UnlockGoodsMessage) Reset() {
	*x = C2S_UnlockGoodsMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UnlockGoodsMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UnlockGoodsMessage) ProtoMessage() {}

func (x *C2S_UnlockGoodsMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UnlockGoodsMessage.ProtoReflect.Descriptor instead.
func (*C2S_UnlockGoodsMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{12}
}

func (x *C2S_UnlockGoodsMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *C2S_UnlockGoodsMessage) GetExtra() []*Condition {
	if x != nil {
		return x.Extra
	}
	return nil
}

type S2C_UnlockGoodsMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_UnlockGoodsMessage) Reset() {
	*x = S2C_UnlockGoodsMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UnlockGoodsMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UnlockGoodsMessage) ProtoMessage() {}

func (x *S2C_UnlockGoodsMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UnlockGoodsMessage.ProtoReflect.Descriptor instead.
func (*S2C_UnlockGoodsMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{13}
}

func (x *S2C_UnlockGoodsMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_LevelUpGoodsMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`        //id
	Level int32  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"` //等级
}

func (x *C2S_LevelUpGoodsMessage) Reset() {
	*x = C2S_LevelUpGoodsMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LevelUpGoodsMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LevelUpGoodsMessage) ProtoMessage() {}

func (x *C2S_LevelUpGoodsMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LevelUpGoodsMessage.ProtoReflect.Descriptor instead.
func (*C2S_LevelUpGoodsMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{14}
}

func (x *C2S_LevelUpGoodsMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *C2S_LevelUpGoodsMessage) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type S2C_LevelUpGoodsMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_LevelUpGoodsMessage) Reset() {
	*x = S2C_LevelUpGoodsMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_train_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_LevelUpGoodsMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_LevelUpGoodsMessage) ProtoMessage() {}

func (x *S2C_LevelUpGoodsMessage) ProtoReflect() protoreflect.Message {
	mi := &file_train_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_LevelUpGoodsMessage.ProtoReflect.Descriptor instead.
func (*S2C_LevelUpGoodsMessage) Descriptor() ([]byte, []int) {
	return file_train_proto_rawDescGZIP(), []int{15}
}

func (x *S2C_LevelUpGoodsMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_train_proto protoreflect.FileDescriptor

var file_train_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x28, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x42, 0x75, 0x79, 0x43, 0x61, 0x72,
	0x72, 0x69, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x91, 0x01, 0x0a,
	0x1c, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x75, 0x79, 0x43, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x2c, 0x0a, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x2f, 0x0a, 0x08, 0x63, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65,
	0x22, 0x31, 0x0a, 0x1f, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x61, 0x67, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x72, 0x0a, 0x22, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f,
	0x70, 0x65, 0x6e, 0x44, 0x6f, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6f,
	0x70, 0x65, 0x6e, 0x44, 0x6f, 0x6f, 0x72, 0x22, 0x2d, 0x0a, 0x1b, 0x43, 0x32, 0x53, 0x5f, 0x4f,
	0x70, 0x65, 0x6e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x44, 0x6f, 0x6f, 0x72, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x34, 0x0a, 0x1e, 0x53, 0x32, 0x43, 0x5f, 0x4f, 0x70,
	0x65, 0x6e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x44, 0x6f, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x4f, 0x0a, 0x17,
	0x43, 0x32, 0x53, 0x5f, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x70,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x2d, 0x0a,
	0x17, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55,
	0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x66, 0x0a, 0x1a,
	0x43, 0x32, 0x53, 0x5f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53,
	0x6b, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x63, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x6b, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x6b, 0x69, 0x6e, 0x22, 0x30, 0x0a, 0x1a, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x6b, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x3e, 0x0a, 0x1c, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x4c, 0x76, 0x55, 0x70, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x72, 0x69, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x72,
	0x69, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x1c, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x4c, 0x76, 0x55, 0x70, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x50, 0x0a, 0x16, 0x43, 0x32,
	0x53, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x2c, 0x0a, 0x16,
	0x53, 0x32, 0x43, 0x5f, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x3f, 0x0a, 0x17, 0x43, 0x32,
	0x53, 0x5f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x70, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x2d, 0x0a, 0x17, 0x53,
	0x32, 0x43, 0x5f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x70, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_train_proto_rawDescOnce sync.Once
	file_train_proto_rawDescData = file_train_proto_rawDesc
)

func file_train_proto_rawDescGZIP() []byte {
	file_train_proto_rawDescOnce.Do(func() {
		file_train_proto_rawDescData = protoimpl.X.CompressGZIP(file_train_proto_rawDescData)
	})
	return file_train_proto_rawDescData
}

var file_train_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_train_proto_goTypes = []interface{}{
	(*C2S_BuyCarriageMessage)(nil),             // 0: proto.C2S_BuyCarriageMessage
	(*S2C_BuyCarriageResultMessage)(nil),       // 1: proto.S2C_BuyCarriageResultMessage
	(*C2S_GetCarriageBuildInfoMessage)(nil),    // 2: proto.C2S_GetCarriageBuildInfoMessage
	(*S2C_GetCarriageBuildInfoResMessage)(nil), // 3: proto.S2C_GetCarriageBuildInfoResMessage
	(*C2S_OpenCarriageDoorMessage)(nil),        // 4: proto.C2S_OpenCarriageDoorMessage
	(*S2C_OpenCarriageDoorResMessage)(nil),     // 5: proto.S2C_OpenCarriageDoorResMessage
	(*C2S_BuildLevelUpMessage)(nil),            // 6: proto.C2S_BuildLevelUpMessage
	(*S2C_BuildLevelUpMessage)(nil),            // 7: proto.S2C_BuildLevelUpMessage
	(*C2S_ChangeBuildSkinMessage)(nil),         // 8: proto.C2S_ChangeBuildSkinMessage
	(*S2C_ChangeBuildSkinMessage)(nil),         // 9: proto.S2C_ChangeBuildSkinMessage
	(*C2S_CarriageThemeLvUpMessage)(nil),       // 10: proto.C2S_CarriageThemeLvUpMessage
	(*S2C_CarriageThemeLvUpMessage)(nil),       // 11: proto.S2C_CarriageThemeLvUpMessage
	(*C2S_UnlockGoodsMessage)(nil),             // 12: proto.C2S_UnlockGoodsMessage
	(*S2C_UnlockGoodsMessage)(nil),             // 13: proto.S2C_UnlockGoodsMessage
	(*C2S_LevelUpGoodsMessage)(nil),            // 14: proto.C2S_LevelUpGoodsMessage
	(*S2C_LevelUpGoodsMessage)(nil),            // 15: proto.S2C_LevelUpGoodsMessage
	(*Condition)(nil),                          // 16: proto.Condition
	(*CarriageInfo)(nil),                       // 17: proto.CarriageInfo
}
var file_train_proto_depIdxs = []int32{
	16, // 0: proto.S2C_BuyCarriageResultMessage.failList:type_name -> proto.Condition
	17, // 1: proto.S2C_BuyCarriageResultMessage.carriage:type_name -> proto.CarriageInfo
	16, // 2: proto.C2S_UnlockGoodsMessage.extra:type_name -> proto.Condition
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_train_proto_init() }
func file_train_proto_init() {
	if File_train_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_train_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BuyCarriageMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BuyCarriageResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetCarriageBuildInfoMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetCarriageBuildInfoResMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_OpenCarriageDoorMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_OpenCarriageDoorResMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BuildLevelUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BuildLevelUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChangeBuildSkinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChangeBuildSkinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CarriageThemeLvUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CarriageThemeLvUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UnlockGoodsMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UnlockGoodsMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LevelUpGoodsMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_train_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_LevelUpGoodsMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_train_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_train_proto_goTypes,
		DependencyIndexes: file_train_proto_depIdxs,
		MessageInfos:      file_train_proto_msgTypes,
	}.Build()
	File_train_proto = out.File
	file_train_proto_rawDesc = nil
	file_train_proto_goTypes = nil
	file_train_proto_depIdxs = nil
}
