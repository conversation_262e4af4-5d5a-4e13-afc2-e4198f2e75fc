// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: explore.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_SyncExploreMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球ID
}

func (x *C2S_SyncExploreMessage) Reset() {
	*x = C2S_SyncExploreMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_explore_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncExploreMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncExploreMessage) ProtoMessage() {}

func (x *C2S_SyncExploreMessage) ProtoReflect() protoreflect.Message {
	mi := &file_explore_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncExploreMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncExploreMessage) Descriptor() ([]byte, []int) {
	return file_explore_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_SyncExploreMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type S2C_SyncExploreMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`               //0
	SurplusTime int64 `protobuf:"varint,2,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
}

func (x *S2C_SyncExploreMessage) Reset() {
	*x = S2C_SyncExploreMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_explore_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncExploreMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncExploreMessage) ProtoMessage() {}

func (x *S2C_SyncExploreMessage) ProtoReflect() protoreflect.Message {
	mi := &file_explore_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncExploreMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncExploreMessage) Descriptor() ([]byte, []int) {
	return file_explore_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_SyncExploreMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncExploreMessage) GetSurplusTime() int64 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

type C2S_StartExploreMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32   `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"`  //星球ID
	Roles    []int32 `protobuf:"varint,2,rep,packed,name=roles,proto3" json:"roles,omitempty"` //派遣的角色id
}

func (x *C2S_StartExploreMessage) Reset() {
	*x = C2S_StartExploreMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_explore_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_StartExploreMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_StartExploreMessage) ProtoMessage() {}

func (x *C2S_StartExploreMessage) ProtoReflect() protoreflect.Message {
	mi := &file_explore_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_StartExploreMessage.ProtoReflect.Descriptor instead.
func (*C2S_StartExploreMessage) Descriptor() ([]byte, []int) {
	return file_explore_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_StartExploreMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *C2S_StartExploreMessage) GetRoles() []int32 {
	if x != nil {
		return x.Roles
	}
	return nil
}

type S2C_StartExploreMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //0
	Rewards []*Condition `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"` //探索奖励
}

func (x *S2C_StartExploreMessage) Reset() {
	*x = S2C_StartExploreMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_explore_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_StartExploreMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_StartExploreMessage) ProtoMessage() {}

func (x *S2C_StartExploreMessage) ProtoReflect() protoreflect.Message {
	mi := &file_explore_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_StartExploreMessage.ProtoReflect.Descriptor instead.
func (*S2C_StartExploreMessage) Descriptor() ([]byte, []int) {
	return file_explore_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_StartExploreMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_StartExploreMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_ClaimExploreRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球ID
}

func (x *C2S_ClaimExploreRewardMessage) Reset() {
	*x = C2S_ClaimExploreRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_explore_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ClaimExploreRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ClaimExploreRewardMessage) ProtoMessage() {}

func (x *C2S_ClaimExploreRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_explore_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ClaimExploreRewardMessage.ProtoReflect.Descriptor instead.
func (*C2S_ClaimExploreRewardMessage) Descriptor() ([]byte, []int) {
	return file_explore_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_ClaimExploreRewardMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type S2C_ClaimExploreRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`               //0
	SurplusTime int64 `protobuf:"varint,2,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
}

func (x *S2C_ClaimExploreRewardMessage) Reset() {
	*x = S2C_ClaimExploreRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_explore_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ClaimExploreRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ClaimExploreRewardMessage) ProtoMessage() {}

func (x *S2C_ClaimExploreRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_explore_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ClaimExploreRewardMessage.ProtoReflect.Descriptor instead.
func (*S2C_ClaimExploreRewardMessage) Descriptor() ([]byte, []int) {
	return file_explore_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_ClaimExploreRewardMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_ClaimExploreRewardMessage) GetSurplusTime() int64 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

type C2S_GetExploreAreaMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanetId int32 `protobuf:"varint,1,opt,name=planetId,proto3" json:"planetId,omitempty"` //星球ID
}

func (x *C2S_GetExploreAreaMessage) Reset() {
	*x = C2S_GetExploreAreaMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_explore_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetExploreAreaMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetExploreAreaMessage) ProtoMessage() {}

func (x *C2S_GetExploreAreaMessage) ProtoReflect() protoreflect.Message {
	mi := &file_explore_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetExploreAreaMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetExploreAreaMessage) Descriptor() ([]byte, []int) {
	return file_explore_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_GetExploreAreaMessage) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type S2C_GetExploreAreaMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
	Area int32 `protobuf:"varint,2,opt,name=area,proto3" json:"area,omitempty"` //区域
}

func (x *S2C_GetExploreAreaMessage) Reset() {
	*x = S2C_GetExploreAreaMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_explore_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetExploreAreaMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetExploreAreaMessage) ProtoMessage() {}

func (x *S2C_GetExploreAreaMessage) ProtoReflect() protoreflect.Message {
	mi := &file_explore_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetExploreAreaMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetExploreAreaMessage) Descriptor() ([]byte, []int) {
	return file_explore_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_GetExploreAreaMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetExploreAreaMessage) GetArea() int32 {
	if x != nil {
		return x.Area
	}
	return 0
}

var File_explore_proto protoreflect.FileDescriptor

var file_explore_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x34, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x79, 0x6e, 0x63,
	0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x16, 0x53, 0x32,
	0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70,
	0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73,
	0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x4b, 0x0a, 0x17, 0x43, 0x32,
	0x53, 0x5f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x59, 0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x22, 0x3b, 0x0a, 0x1d, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x45,
	0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x22,
	0x55, 0x0a, 0x1d, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x45, 0x78, 0x70, 0x6c,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c,
	0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x37, 0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x41, 0x72, 0x65, 0x61, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x22,
	0x43, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72,
	0x65, 0x41, 0x72, 0x65, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x65, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x61, 0x72, 0x65, 0x61, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_explore_proto_rawDescOnce sync.Once
	file_explore_proto_rawDescData = file_explore_proto_rawDesc
)

func file_explore_proto_rawDescGZIP() []byte {
	file_explore_proto_rawDescOnce.Do(func() {
		file_explore_proto_rawDescData = protoimpl.X.CompressGZIP(file_explore_proto_rawDescData)
	})
	return file_explore_proto_rawDescData
}

var file_explore_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_explore_proto_goTypes = []interface{}{
	(*C2S_SyncExploreMessage)(nil),        // 0: proto.C2S_SyncExploreMessage
	(*S2C_SyncExploreMessage)(nil),        // 1: proto.S2C_SyncExploreMessage
	(*C2S_StartExploreMessage)(nil),       // 2: proto.C2S_StartExploreMessage
	(*S2C_StartExploreMessage)(nil),       // 3: proto.S2C_StartExploreMessage
	(*C2S_ClaimExploreRewardMessage)(nil), // 4: proto.C2S_ClaimExploreRewardMessage
	(*S2C_ClaimExploreRewardMessage)(nil), // 5: proto.S2C_ClaimExploreRewardMessage
	(*C2S_GetExploreAreaMessage)(nil),     // 6: proto.C2S_GetExploreAreaMessage
	(*S2C_GetExploreAreaMessage)(nil),     // 7: proto.S2C_GetExploreAreaMessage
	(*Condition)(nil),                     // 8: proto.Condition
}
var file_explore_proto_depIdxs = []int32{
	8, // 0: proto.S2C_StartExploreMessage.rewards:type_name -> proto.Condition
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_explore_proto_init() }
func file_explore_proto_init() {
	if File_explore_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_explore_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncExploreMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_explore_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncExploreMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_explore_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_StartExploreMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_explore_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_StartExploreMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_explore_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ClaimExploreRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_explore_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ClaimExploreRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_explore_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetExploreAreaMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_explore_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetExploreAreaMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_explore_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_explore_proto_goTypes,
		DependencyIndexes: file_explore_proto_depIdxs,
		MessageInfos:      file_explore_proto_msgTypes,
	}.Build()
	File_explore_proto = out.File
	file_explore_proto_rawDesc = nil
	file_explore_proto_goTypes = nil
	file_explore_proto_depIdxs = nil
}
