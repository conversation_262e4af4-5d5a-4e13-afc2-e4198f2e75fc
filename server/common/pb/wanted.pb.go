// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: wanted.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_SyncWantedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` //悬赏下标，从0开始
}

func (x *C2S_SyncWantedMessage) Reset() {
	*x = C2S_SyncWantedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncWantedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncWantedMessage) ProtoMessage() {}

func (x *C2S_SyncWantedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncWantedMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncWantedMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_SyncWantedMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type S2C_SyncWantedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
	Data *WantedInfo `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`  //悬赏数据
}

func (x *S2C_SyncWantedMessage) Reset() {
	*x = S2C_SyncWantedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncWantedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncWantedMessage) ProtoMessage() {}

func (x *S2C_SyncWantedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncWantedMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncWantedMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_SyncWantedMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncWantedMessage) GetData() *WantedInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type C2S_RefrehWantedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` //悬赏下标，从0开始
}

func (x *C2S_RefrehWantedMessage) Reset() {
	*x = C2S_RefrehWantedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_RefrehWantedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_RefrehWantedMessage) ProtoMessage() {}

func (x *C2S_RefrehWantedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_RefrehWantedMessage.ProtoReflect.Descriptor instead.
func (*C2S_RefrehWantedMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_RefrehWantedMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type S2C_RefrehWantedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
	Data *WantedInfo `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`  //悬赏数据
}

func (x *S2C_RefrehWantedMessage) Reset() {
	*x = S2C_RefrehWantedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_RefrehWantedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_RefrehWantedMessage) ProtoMessage() {}

func (x *S2C_RefrehWantedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_RefrehWantedMessage.ProtoReflect.Descriptor instead.
func (*S2C_RefrehWantedMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_RefrehWantedMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_RefrehWantedMessage) GetData() *WantedInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type C2S_StartWantedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`        //悬赏下标，从0开始
	Roles []int32 `protobuf:"varint,2,rep,packed,name=roles,proto3" json:"roles,omitempty"` //派遣的角色id
}

func (x *C2S_StartWantedMessage) Reset() {
	*x = C2S_StartWantedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_StartWantedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_StartWantedMessage) ProtoMessage() {}

func (x *C2S_StartWantedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_StartWantedMessage.ProtoReflect.Descriptor instead.
func (*C2S_StartWantedMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_StartWantedMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *C2S_StartWantedMessage) GetRoles() []int32 {
	if x != nil {
		return x.Roles
	}
	return nil
}

type S2C_StartWantedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_StartWantedMessage) Reset() {
	*x = S2C_StartWantedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_StartWantedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_StartWantedMessage) ProtoMessage() {}

func (x *S2C_StartWantedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_StartWantedMessage.ProtoReflect.Descriptor instead.
func (*S2C_StartWantedMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_StartWantedMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ClaimWantedRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` //悬赏下标，从0开始
}

func (x *C2S_ClaimWantedRewardMessage) Reset() {
	*x = C2S_ClaimWantedRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ClaimWantedRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ClaimWantedRewardMessage) ProtoMessage() {}

func (x *C2S_ClaimWantedRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ClaimWantedRewardMessage.ProtoReflect.Descriptor instead.
func (*C2S_ClaimWantedRewardMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_ClaimWantedRewardMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type S2C_ClaimWantedRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_ClaimWantedRewardMessage) Reset() {
	*x = S2C_ClaimWantedRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ClaimWantedRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ClaimWantedRewardMessage) ProtoMessage() {}

func (x *S2C_ClaimWantedRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ClaimWantedRewardMessage.ProtoReflect.Descriptor instead.
func (*S2C_ClaimWantedRewardMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_ClaimWantedRewardMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_SyncAllWantedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SyncAllWantedMessage) Reset() {
	*x = C2S_SyncAllWantedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncAllWantedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncAllWantedMessage) ProtoMessage() {}

func (x *C2S_SyncAllWantedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncAllWantedMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncAllWantedMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{8}
}

type S2C_SyncAllWantedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Data *Wanted `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`  //
}

func (x *S2C_SyncAllWantedMessage) Reset() {
	*x = S2C_SyncAllWantedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wanted_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncAllWantedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncAllWantedMessage) ProtoMessage() {}

func (x *S2C_SyncAllWantedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_wanted_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncAllWantedMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncAllWantedMessage) Descriptor() ([]byte, []int) {
	return file_wanted_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_SyncAllWantedMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncAllWantedMessage) GetData() *Wanted {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_wanted_proto protoreflect.FileDescriptor

var file_wanted_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x77, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x2d, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x57,
	0x61, 0x6e, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x22, 0x52, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x57, 0x61,
	0x6e, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2f, 0x0a, 0x17, 0x43, 0x32, 0x53, 0x5f, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x68, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x54, 0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x68, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x57, 0x61, 0x6e,
	0x74, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x44, 0x0a,
	0x16, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x14, 0x0a,
	0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x72, 0x6f,
	0x6c, 0x65, 0x73, 0x22, 0x2c, 0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x22, 0x34, 0x0a, 0x1c, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x57, 0x61,
	0x6e, 0x74, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x32, 0x0a, 0x1c, 0x53, 0x32, 0x43, 0x5f, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x1a, 0x0a, 0x18, 0x43,
	0x32, 0x53, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x6c, 0x6c, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x51, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x53,
	0x79, 0x6e, 0x63, 0x41, 0x6c, 0x6c, 0x57, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x57, 0x61,
	0x6e, 0x74, 0x65, 0x64, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_wanted_proto_rawDescOnce sync.Once
	file_wanted_proto_rawDescData = file_wanted_proto_rawDesc
)

func file_wanted_proto_rawDescGZIP() []byte {
	file_wanted_proto_rawDescOnce.Do(func() {
		file_wanted_proto_rawDescData = protoimpl.X.CompressGZIP(file_wanted_proto_rawDescData)
	})
	return file_wanted_proto_rawDescData
}

var file_wanted_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_wanted_proto_goTypes = []interface{}{
	(*C2S_SyncWantedMessage)(nil),        // 0: proto.C2S_SyncWantedMessage
	(*S2C_SyncWantedMessage)(nil),        // 1: proto.S2C_SyncWantedMessage
	(*C2S_RefrehWantedMessage)(nil),      // 2: proto.C2S_RefrehWantedMessage
	(*S2C_RefrehWantedMessage)(nil),      // 3: proto.S2C_RefrehWantedMessage
	(*C2S_StartWantedMessage)(nil),       // 4: proto.C2S_StartWantedMessage
	(*S2C_StartWantedMessage)(nil),       // 5: proto.S2C_StartWantedMessage
	(*C2S_ClaimWantedRewardMessage)(nil), // 6: proto.C2S_ClaimWantedRewardMessage
	(*S2C_ClaimWantedRewardMessage)(nil), // 7: proto.S2C_ClaimWantedRewardMessage
	(*C2S_SyncAllWantedMessage)(nil),     // 8: proto.C2S_SyncAllWantedMessage
	(*S2C_SyncAllWantedMessage)(nil),     // 9: proto.S2C_SyncAllWantedMessage
	(*WantedInfo)(nil),                   // 10: proto.WantedInfo
	(*Wanted)(nil),                       // 11: proto.Wanted
}
var file_wanted_proto_depIdxs = []int32{
	10, // 0: proto.S2C_SyncWantedMessage.data:type_name -> proto.WantedInfo
	10, // 1: proto.S2C_RefrehWantedMessage.data:type_name -> proto.WantedInfo
	11, // 2: proto.S2C_SyncAllWantedMessage.data:type_name -> proto.Wanted
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_wanted_proto_init() }
func file_wanted_proto_init() {
	if File_wanted_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_wanted_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncWantedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wanted_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncWantedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wanted_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_RefrehWantedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wanted_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_RefrehWantedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wanted_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_StartWantedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wanted_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_StartWantedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wanted_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ClaimWantedRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wanted_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ClaimWantedRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wanted_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncAllWantedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wanted_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncAllWantedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_wanted_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wanted_proto_goTypes,
		DependencyIndexes: file_wanted_proto_depIdxs,
		MessageInfos:      file_wanted_proto_msgTypes,
	}.Build()
	File_wanted_proto = out.File
	file_wanted_proto_rawDesc = nil
	file_wanted_proto_goTypes = nil
	file_wanted_proto_depIdxs = nil
}
