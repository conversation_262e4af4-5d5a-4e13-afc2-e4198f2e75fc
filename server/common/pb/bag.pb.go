// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: bag.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_TicketMergeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Num int32 `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"` //合成数量
}

func (x *C2S_TicketMergeMessage) Reset() {
	*x = C2S_TicketMergeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_TicketMergeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_TicketMergeMessage) ProtoMessage() {}

func (x *C2S_TicketMergeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_TicketMergeMessage.ProtoReflect.Descriptor instead.
func (*C2S_TicketMergeMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_TicketMergeMessage) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type S2C_TicketMergeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0成功，1来源物品数量不足以兑换cnt次,2目标物品或者来源物品无法被置换
}

func (x *S2C_TicketMergeMessage) Reset() {
	*x = S2C_TicketMergeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_TicketMergeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_TicketMergeMessage) ProtoMessage() {}

func (x *S2C_TicketMergeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_TicketMergeMessage.ProtoReflect.Descriptor instead.
func (*S2C_TicketMergeMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_TicketMergeMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_DropItemMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Item *Condition `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"` //物品
}

func (x *C2S_DropItemMessage) Reset() {
	*x = C2S_DropItemMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_DropItemMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_DropItemMessage) ProtoMessage() {}

func (x *C2S_DropItemMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_DropItemMessage.ProtoReflect.Descriptor instead.
func (*C2S_DropItemMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_DropItemMessage) GetItem() *Condition {
	if x != nil {
		return x.Item
	}
	return nil
}

type S2C_DropItemMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0成功，1来源物品数量不足以兑换cnt次,2目标物品或者来源物品无法被置换
}

func (x *S2C_DropItemMessage) Reset() {
	*x = S2C_DropItemMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_DropItemMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_DropItemMessage) ProtoMessage() {}

func (x *S2C_DropItemMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_DropItemMessage.ProtoReflect.Descriptor instead.
func (*S2C_DropItemMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_DropItemMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_SpaceStoneLvUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_SpaceStoneLvUpMessage) Reset() {
	*x = C2S_SpaceStoneLvUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SpaceStoneLvUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SpaceStoneLvUpMessage) ProtoMessage() {}

func (x *C2S_SpaceStoneLvUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SpaceStoneLvUpMessage.ProtoReflect.Descriptor instead.
func (*C2S_SpaceStoneLvUpMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{4}
}

type S2C_SpaceStoneLvUpMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0成功，1来源物品数量不足以兑换cnt次,2目标物品或者来源物品无法被置换
}

func (x *S2C_SpaceStoneLvUpMessage) Reset() {
	*x = S2C_SpaceStoneLvUpMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SpaceStoneLvUpMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SpaceStoneLvUpMessage) ProtoMessage() {}

func (x *S2C_SpaceStoneLvUpMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SpaceStoneLvUpMessage.ProtoReflect.Descriptor instead.
func (*S2C_SpaceStoneLvUpMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_SpaceStoneLvUpMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_UseSpaceStoneMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //标记id
}

func (x *C2S_UseSpaceStoneMessage) Reset() {
	*x = C2S_UseSpaceStoneMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UseSpaceStoneMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UseSpaceStoneMessage) ProtoMessage() {}

func (x *C2S_UseSpaceStoneMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UseSpaceStoneMessage.ProtoReflect.Descriptor instead.
func (*C2S_UseSpaceStoneMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_UseSpaceStoneMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_UseSpaceStoneMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_UseSpaceStoneMessage) Reset() {
	*x = S2C_UseSpaceStoneMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UseSpaceStoneMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UseSpaceStoneMessage) ProtoMessage() {}

func (x *S2C_UseSpaceStoneMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UseSpaceStoneMessage.ProtoReflect.Descriptor instead.
func (*S2C_UseSpaceStoneMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_UseSpaceStoneMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_MarkSpaceStoneMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`             //标记id
	RemoveId int32 `protobuf:"varint,2,opt,name=removeId,proto3" json:"removeId,omitempty"` //替换id
}

func (x *C2S_MarkSpaceStoneMessage) Reset() {
	*x = C2S_MarkSpaceStoneMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MarkSpaceStoneMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MarkSpaceStoneMessage) ProtoMessage() {}

func (x *C2S_MarkSpaceStoneMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MarkSpaceStoneMessage.ProtoReflect.Descriptor instead.
func (*C2S_MarkSpaceStoneMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{8}
}

func (x *C2S_MarkSpaceStoneMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_MarkSpaceStoneMessage) GetRemoveId() int32 {
	if x != nil {
		return x.RemoveId
	}
	return 0
}

type S2C_MarkSpaceStoneMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_MarkSpaceStoneMessage) Reset() {
	*x = S2C_MarkSpaceStoneMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MarkSpaceStoneMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MarkSpaceStoneMessage) ProtoMessage() {}

func (x *S2C_MarkSpaceStoneMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MarkSpaceStoneMessage.ProtoReflect.Descriptor instead.
func (*S2C_MarkSpaceStoneMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_MarkSpaceStoneMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_SyncItemMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` //uid
}

func (x *C2S_SyncItemMessage) Reset() {
	*x = C2S_SyncItemMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncItemMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncItemMessage) ProtoMessage() {}

func (x *C2S_SyncItemMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncItemMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncItemMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{10}
}

func (x *C2S_SyncItemMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type S2C_SyncItemMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`               //
	SurplusTime int32 `protobuf:"varint,2,opt,name=surplusTime,proto3" json:"surplusTime,omitempty"` //剩余时间
}

func (x *S2C_SyncItemMessage) Reset() {
	*x = S2C_SyncItemMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncItemMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncItemMessage) ProtoMessage() {}

func (x *S2C_SyncItemMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncItemMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncItemMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_SyncItemMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncItemMessage) GetSurplusTime() int32 {
	if x != nil {
		return x.SurplusTime
	}
	return 0
}

type C2S_UseItemMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Item *Condition `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"` //物品
}

func (x *C2S_UseItemMessage) Reset() {
	*x = C2S_UseItemMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_UseItemMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_UseItemMessage) ProtoMessage() {}

func (x *C2S_UseItemMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_UseItemMessage.ProtoReflect.Descriptor instead.
func (*C2S_UseItemMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{12}
}

func (x *C2S_UseItemMessage) GetItem() *Condition {
	if x != nil {
		return x.Item
	}
	return nil
}

type S2C_UseItemMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //code=0代表无错误
	Rewards []*Condition `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"` //奖励列表
}

func (x *S2C_UseItemMessage) Reset() {
	*x = S2C_UseItemMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bag_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_UseItemMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_UseItemMessage) ProtoMessage() {}

func (x *S2C_UseItemMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bag_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_UseItemMessage.ProtoReflect.Descriptor instead.
func (*S2C_UseItemMessage) Descriptor() ([]byte, []int) {
	return file_bag_proto_rawDescGZIP(), []int{13}
}

func (x *S2C_UseItemMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_UseItemMessage) GetRewards() []*Condition {
	if x != nil {
		return x.Rewards
	}
	return nil
}

var File_bag_proto protoreflect.FileDescriptor

var file_bag_proto_rawDesc = []byte{
	0x0a, 0x09, 0x62, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x2a, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x65,
	0x72, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x2c, 0x0a, 0x16,
	0x53, 0x32, 0x43, 0x5f, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x3b, 0x0a, 0x13, 0x43, 0x32,
	0x53, 0x5f, 0x44, 0x72, 0x6f, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x24, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x22, 0x29, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x44,
	0x72, 0x6f, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x1b, 0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x70, 0x61, 0x63, 0x65, 0x53,
	0x74, 0x6f, 0x6e, 0x65, 0x4c, 0x76, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x2f, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x6e,
	0x65, 0x4c, 0x76, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x2a, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f, 0x55, 0x73, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65,
	0x53, 0x74, 0x6f, 0x6e, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2e, 0x0a, 0x18,
	0x53, 0x32, 0x43, 0x5f, 0x55, 0x73, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x6e,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x47, 0x0a, 0x19,
	0x43, 0x32, 0x53, 0x5f, 0x4d, 0x61, 0x72, 0x6b, 0x53, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x6f,
	0x6e, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x49, 0x64, 0x22, 0x2f, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x4d, 0x61, 0x72,
	0x6b, 0x53, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x27, 0x0a, 0x13, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x79,
	0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22,
	0x4b, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75,
	0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x3a, 0x0a, 0x12,
	0x43, 0x32, 0x53, 0x5f, 0x55, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x22, 0x54, 0x0a, 0x12, 0x53, 0x32, 0x43, 0x5f,
	0x55, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x42, 0x06,
	0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bag_proto_rawDescOnce sync.Once
	file_bag_proto_rawDescData = file_bag_proto_rawDesc
)

func file_bag_proto_rawDescGZIP() []byte {
	file_bag_proto_rawDescOnce.Do(func() {
		file_bag_proto_rawDescData = protoimpl.X.CompressGZIP(file_bag_proto_rawDescData)
	})
	return file_bag_proto_rawDescData
}

var file_bag_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_bag_proto_goTypes = []interface{}{
	(*C2S_TicketMergeMessage)(nil),    // 0: proto.C2S_TicketMergeMessage
	(*S2C_TicketMergeMessage)(nil),    // 1: proto.S2C_TicketMergeMessage
	(*C2S_DropItemMessage)(nil),       // 2: proto.C2S_DropItemMessage
	(*S2C_DropItemMessage)(nil),       // 3: proto.S2C_DropItemMessage
	(*C2S_SpaceStoneLvUpMessage)(nil), // 4: proto.C2S_SpaceStoneLvUpMessage
	(*S2C_SpaceStoneLvUpMessage)(nil), // 5: proto.S2C_SpaceStoneLvUpMessage
	(*C2S_UseSpaceStoneMessage)(nil),  // 6: proto.C2S_UseSpaceStoneMessage
	(*S2C_UseSpaceStoneMessage)(nil),  // 7: proto.S2C_UseSpaceStoneMessage
	(*C2S_MarkSpaceStoneMessage)(nil), // 8: proto.C2S_MarkSpaceStoneMessage
	(*S2C_MarkSpaceStoneMessage)(nil), // 9: proto.S2C_MarkSpaceStoneMessage
	(*C2S_SyncItemMessage)(nil),       // 10: proto.C2S_SyncItemMessage
	(*S2C_SyncItemMessage)(nil),       // 11: proto.S2C_SyncItemMessage
	(*C2S_UseItemMessage)(nil),        // 12: proto.C2S_UseItemMessage
	(*S2C_UseItemMessage)(nil),        // 13: proto.S2C_UseItemMessage
	(*Condition)(nil),                 // 14: proto.Condition
}
var file_bag_proto_depIdxs = []int32{
	14, // 0: proto.C2S_DropItemMessage.item:type_name -> proto.Condition
	14, // 1: proto.C2S_UseItemMessage.item:type_name -> proto.Condition
	14, // 2: proto.S2C_UseItemMessage.rewards:type_name -> proto.Condition
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_bag_proto_init() }
func file_bag_proto_init() {
	if File_bag_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_bag_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_TicketMergeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_TicketMergeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_DropItemMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_DropItemMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SpaceStoneLvUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SpaceStoneLvUpMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UseSpaceStoneMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UseSpaceStoneMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MarkSpaceStoneMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MarkSpaceStoneMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncItemMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncItemMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_UseItemMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bag_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_UseItemMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bag_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_bag_proto_goTypes,
		DependencyIndexes: file_bag_proto_depIdxs,
		MessageInfos:      file_bag_proto_msgTypes,
	}.Build()
	File_bag_proto = out.File
	file_bag_proto_rawDesc = nil
	file_bag_proto_goTypes = nil
	file_bag_proto_depIdxs = nil
}
