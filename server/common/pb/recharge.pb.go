// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: recharge.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_IsFirstPayMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductIds []string `protobuf:"bytes,1,rep,name=productIds,proto3" json:"productIds,omitempty"` //产品id列表
}

func (x *C2S_IsFirstPayMessage) Reset() {
	*x = C2S_IsFirstPayMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recharge_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_IsFirstPayMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_IsFirstPayMessage) ProtoMessage() {}

func (x *C2S_IsFirstPayMessage) ProtoReflect() protoreflect.Message {
	mi := &file_recharge_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_IsFirstPayMessage.ProtoReflect.Descriptor instead.
func (*C2S_IsFirstPayMessage) Descriptor() ([]byte, []int) {
	return file_recharge_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_IsFirstPayMessage) GetProductIds() []string {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

type S2C_IsFirstPayMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductIds []string `protobuf:"bytes,1,rep,name=productIds,proto3" json:"productIds,omitempty"` //返回是首次的列表
}

func (x *S2C_IsFirstPayMessage) Reset() {
	*x = S2C_IsFirstPayMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recharge_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_IsFirstPayMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_IsFirstPayMessage) ProtoMessage() {}

func (x *S2C_IsFirstPayMessage) ProtoReflect() protoreflect.Message {
	mi := &file_recharge_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_IsFirstPayMessage.ProtoReflect.Descriptor instead.
func (*S2C_IsFirstPayMessage) Descriptor() ([]byte, []int) {
	return file_recharge_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_IsFirstPayMessage) GetProductIds() []string {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

var File_recharge_proto protoreflect.FileDescriptor

var file_recharge_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x37, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x49,
	0x73, 0x46, 0x69, 0x72, 0x73, 0x74, 0x50, 0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x73,
	0x22, 0x37, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x49, 0x73, 0x46, 0x69, 0x72, 0x73, 0x74, 0x50,
	0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x73, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_recharge_proto_rawDescOnce sync.Once
	file_recharge_proto_rawDescData = file_recharge_proto_rawDesc
)

func file_recharge_proto_rawDescGZIP() []byte {
	file_recharge_proto_rawDescOnce.Do(func() {
		file_recharge_proto_rawDescData = protoimpl.X.CompressGZIP(file_recharge_proto_rawDescData)
	})
	return file_recharge_proto_rawDescData
}

var file_recharge_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_recharge_proto_goTypes = []interface{}{
	(*C2S_IsFirstPayMessage)(nil), // 0: proto.C2S_IsFirstPayMessage
	(*S2C_IsFirstPayMessage)(nil), // 1: proto.S2C_IsFirstPayMessage
}
var file_recharge_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_recharge_proto_init() }
func file_recharge_proto_init() {
	if File_recharge_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_recharge_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_IsFirstPayMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recharge_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_IsFirstPayMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_recharge_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_recharge_proto_goTypes,
		DependencyIndexes: file_recharge_proto_depIdxs,
		MessageInfos:      file_recharge_proto_msgTypes,
	}.Build()
	File_recharge_proto = out.File
	file_recharge_proto_rawDesc = nil
	file_recharge_proto_goTypes = nil
	file_recharge_proto_depIdxs = nil
}
