// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: resonance.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_SetResonanceRoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` //加入或取消共鸣
	Id   int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`     //乘客id
}

func (x *C2S_SetResonanceRoleMessage) Reset() {
	*x = C2S_SetResonanceRoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resonance_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SetResonanceRoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SetResonanceRoleMessage) ProtoMessage() {}

func (x *C2S_SetResonanceRoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_resonance_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SetResonanceRoleMessage.ProtoReflect.Descriptor instead.
func (*C2S_SetResonanceRoleMessage) Descriptor() ([]byte, []int) {
	return file_resonance_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_SetResonanceRoleMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *C2S_SetResonanceRoleMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_SetResonanceRoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_SetResonanceRoleMessage) Reset() {
	*x = S2C_SetResonanceRoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resonance_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SetResonanceRoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SetResonanceRoleMessage) ProtoMessage() {}

func (x *S2C_SetResonanceRoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_resonance_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SetResonanceRoleMessage.ProtoReflect.Descriptor instead.
func (*S2C_SetResonanceRoleMessage) Descriptor() ([]byte, []int) {
	return file_resonance_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_SetResonanceRoleMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_resonance_proto protoreflect.FileDescriptor

var file_resonance_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x72, 0x65, 0x73, 0x6f, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x41, 0x0a, 0x1b, 0x43, 0x32, 0x53, 0x5f,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x31, 0x0a, 0x1b, 0x53,
	0x32, 0x43, 0x5f, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06,
	0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_resonance_proto_rawDescOnce sync.Once
	file_resonance_proto_rawDescData = file_resonance_proto_rawDesc
)

func file_resonance_proto_rawDescGZIP() []byte {
	file_resonance_proto_rawDescOnce.Do(func() {
		file_resonance_proto_rawDescData = protoimpl.X.CompressGZIP(file_resonance_proto_rawDescData)
	})
	return file_resonance_proto_rawDescData
}

var file_resonance_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_resonance_proto_goTypes = []interface{}{
	(*C2S_SetResonanceRoleMessage)(nil), // 0: proto.C2S_SetResonanceRoleMessage
	(*S2C_SetResonanceRoleMessage)(nil), // 1: proto.S2C_SetResonanceRoleMessage
}
var file_resonance_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_resonance_proto_init() }
func file_resonance_proto_init() {
	if File_resonance_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_resonance_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SetResonanceRoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resonance_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SetResonanceRoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_resonance_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_resonance_proto_goTypes,
		DependencyIndexes: file_resonance_proto_depIdxs,
		MessageInfos:      file_resonance_proto_msgTypes,
	}.Build()
	File_resonance_proto = out.File
	file_resonance_proto_rawDesc = nil
	file_resonance_proto_goTypes = nil
	file_resonance_proto_depIdxs = nil
}
