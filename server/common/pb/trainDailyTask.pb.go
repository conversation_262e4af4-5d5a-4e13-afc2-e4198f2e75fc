// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: trainDailyTask.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_SyncAllTrainDailyTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` //列车日常任务下标,从0开始,-1同步全部
}

func (x *C2S_SyncAllTrainDailyTaskMessage) Reset() {
	*x = C2S_SyncAllTrainDailyTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainDailyTask_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SyncAllTrainDailyTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SyncAllTrainDailyTaskMessage) ProtoMessage() {}

func (x *C2S_SyncAllTrainDailyTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainDailyTask_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SyncAllTrainDailyTaskMessage.ProtoReflect.Descriptor instead.
func (*C2S_SyncAllTrainDailyTaskMessage) Descriptor() ([]byte, []int) {
	return file_trainDailyTask_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_SyncAllTrainDailyTaskMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type S2C_SyncAllTrainDailyTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Data []*TrainDailyTaskItem `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`  //
}

func (x *S2C_SyncAllTrainDailyTaskMessage) Reset() {
	*x = S2C_SyncAllTrainDailyTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainDailyTask_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncAllTrainDailyTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncAllTrainDailyTaskMessage) ProtoMessage() {}

func (x *S2C_SyncAllTrainDailyTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainDailyTask_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncAllTrainDailyTaskMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncAllTrainDailyTaskMessage) Descriptor() ([]byte, []int) {
	return file_trainDailyTask_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_SyncAllTrainDailyTaskMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_SyncAllTrainDailyTaskMessage) GetData() []*TrainDailyTaskItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type C2S_StartTrainDailyTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`        //列车日常任务下标,从0开始
	Roles []int32 `protobuf:"varint,2,rep,packed,name=roles,proto3" json:"roles,omitempty"` //派遣的角色id
}

func (x *C2S_StartTrainDailyTaskMessage) Reset() {
	*x = C2S_StartTrainDailyTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainDailyTask_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_StartTrainDailyTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_StartTrainDailyTaskMessage) ProtoMessage() {}

func (x *C2S_StartTrainDailyTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainDailyTask_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_StartTrainDailyTaskMessage.ProtoReflect.Descriptor instead.
func (*C2S_StartTrainDailyTaskMessage) Descriptor() ([]byte, []int) {
	return file_trainDailyTask_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_StartTrainDailyTaskMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *C2S_StartTrainDailyTaskMessage) GetRoles() []int32 {
	if x != nil {
		return x.Roles
	}
	return nil
}

type S2C_StartTrainDailyTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_StartTrainDailyTaskMessage) Reset() {
	*x = S2C_StartTrainDailyTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainDailyTask_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_StartTrainDailyTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_StartTrainDailyTaskMessage) ProtoMessage() {}

func (x *S2C_StartTrainDailyTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainDailyTask_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_StartTrainDailyTaskMessage.ProtoReflect.Descriptor instead.
func (*S2C_StartTrainDailyTaskMessage) Descriptor() ([]byte, []int) {
	return file_trainDailyTask_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_StartTrainDailyTaskMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_ClaimTrainDailyTaskRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` //列车日常任务下标,从0开始
}

func (x *C2S_ClaimTrainDailyTaskRewardMessage) Reset() {
	*x = C2S_ClaimTrainDailyTaskRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainDailyTask_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ClaimTrainDailyTaskRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ClaimTrainDailyTaskRewardMessage) ProtoMessage() {}

func (x *C2S_ClaimTrainDailyTaskRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainDailyTask_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ClaimTrainDailyTaskRewardMessage.ProtoReflect.Descriptor instead.
func (*C2S_ClaimTrainDailyTaskRewardMessage) Descriptor() ([]byte, []int) {
	return file_trainDailyTask_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_ClaimTrainDailyTaskRewardMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type S2C_ClaimTrainDailyTaskRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0
}

func (x *S2C_ClaimTrainDailyTaskRewardMessage) Reset() {
	*x = S2C_ClaimTrainDailyTaskRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainDailyTask_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ClaimTrainDailyTaskRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ClaimTrainDailyTaskRewardMessage) ProtoMessage() {}

func (x *S2C_ClaimTrainDailyTaskRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainDailyTask_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ClaimTrainDailyTaskRewardMessage.ProtoReflect.Descriptor instead.
func (*S2C_ClaimTrainDailyTaskRewardMessage) Descriptor() ([]byte, []int) {
	return file_trainDailyTask_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_ClaimTrainDailyTaskRewardMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_trainDailyTask_proto protoreflect.FileDescriptor

var file_trainDailyTask_proto_rawDesc = []byte{
	0x0a, 0x14, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x38, 0x0a, 0x20, 0x43,
	0x32, 0x53, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x6c, 0x6c, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x65, 0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x79, 0x6e,
	0x63, 0x41, 0x6c, 0x6c, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61,
	0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4c, 0x0a, 0x1e,
	0x43, 0x32, 0x53, 0x5f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x05, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x34, 0x0a, 0x1e, 0x53, 0x32,
	0x43, 0x5f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x3c, 0x0a, 0x24, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x3a,
	0x0a, 0x24, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_trainDailyTask_proto_rawDescOnce sync.Once
	file_trainDailyTask_proto_rawDescData = file_trainDailyTask_proto_rawDesc
)

func file_trainDailyTask_proto_rawDescGZIP() []byte {
	file_trainDailyTask_proto_rawDescOnce.Do(func() {
		file_trainDailyTask_proto_rawDescData = protoimpl.X.CompressGZIP(file_trainDailyTask_proto_rawDescData)
	})
	return file_trainDailyTask_proto_rawDescData
}

var file_trainDailyTask_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_trainDailyTask_proto_goTypes = []interface{}{
	(*C2S_SyncAllTrainDailyTaskMessage)(nil),     // 0: proto.C2S_SyncAllTrainDailyTaskMessage
	(*S2C_SyncAllTrainDailyTaskMessage)(nil),     // 1: proto.S2C_SyncAllTrainDailyTaskMessage
	(*C2S_StartTrainDailyTaskMessage)(nil),       // 2: proto.C2S_StartTrainDailyTaskMessage
	(*S2C_StartTrainDailyTaskMessage)(nil),       // 3: proto.S2C_StartTrainDailyTaskMessage
	(*C2S_ClaimTrainDailyTaskRewardMessage)(nil), // 4: proto.C2S_ClaimTrainDailyTaskRewardMessage
	(*S2C_ClaimTrainDailyTaskRewardMessage)(nil), // 5: proto.S2C_ClaimTrainDailyTaskRewardMessage
	(*TrainDailyTaskItem)(nil),                   // 6: proto.TrainDailyTaskItem
}
var file_trainDailyTask_proto_depIdxs = []int32{
	6, // 0: proto.S2C_SyncAllTrainDailyTaskMessage.data:type_name -> proto.TrainDailyTaskItem
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_trainDailyTask_proto_init() }
func file_trainDailyTask_proto_init() {
	if File_trainDailyTask_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_trainDailyTask_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SyncAllTrainDailyTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainDailyTask_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncAllTrainDailyTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainDailyTask_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_StartTrainDailyTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainDailyTask_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_StartTrainDailyTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainDailyTask_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ClaimTrainDailyTaskRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainDailyTask_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ClaimTrainDailyTaskRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_trainDailyTask_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_trainDailyTask_proto_goTypes,
		DependencyIndexes: file_trainDailyTask_proto_depIdxs,
		MessageInfos:      file_trainDailyTask_proto_msgTypes,
	}.Build()
	File_trainDailyTask_proto = out.File
	file_trainDailyTask_proto_rawDesc = nil
	file_trainDailyTask_proto_goTypes = nil
	file_trainDailyTask_proto_depIdxs = nil
}
