package ta

const (
	Register  = "ta_register"  // 用户注册
	Entry     = "ta_entry"     // 进入游戏
	ResChange = "ta_resChange" // 资源改变
	Purchase  = "ta_purchase"  // 内购
)

const (
	ResChangeSceneTypeNoReport            = -1 // 无需记录
	ResChangeSceneTypeUnknown             = 0  // 未知
	ResChangeSceneTypeTrainBuildLevelUp   = 1  // 修建设施扣除
	ResChangeSceneTypeTrainPickUp         = 2  // 车厢拾取增加
	ResChangeSceneTypeJackpot             = 3  // 抽卡扣除
	ResChangeSceneTypeBlackHole           = 4  // 迷宫奖励
	ResChangeSceneTypeTower               = 5  // 爬塔
	ResChangeSceneTypeInstance            = 6  // 副本
	ResChangeSceneTypeCdk                 = 7  // 兑换码
	ResChangeSceneTypeChest               = 8  // 宝箱获取
	ResChangeSceneTypeCollect             = 9  // 采集
	ResChangeSceneTypeDeepExplore         = 10 // 星球深度探索
	ResChangeSceneTypeField               = 11 // 种植玩法
	ResChangeSceneTypeMail                = 12 // 邮件领取
	ResChangeSceneTypeOre                 = 13 // 矿洞玩法
	ResChangeSceneTypeSpaceStone          = 14 // 空间宝石
	ResChangeSceneTypeStore               = 15 // 商店购买
	ResChangeSceneTypeTool                = 16 // 工具
	ResChangeSceneTypeWanted              = 17 // 委托
	ResChangeSceneTypeAchievement         = 18 // 成就奖励
	ResChangeSceneTypeTask                = 19 // 任务奖励
	ResChangeSceneTypeBag                 = 20 // 背包操作
	ResChangeSceneTypeBattery             = 21 // 购买电池
	ResChangeSceneTypeDailyTask           = 22 // 每日任务
	ResChangeSceneTypeEquipMake           = 23 // 装备打造
	ResChangeSceneTypeEquipBuy            = 24 // 装备购买
	ResChangeSceneTypeEquipSell           = 25 // 装备出售
	ResChangeSceneTypePassengerLvUp       = 26 // 乘客升级
	ResChangeSceneTypePassengerStarLvUp   = 27 // 乘客升星
	ResChangeSceneTypePassengerTalentLvUp = 28 // 乘客天赋升级
	ResChangeSceneTypePassengerFragMerge  = 29 // 乘客投影合成
	ResChangeSceneTypePay                 = 30 // 支付
	ResChangeSceneTypeChapterPass         = 31 // 星球节点通关
	ResChangeSceneTypeBranchPass          = 32 // 星球支线通关
	ResChangeSceneTypeTrain               = 33 // 车厢操作
	ResChangeSceneTypeTrainFood           = 34 // 车厢食材
	ResChangeSceneTypeTransport           = 35 // 运送奖励

)
