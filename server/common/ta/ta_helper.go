package ta

import (
	"train/base/enum"
	com "train/common"

	"github.com/ThinkingDataAnalytics/go-sdk/thinkingdata"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

var (
	ta thinkingdata.TDAnalytics
)

func Init() {
	config := com.GetTa()
	if config == nil {
		return
	}
	if !com.IsTaOpen() {
		return
	}
	appId := config["appId"].(string)
	url := config["url"].(string)
	batchSize := 20
	if com.IsTaDebug() {
		batchSize = 1
	}
	consumer, err := thinkingdata.NewBatchConsumerWithBatchSize(url, appId, batchSize)
	if err != nil {
		log.Error("init ta error", err.Error())
	}
	ta = thinkingdata.New(consumer)
	log.Info("init ta done.")
}

func Olog(format string, args ...interface{}) {
	if com.IsTaDebug() {
		log.Debug(format, args...)
	}
}

func getAccountId(uid string) string {
	accountId := uid
	return accountId
}

func _track(uid string, did string, eventName string, properties map[string]interface{}) {
	properties["uid"] = uid
	if err := ta.Track(getAccountId(uid), did, eventName, properties); err != nil {
		log.Error("ta track error %s", err.Error())
		return
	}
	Olog("ta track msg: uid:%s, distanceId:%s, evt: %s, prop: %v", uid, did, eventName, properties)
}

func _userSet(uid string, did string, properties map[string]interface{}) {
	properties["uid"] = uid
	if err := ta.UserSet(getAccountId(uid), did, properties); err != nil {
		log.Error("ta UserSet error ", err.Error())
		return
	}
	Olog("ta userSet msg: uid:%s, distanceId:%s, prop: %v", uid, did, properties)
}

func Track(uid string, did string, eventName string, properties map[string]interface{}) {
	if !com.IsTaOpen() {
		return
	}
	go _track(uid, did, eventName, properties)
}

func TrackBySession(session gate.Session, eventName string, properties map[string]interface{}) {
	if !com.IsTaOpen() {
		return
	}
	uid := session.GetUserID()
	did := session.Get(enum.DistanceId)
	go _track(uid, did, eventName, properties)
}

func UserSet(session gate.Session, properties map[string]interface{}) {
	if com.IsTaOpen() {
		uid := session.GetUserID()
		did := session.Get(enum.DistanceId)
		go _userSet(uid, did, properties)
	}
}
