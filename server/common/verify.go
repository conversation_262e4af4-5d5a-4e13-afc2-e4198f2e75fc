package comm

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"regexp"
	"time"
	ut "train/utils"
)

const Base16Key = "0b0ab8e6f04190958e90dfbaef180f7e" //16进制
const GameCd = "3002"
const AuthUrl = "http://anti-obsession.dev-dh.com:18888/api/authentication_check"

// TestCard 特判断实名验证  内部测试使用
type TestCard struct {
	name   string
	idCard string
	age    int
}

func (this *TestCard) IsMatch(name, idCard string) bool {
	return this.name == name && this.idCard == idCard
}

// GetAge 嫌麻烦就不去规范身份证格式了，直接固定age返回
func (this *TestCard) GetAge() int {
	return this.age
}

// TryGetAge 用于GetAge特判
func (this *TestCard) TryGetAge(idCard string) int {
	if idCard == this.idCard {
		return this.age
	}
	return 0
}

var Age7 = &TestCard{
	name:   "苏大强",
	idCard: "100100100100100107",
	age:    7,
}
var Age15 = &TestCard{
	name:   "苏大强",
	idCard: "100100100100100115",
	age:    15,
}
var Age17 = &TestCard{
	name:   "苏大强",
	idCard: "100100100100100117",
	age:    17,
}
var Age20 = &TestCard{
	name:   "苏大强",
	idCard: "100100100100100120",
	age:    20,
}

// IdentityVerify 实名验证 0成功 -1姓名不对 -2身份证号不对 -3身份证格式不对 -4信息不匹配 -5网络错误
func IdentityVerify(uid, name, identity string) (age int, err int) {
	name = ut.Trim(name)
	identity = ut.Trim(identity)
	if Age7.IsMatch(name, identity) {
		return Age7.GetAge(), 0
	}
	if Age15.IsMatch(name, identity) {
		return Age15.GetAge(), 0
	}
	if Age17.IsMatch(name, identity) {
		return Age17.GetAge(), 0
	}
	if Age20.IsMatch(name, identity) {
		return Age20.GetAge(), 0
	}
	if r := CheckTrueName(name); !r {
		return -1, -1
	}
	if identity == "" {
		return -1, -2
	}
	if r := BeforeCheck(identity); r != 0 {
		return -1, -3
	}
	// 这里是去调api验证
	text := fmt.Sprintf("%s@%s", name, identity)
	text, _ = EncodeGcm(text)
	body := map[string]interface{}{
		"game_cd":     GameCd,
		"id":          fmt.Sprintf("%s_%d", uid, time.Now().Unix()),
		"player_info": text,
	}
	jsonData, _ := json.Marshal(body)
	buffer := bytes.NewReader(jsonData)
	resp, err2 := http.Post(AuthUrl, "application/json;", buffer)
	if resp != nil {
		defer resp.Body.Close()
	}
	if err2 != nil || resp.StatusCode != 200 {
		// request出错
		return -1, -5
	}
	bodyData, _ := io.ReadAll(resp.Body)
	data := make(map[string]interface{})
	err2 = json.Unmarshal(bodyData, &data)
	if err2 != nil {
		// 结果序列化出错
		return -1, -5
	}
	if data["err_code"] == "0" || data["err_code"] == "-201000" {
		return GetAge(identity), 0
	}
	return -1, -4
}

// GetAge 根据身份证获取年龄
func GetAge(val string) int {
	if len(val) < 18 {
		return 0
	}
	// 特判身份证age获取  暂时用不上
	//max := int(math.Max(math.Max(float64(Age7.TryGetAge(val)), float64(Age15.TryGetAge(val))), math.Max(float64(Age17.TryGetAge(val)), float64(Age20.TryGetAge(val)))))
	//if max > 0 {
	//	return max
	//}
	birthDay := ut.Atoi(val[6:14])
	now := time.Now()
	today := ut.Atoi(now.Format("20060204"))
	return int(math.Floor(float64((today - birthDay) / 10000)))
}

// CheckTrueName 验证名字 通过返回true
func CheckTrueName(val string) bool {
	p := "^[\u4E00-\u9FA5.·]{0,}$"
	if val == "" {
		return false
	}
	matched, _ := regexp.Match(p, []byte(val))
	return matched
}

// BeforeCheck 验证身份证号 通过时返回0
func BeforeCheck(val string) int {
	if len(val) != 18 {
		return 1
	}
	p := "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$"
	factor := []int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2}
	parity := []string{"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"}
	last := val[17:18]
	matched, _ := regexp.Match(p, []byte(val))
	if !matched {
		return -1 // 身份证格式不正确
	}
	sum := 0
	for i := 0; i < 17; i++ {
		sum += factor[i] * ut.Atoi(val[i:i+1])
	}
	if parity[sum%11] != last {
		return 2 //校验码错误
	}
	return 0
}

// 充值验证
// 未满8周岁的用户不能付费
// 8周岁以上未满16周岁的未成年人用户，单次充值金额不得超过50元人民币，每月充值金额累计不得超过200元人民币
// 16周岁以上的未成年人用户，单次充值金额不得超过100元人民币，每月充值金额累计不得超过400元人民币
func RechargeVerify(age int, money int, monthMoney int) bool {
	monthMoney = monthMoney + money
	if age < 8 {
		return false
	} else if age < 16 {
		return money < 50 && monthMoney < 200
	} else if age < 18 {
		return money < 100 && monthMoney < 400
	}
	return true
}

// EncodeGcm gcm加密
func EncodeGcm(word string) (string, error) {
	if word == "" {
		return "", errors.New("word is empty.")
	}
	bytes, _ := hex.DecodeString(Base16Key)
	block, err := aes.NewCipher(bytes)
	if err != nil {
		return "", err
	}
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	iv := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}
	cipherText := gcm.Seal(iv, iv, []byte(word), nil)
	encoded := base64.StdEncoding.EncodeToString(cipherText)
	return encoded, nil
}
