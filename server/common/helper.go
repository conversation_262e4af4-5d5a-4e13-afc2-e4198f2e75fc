package comm

import (
	"github.com/huyangv/vmqant/conf"
	"github.com/spf13/cast"
)

var ServerConfig *conf.Config //server.json

func IsDebug() bool { return cast.ToBool(GetSetting()["Debug"]) }

// 开启备份
func IsBackup() bool { return cast.ToBool(GetSetting()["Backup"]) }

func IsInvite() bool { return cast.ToBool(GetSetting()["Invite"]) }

// 获取服务器发现ip
func GetConsulUrl() string {
	return GetSetting()["ConsulURL"].(string)
}

func GetConsulToken() string {
	v := GetSetting()["ConsulToken"]
	if v == nil {
		return ""
	}
	return v.(string)
}

func GetNatsUrl() string {
	return GetSetting()["NatsURL"].(string)
}

func GetSeverArea() string {
	return cast.ToString(GetSetting()["SeverArea"])
}

func IsTaDebug() bool {
	ta := GetTa()
	if ta == nil {
		return false
	}
	return cast.ToBool(ta["debug"])
}

func IsTaOpen() bool {
	ta := GetTa()
	if ta == nil {
		return false
	}
	return cast.ToBool(ta["open"])
}

func GetTa() map[string]interface{} {
	if ta, ok := GetSetting()["Ta"]; ok && ta != nil {
		return ta.(map[string]interface{})
	}
	return make(map[string]interface{})
}

func IsSandBox() bool {
	return cast.ToBool(GetSetting()["SandBox"])
}

// 是否国内区域
func IsInland() bool {
	return GetSeverArea() == "inland"
}

func GetSetting() map[string]interface{} {
	return ServerConfig.Settings
}

func GetPackageSign() string {
	return "D5A453F61DCDA623CB6606CF453409DA269B362E"
}

func GetChannel() string {
	return cast.ToString(GetSetting()["Channel"])
}
