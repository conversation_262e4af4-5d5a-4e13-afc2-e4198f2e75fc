package mgate

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"
	"train/base/enum"
	"train/base/enum/logout_reason"
	com "train/common"
	"train/common/ecode"
	"train/common/pb"
	"train/db"
	ut "train/utils"

	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"github.com/spf13/cast"

	argsutil "github.com/huyangv/vmqant/rpc/util"
	"github.com/huyangv/vmqant/server"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	basegate "github.com/huyangv/vmqant/gate/base"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/huyangv/vmqant/registry"
	"github.com/huyangv/vmqant/selector"
	"github.com/pkg/errors"
)

var Module = func() module.Module {
	return new(Gate)
}

type Gate struct {
	basegate.Gate //继承
}

func (this *Gate) GetType() string {
	return "gate"
}

func (this *Gate) Version() string {
	return "1.0.0"
}

func (this *Gate) GetMinClientVersion() string {
	return "0.0.0"
}

func (this *Gate) OnInit(app module.App, settings *conf.ModuleSettings) {
	// 可执行文件所在目录
	eDir, _ := os.Executable()
	// 工作目录
	wDir, _ := os.Getwd()
	//注意这里一定要用 gate.Gate 而不是 module.BaseModule

	// 超过一段时间还没有建立mqtt连接则直接关闭网络连接
	ot := lo.If(com.IsDebug(), time.Second*3).Else(time.Second * 30)
	this.Gate.OnInit(this, app, settings,
		gate.Heartbeat(time.Second*30),
		//gate.BufSize(2048*20), //网络读写缓存大小
		gate.SetSessionLearner(this), //设置监听是否链接成功和断开链接
		gate.SetStorageHandler(this), //设置持久化处理器
		gate.SetRouteHandler(this),
		gate.OverTime(ot),
		gate.ConcurrentTasks(10000),
		gate.ServerOpts([]server.Option{server.Metadata(
			map[string]string{
				enum.CODE_VERSION:       this.Version(),
				enum.MIN_CLIENT_VERSION: this.GetMinClientVersion(),
				enum.STATE:              enum.NORMAL,
				enum.EXECUTE_DIR:        eDir,
				enum.WORK_DIR:           wDir,
			},
		)}),
	)
}

// Connect 当连接建立 并且MQTT协议握手成功
func (this *Gate) Connect(session gate.Session) {
	log.Info(session.GetIP() + " -> 建立链接(" + session.GetNetwork() + ")")
}

// DisConnect 当连接关闭	或者客户端主动发送MQTT DisConnect命令 ,这个函数中Session无法再继续后续的设置操作，只能读取部分配置内容了
func (this *Gate) DisConnect(session gate.Session) {
	uid := session.GetUserID()
	log.Info("%s -> 断开链接, %s", session.GetIP(), uid)
	if uid == "" {
		return //如果是游客的话 直接返回
	}
	// 通知游戏服
	gameNodeId := session.Get(enum.GAME_NODE_ID)
	if gameNodeId != "" {
		if _, err := this.Invoke(gameNodeId, "OnLeave", session); err != "" {
			log.Error("[%s] game OnLeave Error %s", uid, err)
		}
	}
}

func (gate *Gate) Storage(session gate.Session) (err error) {
	//log.Info("需要处理对Session的持久化")
	return nil
}

func (gate *Gate) Delete(session gate.Session) (err error) {
	//log.Info("需要删除Session持久化数据")
	return nil
}

func (gate *Gate) Query(Userid string) ([]byte, error) {
	//log.Info("查询Session持久化数据")
	return nil, fmt.Errorf("no redis")
}

func (this *Gate) Heartbeat(session gate.Session) {
	//log.Info("用户在线的心跳包", session.GetIP())
}

func (this *Gate) OnRoute(session gate.Session, topic string, msg []byte) (bool, interface{}, error) {
	var err error
	log.Info("OnRoute %s %s %s", topic, session.TraceID(), session.GetUserID())

	topics := strings.Split(topic, "/")

	if len(topics) != 3 {
		// 正确格式为:module/func/reqId"
		return true, nil, errors.Errorf("OnRoute topic format error %s %s", session.GetUserID(), topic)
	}

	targetModule := topics[0]
	msgId := topics[1]
	// reqId := cast.ToInt(topics[2])
	needReturn := true

	//这里特殊处理topic = `gate/${version}`，设置session客户端版本
	if targetModule == enum.ServerTypeGate {
		ok, err := this.checkServerStatus()
		if err != nil {
			return needReturn, nil, errors.Errorf("checkServerStatus error %s", err.Error())
		}
		if !ok {
			this.Logout(session, logout_reason.SERVER_CLOSED)
			return false, nil, nil
		}
		session.Set(enum.ClientVersion, msgId)
		return needReturn, pb.ProtoMarshalForce(&pb.S2C_ErrorResultMessage{Code: 0}), nil
	}

	//设置rpc参数
	var ArgsType = make([]string, 2)
	var args = make([][]byte, 2)
	args[0], err = session.Serializable()
	if err != nil {
		return needReturn, nil, errors.Errorf("onRoute session Serializable error %s %s %s", session.GetUserID(), topic, err.Error())
	}
	ArgsType[0] = gate.RPCParamSessionType
	ArgsType[1] = argsutil.BYTES
	args[1] = msg

	var call func(retry int) (bool, interface{}, error)
	call = func(retry int) (bool, interface{}, error) {
		//获取节点
		serverSession, err := this.getServer(session, targetModule)
		if err != nil {
			if err.Error() == ecode.SERVER_CLOSED.String() {
				this.Logout(session, logout_reason.DEFAULT)
				return false, nil, nil
			}
			if err.Error() == ecode.NEED_UPDATE.String() {
				this.Logout(session, logout_reason.DEFAULT)
				return false, nil, nil
			}
			return needReturn, nil, errors.Errorf("onRoute serverSession error %s %s %s", session.GetUserID(), topic, err.Error())
		}

		//逻辑服如果转发的节点和上次的不一样，走OnPlayerLogin
		if targetModule == enum.ServerTypeGame && session.Get(enum.GAME_NODE_ID) != serverSession.GetID() {
			//重新设置节点id
			session.SetPush(enum.GAME_NODE_ID, serverSession.GetID())
			_, e := this.Invoke(serverSession.GetID(), "OnPlayerLogin", session)
			if e != "" {
				session.SetPush(enum.GAME_NODE_ID, "")
				return needReturn, nil, errors.New(e)
			}
		}

		// 超时 这里实测server.json中的Rpc配置没有生效
		// debug环境下增加等待时间(长时间断点) 无效，需要看看原因
		// expired := lo.If(com.IsDebug(), time.Second*time.Duration(60)).Else(this.App.Options().RPCExpired)
		expired := this.App.Options().RPCExpired

		ctx, cancel := context.WithTimeout(context.TODO(), expired)
		defer cancel()
		if needReturn {
			result, e := serverSession.CallArgs(ctx, msgId, ArgsType, args)
			if e != "" {
				if retry > 0 && e == ecode.PLAYER_NOT_FOUND.String() { //重新找一个节点
					return call(retry - 1)
				}
				return needReturn, nil, errors.Errorf("onRoute call error %s %s %s", session.GetUserID(), topic, e)
			}
			return needReturn, result, nil
		}
		err = serverSession.CallNRArgs(msgId, ArgsType, args)
		if err != nil {
			return needReturn, nil, errors.Errorf("onRoute callNR error %s %s %s", session.GetUserID(), topic, err.Error())
		}
		return needReturn, nil, nil
	}

	return call(3)
}

func (this *Gate) getServer(session gate.Session, targetModule string) (s module.ServerSession, err error) {
	sid := session.Get(enum.PlayerSid)
	clientVersion := session.Get(enum.ClientVersion)
	gameNodeId := ""
	if targetModule == enum.ServerTypeGame {
		gameNodeId = session.Get(enum.GAME_NODE_ID)
	}

	return this.GetRouteServer(targetModule,
		selector.WithStrategy(func(services []*registry.Service) selector.Next {
			return func() (*registry.Node, error) {
				var nodes []*registry.Node
				isServerClose := true
				isNeedUpdate := true
				for _, service := range services {
					for _, node := range service.Nodes {
						// 过滤节点状态
						state := node.Metadata[enum.STATE]
						if state == enum.OFFLINE {
							continue
						}
						isServerClose = false

						//过滤节点版本
						minVersion := node.Metadata[enum.MIN_CLIENT_VERSION]
						maxVersion := node.Metadata[enum.MAX_CLIENT_VERSION]
						if !ut.IsEmpty(minVersion) && ut.CmpVersion(clientVersion, minVersion) < 0 {
							continue
						}
						isNeedUpdate = false
						if !ut.IsEmpty(maxVersion) && ut.CmpVersion(clientVersion, maxVersion) > 0 {
							continue
						}

						//game服过滤sid
						if targetModule == enum.ServerTypeGame && sid != node.Metadata[enum.PlayerSid] {
							continue
						}

						// 节点id固定 找到了就直接返回
						if node.Id == gameNodeId {
							return node, nil
						}
						//如果没有gameNodeId，不走准备下线的节点
						if state == enum.READY_OFFLINE {
							continue
						}
						nodes = append(nodes, node)
					}
				}
				if len(nodes) > 0 {
					index := ut.Random(0, len(nodes)-1)
					return nodes[index], nil
				}
				if isServerClose {
					return nil, errors.Errorf(ecode.SERVER_CLOSED.String())
				}
				if isNeedUpdate {
					return nil, errors.Errorf(ecode.NEED_UPDATE.String())
				}
				return nil, errors.Errorf("not found %s", clientVersion)
			}
		}))
}

// 检查服务器状态
func (this *Gate) checkServerStatus() (bool, error) {
	val := db.GetRedis().Get(context.TODO(), db.ServerStatus())
	if val.Err() == redis.Nil {
		return true, nil
	}
	if val.Err() != nil {
		return false, val.Err()
	}
	return cast.ToInt(val.Val()) == 0, nil
}

func (this *Gate) Logout(session gate.Session, reason int32) bool {
	err := session.SendNR(pb.S2CLogoutMessage, pb.ProtoMarshalForce(&pb.S2C_LogoutMessage{Reason: reason}))
	if err == "" {
		return true
	}
	log.Error("TellPlayerMsg 发送给客户端消息时出错:%s", err)
	return false
}
