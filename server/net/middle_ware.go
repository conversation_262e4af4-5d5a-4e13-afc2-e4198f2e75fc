package net

import (
	"fmt"
	"reflect"
	"strings"
	"train/base/manager"
	"train/common/ecode"
	ut "train/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
)

type HandlerFun struct {
	in []reflect.Type // 方法参数类型列表
	ft reflect.Type   // 保留
	fv reflect.Value  // 方法
}

func Create(mod ServerModule) *Middleware {
	return &Middleware{
		mod:   mod,
		route: make(map[string]*HandlerFun),
	}
}

type Middleware struct {
	route map[string]*HandlerFun
	mod   ServerModule
	lock  deadlock.RWMutex
}

func (this *Middleware) Wrap(msgId string, f any, opts ...WrapOptionString) (string, any) {
	logPrint := true //默认打印日志
	setLock := true  //如果函数的第一个参为structs.Player，那么默认要操作角色数据，这里默认加锁
	if opts != nil {
		opt := opts[0]
		logPrint = ut.If(opt.Contains("log=false"), false, logPrint)
		setLock = ut.If(opt.Contains("lock=false"), false, setLock)
	}

	funcValue := reflect.ValueOf(f)
	funcType := reflect.ValueOf(f).Type()
	argTypes := make([]reflect.Type, 0)
	for i := 0; i < funcType.NumIn(); i++ {
		rv := funcType.In(i)
		argTypes = append(argTypes, rv)
	}

	logic := func(args ...any) (res any, errStr string) {
		uid := ""
		traceId := ""

		transArg := func(typ reflect.Type, value any) reflect.Value {
			switch ts := typ.String(); ts {
			case "*structs.Player":
				session := value.(gate.Session)
				uid := session.GetUserID()
				plr, _ := manager.GlobalGetPlayerManager().TryGetPlayerByUid(uid)
				if plr == nil {
					return reflect.ValueOf(nil)
				}
				return reflect.ValueOf(plr)
			}
			return reflect.ValueOf(value)
		}

		//如果第一个传入参数为session，接受参数为structs.Player，这里做一下判断；应该只用于game
		if len(args) > 0 {
			mgr := manager.GlobalGetPlayerManager()
			arg := args[0]
			if session, ok := arg.(gate.Session); ok {
				uid = session.GetUserID()
				traceId = session.TraceID()
				if argTypes[0].String() == "*structs.Player" {
					if setLock {
						lock := mgr.Lock(uid)
						defer ut.Unlock(lock)
					}
					plr, _ := mgr.TryGetPlayerByUid(uid)
					if plr == nil || !plr.IsValid() {
						log.Debug("PLAYER_NOT_FOUND [%s] [%s] %v", traceId, uid, plr == nil)
						return nil, ecode.PLAYER_NOT_FOUND.String() //抛回上层，重新找节点
					}
				}
			}
		}

		if logPrint {
			log.Info("I - %s [%s] [%s] %+v", msgId, traceId, uid, args)
		}

		in := make([]reflect.Value, 0)
		// 组合参数
		for i, t := range argTypes {
			_arg := transArg(t, args[i])
			in = append(in, _arg)
		}

		replay := funcValue.Call(in)

		// rpc 均返回一个正常值和一个字符串错误
		if len(replay) == 2 {
			if logPrint {
				log.Info("O - %s [%s] [%s] %+v", msgId, traceId, uid, replay[0].Interface())
			}
			return replay[0].Interface(), cast.ToString(replay[1].Interface())
		}
		if _recover := replay[0]; _recover.Interface() != nil {
			j, ok := _recover.Interface().(proto.Message)
			if ok {
				if logPrint {
					log.Info("O - %s [%s] [%s] %+v", msgId, traceId, uid, j)
				}
				msgBytes, err := proto.Marshal(j)
				if err != nil {
					errStr = err.Error()
				}
				return msgBytes, errStr
			}
		}
		return
	}

	// 注册
	this.mod.GetModuleServer().RegisterGO(msgId, logic)
	// 修改
	rpcServer := this.mod.GetRpcServer()
	functionInfo := rpcServer.GetFunction(msgId)
	// 方法
	functionInfo.FuncType = funcType
	// 形参
	functionInfo.InType = argTypes
	return msgId, logic
}

// Deprecated: 弃用
func (this *Middleware) Call(session gate.Session, bytes []byte, id string) (msgBytes []byte, errStr string) {
	this.lock.RLock()
	hd, ok := this.route[id]
	this.lock.RUnlock()
	if !ok { // 路由错误
		return nil, fmt.Sprintf("route not found: %s", id)
	}
	// log.Debug("Middleware: OnMessage Call [%s].", id)
	in := make([]reflect.Value, 0)
	t := hd.in[1]
	elem := reflect.New(t.Elem())
	pb, _ := elem.Interface().(proto.Message)
	err := proto.Unmarshal(bytes, pb)
	if err != nil {
		return nil, err.Error()
	}
	// 第一个参数 可能是玩家(指针)  可能是session,也可以是其他类型, 这里加入一个switch方便扩展
	first := hd.in[0]
	uid := ""
	switch first.String() {
	case "gate.Session":
		in = append(in, reflect.ValueOf(session))
	case "*structs.Player":
		plr, exists := manager.GlobalGetPlayerManager().TryGetPlayerByUid(session.GetUserID())
		if !exists || plr == nil {
			return nil, fmt.Sprintf("plr not found %s", session.GetUserID())
		}
		uid = plr.GetUid()
		in = append(in, reflect.ValueOf(plr))
	default:
		return nil, fmt.Sprintf("未处理的参数类型，消息中断. %s", first.String())
	}
	// 第二个参数 固定消息(指针)
	in = append(in, reflect.ValueOf(pb))
	log.Info("I - [%s] [%s] %+v", uid, id, pb)
	replay := hd.fv.Call(in)
	// 返回的消息固定是一个S2C(proto.Message)消息类型,这里只处理返回一个值的情况
	if len(replay) != 1 {
		return nil, fmt.Sprintf("只能处理一个返回参数，但是返回了%d个值[%s]", len(replay), id)
	}
	_recover := replay[0]
	if _recover.Interface() == nil {
		return nil, "replay nil"
	}
	j := _recover.Interface().(proto.Message)
	log.Info("O - [%s] [%s] %+v", uid, id, j)
	msgBytes, e := proto.Marshal(j)
	if e != nil {
		errStr = e.Error()
	}
	// 错误消息似乎不用返回?
	return msgBytes, errStr
}

// WrapOptionString 包装option字符串
type WrapOptionString string

func (ws WrapOptionString) String() string {
	return string(ws)
}

func (ws WrapOptionString) Contains(str string) bool {
	split := strings.Split(ws.String(), "|")
	_, _, exists := lo.FindIndexOf(split, func(sps string) bool { return str == sps })
	return exists
}
