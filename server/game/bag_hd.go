package game

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum/character_quality"
	"train/base/enum/item_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"train/utils/array"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitBagHD 自动生成，不要在这个方法添加任何内容。
func InitBagHD(this *Game) {
	// 车票碎片合成
	this.middleware.Wrap("C2S_TicketMergeMessage", this.C2sTicketMergeMessageHandler)
	// 丢弃物品
	this.middleware.Wrap("C2S_DropItemMessage", this.C2sDropItemMessageHandler)
	// 空间宝石升级
	this.middleware.Wrap("C2S_SpaceStoneLvUpMessage", this.C2sSpaceStoneLvUpMessageHandler)
	// 使用空间宝石传送
	this.middleware.Wrap("C2S_UseSpaceStoneMessage", this.C2sUseSpaceStoneMessageHandler)
	// 使用空间宝石标记
	this.middleware.Wrap("C2S_MarkSpaceStoneMessage", this.C2sMarkSpaceStoneMessageHandler)
	// 同步物品
	this.middleware.Wrap("C2S_SyncItemMessage", this.C2sSyncItemMessageHandler)
	// 使用物品
	this.middleware.Wrap("C2S_UseItemMessage", this.C2sUseItemMessageHandler)
}
func (this *Game) C2sTicketMergeMessageHandler(player *structs.Player, msg *pb.C2S_TicketMergeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) *pb.S2C_TicketMergeMessage {
		return &pb.S2C_TicketMergeMessage{Code: code}
	}
	exChangeNum := cast.ToInt(msg.GetNum())
	misc := cfg.GetMisc()
	ratio := misc.Ticket.MergeCnt
	frag := &structs.Condition{Id: item_id.TICKET_FRAG, Type: condition.PROP}
	curNum := player.GetNumByCondition(frag)
	needNum := exChangeNum * ratio
	frag.Num = needNum

	if needNum > curNum {
		return send(1)
	}

	player.DeductCost(frag, ta.ResChangeSceneTypeBag)
	player.GrantReward(&structs.Condition{Id: item_id.TICKET, Type: condition.PROP, Num: exChangeNum}, ta.ResChangeSceneTypeBag)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sDropItemMessageHandler(player *structs.Player, msg *pb.C2S_DropItemMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) *pb.S2C_DropItemMessage {
		return &pb.S2C_DropItemMessage{Code: code}
	}
	item := msg.GetItem()
	if item.Type != condition.PROP {
		return send(1)
	}
	cond := &structs.Condition{}
	cond.FromPb(item)
	id := cast.ToInt(cond.Id)
	bean, _ := cfg.ItemContainer.GetBeanById(id)
	if bean == nil {
		return send(2)
	}
	if bean.Type != item_type.COLLECT {
		return send(2)
	}
	player.DeductCost(cond, ta.ResChangeSceneTypeBag)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sSpaceStoneLvUpMessageHandler(player *structs.Player, msg *pb.C2S_SpaceStoneLvUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_SpaceStoneLvUpMessage{Code: cast.ToInt32(code)}
	}
	spaceStone := player.SpaceStone
	bean, _ := cfg.SpaceStoneContainer.GetBeanById(spaceStone.Lv + 1)
	if bean == nil {
		return send(1)
	}
	costs := structs.ToConditions(bean.BuyCost)
	if len(player.CheckConditions(costs)) > 0 {
		return send(2)
	}
	player.DeductCosts(costs, ta.ResChangeSceneTypeSpaceStone)
	spaceStone.LvUp()
	return &pb.S2C_SpaceStoneLvUpMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sUseSpaceStoneMessageHandler(player *structs.Player, msg *pb.C2S_UseSpaceStoneMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_UseSpaceStoneMessage{Code: cast.ToInt32(code)}
	}
	id := cast.ToInt(msg.GetId())
	spaceStone := player.SpaceStone
	if !spaceStone.HasMark(id) {
		return send(1)
	}
	planet := player.PlanetData.GetPlanet(id)
	if planet == nil {
		return send(2)
	}

	misc := cfg.GetMisc()
	cost := misc.SpaceStone.Cost
	if spaceStone.Energy < cost {
		return send(3)
	}
	spaceStone.Energy -= cost
	player.PlanetData.ReachPlanet(id)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sMarkSpaceStoneMessageHandler(player *structs.Player, msg *pb.C2S_MarkSpaceStoneMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_MarkSpaceStoneMessage{Code: cast.ToInt32(code)}
	}
	id := cast.ToInt(msg.GetId())
	removeId := cast.ToInt(msg.GetRemoveId())
	spaceStone := player.SpaceStone
	if spaceStone.HasMark(id) {
		return send(1)
	}
	if spaceStone.GetMaxMarkCnt() <= len(spaceStone.Marks) && removeId == 0 {
		return send(2)
	}
	planet := player.PlanetData.GetPlanet(id)
	if planet == nil {
		return send(3)
	}
	spaceStone.RemoveMark(removeId)
	spaceStone.Mark(id)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sSyncItemMessageHandler(player *structs.Player, msg *pb.C2S_SyncItemMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return nil
	//@action-code-end
}
func (this *Game) C2sUseItemMessageHandler(player *structs.Player, msg *pb.C2S_UseItemMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	result := make([]*structs.Condition, 0)
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_UseItemMessage{
			Code:    cast.ToInt32(code),
			Rewards: lo.Map(result, func(t *structs.Condition, i int) *pb.Condition { return t.ToPb() }),
		}
	}
	cond := &structs.Condition{}
	cond.FromPb(msg.GetItem())
	if cond.Num <= 0 {
		cond.Num = 1
	}
	if !player.CheckCondition(cond) {
		return send(1)
	}
	if cond.Type == condition.PROP {
		id := cast.ToInt(cond.Id)
		switch cond.Id {
		case item_id.InviteCard1:
			fallthrough
		case item_id.InviteCard2:
			fallthrough
		case item_id.InviteCard3:
			arr := lo.Filter(cfg.CharacterContainer.GetData(), func(character *cfg.Character[int], i int) bool {
				return character.Quality == character_quality.GetInviteCardQuality(id)
			})
			for i := 0; i < cond.Num; i++ {
				result = append(result, player.CheckChangePassenger(player.RandomPassenger(arr...), result))
			}
		}
		itemBean, _ := cfg.ItemContainer.GetBeanById(id)
		if itemBean == nil {
			return send(2)
		}
		if itemBean.Type == item_type.ROLE_TICKET {
			charaBean, _ := cfg.CharacterContainer.GetBeanById(id)
			if charaBean == nil {
				return send(3)
			}
			result = append(result, player.CheckChangePassenger(charaBean, result))
		} else if itemBean.Type == item_type.TIME_BOX {
			uid := cast.ToString(cond.Extra["uid"])
			if uid == "" {
				return send(4)
			}
			item := player.GetItemByUid(uid)
			if item == nil {
				return send(5)
			}
			if item.GetSurplusTime() > 0 {
				return send(6)
			}
			misc := cfg.GetMisc()
			timeBoxBean := array.Find(misc.TimeBox, func(t *cfg.TimeBoxConfig) bool {
				return t.Id == id
			})
			if timeBoxBean == nil {
				return send(7)
			}
			rewards := player.GenerateRewards(timeBoxBean.Rewards, nil)
			result = append(result, rewards...)
		} else if itemBean.Type == item_type.RANDOM_BOX {
			for i := 0; i < cond.Num; i++ {
				rewards := player.GenRandomBoxRewards(id)
				result = append(result, rewards...)
			}
		}
		player.DeductCost(cond, ta.ResChangeSceneTypeBag)
		player.GrantRewards(result, ta.ResChangeSceneTypeBag)
		return send(0)
	}
	return send(2)
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
