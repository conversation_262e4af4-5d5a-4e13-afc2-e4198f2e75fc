package game

import (
	"os"
	"train/base/enum"
	"train/base/enum/logout_reason"
	"train/base/manager"
	"train/base/script"
	"train/net"
	"train/sdk/apple"

	"github.com/huyangv/vmqant/registry"
	mqrpc "github.com/huyangv/vmqant/rpc"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/huyangv/vmqant/server"
	"github.com/spf13/cast"
)

var Module = func() module.Module {
	return new(Game)
}

type Game struct {
	basemodule.BaseModule
	middleware *net.Middleware
}

func (this *Game) GetType() string {
	return "game"
}

func (this *Game) Version() string {
	return "1.0.0"
}

func (this *Game) GetMinClientVersion() string {
	return "1.0.0"
}

func (this *Game) GetMaxClientVersion() string {
	return ""
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Game) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
}

func (this *Game) OnInit(app module.App, settings *conf.ModuleSettings) {
	// 可执行文件所在目录
	eDir, _ := os.Executable()
	// 工作目录
	wDir, _ := os.Getwd()

	this.BaseModule.OnInit(this, app, settings, func(op *server.Options) {
		op.Metadata = map[string]string{
			enum.PlayerSid:          cast.ToString(script.Sid),
			enum.CODE_VERSION:       this.Version(),
			enum.MIN_CLIENT_VERSION: this.GetMinClientVersion(),
			enum.MAX_CLIENT_VERSION: this.GetMaxClientVersion(),
			enum.STATE:              enum.NORMAL,
			enum.EXECUTE_DIR:        eDir,
			enum.WORK_DIR:           wDir,
		}
	})
	// 作为集群启
	//if script.IsClusterMod() {
	//	this.GetServer().Options().Metadata["cluster"] = cast.ToString(script.Cluster)
	//}

	this.middleware = net.Create(this)
	InitMsgHD(this)
	InitTrainHD(this)
	InitAchievementHD(this)
	InitToolHD(this)
	InitBagHD(this)
	InitTowerHD(this)
	InitBlack_holeHD(this)
	InitPassengerHD(this)
	InitEquipHD(this)
	InitInstanceHD(this)
	InitWantedHD(this)
	InitStoreHD(this)
	InitPaymentHD(this)
	InitTransportHD(this)
	InitFieldHD(this)
	InitOreHD(this)
	InitCollectHD(this)
	InitArrestHD(this)
	InitPlanetHD(this)
	InitDaily_taskHD(this)
	InitResonanceHD(this)
	InitPvpHD(this)
	InitExploreHD(this)
	InitProfile_branchHD(this)
	InitTrainDailyTaskHD(this)
	InitBurstTaskHD(this)
	InitTrainActivityHD(this)
	InitTrainTechHD(this)
	this.InitRpc()
	this.InitHttpRpc()
	apple.InitAppleRootCerts()
}

func (this *Game) Run(closeSig chan bool) {
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	// 关闭区服
	// 保存玩家数据数据
	this.Offline(false)
}

func (this *Game) OnDestroy() {
	this.BaseModule.OnDestroy()
}

func (this *Game) InvokeSelf(route string, args ...any) (result interface{}, err string) {
	return this.Invoke(this.GetServerID(), route, args...)
}

func (this *Game) GetModuleServer() server.Server {
	return this.GetServer()
}

func (this *Game) GetRpcServer() mqrpc.RPCServer {
	return this.GetServer().GetRpcServer()
}

// 节点下线
func (this *Game) Offline(kick bool) (result bool, err string) {
	this.setMeta(enum.STATE, enum.READY_OFFLINE)
	reason := 0
	if kick {
		reason = logout_reason.DEFAULT
	}
	manager.GlobalGetPlayerManager().ClearAll(reason)
	this.setMeta(enum.STATE, enum.OFFLINE)
	return true, ""
}

func (this *Game) setMeta(key string, value string) {
	this.GetServer().Options().Metadata[key] = value
	this.GetServer().ServiceRegister()
}

func (this *Game) All(moduleType, topic string, params ...any) {
	var srv []*registry.Service
	srv, _ = this.GetApp().Registry().GetService(moduleType)
	for _, sr := range srv {
		if sr == nil {
			continue
		}
		for _, node := range sr.Nodes {
			this.Invoke(node.Id, topic, params...)
		}
	}
}
