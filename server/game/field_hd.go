package game

import (
	"train/base/structs"
	"train/common/pb"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitFieldHD 自动生成，不要在这个方法添加任何内容。
func InitFieldHD(this *Game) {
	// 农场格子操作
	this.middleware.Wrap("C2S_CeilOperationMessage", this.C2sCeilOperationMessageHandler)
	// 格子数据同步
	this.middleware.Wrap("C2S_CeilSyncMessage", this.C2sCeilSyncMessageHandler)
}
func (this *Game) C2sCeilOperationMessageHandler(player *structs.Player, msg *pb.C2S_CeilOperationMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	typ := msg.GetType()
	id := cast.ToInt(msg.GetId())
	value := cast.ToInt(msg.GetPowerValue())
	mod := player.FieldModule
	rewards, code := mod.HandleCeilOperation(typ, id, value)
	var data *pb.FieldCeil
	if code == pb.CeilOperationCode_Success {
		data = mod.GetCeil(id).ToPb()
	}
	return &pb.S2C_CeilOperationMessage{
		Code:    code,
		Data:    data,
		Rewards: lo.Map(rewards, func(e *structs.Condition, i int) *pb.Condition { return e.ToPb() }),
	}
	//@action-code-end
}
func (this *Game) C2sCeilSyncMessageHandler(player *structs.Player, msg *pb.C2S_CeilSyncMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := cast.ToInt(msg.GetId())
	mod := player.FieldModule
	ceil := mod.GetCeil(id)
	return &pb.S2C_CeilSyncMessage{Data: ceil.ToPb()}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
