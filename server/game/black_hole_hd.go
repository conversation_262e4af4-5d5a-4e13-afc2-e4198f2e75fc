package game

import (
	"fmt"
	"train/base/cfg"
	"train/base/enum"
	"train/base/enum/battle_skill_effect_type"
	"train/base/enum/battle_skill_trigger_type"
	"train/base/enum/black_hole_buff_type"
	"train/base/enum/black_hole_node_type"
	"train/base/enum/time_stone_evt"
	"train/base/enum/time_stone_record_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitBlack_holeHD 自动生成，不要在这个方法添加任何内容。
func InitBlack_holeHD(this *Game) {
	// 预加载黑洞的一些信息
	this.middleware.Wrap("C2S_ReadyStartBlackHoleMessage", this.C2sReadyStartBlackHoleMessageHandler)
	// 开始黑洞玩法
	this.middleware.Wrap("C2S_StartBlackHoleMessage", this.C2sStartBlackHoleMessageHandler)
	// 选择下一步节点
	this.middleware.Wrap("C2S_SelectBlackHoleNodeMessage", this.C2sSelectBlackHoleNodeMessageHandler)
	// 同步黑洞数据
	this.middleware.Wrap("C2S_SyncBlackHoleMessage", this.C2sSyncBlackHoleMessageHandler)
	// 解锁黑洞
	this.middleware.Wrap("C2S_UnlockBlackHoleMessage", this.C2sUnlockBlackHoleMessageHandler)
}
func (this *Game) C2sReadyStartBlackHoleMessageHandler(player *structs.Player, msg *pb.C2S_ReadyStartBlackHoleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	blackHole := player.BlackHole
	level := blackHole.Level
	boss := blackHole.GetBossByLv(level)
	if boss == nil {
		boss = &structs.BlackHoleBoss{
			Level: level,
			Roles: blackHole.GenBoss(),
		}
		blackHole.Bosses = append(blackHole.Bosses, boss)
	}
	return &pb.S2C_ReadyStartBlackHoleMessage{Code: 0, Boss: lo.Map(boss.Roles, func(e *structs.BattleRole, i int) *pb.BattleRole { return e.ToPb() })}
	//@action-code-end
}
func (this *Game) C2sStartBlackHoleMessageHandler(player *structs.Player, msg *pb.C2S_StartBlackHoleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_StartBlackHoleMessage{Code: code}
	}
	blackHole := player.BlackHole
	level := cast.ToInt(msg.GetLevel())
	if blackHole.Level+1 != level {
		return send(2)
	}
	if level > blackHole.GetMaxUnlockLevel() {
		return send(2)
	}
	// 进入玩法时 设置战斗乘客
	if level == 1 {
		blackHole.Refresh()
		hasEmpty := false
		roles := lo.Map(ut.ToInt(msg.GetRoles()), func(id int, i int) *structs.BattleRole {
			passenger := player.GetPassengerById(id)
			if passenger == nil {
				hasEmpty = true
				return nil
			}
			role := passenger.ToResonance()
			return &structs.BattleRole{
				Uid:     fmt.Sprintf("%d_%s", id, ut.ID()),
				Id:      id,
				Lv:      role.Level,
				StarLv:  role.StarLv,
				Talents: role.Talents,
				Equips:  role.GetEquips(),
			}
		})
		if hasEmpty {
			return send(1)
		}
		blackHole.Roles = roles
	}
	blackHole.Start(level)
	return &pb.S2C_StartBlackHoleMessage{Code: 0, BlackHole: blackHole.ToPb()}
	//@action-code-end
}
func (this *Game) C2sSelectBlackHoleNodeMessageHandler(player *structs.Player, msg *pb.C2S_SelectBlackHoleNodeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_SelectBlackHoleNodeMessage{Code: code}
	}
	res := &pb.S2C_SelectBlackHoleNodeMessage{Code: 0}
	nodeId := msg.GetNodeId()
	blackHole := player.BlackHole
	if blackHole.NextId != "" && blackHole.NextId != nodeId {
		return send(7)
	}

	curId := blackHole.CurId
	curNode := blackHole.GetNode(curId)
	curNode.GetJson()
	if !curNode.IsNext(nodeId) {
		return send(1)
	}
	node := array.Find(player.BlackHole.Map, func(n *structs.BlackHoleNode) bool { return n.Id == nodeId })
	if node == nil {
		return send(3)
	}
	player.DoTimeStoneRecord(time_stone_record_type.BlackHoleBattle, &time_stone_evt.BlackHoleBattle{})
	canNext := true
	getReward := false
	Type := node.Type
	if Type == black_hole_node_type.BATTLE {
		blackHole.NextId = nodeId
		battle := msg.GetBattle()
		canNext = false
		if battle != nil {
			uids := battle.GetUids()
			deads := []string{}
			if battle.IsWin {
				getReward = true
				for _, uid := range blackHole.Team {
					has := array.Some(uids, func(u string) bool { return u == uid })
					if !has {
						deads = append(deads, uid)
					}
				}
				blackHole.Team = lo.Filter(blackHole.Team, func(uid string, i int) bool { return array.Has(uids, uid) })
				node.Enemies = node.Enemies[:0]

				//变成奖励节点
				// 仅最后一层的最后一关没得科技装备
				if !node.GetJson().IsEndBoss() {
					//生成装备
					node.Equips = blackHole.GenEquips()
					res.Equips = lo.Map(node.Equips, func(e *structs.BlackHoleEquip, i int) *pb.BlackHoleEquip { return e.ToPb() })

					//处理战斗结束后触发的效果
					equips := lo.Filter(blackHole.Equips, func(e *structs.BlackHoleEquip, i int) bool {
						return e.GetJson().Trigger.Type == battle_skill_trigger_type.BATTLE_AFTER
					})
					hp := 0
					attack := 0
					id := enum.BLACK_HOLE_ATTR_ID
					for _, equip := range equips {
						effect := array.Find(equip.GetJson().Effects, func(effect *cfg.BattleSkillEffect) bool {
							return effect.Type == battle_skill_effect_type.CHANGE_ATTACK
						})
						if effect != nil {
							attack += ut.Round(effect.Value * float64(cfg.GetRoleAttr(id, equip.Level, 0, enum.RoleAttack)) * blackHole.Add)
						}
						effect = array.Find(equip.GetJson().Effects, func(effect *cfg.BattleSkillEffect) bool {
							return effect.Type == battle_skill_effect_type.CHANGE_HP
						})
						if effect != nil {
							hp += ut.Round(effect.Value * float64(cfg.GetRoleAttr(id, equip.Level, 0, enum.RoleAttack)) * blackHole.Add)
						}
					}
					if hp > 0 || attack > 0 {
						buff := &structs.BlackHoleBuff{Type: black_hole_buff_type.ALL, Add: map[string]int{enum.RoleHP: hp, enum.RoleAttack: attack}}
						blackHole.AddBuff(buff)
						res.Buff = buff.ToPb()
					}
				}
				if len(node.Buffs) > 0 || len(node.Equips) > 0 {
					node.Type = black_hole_node_type.ATTR
				} else {
					canNext = true
				}
			} else {
				//失败不结算
				// deads = blackHole.Team
				// blackHole.Team = blackHole.Team[:0]
				// node.Enemies = lo.Filter(node.Enemies, func(r *structs.BattleRole, i int) bool { return array.Has(uids, r.Uid) })
			}
			blackHole.Deads = append(blackHole.Deads, deads...)
			res.Battle = battle
		}
	} else if Type == black_hole_node_type.AID {
		aid := msg.GetAid()
		role := array.Find(node.Aids, func(role *structs.BattleRole) bool { return role.Uid == aid })
		if role == nil {
			return send(4)
		}
		blackHole.Aids = append(blackHole.Aids, role)
	} else if Type == black_hole_node_type.ATTR {
		_equip := msg.GetEquip()
		equip := array.Find(node.Equips, func(e *structs.BlackHoleEquip) bool {
			return e.Id == int(_equip.GetId()) && e.Target == int(_equip.GetTarget())
		})
		if equip == nil {
			return send(7)
		}
		blackHole.Equips = append(blackHole.Equips, equip)
	} else if Type == black_hole_node_type.REBIRTH {
		if len(player.BlackHole.Deads) <= 0 {
			buff := blackHole.AddBuff(node.Buffs[0])
			res.Buff = buff.ToPb()
		} else {
			res.Rebirth = blackHole.RandomRebirth()
		}
	} else if Type == black_hole_node_type.END {
		getReward = true
	}
	if canNext {
		blackHole.MoveTo(nodeId)
	}
	if getReward {
		curNode := blackHole.GetNode(nodeId)
		if curNode != nil && len(curNode.Rewards) > 0 {
			player.GrantRewards(curNode.Rewards, ta.ResChangeSceneTypeBlackHole)
		}
	}
	res.CurId = blackHole.CurId
	res.NextId = blackHole.NextId
	return res
	//@action-code-end
}
func (this *Game) C2sSyncBlackHoleMessageHandler(player *structs.Player, msg *pb.C2S_SyncBlackHoleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return &pb.S2C_SyncBlackHoleMessage{Code: 0, BlackHole: player.BlackHole.ToPb()}
	//@action-code-end
}
func (this *Game) C2sUnlockBlackHoleMessageHandler(player *structs.Player, msg *pb.C2S_UnlockBlackHoleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.BlackHole.Unlock()
	return &pb.S2C_UnlockBlackHoleMessage{Code: 0, BlackHole: player.BlackHole.ToPb()}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
