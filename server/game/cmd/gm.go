package cmd

import (
	"errors"
	"strings"
	"train/base/structs"
	"train/common/pb"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

var gmCmdLock deadlock.Once
var cmd *gmCmd

// GlobalGetGmCmd 全局gm工具
func GlobalGetGmCmd() *gmCmd {
	gmCmdLock.Do(func() {
		cmd = &gmCmd{
			cmdMap: map[string]GmExecutor{},
		}
	})
	return cmd
}

// GmExecutor gm执行代理
type GmExecutor func(player *structs.Player, params ...string) (string, error)

type gmCmd struct {
	cmdMap map[string]GmExecutor
}

// Register 注册cmd脚本 cmd会自动加上@前缀 重复的注册会警告并覆盖
func (this *gmCmd) Register(cmd string, executor GmExecutor) *gmCmd {
	if ut.IsEmpty(cmd) {
		log.Error("尝试注册Gm执行器，但是Gm代码是空值.")
		return this
	}
	if executor == nil {
		log.Warning("%s:注册了一个空的Gm执行器", cmd)
	}
	if this.cmdMap[cmd] != nil {
		log.Warning("%s:Gm执行器被覆盖", cmd)
	}
	this.cmdMap[cmd] = executor
	return this
}

// Execute command的标准格式应该是@add-diamond 30 或者@add-test 2,1
func (this *gmCmd) Execute(command string, player *structs.Player, send bool) (string, error) {
	if player == nil {
		return "", errors.New("gm指令实现失败,玩家数据错误")
	}
	if !strings.HasPrefix(command, "@") {
		return "", errors.New("gm指令格式错误: " + command)
	}
	split := strings.Split(command, " ")
	if len(split) > 2 || len(split) < 1 {
		return "", errors.New("gm指令格式错误: " + command)
	}
	executor := this.cmdMap[split[0]]
	if executor == nil {
		return "", errors.New("gm指令执行失败,没有对应的执行器")
	}
	params := make([]string, 0)
	if len(split) == 2 {
		str := split[1]
		params = strings.Split(str, ",")
	}
	// 丢给解释器去执行
	s, e := executor(player, params...)
	if e == nil {
		log.Info("玩家:%s执行%s.", player.GetUid(), command)
		if send {
			player.TellPlayerMsg(pb.S2CGmExecuteRspMessage, &pb.S2C_GmExecuteRspMessage{
				Reply: command,
			})
		}
	}
	return s, e
}
