package cmd

import (
	"encoding/json"
	"errors"
	"fmt"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum/build_attr"
	"train/base/enum/daily_task_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"

	"github.com/spf13/cast"
)

func InitGm() {
	GlobalGetGmCmd().Register("@add-currency", func(player *structs.Player, params ...string) (string, error) {
		if len(params) != 2 {
			return "", errors.New("@add-currency params err")
		}
		cond := &structs.Condition{Type: cast.ToInt(params[0]), Num: cast.ToInt(params[1])}
		player.GrantReward(cond, ta.ResChangeSceneTypeNoReport)
		return "", nil
	}).Register("@add-item", func(player *structs.Player, params ...string) (string, error) {
		if len(params) != 2 {
			return "", errors.New("@add-item params err")
		}
		cond := &structs.Condition{Type: condition.PROP, Id: cast.ToInt(params[0]), Num: cast.ToInt(params[1])}
		player.GrantReward(cond, ta.ResChangeSceneTypeNoReport)
		return "", nil
	}).Register("@set-energy", func(player *structs.Player, params ...string) (string, error) {
		if len(params) != 1 {
			return "", errors.New("@set-energy params err")
		}
		num := cast.ToInt(params[0])
		player.Energy.Energy = num
		return "", nil
	}).Register("@set-planet", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		mapId := cast.ToInt(params[1])
		nodeId := cast.ToInt(params[2])
		planet := player.PlanetData.GetPlanet(id)
		if planet == nil {
			planet = player.PlanetData.UnlockPlanet(id)
			if planet == nil {
				return "", nil
			}
		}
		player.PlanetData.ReachPlanet(id)
		planet.MapId = 1
		planet.CurNodeId = 1
		planet.Landed = false
		for {
			if planet.MapId == mapId && planet.CurNodeId == nodeId {
				break
			}
			if planet.IsDone() {
				planet.Landed = true
				player.PlanetData.UnlockPlanetByPre(planet.Id)
				break
			}
			data := planet.GetMapDataById(planet.CurNodeId)
			if data != nil {
				rewards := player.PlanetData.GetPlanetNodeRewards(data)
				player.GrantRewards(rewards, ta.ResChangeSceneTypeNoReport)
			}
			player.PassPlanetCurNode(planet)
		}
		return "", nil
	}).Register("@set-planet-branch", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		mapId := cast.ToInt(params[1])
		nodeId := cast.ToInt(params[2])
		branchId := cast.ToInt(params[3])
		planet := player.PlanetData.GetPlanet(id)
		if planet == nil {
			planet = player.PlanetData.UnlockPlanet(id)
			if planet == nil {
				return "", nil
			}
		}
		player.PlanetData.ReachPlanet(id)
		branch := planet.Branches[branchId-1]
		branch.MapId = 1
		branch.NodeId = 1
		branch.NodeRewards = make([]int, 0)
		for {
			if branch.MapId == mapId && branch.NodeId == nodeId {
				break
			}
			if branch.IsDone() {
				break
			}
			player.PassBranchPlanetNode(branch)
		}
		return "", nil
	}).Register("@add-passenger", func(player *structs.Player, params ...string) (string, error) {
		if len(params) != 1 {
			return "", errors.New("@add-passenger params err")
		}
		id := cast.ToInt(params[0])
		player.UnlockPassenger(id)
		return "", nil
	}).Register("@add-worldtime", func(player *structs.Player, params ...string) (string, error) {
		num := cast.ToInt(params[0])
		player.WorldTime += num
		return "", nil
	}).Register("@clear-train-build-cd", func(player *structs.Player, params ...string) (string, error) {
		for _, carriage := range player.Train.Carriages {
			carriage.SetBuiltDone()
		}
		return "", nil
	}).Register("@train-item-built", func(player *structs.Player, params ...string) (string, error) {
		if len(params) != 2 {
			return "", nil
		}

		trainId := cast.ToInt(params[0])
		level := cast.ToInt(params[1])
		level = ut.Max(1, level)

		unlock := func(carriage *structs.Carriage, level int) (string, error) {
			carriage.SetBuiltDone()
			themeBean := array.Find(cfg.TrainThemeContainer.GetData(), func(b *cfg.TrainTheme[string]) bool { return b.CarriageId == carriage.Id && level <= b.UnlockLevel })
			if themeBean == nil {
				return fmt.Sprintf("没有符合等级的主题了:%d", level), nil
			}
			carriage.ThemeLv = themeBean.Order
			// 当前主题设施可升级的最高等级
			maxBuildLv := themeBean.UnlockLevel
			beans := cfg.TrainItemContainer.GetData()
			// 可建造的列表
			beans = lo.Filter(beans, func(item *cfg.TrainItem[string], i int) bool {
				return item.CarriageId == carriage.Id && item.Skin == 1 && item.Show == 1
			})
			for _, item := range beans {
				build := carriage.GetBuildByOrder(item.Order)
				if build == nil {
					carriage.UnlockBuild(item.Order)
					build = carriage.GetBuildByOrder(item.Order)
				}
				build.Lv = ut.Min(maxBuildLv, level)
				build.Skin = build.GetAttr(build_attr.SKIN)
			}
			return "", nil
		}

		if trainId == 0 {
			for _, carriage := range player.Train.Carriages {
				unlock(carriage, level)
			}
			return "成功", nil
		}
		carriage := player.GetCarriageById(trainId)
		return unlock(carriage, level)
		//return "", nil
	}).Register("@watch-tool", func(player *structs.Player, params ...string) (string, error) {
		return "", nil
	}).Register("@close-guide", func(player *structs.Player, params ...string) (string, error) {
		open := cast.ToBool(params[0])
		player.CloseGuide = open
		return "", nil
	}).Register("@unlock-build", func(player *structs.Player, params ...string) (string, error) {
		// id := cast.ToString(params[0])
		// player.UnlockBuild(id)
		return "", nil
	}).Register("@complete-task", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToString(params[0])
		complete := func(taskId string) {
		}
		complete = func(taskId string) {
			json, _ := cfg.TaskContainer.GetBeanByUnique(taskId)
			if json.PreTask != "" && !player.IsTaskCompleted(json.PreTask) {
				complete(json.PreTask)
			}
			if !player.IsTaskUnlock(id) {
				player.UnlockTask(id)
			}
			player.CompleteTask(id)
			rewards := structs.ToConditions(json.Reward)
			player.GrantRewards(rewards, ta.ResChangeSceneTypeNoReport)
		}
		complete(id)

		return "", nil
	}).Register("@all", func(player *structs.Player, params ...string) (string, error) {
		datas := cfg.ItemContainer.GetData()
		for _, data := range datas {
			if data.Type == 2 {
				continue
			}
			player.GrantReward(&structs.Condition{Type: condition.PROP, Id: data.Id, Num: 100}, ta.ResChangeSceneTypeNoReport)
		}

		player.GrantRewards([]*structs.Condition{
			&structs.Condition{Type: condition.STAR_DUST, Num: 1000000},
			&structs.Condition{Type: condition.DIAMOND, Num: 10000},
			&structs.Condition{Type: condition.HEART, Num: 10000},
		}, ta.ResChangeSceneTypeUnknown)
		return "", nil
	}).Register("@all-passenger", func(player *structs.Player, params ...string) (string, error) {
		datas := cfg.CharacterContainer.GetData()
		for _, data := range datas {
			player.UnlockPassenger(data.Id)
		}
		return "", nil
	}).Register("@set-passenger-lv", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		lv := cast.ToInt(params[1])
		if id == 0 {
			lo.ForEach(player.Passenger, func(passenger *structs.Passenger, i int) {
				player.UpdateAllOutput()
				passenger.Level = lv
			})
			return "", nil
		}
		passenger := player.GetPassengerById(id)
		player.UpdateAllOutput()
		passenger.Level = lv
		return "", nil
	}).Register("@set-passenger-starlv", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		lv := cast.ToInt(params[1])
		if id == 0 {
			lo.ForEach(player.Passenger, func(passenger *structs.Passenger, i int) {
				player.UpdateAllOutput()
				passenger.StarLv = lv
			})
			return "", nil
		}
		passenger := player.GetPassengerById(id)
		player.UpdateAllOutput()
		passenger.StarLv = lv
		return "", nil
	}).Register("@test", func(player *structs.Player, params ...string) (string, error) {
		return "", nil
	}).Register("@add", func(player *structs.Player, params ...string) (string, error) {
		cond := &structs.Condition{Type: cast.ToInt(params[0]), Id: params[1], Num: cast.ToInt(params[2])}
		player.GrantReward(cond, ta.ResChangeSceneTypeNoReport)
		return "", nil
	}).Register("@add-game-time", func(player *structs.Player, params ...string) (string, error) {
		time := cast.ToInt(params[0])
		player.OffsetTime += time
		return "", nil
	}).Register("@add-chest", func(player *structs.Player, params ...string) (string, error) {
		if len(params) != 2 {
			return "", errors.New("@add-chest params err")
		}
		//typ := cast.ToInt(params[0])
		id := cast.ToInt(params[0])
		num := cast.ToInt(params[1])
		bean, _ := cfg.ChestContainer.GetBeanById(id)
		if bean == nil {
			return "", errors.New("@add-chest params err")
		}
		player.AddChest(id, num)
		return "", nil
	}).Register("@test-chest", func(player *structs.Player, params ...string) (string, error) {
		if len(params) != 2 {
			return "", errors.New("@add-chest params err")
		}
		//typ := cast.ToInt(params[0])
		id := cast.ToInt(params[0])
		num := cast.ToInt(params[1])
		out := map[int][]*structs.Condition{}
		for i := 0; i < num; i++ {
			out[i] = player.ChestSingleOpen(id)
		}
		bytes, err := json.Marshal(&out)
		return string(bytes), err
	}).Register("@refresh-blackhole", func(player *structs.Player, params ...string) (string, error) {
		player.BlackHole.Refresh()
		player.BlackHole.Bosses = player.BlackHole.Bosses[:0]
		return "", nil
	}).Register("@watch-blackhole-aid", func(player *structs.Player, params ...string) (string, error) {
		num := cast.ToInt(params[0])
		blackHole := player.BlackHole
		lv := player.GetAvgLv()
		aids := make([]int, 0)
		for i := 0; i < num; i++ {
			ids := blackHole.RandomAids(lv)
			aids = append(aids, ids...)
		}
		bytes, err := json.Marshal(&aids)
		return string(bytes), err
	}).Register("@add-equip", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		level := cast.ToInt(params[1])
		if id > 0 {
			_, exists := cfg.EquipContainer.GetBeanById(id)
			if !exists {
				return "找不到装备", nil
			}
			equipItem := player.Equip.Make(id, level)
			player.Equip.Push(equipItem)
			return "添加成功", nil
		}
		//equips := make([]*structs.EquipItem, 0)
		//lo.ForEach(cfg.EquipLevelContainer.GetData(), func(level *cfg.EquipLevel, i int) {
		//	if lv != -1 && level.GetLevel() != lv {
		//		return
		//	}
		//	equipItem := structs.NewEquip(level.GetEquipId(), level.GetLevel())
		//	equips = append(equips, equipItem)
		//})
		//player.Equip.Push(equips)
		return "添加成功", nil
	}).Register("@jackpot", func(player *structs.Player, params ...string) (string, error) {
		player.CalcAvgStarLv()
		// cnt := cast.ToInt(params[0])
		// results := player.TestJackpot(cnt)

		// idMap := make(map[int]int)
		// qMap := make(map[int]int)
		// for _, data := range results {
		// 	id := cast.ToInt(data.Id)
		// 	idMap[id]++
		// }
		// log.Debug("idMap: %v", idMap)
		// for id, val := range idMap {
		// 	characData, _ := cfg.CharacterContainer.GetBeanById(id)
		// 	qMap[characData.Quality] += val
		// }
		// log.Debug("qMap: %v", qMap)

		// bytes, err := json.Marshal(idMap)
		// j := ""
		// if err == nil {
		// 	j = string(bytes)
		// }
		return "", nil
	}).Register("@refresh-wanted", func(player *structs.Player, params ...string) (string, error) {
		index := cast.ToInt(params[0])
		Wanted := player.Wanted
		if index == -1 {
			Wanted.Refresh()
		} else {
			Wanted.ManualRefresh(index)
		}

		return "", nil
	}).Register("@cd-wanted", func(player *structs.Player, params ...string) (string, error) {
		index := cast.ToInt(params[0])
		Wanted := player.Wanted
		if index != -1 {
			wanted := Wanted.Get(index)
			wanted.EndTime = player.GetNowTime()
			return "", nil
		}
		for _, wanted := range Wanted.Wanteds {
			wanted.EndTime = player.GetNowTime()
		}
		return "", nil
	}).Register("@watch-wanted", func(player *structs.Player, params ...string) (string, error) {
		Wanted := player.Wanted
		num := cast.ToInt(params[0])
		if len(params) >= 1 {
			point := cast.ToInt(params[1])
			if point >= 0 {
				Wanted.Point = point
			}
		}
		datas := []*structs.Wanted{}
		for i := 0; i < num; i++ {
			Wanted.Refresh()
			datas = append(datas, Wanted.Wanteds...)
		}
		bytes, err := json.Marshal(&datas)
		return string(bytes), err
	}).Register("@refresh-store", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		store := player.Store.Get(id)
		store.Refresh()
		return "", nil
	}).Register("@watch-store", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		cnt := cast.ToInt(params[1])
		store := player.Store.Get(id)
		data := make([]*pb.StoreInfo, 0)
		for i := 0; i < cnt; i++ {
			store.Refresh()
			data = append(data, store.ToPb())
		}
		bytes, err := json.Marshal(&data)
		return string(bytes), err
	}).Register("@all-carriage", func(player *structs.Player, params ...string) (string, error) {
		datas := cfg.TrainContainer.GetData()
		for _, data := range datas {
			carriage := player.UnlockCarriage(data.Id)
			carriage.SetBuiltDone()
		}
		return "", nil
	}).Register("@watch-instance", func(player *structs.Player, params ...string) (string, error) {
		return "", nil
	}).Register("@up-tool", func(player *structs.Player, params ...string) (string, error) {
		lv := cast.ToInt(params[0])
		typ := -1
		if lv <= 0 {
			return "等级不能小于0", nil
		}
		if player.Tool == nil {
			return "解锁工具模块后再使用！", nil
		}
		if len(params) >= 2 {
			typ = cast.ToInt(params[1])
		}
		if typ > -1 {
			tool := player.GetToolByType(typ)
			if tool == nil {
				tool = structs.NewTool(typ, lv)
				player.Tool.Tools[typ] = tool
			}
			tool.Lv = lv
		} else {
			for _, tool := range player.Tool.Tools {
				tool.Lv = lv
			}
		}
		return "", nil
	}).Register("@refresh-transport", func(player *structs.Player, params ...string) (string, error) {
		lv := cast.ToInt(params[0])
		if lv > 0 {
			player.Transport.MonsterIdx = lv
			player.Transport.Exp = lv * cfg.Misc_CContainer.GetObj().Transport.RareExp
		}
		player.Transport.Refresh()
		return "", nil
	}).Register("@add-all-dosing", func(player *structs.Player, params ...string) (string, error) {
		// 添加所有食材
		cnt := cast.ToInt(params[0])
		cnt = ut.Max(1, cnt)
		for _, item := range cfg.ItemContainer.GetData() {
			if item.Type == condition.DOSING {
				player.ChangeItem(item.Id, cnt)
			}
		}
		return "", nil
	}).Register("@add-all-seed", func(player *structs.Player, params ...string) (string, error) {
		// 添加所有种子
		cnt := cast.ToInt(params[0])
		cnt = ut.Max(1, cnt)

		for _, seed := range cfg.FieldSeedContainer.GetData() {
			player.ChangeSeed(seed.Id, cnt)
		}
		return "", nil
	}).Register("@instance-rest", func(player *structs.Player, params ...string) (string, error) {
		mod := player.Instance
		mod.Level = 0
		return "", nil
	}).Register("@instance-skip", func(player *structs.Player, params ...string) (string, error) {
		ch := 1
		if len(params) > 0 {
			ch = cast.ToInt(params[0])
		}
		mod := player.Instance
		for i := 0; i < ch; i++ {
			if mod.GetNextCfg() != nil {
				mod.Level += 1
				return "", nil
			}
		}
		return "没有下一关了！！", nil
	}).Register("@ore-reset", func(player *structs.Player, params ...string) (string, error) {
		level := cast.ToInt(params[0])
		mod := player.Ore
		data := mod.GetLevelData(level)
		if data == nil {
			data = mod.UnlockLevelData(level)
		}
		data.Depth = 0
		data.Data = nil
		data.Records = make(map[int]*structs.OreLevelRecord)
		data.GenNextPageType = 0
		data.LastGenNextCeil = 2
		data.IsSpecialArea = false
		data.Generate(true)
		return "", nil
	}).Register("@refresh-daily", func(player *structs.Player, params ...string) (string, error) {
		player.DailyTask.Refresh()
		return "", nil
	}).Register("@refresh-arrest", func(player *structs.Player, params ...string) (string, error) {
		player.ArrestModule.Refresh()
		return "", nil
	}).Register("@transport-set-done", func(player *structs.Player, params ...string) (string, error) {
		index := cast.ToInt(params[0])
		mod := player.Transport
		mod.List[index].State = pb.TransportDataState_Done
		return "", nil
	}).Register("@reach-planet", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		planet := player.PlanetData.GetPlanet(id)
		if planet == nil {
			planet = player.PlanetData.UnlockPlanet(id)
			if planet == nil {
				return "", nil
			}
			planet.MapId = 1
			planet.CurNodeId = 1
		}
		player.PlanetData.ReachPlanet(id)
		return "", nil
	}).Register("@reset-pvp-ticket", func(player *structs.Player, params ...string) (string, error) {
		pvp := player.Pvp
		if pvp != nil {
			pvp.ResetTicket(pb.PvpType_NORMAL)
			pvp.ResetTicket(pb.PvpType_HIGH)
			pvp.ResetTicket(pb.PvpType_PEAK)
		}
		return "", nil
	}).Register("@set-done-explore", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		exp := player.Explore
		team := exp.GetTeamByPlanetId(id)
		team.EndTime = player.GetNowTime()
		return "", nil
	}).Register("@reset-profile-branch", func(player *structs.Player, params ...string) (string, error) {
		num := cast.ToInt(params[0])
		profileBranch := player.ProfileBranch
		profileBranch.Levels = make([]*structs.ProfileBranchLevel, 0)
		for i := 0; i < num; i++ {
			profileBranch.Levels = append(profileBranch.Levels, profileBranch.GenLevel(i+1))
		}
		if num == 1 {
			profileBranch.Levels = append(profileBranch.Levels, profileBranch.GenLevel(2))
		}
		profileBranch.Id = num
		profileBranch.ResetEnergy()
		return "", nil
	}).Register("@change-profile-branch-energy", func(player *structs.Player, params ...string) (string, error) {
		profileBranch := player.ProfileBranch
		energy := cast.ToInt(params[0])
		profileBranch.ChangeEnergy(energy)
		return "", nil
	}).Register("@set-daily-task-done", func(player *structs.Player, params ...string) (string, error) {
		ary := player.DailyTask.Tasks
		for _, task := range ary {
			if task.GetJson().Type == daily_task_type.COLLECT {
				player.GrantRewards(task.Target, ta.ResChangeSceneTypeNoReport)
			} else if task.GetJson().Type == daily_task_type.EQUIP {
				target := task.Target[0]
				player.Equip.Buy(cast.ToInt(target.Id), 1)
				// cond := equip.ToCondition()
				// player.GrantReward(cond, ta.ResChangeSceneTypeNoReport)
			} else if task.GetJson().Type == daily_task_type.BATTLE {
				task.BattleInfo = nil
			} else if task.GetJson().Type == daily_task_type.DIALOG {
				task.AddProgress(task.Target)
				task.Target = nil
			}
		}
		return "", nil
	}).Register("@refresh-collect-map", func(player *structs.Player, params ...string) (string, error) {
		player.Collect.RefreshMap()
		return "", nil
	}).Register("@done-item-time", func(player *structs.Player, params ...string) (string, error) {
		id := cast.ToInt(params[0])
		items := lo.Filter(player.Bag, func(item *structs.Item, _ int) bool {
			return item.Id == id || id == -1
		})
		for _, item := range items {
			item.EndTime = 0
		}
		return "", nil
	}).Register("@refresh-train-daily-task", func(player *structs.Player, params ...string) (string, error) {
		player.TrainDailyTask.List = make([]*structs.TrainDailyTaskItem, 0)
		player.TrainDailyTask.Refresh()
		return "", nil
	}).Register("@set-train-daily-task-done", func(player *structs.Player, params ...string) (string, error) {
		index := cast.ToInt(params[0])
		list := player.TrainDailyTask.List
		list = lo.Filter(list, func(item *structs.TrainDailyTaskItem, i int) bool {
			if item.State != pb.CommonState_InProcess {
				return false
			}
			if index == -1 {
				return true
			}
			return i == index
		})

		for _, item := range list {
			item.EndTime = player.GetNowTime()
			item.State = pb.CommonState_DoneWithoutReward
		}
		return "", nil
	}).Register("@refresh-train-activity", func(player *structs.Player, params ...string) (string, error) {
		day := 0
		if len(params) > 0 {
			day = cast.ToInt(params[0])
		}
		player.TrainActivity.Refresh()
		if day > 0 {
			lo.ForEach(player.TrainActivity.List, func(item *structs.TrainActivityItem, _ int) { item.CostDay = day })
		}
		return "", nil
	})
}
