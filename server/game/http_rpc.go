package game

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"train/base/data/condition"
	"train/base/enum/logout_reason"
	"train/base/manager"
	"train/base/structs"
	"train/db"
	"train/game/cmd"
	"train/http/h"
	"train/http/types"
	ut "train/utils"

	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (this *Game) InitHttpRpc() {
	this.middleware.Wrap("/kickPlayer", this.kickPlayer)
	this.middleware.Wrap("/deletePlayer", this.deletePlayer)
	this.middleware.Wrap("/getPlayer", this.getPlayer)
	this.middleware.Wrap("/changeProp", this.changeProp)
	this.middleware.Wrap("/execGmCmd", this.execGmCmd)
	this.middleware.Wrap("/httpSendMail", this.httpSendMail)
	this.middleware.Wrap("/httpListMail", this.httpListMail)
	this.middleware.Wrap("/httpRecordPlayer", this.httpRecordPlayer)
	this.middleware.Wrap("/httpDebugCopy", this.httpDebugCopy)
	this.middleware.Wrap("/kickAllPlayer", this.kickAllPlayer)
}

func (this *Game) httpGetPlayer(id string) (plr *structs.Player, online bool) {
	plr, exists := manager.GlobalGetPlayerManager().TryGetPlayerByUid(id)
	if !exists || !plr.IsOnline() {
		plr, _ = structs.TryGetPlayerFromDb(id, false)
	}
	if plr != nil {
		online = plr.IsOnline()
	}
	return
}

func (this *Game) kickPlayer(uid string) (ret map[string]interface{}, err string) {
	mgr := manager.GlobalGetPlayerManager()
	lock := mgr.Lock(uid)
	defer ut.Unlock(lock)
	plr, exists := mgr.TryGetPlayerByUid(uid)
	if !exists {
		return h.ResponseErrorNoDataWithDesc("该玩家不存在.")
	}
	manager.GlobalGetPlayerManager().Clear(plr, logout_reason.DEFAULT)
	return h.ResponseSuccessNoDataWithDesc("操作成功!")
}

func (this *Game) deletePlayer(uid string) (ret map[string]interface{}, err string) {
	plr, exists := manager.GlobalGetPlayerManager().TryGetPlayerByUid(uid)
	if exists {
		// 先使其离线
		manager.GlobalGetPlayerManager().Clear(plr, logout_reason.DEFAULT)
	}
	if plr == nil {
		plr, _ = structs.TryGetPlayerFromDb(uid, false)
	}
	if plr == nil {
		return h.ResponseErrorNoDataWithDesc("玩家不存在.")
	}
	recordDeletePlayer := types.RecordPlayer(plr, 1, "")
	_, e := db.GetCollection(db.DELETE_PLAYER).InsertOne(context.TODO(), recordDeletePlayer)
	if e != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("备份数据时出错,停止删除: %v", e.Error()))
	}
	db.GetCollection(db.PLAYER).DeleteOne(context.TODO(), &bson.M{
		"id": plr.Id,
	})
	return h.ResponseSuccessNoDataWithDesc("删除完成!")
}

func (this *Game) getPlayer(typ int, uid string, size, page int) (ret map[string]interface{}, err string) {
	size = ut.Max(size, 10)
	page = ut.Max(page, 1)
	limit := int64(size)
	skip := int64(size * (page - 1))
	online := lo.Empty[[]map[string]interface{}]()
	offline := lo.Empty[[]map[string]interface{}]()

	fillBaseData := func(plr *structs.Player) map[string]interface{} {
		out := map[string]interface{}{}
		if plr == nil {
			return out
		}
		hex, _ := primitive.ObjectIDFromHex(plr.Id)
		cursor := db.GetCollection(db.USER).FindOne(context.TODO(), bson.M{"_id": hex}, &options.FindOneOptions{
			Projection: bson.M{
				"nickName":    1,
				"avatarUrl":   1,
				"wxAppOpenid": 1,
				"openid":      1,
				"userType":    1,
			},
		})
		line := &structs.User{}
		_ = cursor.Decode(line)
		if ut.IsEmpty(plr.NickName) {
			out["NickName"] = line.NickName
		} else {
			out["NickName"] = plr.NickName
		}
		out["AvatarUrl"] = line.AvatarUrl
		out["WxAppOpenid"] = line.WxAppOpenid
		out["Openid"] = line.Openid
		out["UserType"] = line.UserType
		out["StarDust"] = plr.StarDust
		out["Heart"] = plr.Heart
		out["Diamond"] = plr.Diamond
		out["CreateTime"] = plr.CreateTime
		out["LastLoginTime"] = plr.LastLoginTime
		out["WorldTime"] = plr.WorldTime
		out["WorldTime"] = plr.WorldTime
		out["Id"] = plr.Id
		out["_id"] = plr.Uid.Get()
		return out
	}

	// 在线人数
	onlineCnt := manager.GlobalGetPlayerManager().GetAll().Count()
	// 离线数量
	offlineCnt := 0
	onlineGet := func() {
		all := manager.GlobalGetPlayerManager().GetAll()
		var index int64 = 0
		for _, player := range all.Items() {
			index++
			if index <= skip {
				continue
			}
			if limit <= 0 {
				break
			}
			online = append(online, fillBaseData(player))
		}
	}
	// 获取离线的用户数量
	offlineCntGet := func() {
		Cnt, _ := db.GetCollection(db.PLAYER).CountDocuments(context.TODO(), &bson.M{})
		// 离线数量
		offlineCnt = cast.ToInt(Cnt) - onlineCnt
	}

	getFromDb := func() {
		filter := bson.M{}
		cursor, e := db.GetCollection(db.PLAYER).Find(context.TODO(), &filter, &options.FindOptions{
			Projection: bson.M{
				"id":            1,
				"nickName":      1,
				"createTime":    1,
				"lastLoginTime": 1,
				"diamond":       1,
				"heart":         1,
				"starDust":      1,
				"worldTime":     1,
			},
			Limit: &limit,
			Skip:  &skip,
		})
		if e == nil {
			for cursor.Next(context.Background()) {
				var temp bson.M
				var dp *structs.Player
				err := cursor.Decode(&temp)
				if err == nil {
					_ = cursor.Decode(&dp)
					oid := temp["_id"].(primitive.ObjectID)
					dp.Uid, _ = ut.NewMongoIdFrom(oid.Hex())
					if _, exists := manager.GlobalGetPlayerManager().GetAll().Get(dp.Id); !exists {
						offline = append(offline, fillBaseData(dp))
					}
				}
			}
		}
	}
	// 查询所有在线的
	if typ == 2 {
		onlineGet()
		offlineCntGet()
		return h.ResponseSuccessWithDataNoDesc(map[string]interface{}{
			"online":     online,
			"offline":    offline,
			"onlineCnt":  onlineCnt,
			"offlineCnt": offlineCnt,
		})
	}

	// 无条件查询所有的
	if typ == 1 {
		getFromDb()
		offlineCntGet()
	}
	// 查询个人
	if typ == 3 {
		// 填充离线人数
		offlineCntGet()
		offline = lo.Empty[[]map[string]interface{}]()
		plr, exists := manager.GlobalGetPlayerManager().TryGetPlayerByUid(uid)
		if !exists {
			plr, _ = structs.TryGetPlayerFromDb(uid, false)
			if plr != nil {
				offline = append(offline, fillBaseData(plr))
			}
		} else {
			online = append(online, fillBaseData(plr))
		}
	}

	return h.ResponseSuccessWithDataNoDesc(map[string]interface{}{
		"online":     online,
		"offline":    offline,
		"onlineCnt":  onlineCnt,
		"offlineCnt": offlineCnt,
	})
}

func (this *Game) changeProp(uid string, num int, typ int) (ret map[string]interface{}, err string) {
	command := ""
	switch typ {
	case condition.DIAMOND:
		fallthrough
	case condition.STAR_DUST:
		fallthrough
	case condition.HEART:
		command = fmt.Sprintf("@add-currency %d,%d", typ, num)
	}
	if ut.IsEmpty(command) {
		return h.ResponseErrorNoDataWithDesc("数据错误!")
	}
	return this.execGmCmd(uid, command)
}

func (this *Game) execGmCmd(uid, command string) (ret map[string]interface{}, err string) {
	mgr := manager.GlobalGetPlayerManager()
	lock := mgr.Lock(uid)
	defer ut.Unlock(lock)
	plr, exists := mgr.TryGetPlayerByUid(uid)
	if !exists || !plr.IsOnline() {
		return h.ResponseErrorNoDataWithDesc("玩家不在线,无法操作!")
	}
	_, e := cmd.GlobalGetGmCmd().Execute(command, plr, true)
	if e != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("Gm命令执行失败:%s", e.Error()))
	}
	return h.ResponseSuccessNoDataWithDesc("修改成功")
}

func (this *Game) httpListMail(id string) (ret map[string]interface{}, err string) {
	plr, _ := this.httpGetPlayer(id)
	if plr == nil {
		return h.ResponseErrorNoDataWithDesc("未查询到该用户")
	}
	plr.InitMailModule()
	return h.ResponseSuccessWithDataNoDesc(plr.Mail.List)
}

func (this *Game) httpSendMail(id, title, content, rewards string) (ret map[string]interface{}, err string) {
	plr, exists := manager.GlobalGetPlayerManager().TryGetPlayerByUid(id)
	if !exists || !plr.IsOnline() {
		plr, _ = structs.TryGetPlayerFromDb(id, false)
		if plr == nil {
			// 玩家不存在
			return h.ResponseErrorNoDataWithDesc("玩家不存在,操作失败!")
		}
	}
	c := lo.Empty[[]*structs.Condition]()
	if !ut.IsEmpty(rewards) {
		// 反序列化
		m := lo.Empty[[]map[string]interface{}]()
		e := json.Unmarshal([]byte(rewards), &m)
		if e != nil {
			return h.ResponseErrorNoDataWithDesc("奖励数据序列化失败!")
		}
		c = structs.ToConditions(m)
	}
	mail := plr.CreateMail(title, content, c)
	if !plr.IsOnline() {
		plr.Mail.Save()
	}
	return h.ResponseSuccessWithDataWithDesc(mail, "发送成功!")
}

func (this *Game) httpRecordPlayer(uid, name string) (ret map[string]interface{}, err string) {
	plr, _ := structs.TryGetPlayerFromDb(uid, false)
	if plr == nil {
		// 玩家不存在
		return h.ResponseErrorNoDataWithDesc("玩家不存在,操作失败!")
	}

	r := types.RecordPlayer(plr, 2, name)
	iRes, e := db.GetCollection(db.DELETE_PLAYER).InsertOne(context.Background(), r)
	if e != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("备份数据时出错:%s", e.Error()))
	}
	return h.ResponseSuccessWithDataWithDesc(map[string]interface{}{
		"_id": iRes.InsertedID,
	}, "备份完成!")

}

func (this *Game) httpDebugCopy(recordId, playerId string, lockTime bool) (ret map[string]interface{}, e string) {
	hex, err := primitive.ObjectIDFromHex(recordId)
	if err != nil {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("id错误:%v", err.Error()))
	}
	singleResult := db.DELETE_PLAYER.GetCollection().FindOne(context.TODO(), bson.M{"_id": hex})
	line := &types.DeletePlayer{}
	err = singleResult.Decode(line)
	if err != nil || ut.IsEmpty(line.Data) {
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("查找记录失败:%v", err.Error()))
	}

	// 处理冻结时间的case
	if lockTime {
		diff := ut.Now() - line.Time
		pattern := `(\b\d{13}\b)`
		regExp, err := regexp.Compile(pattern)
		if err != nil {
			return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("正则表达式编译错误:%v", err.Error()))
		}
		// 替换所有匹配项
		line.Data = regExp.ReplaceAllStringFunc(line.Data, func(str string) string {
			return cast.ToString(cast.ToInt(str) + diff)
		})
	}

	// 尝试反序列化一次
	var tempPlr *structs.Player
	if strings.Contains(line.Data, "NickName") || (strings.Contains(line.Data, "achievement") && strings.Contains(line.Data, "Completes")) {
		err = json.Unmarshal([]byte(line.Data), &tempPlr)
	} else {
		err = ut.JsonToBsonUnmarshal([]byte(line.Data), &tempPlr)
	}
	if err != nil {
		return h.ResponseErrorNoDataWithDesc("反序列化plr数据失败,无法完成操作")
	}
	// 删除旧数据
	ret, _ = this.deletePlayer(playerId)
	if ret["code"] != 0 {
		return h.ResponseErrorNoDataWithDesc("目标玩家不存在")
	}

	// 迁移数据
	tempPlr.Id = playerId
	_, err = db.PLAYER.GetCollection().InsertOne(context.TODO(), tempPlr)
	if err != nil {
		if !mongo.IsDuplicateKeyError(err) {
			return h.ResponseErrorNoDataWithDesc("id重复错误，逻辑代码有问题")
		}
		return h.ResponseErrorNoDataWithDesc(fmt.Sprintf("错误: %v", err.Error()))
	}
	return h.ResponseSuccessNoDataWithDesc("操作完成!")
}

func (this *Game) kickAllPlayer() (ret map[string]interface{}, err string) {
	mgr := manager.GlobalGetPlayerManager()
	mgr.ClearAll(logout_reason.DEFAULT)
	return h.ResponseSuccessNoDataWithDesc("操作成功!")
}
