package game

import (
	"train/base/structs"
	"train/common/pb"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitResonanceHD 自动生成，不要在这个方法添加任何内容。
func InitResonanceHD(this *Game) {
	// 设置共鸣乘客
	this.middleware.Wrap("C2S_SetResonanceRoleMessage", this.C2sSetResonanceRoleMessageHandler)
}
func (this *Game) C2sSetResonanceRoleMessageHandler(player *structs.Player, msg *pb.C2S_SetResonanceRoleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_SetResonanceRoleMessage{Code: cast.ToInt32(code)}
	}
	Type := cast.ToInt(msg.GetType())
	id := cast.ToInt(msg.GetId())

	passenger := player.GetPassengerById(id)
	if passenger == nil {
		return send(1)
	}
	if Type == 1 {
		if player.Resonance.IsBeResonated(id) {
			return send(2)
		}
		player.Resonance.Add(id)
	} else {
		if !player.Resonance.IsBeResonated(id) {
			return send(3)
		}
		player.Resonance.Remove(id)
	}

	return send(0)
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
