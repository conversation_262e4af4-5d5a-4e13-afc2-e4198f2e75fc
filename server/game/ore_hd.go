package game

import (
	"train/base/cfg"
	"train/base/data/item_id"
	"train/base/enum/function_type"
	"train/base/enum/time_stone_evt"
	"train/base/enum/time_stone_record_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitOreHD 自动生成，不要在这个方法添加任何内容。
func InitOreHD(this *Game) {
	// 矿场格子操作
	this.middleware.Wrap("C2S_OreActionMessage", this.C2sOreActionMessageHandler)
	// 同步稿子恢复时间
	this.middleware.Wrap("C2S_OreSyncBreakItemTimeMessage", this.C2sOreSyncBreakItemTimeMessageHandler)
	// 矿场难度挑战成功
	this.middleware.Wrap("C2S_OreLevelFightMessage", this.C2sOreLevelFightMessageHandler)
	// 获取矿场指定难度矿洞的数据
	this.middleware.Wrap("C2S_GetOreLevelDataMessage", this.C2sGetOreLevelDataMessageHandler)
	// 解锁矿场
	this.middleware.Wrap("C2S_UnlockOreMessage", this.C2sUnlockOreMessageHandler)
}
func (this *Game) C2sOreActionMessageHandler(player *structs.Player, msg *pb.C2S_OreActionMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间

	x, y := cast.ToInt(msg.GetX()), cast.ToInt(msg.GetY())
	typ := msg.GetType()
	level := int(msg.GetLevel())
	levelData := player.Ore.GetLevelData(level)
	actionCode, addGridData, rewards := levelData.Action(player.Ore, x, y, typ, int(msg.GetExtra()))

	replay := &pb.S2C_OreActionMessage{
		Code: actionCode,
	}

	if addGridData != nil {
		for _, row := range addGridData {
			oreRow := new(pb.OreRowData)
			oreRow.Data = make([]*pb.OreCeilData, 0)
			for _, ceil := range row {
				oreRow.Data = append(oreRow.Data, ceil.ToPb())
			}
			replay.Data = append(replay.Data, oreRow)
		}
	}

	if rewards != nil {
		replay.Rewards = lo.Map(rewards, func(item *structs.Condition, index int) *pb.Condition {
			return item.ToPb()
		})
	}

	return replay
	//@action-code-end
}
func (this *Game) C2sOreSyncBreakItemTimeMessageHandler(player *structs.Player, msg *pb.C2S_OreSyncBreakItemTimeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	item := player.GetItem(item_id.OreBreakItem)
	if item == nil {
		item = &structs.Item{Id: item_id.OreBreakItem, Num: 0}
	}
	mod := player.Ore
	player.CheckAndUpdateDaily()
	player.CheckAndUpdateDailyPm()
	return &pb.S2C_OreSyncBreakItemTimeMessage{
		Item: item.ToItemInfo(),
		Time: int32(mod.NextRecoverTime()),
	}
	//@action-code-end
}
func (this *Game) C2sOreLevelFightMessageHandler(player *structs.Player, msg *pb.C2S_OreLevelFightMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	level := int(msg.GetLevel())
	levelData := player.Ore.GetLevelData(level)
	if levelData != nil {
		// 已经解锁难度？
		return &pb.S2C_OreLevelFightMessage{Code: 1}
	}

	bean, _ := cfg.OreLevelContainer.GetBeanById(level)
	if bean == nil {
		// 不存在的难度配置
		return &pb.S2C_OreLevelFightMessage{Code: 2}
	}
	player.DoTimeStoneRecord(time_stone_record_type.OreBattle, &time_stone_evt.OreBattle{})
	// 发放战斗奖励
	player.GrantRewards(structs.ConfigConditionConvert(bean.Reward...).All(), ta.ResChangeSceneTypeOre)
	player.Ore.UnlockLevelData(level)

	return &pb.S2C_OreLevelFightMessage{}
	//@action-code-end
}
func (this *Game) C2sGetOreLevelDataMessageHandler(player *structs.Player, msg *pb.C2S_GetOreLevelDataMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	level := int(msg.GetLevel())
	levelData := player.Ore.GetLevelData(level)
	return &pb.S2C_GetOreLevelDataMessage{Data: levelData.ToPb()}
	//@action-code-end
}
func (this *Game) C2sUnlockOreMessageHandler(player *structs.Player, msg *pb.C2S_UnlockOreMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.UnlockFunction(function_type.ORE)
	return &pb.S2C_UnlockOreMessage{}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
