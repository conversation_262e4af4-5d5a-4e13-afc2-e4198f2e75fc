package game

import (
	"train/base/cfg"
	"train/base/event"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitToolHD 自动生成，不要在这个方法添加任何内容。
func InitToolHD(this *Game) {
	// 打造工具
	this.middleware.Wrap("C2S_ToolMakeMessage", this.C2sToolMakeMessageHandler)
	// 打造台升级
	this.middleware.Wrap("C2S_FurnaceUpgradeMessage", this.C2sFurnaceUpgradeMessageHandler)
}
func (this *Game) C2sToolMakeMessageHandler(player *structs.Player, msg *pb.C2S_ToolMakeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	code := player.ToolMakeLogic(cast.ToInt(msg.Type))
	return &pb.S2C_ToolMakeRespMessage{
		Code: code,
	}
	//@action-code-end
}
func (this *Game) C2sFurnaceUpgradeMessageHandler(player *structs.Player, msg *pb.C2S_FurnaceUpgradeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	targetLv := player.Tool.Lv + 1
	bean, exists := cfg.ToolTableContainer.GetBeanById(targetLv)
	if !exists {
		return &pb.S2C_FurnaceUpgradeRespMessage{
			Code: 2, // 等级已经最高了？
		}
	}
	bean, _ = cfg.ToolTableContainer.GetBeanById(player.Tool.Lv)
	// 消耗检查
	toConditions := structs.ToConditions(bean.BuyCost)
	failed := player.CheckConditions(toConditions)
	if len(failed) > 0 {
		return &pb.S2C_FurnaceUpgradeRespMessage{
			Code:     1,
			FailList: structs.ToPbConditions(failed),
		}
	}
	player.Tool.Lv += 1
	player.GetEvent().Emit(event.ToolTableUp)
	// 扣除
	player.DeductCosts(toConditions, ta.ResChangeSceneTypeTool)
	return &pb.S2C_FurnaceUpgradeRespMessage{
		Code: 0,
	}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
