package game

import (
	"fmt"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum/time_stone_record_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"train/utils/array"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitEquipHD 自动生成，不要在这个方法添加任何内容。
func InitEquipHD(this *Game) {
	// 穿戴装备
	this.middleware.Wrap("C2S_WearEquipMessage", this.C2sWearEquipMessageHandler)
	// 卸下装备
	this.middleware.Wrap("C2S_UnWearEquipMessage", this.C2sUnWearEquipMessageHandler)
	// 打造装备
	this.middleware.Wrap("C2S_MakeEquipMessage", this.C2sMakeEquipMessageHandler)
	// 购买装备
	this.middleware.Wrap("C2S_BuyEquipMessage", this.C2sBuyEquipMessageHandler)
	// 出售装备
	this.middleware.Wrap("C2S_SellEquipMessage", this.C2sSellEquipMessageHandler)
}
func (this *Game) C2sWearEquipMessageHandler(player *structs.Player, msg *pb.C2S_WearEquipMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	replay := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_WearEquipMessage{Code: code}
	}
	equipItem := player.GetEquipByUid(msg.GetUid())
	if equipItem == nil {
		return replay(1)
	}
	equips := player.GetEquips()
	for _, item := range equips {
		if item.Used && item.Id == equipItem.Id {
			item.Used = false
		}
	}
	equipItem.Used = true
	return replay(0)
	//@action-code-end
}
func (this *Game) C2sUnWearEquipMessageHandler(player *structs.Player, msg *pb.C2S_UnWearEquipMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	equip := player.GetEquipByUid(msg.GetUid())
	if equip == nil {
		return &pb.S2C_UnWearEquipMessage{Code: 1}
	}
	equip.Used = false
	return &pb.S2C_UnWearEquipMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sMakeEquipMessageHandler(player *structs.Player, msg *pb.C2S_MakeEquipMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_MakeEquipMessage{Code: code}
	}
	id := cast.ToInt(msg.GetId())
	tableId := cast.ToInt(msg.GetTableId())

	pro := player.Equip.GetProficiency(id, tableId)
	pLv := cfg.GetProficiencyLv(tableId, pro)
	makeBean, _ := cfg.EquipMakeContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", tableId, pLv))
	if makeBean == nil {
		return send(1)
	}
	lv := makeBean.Level
	lvBean, _ := cfg.EquipLevelContainer.GetBeanById(lv)
	if lvBean == nil {
		return send(2)
	}
	equpBean, _ := cfg.EquipContainer.GetBeanById(id)
	if equpBean == nil {
		return send(3)
	}
	roleBean, _ := cfg.CharacterContainer.GetBeanById(equpBean.RoleId)
	if roleBean == nil {
		return send(4)
	}
	conds := structs.ConfigConditionConvert(makeBean.Cost...).All()
	failed := player.CheckConditions(conds)
	if len(failed) > 0 {
		return send(5)
	}
	player.DoTimeStoneRecord(time_stone_record_type.EquipMake)
	equip := player.Equip.MakeByTable(id, tableId)
	player.DeductCosts(conds, ta.ResChangeSceneTypeEquipMake)
	return &pb.S2C_MakeEquipMessage{Code: 0, Equip: equip.ToPb()}
	//@action-code-end
}
func (this *Game) C2sBuyEquipMessageHandler(player *structs.Player, msg *pb.C2S_BuyEquipMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_BuyEquipMessage{Code: code}
	}
	id := cast.ToInt(msg.GetId())
	level := cast.ToInt(msg.GetLevel())

	lvBean, _ := cfg.EquipLevelContainer.GetBeanById(level)
	if lvBean == nil {
		return send(1)
	}
	equpBean, _ := cfg.EquipContainer.GetBeanById(id)
	if equpBean == nil {
		return send(2)
	}
	roleBean, _ := cfg.CharacterContainer.GetBeanById(equpBean.RoleId)
	if roleBean == nil {
		return send(3)
	}
	costBean := array.Find(cfg.EquipStoreContainer.GetData(), func(item *cfg.EquipStore[int]) bool {
		return item.Level == level && item.EquipIndex == equpBean.Index
	})
	if costBean == nil {
		return send(4)
	}
	conds := structs.ConfigConditionConvert(costBean.Cost...).All()
	failed := player.CheckConditions(conds)
	if len(failed) > 0 {
		return send(4)
	}
	player.DeductCosts(conds, ta.ResChangeSceneTypeEquipBuy)
	equip := player.Equip.Buy(id, level)
	return &pb.S2C_BuyEquipMessage{Code: 0, Equip: equip.ToPb()}
	//@action-code-end
}
func (this *Game) C2sSellEquipMessageHandler(player *structs.Player, msg *pb.C2S_SellEquipMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	money := &structs.Condition{Type: condition.STAR_DUST, Num: 0}
	ary := make([]string, 0)
	for _, uid := range msg.GetUid() {
		_, exists := lo.Find(ary, func(item string) bool { return item == uid })
		if exists {
			return &pb.S2C_SellEquipMessage{Code: 1}
		}
		equip := player.Equip.Get(uid)
		if equip == nil {
			return &pb.S2C_SellEquipMessage{Code: 2}
		}
		money.Num += cfg.GetEquipSellPrice(equip.Level)
		ary = append(ary, uid)
	}

	for _, uid := range ary {
		player.Equip.Pull(uid)
	}
	player.GrantReward(money, ta.ResChangeSceneTypeEquipSell)
	return &pb.S2C_SellEquipMessage{Code: 0}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
