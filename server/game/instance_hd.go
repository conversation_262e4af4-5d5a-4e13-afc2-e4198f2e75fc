package game

import (
	"train/base/enum/function_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitInstanceHD 自动生成，不要在这个方法添加任何内容。
func InitInstanceHD(this *Game) {
	// 发起副本挑战(临时使用)
	this.middleware.Wrap("C2S_InstanceFightMessage", this.C2sInstanceFightMessageHandler)
	// 同步副本数据
	this.middleware.Wrap("C2S_SyncInstanceMessage", this.C2sSyncInstanceMessageHandler)
	// 解锁副本
	this.middleware.Wrap("C2S_UnlockInstanceMessage", this.C2sUnlockInstanceMessageHandler)
	// 完成解密
	this.middleware.Wrap("C2S_CompleteInstancePuzzleMessage", this.C2sCompleteInstancePuzzleMessageHandler)
}
func (this *Game) C2sInstanceFightMessageHandler(player *structs.Player, msg *pb.C2S_InstanceFightMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	level := cast.ToInt(msg.GetLevel())
	mod := player.Instance
	if level-mod.Level != 1 {
		return &pb.S2C_InstanceFightMessage{Code: 1}
	}
	bean := mod.GetNextCfg()
	if bean == nil {
		return &pb.S2C_InstanceFightMessage{Code: 2}
	}
	battleRewards := structs.ToConditions(bean.Reward)
	player.GrantRewards(battleRewards, ta.ResChangeSceneTypeInstance)
	mod.Level = level
	return &pb.S2C_InstanceFightMessage{}
	//@action-code-end
}
func (this *Game) C2sSyncInstanceMessageHandler(player *structs.Player, msg *pb.C2S_SyncInstanceMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return nil
	//@action-code-end
}
func (this *Game) C2sUnlockInstanceMessageHandler(player *structs.Player, msg *pb.C2S_UnlockInstanceMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.UnlockFunction(function_type.INSTANCE)
	return &pb.S2C_UnlockInstanceMessage{}
	//@action-code-end
}
func (this *Game) C2sCompleteInstancePuzzleMessageHandler(player *structs.Player, msg *pb.C2S_CompleteInstancePuzzleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.Instance.CompletePuzzle()
	return &pb.S2C_CompleteInstancePuzzleMessage{}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
