package game

import (
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitTrainActivityHD 自动生成，不要在这个方法添加任何内容。
func InitTrainActivityHD(this *Game) {
	// 获取列车活动数据
	this.middleware.Wrap("C2S_GetTrainActivityMessage", this.C2sGetTrainActivityMessageHandler)
	// 安排列车活动
	this.middleware.Wrap("C2S_ArrangeTrainActivityMessage", this.C2sArrangeTrainActivityMessageHandler)
	// 领取列车活动奖励
	this.middleware.Wrap("C2S_GetTrainActivityRewardMessage", this.C2sGetTrainActivityRewardMessageHandler)
}
func (this *Game) C2sGetTrainActivityMessageHandler(player *structs.Player, msg *pb.C2S_GetTrainActivityMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	act := player.TrainActivity
	act.CheckAndRefresh()
	return &pb.S2C_GetTrainActivityMessage{Data: act.ToPb()}
	//@action-code-end
}
func (this *Game) C2sArrangeTrainActivityMessageHandler(player *structs.Player, msg *pb.C2S_ArrangeTrainActivityMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	idAry := ut.ToInt(msg.GetAry())
	if len(idAry) == 0 {
		return &pb.S2C_ArrangeTrainActivityMessage{Code: 1}
	}
	act := player.TrainActivity
	// 检查是否今天已经安排过
	if act.IsTodayArranged() {
		return &pb.S2C_ArrangeTrainActivityMessage{Code: 2}
	}

	actAry := make([]*structs.TrainActivityItem, 0)
	sumDayCost := 0
	for _, v := range idAry {
		item, _ := lo.Find(act.List, func(item *structs.TrainActivityItem) bool { return item.Id == v })
		if item == nil {
			return &pb.S2C_ArrangeTrainActivityMessage{Code: 3}
		}
		if !item.CanArrange() {
			return &pb.S2C_ArrangeTrainActivityMessage{Code: 4}
		}
		actAry = append(actAry, item)
		sumDayCost += item.CostDay
	}
	// 检查时间
	if sumDayCost > 30 {
		return &pb.S2C_ArrangeTrainActivityMessage{Code: 5}
	}
	usedTime := int64(player.GetWorldTime())
	usedTime -= usedTime % ut.TIME_DAY
	act.ArrangeWorldTime = usedTime
	for _, v := range actAry {
		usedTime += cast.ToInt64(v.CostDay * ut.TIME_DAY)
		v.EndTime = usedTime
		v.State = pb.CommonState_InProcess
	}
	// 更新列表
	act.List = actAry
	return &pb.S2C_ArrangeTrainActivityMessage{}
	//@action-code-end
}
func (this *Game) C2sGetTrainActivityRewardMessageHandler(player *structs.Player, msg *pb.C2S_GetTrainActivityRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	index := cast.ToInt(msg.GetIndex())
	isUnget := msg.GetIsUnget()
	if !isUnget {
		player.TrainActivity.UpdateState()
		if ut.IsIndexOutOfBounds(player.TrainActivity.List, index) {
			return &pb.S2C_GetTrainActivityRewardMessage{Code: 1}
		}
		item := player.TrainActivity.List[index]
		// 不可领奖
		if item.State != pb.CommonState_DoneWithoutReward {
			return &pb.S2C_GetTrainActivityRewardMessage{Code: 2}
		}
		player.GrantRewards(item.Rewards, ta.ResChangeSceneTypeNoReport)
		item.State = pb.CommonState_FinishWithReward
		return &pb.S2C_GetTrainActivityRewardMessage{}
	}

	// 未领取奖励列表
	ary := player.TrainActivity.UngetRewards
	if len(ary) == 0 {
		return &pb.S2C_GetTrainActivityRewardMessage{Code: 1}
	}
	trainId := int(msg.GetTrainId())
	item, _ := lo.Find(ary, func(item *structs.TrainActivityUngetReward) bool { return item.TrainId == trainId })
	if item == nil || len(item.Rewards) == 0 {
		return &pb.S2C_GetTrainActivityRewardMessage{Code: 1}
	}
	if ut.IsIndexOutOfBounds(item.Rewards, index) {
		return &pb.S2C_GetTrainActivityRewardMessage{Code: 1}
	}
	rewards := item.Rewards[index]
	player.GrantRewards(rewards, ta.ResChangeSceneTypeNoReport)
	item.Rewards = append(item.Rewards[:index], item.Rewards[index+1:]...)
	if len(item.Rewards) == 0 {
		player.TrainActivity.UngetRewards = lo.Filter(player.TrainActivity.UngetRewards, func(item *structs.TrainActivityUngetReward, _ int) bool { return item.TrainId != trainId })
	}
	return &pb.S2C_GetTrainActivityRewardMessage{}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
