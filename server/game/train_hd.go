package game

import (
	"fmt"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum/build_attr"
	"train/base/event"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitTrainHD 自动生成，不要在这个方法添加任何内容。
func InitTrainHD(this *Game) {
	// 解锁车厢
	this.middleware.Wrap("C2S_BuyCarriageMessage", this.C2sBuyCarriageMessageHandler)
	// 获取车厢建造信息
	this.middleware.Wrap("C2S_GetCarriageBuildInfoMessage", this.C2sGetCarriageBuildInfoMessageHandler)
	// 车厢门打开
	this.middleware.Wrap("C2S_OpenCarriageDoorMessage", this.C2sOpenCarriageDoorMessageHandler)
	// 升级设施等级
	this.middleware.Wrap("C2S_BuildLevelUpMessage", this.C2sBuildLevelUpMessageHandler)
	// 改变设施皮肤
	this.middleware.Wrap("C2S_ChangeBuildSkinMessage", this.C2sChangeBuildSkinMessageHandler)
	// 提升车厢主题等级
	this.middleware.Wrap("C2S_CarriageThemeLvUpMessage", this.C2sCarriageThemeLvUpMessageHandler)
	// 解锁货物
	this.middleware.Wrap("C2S_UnlockGoodsMessage", this.C2sUnlockGoodsMessageHandler)
	// 升级货物
	this.middleware.Wrap("C2S_LevelUpGoodsMessage", this.C2sLevelUpGoodsMessageHandler)
}
func (this *Game) C2sBuyCarriageMessageHandler(player *structs.Player, msg *pb.C2S_BuyCarriageMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := cast.ToInt(msg.Id)
	json, exists := cfg.TrainContainer.GetBeanById(id)
	if !exists {
		// 找不到配置
		return &pb.S2C_BuyCarriageResultMessage{Code: 1}
	}
	carriage := player.GetCarriageById(json.GetUnique())
	if carriage != nil {
		return &pb.S2C_BuyCarriageResultMessage{Code: 2}
	}
	// 处理解锁车厢的消耗...
	conds := structs.ToConditions(json.BuyCost)
	failList := player.CheckConditions(conds)
	if len(failList) > 0 {
		f := make([]*pb.Condition, 0)
		for _, condition := range failList {
			f = append(f, condition.ToPb())
		}
		return &pb.S2C_BuyCarriageResultMessage{Code: 3, FailList: f}
	}
	carriage = player.UnlockCarriage(json.Id)
	player.DeductCosts(conds, ta.ResChangeSceneTypeTrain)
	return &pb.S2C_BuyCarriageResultMessage{
		Code:     0,
		Carriage: carriage.ToPb(),
	}
	//@action-code-end
}
func (this *Game) C2sGetCarriageBuildInfoMessageHandler(player *structs.Player, msg *pb.C2S_GetCarriageBuildInfoMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.UpdateSpeedUp()
	id := cast.ToInt(msg.Id)
	carriage := player.GetCarriageById(id)
	if carriage == nil {
		return &pb.S2C_GetCarriageBuildInfoResMessage{Code: 1}
	}
	if carriage.IsBuilt() {
		player.GetEvent().Emit(event.BuildEndCarriage, carriage)
	}
	return &pb.S2C_GetCarriageBuildInfoResMessage{Code: 0, BuildTime: int32(carriage.GetBuildSurplusTime()), OpenDoor: carriage.OpenDoor}
	//@action-code-end
}
func (this *Game) C2sOpenCarriageDoorMessageHandler(player *structs.Player, msg *pb.C2S_OpenCarriageDoorMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := cast.ToInt(msg.Id)
	carraige := player.GetCarriageById(id)
	if carraige == nil {
		return &pb.S2C_OpenCarriageDoorResMessage{Code: 1}
	}
	if !carraige.IsBuilt() {
		return &pb.S2C_OpenCarriageDoorResMessage{Code: 2}
	}
	carraige.OpenDoor = true
	return &pb.S2C_OpenCarriageDoorResMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sBuildLevelUpMessageHandler(player *structs.Player, msg *pb.C2S_BuildLevelUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_BuildLevelUpMessage{Code: code}
	}
	carriageId := cast.ToInt(msg.GetCarriageId())
	order := cast.ToInt(msg.GetOrder())
	carriage := player.GetCarriageById(carriageId)
	if carriage == nil {
		return send(1)
	}
	max := carriage.GetThemeUnlockLevel(carriage.ThemeLv)
	build := carriage.GetBuildByOrder(order)
	if build != nil && build.IsMaxLv(max) {
		return send(2)
	}
	nextLv := 1
	if build != nil {
		nextLv = build.Lv + 1
	}
	bean, _ := cfg.TrainItemLevelContainer.GetBeanByUnique(fmt.Sprintf("%d-%d-%d", carriageId, order, nextLv))
	if bean == nil {
		return send(3)
	}
	costs := structs.ToConditions(bean.BuyCost)
	failList := player.CheckConditions(costs)
	if len(failList) > 0 {
		log.Error("[%s] C2sBuildLevelUpMessageHandler cost fail %+v", player.GetUid(), failList)
		return send(4)
	}
	player.DeductCosts(costs, ta.ResChangeSceneTypeTrainBuildLevelUp)
	carriage.BuildLvUp(order)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sChangeBuildSkinMessageHandler(player *structs.Player, msg *pb.C2S_ChangeBuildSkinMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_ChangeBuildSkinMessage{Code: code}
	}
	carriageId := cast.ToInt(msg.GetCarriageId())
	order := cast.ToInt(msg.GetOrder())
	skin := cast.ToInt(msg.GetSkin())
	carriage := player.GetCarriageById(carriageId)
	if carriage == nil {
		return send(1)
	}
	build := carriage.GetBuildByOrder(order)
	if build == nil {
		return send(2)
	}
	skinLv := build.GetAttr(build_attr.SKIN)
	if skinLv < skin { //没解锁
		return send(3)
	}
	build.ChangeSkin(skin)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sCarriageThemeLvUpMessageHandler(player *structs.Player, msg *pb.C2S_CarriageThemeLvUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_CarriageThemeLvUpMessage{Code: code}
	}
	carriageId := cast.ToInt(msg.GetCarriageId())
	carriage := player.GetCarriageById(carriageId)
	if carriage == nil {
		return send(1)
	}
	nextLv := carriage.ThemeLv + 1
	bean, _ := cfg.TrainThemeContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", carriageId, nextLv))
	if bean == nil {
		return send(2)
	}
	costs := structs.ToConditions(bean.BuyCost)
	failList := player.CheckConditions(costs)
	if len(failList) > 0 {
		log.Error("[%s] C2sCarriageThemeLvUpMessageHandler cost fail %+v", player.GetUid(), failList)
		return send(3)
	}
	player.DeductCosts(costs, ta.ResChangeSceneTypeTrain)
	carriage.ThemeLvUp()
	return send(0)
	//@action-code-end
}
func (this *Game) C2sUnlockGoodsMessageHandler(player *structs.Player, msg *pb.C2S_UnlockGoodsMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	replay := &pb.S2C_UnlockGoodsMessage{}
	id := msg.GetId()
	extra := msg.GetExtra()
	lv := 1
	// 检查配置
	bean, exists := cfg.TrainGoodsLevelContainer.GetBeanByUnique(fmt.Sprintf("%s-%d", id, lv))
	if !exists {
		replay.Code = 1
		return replay
	}
	goodsBean, _ := cfg.TrainGoodsContainer.GetBeanByUnique(bean.GoodsId)
	// 检查车厢
	carriage := player.GetCarriageById(goodsBean.TrainId)
	if carriage == nil {
		replay.Code = 2
		return replay
	}
	// 检查解锁情况
	if !carriage.IsGoodsCanUnlock(id, lv) {
		replay.Code = 3
		return replay
	}
	uploadCondition := structs.ConditionsFromPb(extra)
	failed := player.CheckConditions(uploadCondition)
	if len(failed) > 0 {
		replay.Code = 4
		return replay
	}

	// 成功才扣除消耗
	// player.DeductCosts(uploadCondition)
	cost := make([]*structs.Condition, 0)
	cost = append(cost, uploadCondition...)
	if bean.BuyCost != nil {
		for _, unlock := range bean.BuyCost {
			obj, index, _ := lo.FindIndexOf(uploadCondition, func(c *structs.Condition) bool { return c.Type == condition.PROP && cast.ToInt(c.GetId()) == unlock.Id })
			if index == -1 {
				return &pb.S2C_UnlockGoodsMessage{Code: 5} // 少了必要的食材
			}
			if obj.Num != unlock.Num {
				return &pb.S2C_UnlockGoodsMessage{Code: 6} // 食材数量不对
			}
			// 移除obj
			uploadCondition = append(uploadCondition[:index], uploadCondition[index+1:]...)
		}
	}

	if len(uploadCondition) > 0 {
		return &pb.S2C_UnlockGoodsMessage{Code: 5} // 食材类型多了，不匹配
	}

	player.DeductCosts(cost, ta.ResChangeSceneTypeTrainFood)
	// 解锁
	player.UnlockGoods(carriage, id, lv)
	return replay
	//@action-code-end
}
func (this *Game) C2sLevelUpGoodsMessageHandler(player *structs.Player, msg *pb.C2S_LevelUpGoodsMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := msg.GetId()
	lv := cast.ToInt(msg.GetLevel())
	goodsBean, _ := cfg.TrainGoodsContainer.GetBeanByUnique(id)
	carriage := player.GetCarriageById(goodsBean.TrainId)
	levelBean, exists := cfg.TrainGoodsLevelContainer.GetBeanByUnique(fmt.Sprintf("%s-%d", id, lv))
	if !exists {
		return &pb.S2C_LevelUpGoodsMessage{Code: 1} // 没有等级配置？
	}

	buyConditions := structs.ToConditions(levelBean.GetBuyCostMap())
	failed := player.CheckConditions(buyConditions)
	if len(failed) > 0 || lv <= 0 {
		return &pb.S2C_LevelUpGoodsMessage{Code: 2} // 升级消耗不足
	}
	carriage.UnlockGoods(id, lv)
	player.DeductCosts(buyConditions, ta.ResChangeSceneTypeTrainFood)
	return &pb.S2C_LevelUpGoodsMessage{}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
