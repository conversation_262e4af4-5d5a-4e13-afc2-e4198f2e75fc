package game

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/event"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"train/utils/array"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitPassengerHD 自动生成，不要在这个方法添加任何内容。
func InitPassengerHD(this *Game) {
	// 修改乘客入住信息
	this.middleware.Wrap("C2S_ChangePassengerDormMessage", this.C2sChangePassengerDormMessageHandler)
	// 乘客升级/升星
	this.middleware.Wrap("C2S_PassengerLevelUpMessage", this.C2sPassengerLevelUpMessageHandler)
	// 修改乘客工作信息
	this.middleware.Wrap("C2S_ChangePassengerWorkMessage", this.C2sChangePassengerWorkMessageHandler)
	// 完成乘客剧情
	this.middleware.Wrap("C2S_CompletePassengerPlotMessage", this.C2sCompletePassengerPlotMessageHandler)
	// 解锁乘客皮肤
	this.middleware.Wrap("C2S_UnlockSkinMessage", this.C2sUnlockSkinMessageHandler)
	// 更换乘客皮肤
	this.middleware.Wrap("C2S_ChangeSkinMessage", this.C2sChangeSkinMessageHandler)
	// 升级天赋
	this.middleware.Wrap("C2S_TalentLevelUpMessage", this.C2sTalentLevelUpMessageHandler)
	// 投影合成
	this.middleware.Wrap("C2S_FragMergeMessage", this.C2sFragMergeMessageHandler)
	// 乘客解锁资料
	this.middleware.Wrap("C2S_PassengerUnlockProfileMessage", this.C2sPassengerUnlockProfileMessageHandler)
	// 乘客转换
	this.middleware.Wrap("C2S_TransPassengerMessage", this.C2sTransPassengerMessageHandler)
	// 乘客资料排序更换
	this.middleware.Wrap("C2S_PassengerProfileSortChangeMessage", this.C2sPassengerProfileSortChangeMessageHandler)
}
func (this *Game) C2sChangePassengerDormMessageHandler(player *structs.Player, msg *pb.C2S_ChangePassengerDormMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChangePassengerDormRespMessage{Code: cast.ToInt32(code)}
	}
	passengerId := cast.ToInt(msg.Id)
	passenger := player.GetPassengerById(passengerId)
	if passenger == nil {
		return send(1)
	}
	dormId := cast.ToInt(msg.DormId)

	if dormId == 0 { //离开寝室
		player.PassengerCheckOut(passenger)
	} else { //入住
		index := cast.ToInt(msg.Index)
		if index == 0 {
			return send(1)
		}
		carriage := player.GetCarriageById(dormId)
		if carriage == nil {
			return send(2)
		}
		passengers := carriage.GetCheckIns()
		if index > carriage.GetRoleCnt() {
			return send(3)
		}
		tempPasse, exists := lo.Find(passengers, func(p *structs.Passenger) bool { return p.DormIndex == index })
		if exists { //目标位置已经有人了
			replaceIndex := passenger.DormIndex
			if replaceIndex > 0 {
				player.PassengerCheckIn(tempPasse, carriage.Id, replaceIndex)
			} else {
				player.PassengerCheckOut(tempPasse)
			}
		}
		player.PassengerCheckIn(passenger, carriage.Id, index)
	}
	return &pb.S2C_ChangePassengerDormRespMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sPassengerLevelUpMessageHandler(player *structs.Player, msg *pb.C2S_PassengerLevelUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_PassengerLevelUpResultMessage{Code: cast.ToInt32(code)}
	}
	passengerId := cast.ToInt(msg.Id)
	passenger := player.GetPassengerById(passengerId)
	// 星级配置
	starBean, _ := cfg.StarUpContainer.GetBeanById(passenger.StarLv)
	if starBean == nil {
		return send(2)
	}
	if passenger == nil {
		return send(1)
	}
	if msg.Type == 1 {
		cost, exists := cfg.GetCharacterLevelData(passengerId, passenger.Level)
		if !exists { // 找不到下一个等级级的配置
			return send(2)
		}
		if passenger.Level >= starBean.LevelMax {
			return send(3)
		}
		// 共鸣槽里面的乘客才能升级
		res := player.Resonance
		if !res.IsCanOperate(passenger) {
			return send(5)
		}
		cs := structs.ConfigConditionConvert(cost...).All()
		failed := player.CheckConditions(cs)
		if len(failed) != 0 {
			return send(4)
		}
		player.DeductCosts(cs, ta.ResChangeSceneTypePassengerLvUp)
		player.PassengerLevelUp(passenger)
	}
	if msg.Type == 2 {
		fragBean, _ := lo.Find(cfg.CharacterFragContainer.GetData(), func(frag *cfg.CharacterFrag[int]) bool {
			return frag.CharacterId == passengerId && frag.Quality == starBean.Quality
		})
		if fragBean == nil {
			return send(2)
		}

		cs := &structs.Condition{
			Type: condition.PASSENGER_FRAG,
			Id:   fragBean.Id,
			Num:  starBean.UpCost,
		}
		if !player.CheckCondition(cs) {
			return send(3)
		}
		player.DeductCost(cs, ta.ResChangeSceneTypePassengerStarLvUp)
		player.PassengerStarLevelUp(passenger)
	}
	return send(0)
	//@action-code-end
}
func (this *Game) C2sChangePassengerWorkMessageHandler(player *structs.Player, msg *pb.C2S_ChangePassengerWorkMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChangePassengerWorkMessage{Code: cast.ToInt32(code)}
	}
	passengerId := cast.ToInt(msg.Id)
	passenger := player.GetPassengerById(passengerId)
	if passenger == nil {
		return send(1)
	}
	workId := cast.ToInt(msg.GetWorkId())
	workIndex := cast.ToInt(msg.GetWorkIndex())

	if workId == 0 {
		passenger.Fire()
	} else {
		carriage := player.GetCarriageById(workId)
		if carriage == nil {
			return send(2)
		}
		if !carriage.IsUnlockWork(workIndex) {
			return send(3)
		}
		passengers := carriage.GetWorkers()
		tempPasse := array.Find(passengers, func(p *structs.Passenger) bool { return p.WorkIndex == workIndex })
		if tempPasse != nil { //工位已有人
			replaceIndex := passenger.WorkIndex
			if replaceIndex > 0 {
				tempPasse.Hire(carriage.Id, replaceIndex)
			} else {
				tempPasse.Fire()
			}
		}
		passenger.Hire(workId, workIndex)
	}
	player.GetEvent().Emit(event.TrainChangeWork)
	return &pb.S2C_ChangePassengerWorkMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sCompletePassengerPlotMessageHandler(player *structs.Player, msg *pb.C2S_CompletePassengerPlotMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_CompletePassengerPlotMessage{Code: cast.ToInt32(code)}
	}
	id := cast.ToString(msg.GetId())
	bean, _ := cfg.CharacterPlotControlContainer.GetBeanByUnique(id)
	if bean == nil {
		return send(1)
	}
	passenger := player.GetPassengerById(bean.CharacterId)
	if passenger == nil {
		return send(2)
	}
	passenger.CompletePlot(id)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sUnlockSkinMessageHandler(player *structs.Player, msg *pb.C2S_UnlockSkinMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	skinId := msg.GetId()
	bean, _ := cfg.CharacterSkinContainer.GetBeanByUnique(skinId)
	if bean == nil {
		return &pb.S2C_UnlockSkinMessage{Code: 1}
	}
	conditions := structs.ToConditions(bean.BuyCost)
	if len(player.CheckConditions(conditions)) > 0 {
		return &pb.S2C_UnlockSkinMessage{Code: 2}
	}
	player.DeductCosts(conditions, ta.ResChangeSceneTypeNoReport)
	player.UnlockSkinByCfgBean(bean)
	passenger := player.GetPassengerById(bean.CharacterId)
	if passenger != nil {
		passenger.UseSkinIndex = bean.Index
	}
	return &pb.S2C_UnlockSkinMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sChangeSkinMessageHandler(player *structs.Player, msg *pb.C2S_ChangeSkinMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	passengerId := cast.ToInt(msg.GetPassengerId())
	skinIndex := cast.ToInt(msg.GetSkinIndex())
	passenger := player.GetPassengerById(passengerId)
	if passenger == nil {
		return &pb.S2C_ChangeSkinMessage{Code: 1}
	}
	if passenger.GetSkinByIndex(skinIndex) == nil {
		return &pb.S2C_ChangeSkinMessage{Code: 2}
	}
	passenger.UseSkinIndex = skinIndex
	return &pb.S2C_ChangeSkinMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sTalentLevelUpMessageHandler(player *structs.Player, msg *pb.C2S_TalentLevelUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间

	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_TalentLevelUpMessage{Code: cast.ToInt32(code)}
	}

	pid := msg.GetPassengerId()
	passenger := player.GetPassengerById(cast.ToInt(pid))
	if passenger == nil {
		return send(1)
	}

	id := cast.ToInt(msg.GetId())

	lv := passenger.GetTalentLevel(id)

	next := lv + 1
	bean, _ := cfg.TalentAttrLevelContainer.GetBeanById(next)
	if bean == nil {
		return send(2)
	}
	cost := &structs.Condition{
		Type: condition.PROP,
		Id:   item_id.Vitality,
		Num:  bean.Cost,
	}
	if !player.CheckCondition(cost) {
		return send(3) // 满意度不够
	}
	passenger.TalentLevelUp(id, 1)
	player.DeductCost(cost, ta.ResChangeSceneTypePassengerTalentLvUp)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sFragMergeMessageHandler(player *structs.Player, msg *pb.C2S_FragMergeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id, num := cast.ToInt(msg.GetId()), cast.ToInt(msg.GetNum())
	bean, exists := cfg.CharacterFragContainer.GetBeanById(id)
	if !exists {
		// 材料投影不存在
		return &pb.S2C_FragMergeMessage{Code: 1}
	}
	if num <= 0 {
		// 合成数量不正确
		return &pb.S2C_FragMergeMessage{Code: 2}
	}
	nextFrag, _ := lo.Find(cfg.CharacterFragContainer.GetData(), func(frag *cfg.CharacterFrag[int]) bool {
		return frag.CharacterId == bean.CharacterId && frag.Id-id == 1
	})
	if nextFrag == nil {
		// 要合成的投影不存在
		return &pb.S2C_FragMergeMessage{Code: 1}
	}
	items := cfg.Misc_CContainer.GetObj().FragUp
	if len(items) < bean.Quality {
		// 已经是最高级了
		return &pb.S2C_FragMergeMessage{Code: 3}
	}
	need := items[bean.Quality-1] * num
	cost := &structs.Condition{
		Type: condition.PASSENGER_FRAG,
		Id:   id,
		Num:  need,
	}
	pass := player.CheckCondition(cost)
	if !pass {
		// 合成投影材料不足
		return &pb.S2C_FragMergeMessage{Code: 4}
	}
	player.DeductCost(cost, ta.ResChangeSceneTypePassengerFragMerge)
	reward := &structs.Condition{
		Type: condition.PASSENGER_FRAG,
		Id:   nextFrag.Id,
		Num:  num,
	}
	player.GrantReward(reward, ta.ResChangeSceneTypePassengerFragMerge)
	return &pb.S2C_FragMergeMessage{
		Rewards: structs.ToPbConditions([]*structs.Condition{reward}),
	}
	//@action-code-end
}
func (this *Game) C2sPassengerUnlockProfileMessageHandler(player *structs.Player, msg *pb.C2S_PassengerUnlockProfileMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_PassengerUnlockProfileMessage{Code: code}
	}
	profileId := msg.GetProfileId()
	passengerId := cast.ToInt(msg.GetPassengerId())
	passenger := player.GetPassengerById(passengerId)
	if passenger == nil {
		return send(1)
	}
	profileBean, _ := cfg.CharacterProfileContainer.GetBeanById(cast.ToInt(profileId))
	if profileBean == nil {
		return send(2)
	}
	characterBean, _ := cfg.CharacterContainer.GetBeanById(profileBean.CharacterId)
	if characterBean == nil {
		return send(3)
	}
	curBean := passenger.GetJson()
	if curBean.Id != profileBean.CharacterId {
		return send(4)
	}

	position := int(msg.GetPosition())
	if !isProfilePositionValid(profileBean.Type, position) {
		return send(5)
	}
	// 检查位置被占用没
	for _, pos := range passenger.ProfileData {
		if pos == position {
			return send(6)
		}
	}

	player.RemovePassengerProfile(cast.ToInt(profileId))
	passenger.UnlockProfile(profileBean.Type, position)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sTransPassengerMessageHandler(player *structs.Player, msg *pb.C2S_TransPassengerMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_TransPassengerMessage{Code: cast.ToInt32(code)}
	}

	fromId := cast.ToInt(msg.GetFromId())
	toId := cast.ToInt(msg.GetToId())

	fromPassenger := player.GetPassengerById(fromId)
	toPassenger := player.GetPassengerById(toId)

	if fromPassenger == nil || toPassenger == nil {
		return send(1)
	}

	// 保存原始数据
	fromLevel := fromPassenger.Level
	fromEquips := fromPassenger.GetEquips()
	fromTalents := fromPassenger.Talents

	// 防止高转低
	starBean, _ := cfg.StarUpContainer.GetBeanById(toPassenger.StarLv)
	if fromLevel > starBean.LevelMax {
		return send(2)
	}

	toLevel := toPassenger.Level
	toEquips := toPassenger.GetEquips()
	toTalents := toPassenger.Talents

	// 转换函数
	transPassenger := func(passenger *structs.Passenger, level int, orgEquips []*structs.EquipItem, orgTalents []*structs.PassengerTalent) {
		// 处理装备
		for _, orgEquip := range orgEquips {
			// 删除原装备
			player.Equip.Pull(orgEquip.Uid)

			// 查找对应角色的装备配置
			equipCfg, _ := lo.Find(cfg.EquipContainer.GetData(), func(e *cfg.Equip[int]) bool {
				return e.Index == orgEquip.GetIndex() && e.RoleId == passenger.Id
			})

			// 转换装备效果
			effects := lo.Map(orgEquip.Effects, func(e *structs.EquipEffect, _ int) *structs.EquipEffect {
				attr := cfg.GetEquipEffectAttrByLv(equipCfg.Id, e.GetJson(), e.Level)
				return &structs.EquipEffect{
					Id:    e.Id,
					Level: e.Level,
					Attr:  attr,
				}
			})

			// 创建新装备
			newEquip := &structs.EquipItem{
				Uid:     orgEquip.Uid,
				Level:   orgEquip.Level,
				Id:      equipCfg.Id,
				Effects: effects,
				Used:    true,
			}

			// 添加新装备
			player.Equip.Push(newEquip)
		}

		// 处理天赋
		talents := make([]*structs.PassengerTalent, 0)
		for _, orgTalent := range orgTalents {
			talent := &structs.PassengerTalent{}
			id := orgTalent.Id
			talent.Id = id
			talent.Level = orgTalent.Level
			talents = append(talents, talent)
		}

		passenger.Level = level
		passenger.Talents = talents
		passenger.InitSkills()
	}

	// 执行转换
	transPassenger(toPassenger, fromLevel, fromEquips, fromTalents)
	transPassenger(fromPassenger, toLevel, toEquips, toTalents)

	player.GetEvent().Emit(event.PassengerTrans)

	return send(0)
	//@action-code-end
}
func (this *Game) C2sPassengerProfileSortChangeMessageHandler(player *structs.Player, msg *pb.C2S_PassengerProfileSortChangeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间

	passengerId := cast.ToInt(msg.GetPassengerId())
	passenger := player.GetPassengerById(passengerId)
	if passenger == nil {
		return &pb.S2C_PassengerProfileSortChangeMessage{Code: 1}
	}
	sortMap := msg.GetSort()
	if len(sortMap) == 0 {
		return &pb.S2C_PassengerProfileSortChangeMessage{Code: 0}
	}
	for typ, sort := range sortMap {
		if !isProfilePositionValid(typ, sort) {
			return &pb.S2C_PassengerProfileSortChangeMessage{Code: 2}
		}
		_, ok := passenger.ProfileData[int(typ)]
		if !ok {
			// 没解锁？
			return &pb.S2C_PassengerProfileSortChangeMessage{Code: 3}
		}
		sortMap[typ] = sort
	}

	for typ, sort := range sortMap {
		passenger.ProfileData[int(typ)] = cast.ToInt(sort)
	}

	return &pb.S2C_PassengerProfileSortChangeMessage{Code: 0}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

func isProfilePositionValid[T int32 | int](typ, position T) bool {
	validPos := false
	switch typ {
	case 1:
		fallthrough
	case 2:
		validPos = position == 1 || position == 2
	case 3:
		validPos = position == 3
	case 4:
		validPos = position == 4
	case 5:
		validPos = position == 5
	case 6:
		fallthrough
	case 7:
		fallthrough
	case 8:
		validPos = position == 6 || position == 7 || position == 8
	case 9:
		fallthrough
	case 11:
		validPos = position == 9 || position == 11
	case 10:
		validPos = position == 10
	}
	return validPos
}

//@logic-code-end
