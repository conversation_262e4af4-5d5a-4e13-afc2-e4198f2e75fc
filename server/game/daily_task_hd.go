package game

import (
	"encoding/json"
	"train/base/cfg"
	"train/base/enum/daily_task_state"
	"train/base/enum/daily_task_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitDaily_taskHD 自动生成，不要在这个方法添加任何内容。
func InitDaily_taskHD(this *Game) {
	// 完成每日任务，领取奖励
	this.middleware.Wrap("C2S_FinishDailyTaskMessage", this.C2sFinishDailyTaskMessageHandler)
	// 同步每日任务模块数据
	this.middleware.Wrap("C2S_SyncDailyTaskInfoMessage", this.C2sSyncDailyTaskInfoMessageHandler)
	// 完成任务对话
	this.middleware.Wrap("C2S_DialogTaskDoneMessage", this.C2sDialogTaskDoneMessageHandler)
	// 完成战斗任务
	this.middleware.Wrap("C2S_BattleTaskDoneTestMessage", this.C2sBattleTaskDoneTestMessageHandler)
}
func (this *Game) C2sFinishDailyTaskMessageHandler(player *structs.Player, msg *pb.C2S_FinishDailyTaskMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_FinishDailyTaskMessage{Code: code}
	}

	DailyTask := player.DailyTask
	index := int(msg.GetId())
	// 领取大奖励
	if index == -1 {
		if DailyTask.BigGet {
			return send(5)
		}
		cnt := lo.CountBy(DailyTask.Tasks, func(item *structs.DailyTask) bool {
			return item.State == daily_task_state.FINISH
		})
		if cnt != len(DailyTask.Tasks) || cnt != cfg.Misc_CContainer.GetObj().DailyTask.Count {
			return send(6)
		}
		reward := cfg.Misc_CContainer.GetObj().DailyTask.BigReward
		r := player.GenerateRewards(reward, nil)
		player.GrantRewards(r, ta.ResChangeSceneTypeDailyTask)
		DailyTask.BigGet = true
		return send(0)
	}

	if ut.IsIndexOutOfBounds(DailyTask.Tasks, index) {
		return send(1)
	}
	task := DailyTask.Tasks[index]
	if task == nil {
		return send(1)
	}
	if task.State == daily_task_state.FINISH {
		return send(2)
	}
	if task.GetJson().Type == daily_task_type.BATTLE && len(task.BattleInfo) != 0 {
		return send(3)
	}
	if !task.CheckProgress() {
		return send(3)
	}

	extras := msg.GetExtras()
	targets := lo.Map(task.Target, func(item *structs.Condition, i int) *structs.Condition {
		target := ut.Clone(item).(*structs.Condition)
		if len(extras)-1 >= i {
			extra := extras[i]
			json.Unmarshal([]byte(extra), &target.Extra)
		}
		return target
	})

	if len(player.CheckConditions(targets)) > 0 {
		return send(4)
	}

	player.DeductCosts(targets, ta.ResChangeSceneTypeDailyTask)
	player.GrantRewards(task.Reward, ta.ResChangeSceneTypeDailyTask)

	task.Finish()
	return send(0)
	//@action-code-end
}
func (this *Game) C2sSyncDailyTaskInfoMessageHandler(player *structs.Player, msg *pb.C2S_SyncDailyTaskInfoMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return &pb.S2C_SyncDailyTaskInfoMessage{Info: player.DailyTask.ToPb()}
	//@action-code-end
}
func (this *Game) C2sDialogTaskDoneMessageHandler(player *structs.Player, msg *pb.C2S_DialogTaskDoneMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	index := int(msg.GetIndex())
	ary := player.DailyTask.Tasks
	if ut.IsIndexOutOfBounds(ary, index) {
		return &pb.S2C_DialogTaskDoneMessage{Code: 2}
	}
	task := ary[index]
	if task == nil {
		return &pb.S2C_DialogTaskDoneMessage{Code: 3}
	}
	if task.GetJson().Type != daily_task_type.DIALOG {
		return &pb.S2C_DialogTaskDoneMessage{Code: 4}
	}
	if task.State != daily_task_state.TAKE {
		return &pb.S2C_DialogTaskDoneMessage{Code: 5}
	}
	target := task.Progress[0]
	v, ok := target.Extra["dialog"]
	if !ok {
		return &pb.S2C_DialogTaskDoneMessage{Code: 6}
	}
	if cast.ToInt(v) != int(msg.GetDialogIndex()) {
		return &pb.S2C_DialogTaskDoneMessage{Code: 1}
	}
	task.AddProgress(task.Target)
	task.Target = nil
	return &pb.S2C_DialogTaskDoneMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sBattleTaskDoneTestMessageHandler(player *structs.Player, msg *pb.C2S_BattleTaskDoneTestMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	index := int(msg.GetIndex())
	ary := player.DailyTask.Tasks
	if ut.IsIndexOutOfBounds(ary, index) {
		return &pb.S2C_DialogTaskDoneMessage{Code: 2}
	}
	task := ary[index]
	if task == nil {
		return &pb.S2C_DialogTaskDoneMessage{Code: 3}
	}
	if task.GetJson().Type != daily_task_type.BATTLE || len(task.BattleInfo) == 0 {
		return &pb.S2C_DialogTaskDoneMessage{Code: 4}
	}
	task.BattleInfo = nil
	return &pb.S2C_DialogTaskDoneMessage{}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
