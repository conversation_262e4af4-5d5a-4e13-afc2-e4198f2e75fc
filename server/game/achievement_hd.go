package game

import (
	"train/base/cfg"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitAchievementHD 自动生成，不要在这个方法添加任何内容。
func InitAchievementHD(this *Game) {
	// 领取成就奖励
	this.middleware.Wrap("C2S_ClaimAchievementRewardMessage", this.C2sClaimAchievementRewardMessageHandler)
}
func (this *Game) C2sClaimAchievementRewardMessageHandler(player *structs.Player, msg *pb.C2S_ClaimAchievementRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := msg.Id
	json, exists := cfg.AchievementContainer.GetBeanByUnique(id)
	if !exists {
		return &pb.S2C_ClaimAchievementRewardMessage{Code: 1}
	}
	if !player.Achievement.IsDoing(id) {
		return &pb.S2C_ClaimAchievementRewardMessage{Code: 2}
	}
	if player.Achievement.IsCompleted(id) {
		return &pb.S2C_ClaimAchievementRewardMessage{Code: 3}
	}
	task := player.Achievement.CurGet(id)
	if !player.CheckTaskDone(task) {
		return &pb.S2C_ClaimTaskRewardResultMessage{Code: 4}
	}
	player.CompleteAchievementTask(id)
	rewards := structs.ToConditions(json.Reward)
	player.GrantRewards(rewards, ta.ResChangeSceneTypeAchievement)
	return &pb.S2C_ClaimAchievementRewardMessage{Code: 0}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
