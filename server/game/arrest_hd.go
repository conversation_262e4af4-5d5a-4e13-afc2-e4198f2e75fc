package game

import (
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitArrestHD 自动生成，不要在这个方法添加任何内容。
func InitArrestHD(this *Game) {
	// 领取通缉令任务
	this.middleware.Wrap("C2S_AcceptArrestMessage", this.C2sAcceptArrestMessageHandler)
	// 通缉令战斗结果
	this.middleware.Wrap("C2S_SetArrestBattleResultMessage", this.C2sSetArrestBattleResultMessageHandler)
	// 通缉令任务领奖
	this.middleware.Wrap("C2S_FinishArrestMessage", this.C2sFinishArrestMessageHandler)
	// 销毁通缉令
	this.middleware.Wrap("C2S_DestroyArrestMessage", this.C2sDestroyArrestMessageHandler)
	// 刷新通缉令
	this.middleware.Wrap("C2S_RefreshAllArrestMessage", this.C2sRefreshAllArrestMessageHandler)
	// 展示战报
	this.middleware.Wrap("C2S_ShowArrestResultMessage", this.C2sShowArrestResultMessageHandler)
}
func (this *Game) C2sAcceptArrestMessageHandler(player *structs.Player, msg *pb.C2S_AcceptArrestMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间

	id := msg.GetId()
	mod := player.ArrestModule
	if id != "" {
		arrest := mod.GetArrestById(id)
		if arrest == nil {
			return &pb.S2C_AcceptArrestMessage{Code: 1}
		}

		if mod.IsArrestExpired(arrest) {
			return &pb.S2C_AcceptArrestMessage{Code: 2}
		}
		if arrest.State != pb.ArrestState_NotCollected {
			return &pb.S2C_AcceptArrestMessage{Code: 3}
		}
		arrest.State = pb.ArrestState_OnGoing
	} else {
		for _, ad := range mod.Arrests {
			if mod.IsArrestExpired(ad) {
				continue
			}
			ad.State = pb.ArrestState_OnGoing
		}
	}
	return &pb.S2C_AcceptArrestMessage{}
	//@action-code-end
}
func (this *Game) C2sSetArrestBattleResultMessageHandler(player *structs.Player, msg *pb.C2S_SetArrestBattleResultMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := msg.GetId()
	result := msg.GetResult()
	mod := player.ArrestModule
	arrest := mod.GetArrestById(id)
	if arrest == nil {
		return &pb.S2C_SetArrestBattleResultMessage{Code: 1}
	}
	var toPb *pb.Arrest
	if result.IsWin {
		arrest.State = pb.ArrestState_DoneNoReward
		arrest.Battle.RoleCnt = len(result.GetUids())
	} else {
		arrest.Battle.FailCnt += 10
		// 逃跑去其他星球
		if ut.Chance(arrest.Battle.FailCnt) {
			planet := mod.GetRandomPlanet()
			arrest.PlanetId = planet.Id
			arrest.Battle.FailCnt = 0
			toPb = arrest.ToPb()
		}
	}

	return &pb.S2C_SetArrestBattleResultMessage{Data: toPb}
	//@action-code-end
}
func (this *Game) C2sFinishArrestMessageHandler(player *structs.Player, msg *pb.C2S_FinishArrestMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := msg.GetId()
	mod := player.ArrestModule
	arrest := mod.GetArrestById(id)
	if arrest == nil {
		return &pb.S2C_AcceptArrestMessage{Code: 1}
	}
	if mod.IsArrestExpired(arrest) {
		return &pb.S2C_AcceptArrestMessage{Code: 2}
	}
	if arrest.State != pb.ArrestState_DoneNoReward {
		return &pb.S2C_AcceptArrestMessage{Code: 3}
	}

	player.GrantRewards(arrest.Rewards, ta.ResChangeSceneTypeNoReport)
	arrest.State = pb.ArrestState_Finished
	return &pb.S2C_FinishArrestMessage{}
	//@action-code-end
}
func (this *Game) C2sDestroyArrestMessageHandler(player *structs.Player, msg *pb.C2S_DestroyArrestMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := msg.GetId()
	mod := player.ArrestModule
	arrest := mod.GetArrestById(id)
	if arrest != nil {
		mod.Arrests = lo.Filter(mod.Arrests, func(item *structs.Arrest, index int) bool { return item.Id != id })
	}
	return &pb.S2C_AcceptArrestMessage{}
	//@action-code-end
}
func (this *Game) C2sRefreshAllArrestMessageHandler(player *structs.Player, msg *pb.C2S_RefreshAllArrestMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	mod := player.ArrestModule
	mod.Refresh()
	return &pb.S2C_RefreshAllArrestMessage{
		Data: mod.ToPb().Arrests,
	}
	//@action-code-end
}
func (this *Game) C2sShowArrestResultMessageHandler(player *structs.Player, msg *pb.C2S_ShowArrestResultMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.ArrestModule.Result = nil
	return &pb.S2C_ShowArrestResultMessage{
		Code: 0,
	}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
