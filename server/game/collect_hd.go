package game

import (
	"train/base/structs"
	"train/common/pb"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitCollectHD 自动生成，不要在这个方法添加任何内容。
func InitCollectHD(this *Game) {
	// 砍倒采集物
	this.middleware.Wrap("C2S_CollectMapMineDoneMessage", this.C2sCollectMapMineDoneMessageHandler)
}
func (this *Game) C2sCollectMapMineDoneMessageHandler(player *structs.Player, msg *pb.C2S_CollectMapMineDoneMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	mineUid := msg.GetMineUid()
	mine := player.Collect.CollectMine(mineUid)
	if mine == nil {
		return &pb.S2C_CollectMapMineDoneMessage{
			Code: 1,
		}
	}
	return &pb.S2C_CollectMapMineDoneMessage{
		Code: 0,
	}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
