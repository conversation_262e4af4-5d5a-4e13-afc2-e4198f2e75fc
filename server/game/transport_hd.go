package game

import (
	"train/base/cfg"
	"train/base/event"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitTransportHD 自动生成，不要在这个方法添加任何内容。
func InitTransportHD(this *Game) {
	// 领取护送任务
	this.middleware.Wrap("C2S_TransportStartMessage", this.C2sTransportStartMessageHandler)
	// 护送挑战(临时使用)
	this.middleware.Wrap("C2S_TransportFightMessage", this.C2sTransportFightMessageHandler)
	// 领取护送奖励
	this.middleware.Wrap("C2S_TransportRewardGetMessage", this.C2sTransportRewardGetMessageHandler)
	// 返回雪星
	this.middleware.Wrap("C2S_TransportBackMessage", this.C2sTransportBackMessageHandler)
	// 同步运送数据
	this.middleware.Wrap("C2S_SyncTransportMessage", this.C2sSyncTransportMessageHandler)
}
func (this *Game) C2sTransportStartMessageHandler(player *structs.Player, msg *pb.C2S_TransportStartMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	mod := player.Transport
	if mod.List == nil || len(mod.List) <= 0 {
		// 没解锁？？
		return &pb.S2C_TransportStartMessage{Code: 1}
	}
	idx := msg.GetIndex()

	transport := mod.List[idx]
	if transport == nil {
		// 不存在的任务
		return &pb.S2C_TransportStartMessage{Code: 2}
	}

	if transport.State != pb.TransportDataState_NoneGet {
		// 任务不可领取
		return &pb.S2C_TransportStartMessage{Code: 3}
	}

	if !mod.CheckLoadIsValid(transport.Load) {
		// 负重不足
		return &pb.S2C_TransportStartMessage{Code: 4}
	}

	transport.State = pb.TransportDataState_Pull

	return &pb.S2C_TransportStartMessage{}
	//@action-code-end
}
func (this *Game) C2sTransportFightMessageHandler(player *structs.Player, msg *pb.C2S_TransportFightMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	mod := player.Transport
	planetId := int(msg.GetPlanetId())
	transport := mod.GetBattleData(planetId)
	if transport == nil {
		// 不存在稀有任务
		return &pb.S2C_TransportStartMessage{Code: 1}
	}

	if !msg.GetSucc() {
		mod.QuickBack = true
		transport.State = pb.TransportDataState_Failed
	} else {
		if transport.Rare {
			mod.MonsterIdx += 1
		}
	}
	transport.ClearBattleData()
	return &pb.S2C_TransportFightMessage{}
	//@action-code-end
}
func (this *Game) C2sTransportRewardGetMessageHandler(player *structs.Player, msg *pb.C2S_TransportRewardGetMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	mod := player.Transport
	if mod.List == nil || len(mod.List) <= 0 {
		// 没解锁？？
		return &pb.S2C_TransportStartMessage{Code: 1}
	}
	idx := msg.GetIndex()

	transport := mod.List[idx]
	if transport == nil {
		// 不存在的任务
		return &pb.S2C_TransportStartMessage{Code: 2}
	}

	if transport.State == pb.TransportDataState_Over {
		// 奖励已经领取
		return &pb.S2C_TransportRewardGetMessage{Code: 2}
	}

	if !transport.CheckAllPass() {
		// 未完成所有战斗
		return &pb.S2C_TransportRewardGetMessage{Code: 3}
	}

	player.GrantRewards(transport.Rewards, ta.ResChangeSceneTypeTransport)
	player.GrantRewards(transport.FixedRewards, ta.ResChangeSceneTypeTransport)

	// after标记完成
	transport.State = pb.TransportDataState_Over
	if transport.Rare {
		mod.Exp += cfg.Misc_CContainer.GetObj().Transport.RareExp
	}

	player.GetEvent().Emit(event.TransportComplete)

	return &pb.S2C_TransportRewardGetMessage{}
	//@action-code-end
}
func (this *Game) C2sTransportBackMessageHandler(player *structs.Player, msg *pb.C2S_TransportBackMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间

	//Planet := player.PlanetData
	//if !Planet.IsMove() {
	//	// 当前没有移动
	//	return &pb.S2C_TransportBackMessage{Code: 1}
	//}
	mod := player.Transport
	if mod == nil {
		return &pb.S2C_TransportBackMessage{Code: 1}
	}
	if !mod.QuickBack {
		return &pb.S2C_TransportBackMessage{Code: 2}
	}
	player.PlanetData.ReachPlanet(1007)
	mod.QuickBack = false
	return &pb.S2C_TransportBackMessage{}
	//@action-code-end
}
func (this *Game) C2sSyncTransportMessageHandler(player *structs.Player, msg *pb.C2S_SyncTransportMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return &pb.S2C_SyncTransportMessage{Transport: player.Transport.ToPb()}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
