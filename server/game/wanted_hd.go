package game

import (
	"sort"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum/wanted_condition_type"
	"train/base/enum/wanted_state"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitWantedHD 自动生成，不要在这个方法添加任何内容。
func InitWantedHD(this *Game) {
	// 同步悬赏状态
	this.middleware.Wrap("C2S_SyncWantedMessage", this.C2sSyncWantedMessageHandler)
	// 手动刷新悬赏
	this.middleware.Wrap("C2S_RefrehWantedMessage", this.C2sRefrehWantedMessageHandler)
	// 开始悬赏
	this.middleware.Wrap("C2S_StartWantedMessage", this.C2sStartWantedMessageHandler)
	// 领取悬赏奖励
	this.middleware.Wrap("C2S_ClaimWantedRewardMessage", this.C2sClaimWantedRewardMessageHandler)
	// 同步悬赏数据
	this.middleware.Wrap("C2S_SyncAllWantedMessage", this.C2sSyncAllWantedMessageHandler)
}
func (this *Game) C2sSyncWantedMessageHandler(player *structs.Player, msg *pb.C2S_SyncWantedMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_SyncWantedMessage{Code: code}
	}
	index := cast.ToInt(msg.GetIndex())
	wanted := player.Wanted.Get(index)
	if wanted == nil {
		return send(1)
	}
	if wanted.CanEnd() {
		wanted.State = wanted_state.END
	}
	return &pb.S2C_SyncWantedMessage{Code: 0, Data: wanted.ToPb()}
	//@action-code-end
}
func (this *Game) C2sRefrehWantedMessageHandler(player *structs.Player, msg *pb.C2S_RefrehWantedMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_RefrehWantedMessage{Code: code}
	}
	index := cast.ToInt(msg.GetIndex())
	wanted := player.Wanted.Get(index)
	if wanted == nil {
		return send(1)
	}
	if wanted.State != wanted_state.DEFAULT {
		return send(2)
	}
	misc := cfg.Misc_CContainer.GetObj()
	cost := &structs.Condition{Type: condition.DIAMOND, Num: misc.Wanted.RefreshCost}
	if !player.CheckCondition(cost) {
		return send(3)
	}
	player.DeductCost(cost, ta.ResChangeSceneTypeWanted)
	wanted = player.Wanted.ManualRefresh(index)
	return &pb.S2C_RefrehWantedMessage{Code: 0, Data: wanted.ToPb()}
	//@action-code-end
}
func (this *Game) C2sStartWantedMessageHandler(player *structs.Player, msg *pb.C2S_StartWantedMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_RefrehWantedMessage{Code: code}
	}
	index := cast.ToInt(msg.GetIndex())
	roles := ut.ToInt(msg.GetRoles())
	wanted := player.Wanted.Get(index)
	if wanted == nil {
		return send(1)
	}
	if wanted.State != wanted_state.DEFAULT {
		return send(2)
	}
	levelData, _ := cfg.WantedLevelContainer.GetBeanById(wanted.Level)
	if levelData == nil {
		return send(4)
	}
	err_role := false
	roleAry := lo.Map(roles, func(id int, i int) *structs.Passenger {
		p := player.GetPassengerById(id)
		if p == nil {
			err_role = true
		} else if _, exists := lo.Find(player.Wanted.Wanteds, func(w *structs.Wanted) bool {
			// 乘客同时只能进行一个工作
			return w != wanted && w.State == wanted_state.START && lo.Contains(w.Roles, id)
		}); exists {
			err_role = true
		}
		return p
	})
	if err_role {
		log.Error("[%s] C2sStartWantedMessageHandler role not found: %v", roles)
		return send(7)
	}

	if len(roleAry) < wanted.People {
		return send(6)
	}

	sort.Slice(wanted.Conditions, func(i, j int) bool {
		a := wanted.Conditions[i]
		b := wanted.Conditions[j]
		if a.Type != b.Type {
			return a.Type < b.Type
		}
		return a.Value > b.Value
	})
	qualityUsed := make(map[*structs.Passenger]int)
	filterCondAry := make([]*structs.WantedCondition, 0)
	cnt := len(wanted.Conditions)
	for _, role := range roleAry {
		for _, condition := range wanted.Conditions {
			if lo.Contains(filterCondAry, condition) {
				continue
			}
			switch condition.Type {
			case wanted_condition_type.QUALITY:
				if qualityUsed[role] > 0 {
					continue
				}
				if role.GetQuality() >= condition.Value {
					qualityUsed[role] = 1
					cnt--
					filterCondAry = append(filterCondAry, condition)
				}
			case wanted_condition_type.ANIMAL_TYPE:
				if role.GetJson().AnimalType == condition.Value {
					cnt--
					filterCondAry = append(filterCondAry, condition)
				}
			case wanted_condition_type.BATTLE_TYPE:
				if role.GetJson().BattleType == condition.Value {
					cnt--
					filterCondAry = append(filterCondAry, condition)
				}
			}
		}
	}
	if cnt > 0 {
		return send(6)
	}

	player.Wanted.Start(index, roles)
	return &pb.S2C_SyncWantedMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sClaimWantedRewardMessageHandler(player *structs.Player, msg *pb.C2S_ClaimWantedRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_ClaimWantedRewardMessage{Code: code}
	}
	index := cast.ToInt(msg.GetIndex())
	wanted := player.Wanted.Get(index)
	if wanted == nil {
		return send(1)
	}
	if !wanted.CanComplete() {
		return send(2)
	}
	player.Wanted.Complete(index)
	return &pb.S2C_ClaimWantedRewardMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sSyncAllWantedMessageHandler(player *structs.Player, msg *pb.C2S_SyncAllWantedMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return &pb.S2C_SyncAllWantedMessage{Data: player.Wanted.ToPb()}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
