package game

import (
	"train/base/data/condition"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitStoreHD 自动生成，不要在这个方法添加任何内容。
func InitStoreHD(this *Game) {
	// 刷新商店
	this.middleware.Wrap("C2S_StoreRefreshMessage", this.C2sStoreRefreshMessageHandler)
	// 商店购买
	this.middleware.Wrap("C2S_StoreBuyMessage", this.C2sStoreBuyMessageHandler)
	// 同步商店
	this.middleware.Wrap("C2S_SyncStoreMessage", this.C2sSyncStoreMessageHandler)
}
func (this *Game) C2sStoreRefreshMessageHandler(player *structs.Player, msg *pb.C2S_StoreRefreshMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	replay := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_StoreRefreshMessage{Code: code}
	}
	id := cast.ToInt(msg.GetId())
	info := player.Store.List[id]
	if info != nil {
		bean := info.GetJson()
		cost := bean.RefreshPrice
		if cost < 0 {
			return replay(1)
		}
		cnt := bean.RefreshNum
		if cost > player.GetCurrency(condition.DIAMOND) {
			return replay(1)
		}
		if cnt > 0 && cnt <= info.RefreshCount {
			return replay(2)
		}
		info.ManualRefresh()
		player.DeductCost(&structs.Condition{Type: condition.HEART, Num: cost}, ta.ResChangeSceneTypeUnknown)
		return &pb.S2C_StoreRefreshMessage{
			Info: info.ToPb(),
		}
	}
	return replay(3)
	//@action-code-end
}
func (this *Game) C2sStoreBuyMessageHandler(player *structs.Player, msg *pb.C2S_StoreBuyMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	replay := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_StoreBuyMessage{Code: code}
	}
	id := cast.ToInt(msg.GetId())
	pos := cast.ToInt(msg.GetPos())
	store := player.Store.Get(id)
	if store == nil {
		return replay(3)
	}
	if store.Goods == nil || len(store.Goods) == 0 || pos >= len(store.Goods) {
		return replay(3)
	}
	goodData := store.Goods[pos]
	// 检查库存
	if goodData.Stock <= 0 {
		return replay(2)
	}
	// 检查货币
	if !player.CheckCondition(goodData.Cost) {
		return replay(1)
	}
	// 添加奖励 扣除货币
	player.DeductCost(goodData.Cost, ta.ResChangeSceneTypeStore)
	player.GrantReward(goodData.Goods, ta.ResChangeSceneTypeStore)
	goodData.Stock -= 1
	return &pb.S2C_StoreBuyMessage{
		Item: goodData.Goods.ToPb(),
	}
	//@action-code-end
}
func (this *Game) C2sSyncStoreMessageHandler(player *structs.Player, msg *pb.C2S_SyncStoreMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := cast.ToInt(msg.GetId())
	store := player.Store.Get(id)
	if store == nil {
		log.Error("[%s] C2sSyncStoreMessageHandler store not found", player.GetUid(), id)
		return &pb.S2C_SyncStoreMessage{Code: 0}
	}
	return &pb.S2C_SyncStoreMessage{Code: 0, Info: store.ToPb()}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
