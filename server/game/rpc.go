package game

import (
	"fmt"
	"strings"
	"time"
	"train/base/enum"
	"train/base/enum/logout_reason"
	"train/base/manager"
	"train/base/script"
	"train/base/structs"
	comm "train/common"
	ut "train/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"
)

func (this *Game) InitRpc() {
	this.middleware.Wrap("OnLeave", this.onLeave)
	this.middleware.Wrap("OnPlayerLogin", this.onPlayerLogin, "lock=false")
	this.middleware.Wrap("IsPlayerOnline", this.isPlayerOnline)
	this.middleware.Wrap("KickPlayerForce", this.kickPlayerForce)
	this.middleware.Wrap("/Offline", this.Offline)
	this.middleware.Wrap("/onClientVersionUpdate", this.onClientVersionUpdate)
	this.middleware.Wrap("/getOnlinePlayerCnt", this.getOnlinePlayerCnt, "log=false")
}

// isPlayerOnline
/*
 * @description 提供给rpc调用
 * @param uid 玩家id
 * @return result 玩家在线不
 * @return err
 */
func (this *Game) isPlayerOnline(uid string) (result bool, err string) {
	_, exists := manager.GlobalGetPlayerManager().TryGetPlayerByUid(uid)
	return exists, ""
}

// kickPlayerForce
/*
 * @description 节点之间相互通知，使玩家离线
 * @param id
 * @return result
 * @return err
 */
// 被动离线
func (this *Game) kickPlayerForce(uid string) (result bool, err string) {
	mgr := manager.GlobalGetPlayerManager()
	lock := mgr.Lock(uid)
	defer ut.Unlock(lock)
	mgr.ClearById(uid, logout_reason.REPEAT_LOGIN)
	return
}

// 主动离线
func (this *Game) onLeave(session gate.Session) (result interface{}, err string) {
	uid := session.GetUserID()
	mgr := manager.GlobalGetPlayerManager()
	lock := mgr.Lock(uid)
	defer ut.Unlock(lock)
	plr, exists := mgr.TryGetPlayerByUid(uid)
	if exists {
		mgr.Clear(plr, 0)
		// 通知http任务
		if comm.IsBackup() {
			this.All("http", "StopRecordPlayer", plr.Id)
		}
	}
	return
}

func (this *Game) onClientVersionUpdate(version string) (result interface{}, err string) {
	manager.GlobalGetPlayerManager().CheckPlayerVersion(version)
	return map[string]interface{}{}, ""
}

func (this *Game) getOnlinePlayerCnt() (result interface{}, err string) {
	return map[string]interface{}{
		"count": manager.GlobalGetPlayerManager().GetAll().Count(),
	}, ""
}

func (this *Game) onPlayerLogin(session gate.Session) (interface{}, string) {
	sTime := time.Now()
	id := session.GetUserID()
	sid := session.Get(enum.PlayerSid)
	if sid == "" || sid != cast.ToString(script.Sid) {
		return nil, comm.SidMismatchError().Error()
	}

	lock := comm.CreateConsulLock().UidKey(id)
	lock.Lock()
	defer lock.Unlock()

	mgr := manager.GlobalGetPlayerManager()
	lock2 := mgr.SetLock(id)
	defer ut.Unlock(lock2)

	nodeId, err := mgr.GetPlayerNodeId(id)
	if err != nil {
		log.Error("[%s] onPlayerLogin GetPlayerNodeId error:%s", id, err.Error())
		return nil, err.Error()
	}
	var player *structs.Player
	if this.GetServerID() != nodeId {
		if !ut.IsEmpty(nodeId) {
			_, err := this.Invoke(nodeId, "KickPlayerForce", id)
			if err != "" && !strings.Contains(err, "nofound") {
				return nil, err
			}
		}
	} else {
		// id相同就表示是本节点，检查session
		player, _ = mgr.TryGetPlayerByUid(id)
		if player != nil {
			if player.Session != nil && player.Session != session {
				manager.GlobalGetPlayerManager().Clear(player, logout_reason.REPEAT_LOGIN)
				player = nil
			}
		}
	}

	err = mgr.SetPlayerNodeId(id, this.GetServerID())
	if err != nil {
		log.Error("[%s] onPlayerLogin SetPlayerNodeId error:%s", id, err.Error())
		return nil, err.Error()
	}

	// 去db获取或者创建
	if player == nil {
		player, err = structs.TryGetPlayerFromDb(id, true)
		if err != nil {
			return nil, err.Error()
		}
	}
	if player == nil {
		return nil, fmt.Sprintf("[%s] onPlayerLogin player not found", id)
	}
	player.Destroy = false

	// 执行上线操作
	player.Online(session)

	mgr.AddPlayer(player)

	// 通知http任务
	if comm.IsBackup() {
		this.All("http", "StartRecordPlayer", player.Id)
	}

	log.Info("玩家:%s进入%s区", session.GetUserID(), sid)

	player.IsNew = false

	log.Info("onPlayerLogin: %s, during:%fs", player.GetUid(), time.Since(sTime).Seconds())
	return nil, ""
}
