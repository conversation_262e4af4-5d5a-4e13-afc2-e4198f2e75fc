package game

import (
	"train/base/structs"
	"train/common/pb"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitExploreHD 自动生成，不要在这个方法添加任何内容。
func InitExploreHD(this *Game) {
	// 同步探索状态
	this.middleware.Wrap("C2S_SyncExploreMessage", this.C2sSyncExploreMessageHandler)
	// 开始探索
	this.middleware.Wrap("C2S_StartExploreMessage", this.C2sStartExploreMessageHandler)
	// 领取探索奖励
	this.middleware.Wrap("C2S_ClaimExploreRewardMessage", this.C2sClaimExploreRewardMessageHandler)
	// 获取要探索的区域
	this.middleware.Wrap("C2S_GetExploreAreaMessage", this.C2sGetExploreAreaMessageHandler)
}
func (this *Game) C2sSyncExploreMessageHandler(player *structs.Player, msg *pb.C2S_SyncExploreMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	code, surplusTime := player.Explore.SyncExplore(int(msg.PlanetId))
	return &pb.S2C_SyncExploreMessage{
		Code:        code,
		SurplusTime: surplusTime,
	}
	//@action-code-end
}
func (this *Game) C2sStartExploreMessageHandler(player *structs.Player, msg *pb.C2S_StartExploreMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	code, rewards := player.Explore.StartExplore(int(msg.PlanetId), lo.Map(msg.Roles, func(role int32, i int) int {
		return int(role)
	}))
	return &pb.S2C_StartExploreMessage{
		Code:    code,
		Rewards: rewards,
	}
	//@action-code-end
}
func (this *Game) C2sClaimExploreRewardMessageHandler(player *structs.Player, msg *pb.C2S_ClaimExploreRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	code, surplusTime := player.Explore.ClaimRewards(int(msg.PlanetId))
	return &pb.S2C_ClaimExploreRewardMessage{
		Code:        code,
		SurplusTime: surplusTime,
	}
	//@action-code-end
}
func (this *Game) C2sGetExploreAreaMessageHandler(player *structs.Player, msg *pb.C2S_GetExploreAreaMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId := int(msg.GetPlanetId())
	area, ok := player.Explore.Area[planetId]
	if !ok {
		area = structs.GetRandomPlanetArea(planetId)
		player.Explore.Area[planetId] = area
	}
	return &pb.S2C_GetExploreAreaMessage{
		Area: int32(area),
	}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
