package game

import (
	"train/base/cfg"
	"train/base/enum"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"train/sdk/apple"
	"train/sdk/google"
	ut "train/utils"
	"train/utils/array"
	"github.com/huyangv/vmqant/log"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitPaymentHD 自动生成，不要在这个方法添加任何内容。
func InitPaymentHD(this *Game) {
	// 创建订单
	this.middleware.Wrap("C2S_CreatePayOrderMessage", this.C2sCreatePayOrderMessageHandler)
	// 验证订单
	this.middleware.Wrap("C2S_VerifyPayOrderMessage", this.C2sVerifyPayOrderMessageHandler)
	// 领取订单奖励
	this.middleware.Wrap("C2S_GetPayRewardsMessage", this.C2sGetPayRewardsMessageHandler)
}
func (this *Game) C2sCreatePayOrderMessageHandler(player *structs.Player, msg *pb.C2S_CreatePayOrderMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	productId, platform := msg.GetProductId(), msg.GetPlatform()
	orderInfo, e := player.Pay.CreateOrder(player.GetUid(), productId, platform)
	if e != "" {
		log.Error("[%s] createOrder order fail uid: %v, err: %v", player.GetUid(), e)
		return &pb.S2C_CreatePayOrderMessage{Code: 1}
	}
	return &pb.S2C_CreatePayOrderMessage{Uid: orderInfo.UID}
	//@action-code-end
}
func (this *Game) C2sVerifyPayOrderMessageHandler(player *structs.Player, msg *pb.C2S_VerifyPayOrderMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_VerifyPayOrderMessage{Code: code}
	}
	uid, orderId, platform, token, purchaseTime := msg.GetCpOrderId(), msg.GetOrderId(), msg.GetPlatform(), msg.GetToken(), msg.GetPurchaseTime()
	productId := msg.GetProductId()
	currencyType, payAmount := msg.GetCurrencyType(), msg.GetPayAmount()
	Pay := player.Pay
	var orderInfo structs.PayOrder
	var e string
	quantity := 1 //购买数量默认为1
	if uid != "" {
		// uid不为空通过内部订单号查找
		orderInfo, e = Pay.FindByUid(uid)
		if e != "" {
			//未查询到订单
			log.Error("[%s] verifyOrder order not found uid: %v, err: %v", player.GetUid(), uid, e)
			return send(1)
		}
	} else {
		// uid为空则创建订单
		orderData, e := Pay.CreateOrder(player.GetUid(), productId, platform)
		if e != "" {
			//创建订单错误
			log.Error("[%s] verifyOrder FindByUserNotVerifyOrder create err uid: %v, err: %v", player.GetUid(), uid, e)
			return send(2)
		}
		orderInfo = *orderData
	}
	if orderInfo.State == structs.ORDER_STATE_PAY {
		//已验证过的的订单直接返回
		log.Warning("[%s] verifyOrder order state err uid: %v, state: %v", player.GetUid(), orderInfo.UID, orderInfo.State)
		return send(0)
	}
	if orderInfo.State == structs.ORDER_STATE_FINISH {
		//已领取过奖励
		log.Warning("[%s] verifyOrder order state err uid: %v, state: %v", player.GetUid(), orderInfo.UID, orderInfo.State)
		return send(3)
	}
	if orderInfo.State == structs.ORDER_STATE_REFUND {
		//订单已退款
		log.Warning("[%s] verifyOrder order state err uid: %v, state: %v", player.GetUid(), orderInfo.UID, orderInfo.State)
		return send(4)
	}
	if orderInfo.UserId != player.GetUid() || orderInfo.Platform != platform {
		//订单数据不匹配
		log.Error("[%s] verifyOrder param err orderInfo: %v platform: %v", player.GetUid(), orderInfo, platform)
		return send(5)
	}
	switch platform {
	case enum.PAY_PLATFORM_GOOGLE:
		e, q, oid := google.GoogleOrderVerify(productId, token)
		if e != nil {
			log.Error("[%s] verifyOrder GoogleOrderVerify orderInfo: %v, err: %v", player.GetUid(), orderInfo, e)
			return send(6)
		}
		orderId = oid
		quantity = q
	case enum.PAY_PLATFORM_APPLE:
		e, q := apple.AppleOrderVerify(orderId, productId, token)
		if e != nil {
			log.Error("[%s] verifyOrder AppleOrderVerify orderInfo: %v, err: %v", player.GetUid(), orderInfo, e)
			return send(7)
		}
		quantity = q
	}
	quantity = ut.Max(quantity, 1)
	//通过外部订单号查询是否有重复订单
	_, oErr := Pay.FindByOrderId(orderId, platform)
	if oErr == "" {
		//已验证过 无法在新的订单重复验证
		log.Warning("[%s] verifyOrder order repeat err uid: %v, orderId: %v", player.GetUid(), uid, orderId)
		return send(8)
	}
	//更新订单状态
	Pay.UpdateVerifiedOrder(orderInfo.UID, orderId, currencyType, int(purchaseTime), payAmount, quantity)
	//更新未完成订单列表
	Pay.NotFinishOrders = append(Pay.NotFinishOrders, &structs.NotFinishOrder{CpOrderId: orderInfo.UID, ProductId: productId, Quantity: quantity, Platform: platform})
	//数数上报
	ta.TrackBySession(player.Session, ta.Purchase, map[string]interface{}{
		"product_id":    productId,
		"currency_type": currencyType,
		"pay_amount":    payAmount,
		"quantity":      quantity,
		"pay_situation": 1,
		"os":            player.GetOs(),
	})
	return &pb.S2C_VerifyPayOrderMessage{Code: 0, Uid: orderInfo.UID}
	//@action-code-end
}
func (this *Game) C2sGetPayRewardsMessageHandler(player *structs.Player, msg *pb.C2S_GetPayRewardsMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_GetPayRewardsMessage{Code: code}
	}
	uid := msg.GetUid()
	Pay := player.Pay
	orderInfo, e := Pay.FindByUid(uid)
	if e != "" {
		//未查询到订单
		log.Error("[%s] getPayRewards order not find uid: %v, err: %v", player.GetUid(), uid, e)
		return send(1)
	}
	if orderInfo.State != structs.ORDER_STATE_PAY {
		//已支付状态的订单才发放物品
		log.Error("[%s] getPayRewards order state err uid: %v, state: %v", player.GetUid(), uid, orderInfo.State)
		return send(2)
	}
	productInfo := array.Find(cfg.ShopContainer.GetData(), func(d *cfg.Shop[string]) bool { return d.ProductId == orderInfo.ProductId })
	if productInfo == nil {
		//未找到商品数据
		log.Error("[%s] getPayRewards productInfo nil itemId: %v, platform: %v", player.GetUid(), orderInfo.ProductId, orderInfo.Platform)
		return send(3)
	}
	//更新订单状态
	Pay.UpdateFinishedOrder(uid)
	//更新未完成订单列表
	notfinishIndex := array.FindIndex(Pay.NotFinishOrders, func(order *structs.NotFinishOrder) bool { return uid == order.CpOrderId })
	if notfinishIndex != -1 {
		Pay.NotFinishOrders = append(Pay.NotFinishOrders[0:notfinishIndex], Pay.NotFinishOrders[notfinishIndex+1:]...)
	} else {
		log.Error("[%s] getPayRewards remove from NotFinishOrders fail uid: %v, NotFinishOrders: %v", player.GetUid(), uid, Pay.NotFinishOrders)
	}
	//发放奖励
	rewards := structs.ToConditions(productInfo.Product)
	rewards = append(rewards, structs.ToConditions(productInfo.Gifts)...)
	player.GrantRewards(rewards, ta.ResChangeSceneTypePay)
	return send(0)
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
