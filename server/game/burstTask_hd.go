package game

import (
	"sort"
	"train/base/enum/wanted_condition_type"
	"train/base/structs"
	"train/common/pb"
	ut "train/utils"
	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitBurstTaskHD 自动生成，不要在这个方法添加任何内容。
func InitBurstTaskHD(this *Game) {
	// 同步任务数据
	this.middleware.Wrap("C2S_SyncAllBurstTaskMessage", this.C2sSyncAllBurstTaskMessageHandler)
	// 开始任务
	this.middleware.Wrap("C2S_StartBurstTaskMessage", this.C2sStartBurstTaskMessageHandler)
	// 领取任务奖励
	this.middleware.Wrap("C2S_ClaimBurstTaskRewardMessage", this.C2sClaimBurstTaskRewardMessageHandler)
}
func (this *Game) C2sSyncAllBurstTaskMessageHandler(player *structs.Player, msg *pb.C2S_SyncAllBurstTaskMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	index := int(msg.GetIndex())
	if index == -1 {
		return &pb.S2C_SyncAllBurstTaskMessage{Data: player.BurstTask.ToPb().List}
	}
	list := player.BurstTask.List
	if ut.IsIndexOutOfBounds(list, index) {
		return &pb.S2C_SyncAllBurstTaskMessage{Code: 1}
	}
	return &pb.S2C_SyncAllBurstTaskMessage{Data: []*pb.BurstTaskItem{list[index].ToPb()}}
	//@action-code-end
}
func (this *Game) C2sStartBurstTaskMessageHandler(player *structs.Player, msg *pb.C2S_StartBurstTaskMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_StartBurstTaskMessage{Code: code}
	}
	index := cast.ToInt(msg.GetIndex())
	roles := ut.ToInt(msg.GetRoles())
	burstTask := player.BurstTask.Get(index)
	if burstTask == nil {
		return send(1)
	}
	if burstTask.State != pb.CommonState_NotStart {
		return send(2)
	}
	err_role := false
	roleAry := lo.Map(roles, func(id int, i int) *structs.Passenger {
		p := player.GetPassengerById(id)
		if p == nil {
			err_role = true
		} else if _, exists := lo.Find(player.BurstTask.List, func(w *structs.BurstTaskItem) bool {
			// 乘客同时只能进行一个工作
			return w != burstTask && w.State == pb.CommonState_NotStart && lo.Contains(w.Roles, id)
		}); exists {
			err_role = true
		} else if _, exists := lo.Find(player.TrainDailyTask.List, func(w *structs.TrainDailyTaskItem) bool {
			// 打工
			return w.State == pb.CommonState_NotStart && lo.Contains(w.Roles, id)
		}); exists {
			err_role = true
		}
		return p
	})
	if err_role {
		log.Error("[%s] C2sStartBurstTaskMessageHandler role not found: %v", roles)
		return send(7)
	}

	if len(roleAry) < burstTask.People {
		return send(6)
	}

	sort.Slice(burstTask.Conditions, func(i, j int) bool {
		a := burstTask.Conditions[i]
		b := burstTask.Conditions[j]
		if a.Type != b.Type {
			return a.Type < b.Type
		}
		return a.Value > b.Value
	})
	qualityUsed := make(map[*structs.Passenger]int)
	filterCondAry := make([]*structs.WantedCondition, 0)
	cnt := len(burstTask.Conditions)
	for _, role := range roleAry {
		for _, condition := range burstTask.Conditions {
			if lo.Contains(filterCondAry, condition) {
				continue
			}
			switch condition.Type {
			case wanted_condition_type.QUALITY:
				if qualityUsed[role] > 0 {
					continue
				}
				if role.GetQuality() >= condition.Value {
					qualityUsed[role] = 1
					cnt--
					filterCondAry = append(filterCondAry, condition)
				}
			case wanted_condition_type.ANIMAL_TYPE:
				if role.GetJson().AnimalType == condition.Value {
					cnt--
					filterCondAry = append(filterCondAry, condition)
				}
			case wanted_condition_type.BATTLE_TYPE:
				if role.GetJson().BattleType == condition.Value {
					cnt--
					filterCondAry = append(filterCondAry, condition)
				}
			}
		}
	}
	if cnt > 0 {
		return send(6)
	}
	player.BurstTask.Start(index, roles)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sClaimBurstTaskRewardMessageHandler(player *structs.Player, msg *pb.C2S_ClaimBurstTaskRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_ClaimBurstTaskRewardMessage{Code: code}
	}
	index := cast.ToInt(msg.GetIndex())
	item := player.BurstTask.Get(index)
	if item == nil {
		return send(1)
	}
	if !item.CanComplete() {
		return send(2)
	}
	player.BurstTask.Complete(index)
	return &pb.S2C_ClaimBurstTaskRewardMessage{Code: 0}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
