package event

const (
	UpdateCurrency       = "UPDATE_CURRENCY"        //货币更新
	ChangeNumProp        = "CHANGE_NUM_PROP"        //道具更新
	BeforeUpdateCurrency = "BEFORE_UPDATE_CURRENCY" //货币更新之前
	PlanetNodeComplete   = "PLANET_NODE_COMPLETE"   //探索节点完成
	PlanetComplete       = "PLANET_COMPLETE"        //星球探索完成
	UnLockBuild          = "UNLOCK_BUILD"           //解锁设施
	LevelUpBuild         = "LEVEL_UP_BUILD"         //设施升级
	UnLockAllBuild       = "UNLOCK_ALL_BUILD"       //解锁某主题所有设施
	CreateNewCarriage    = "CREATE_NEW_CARRIAGE"    //解锁车厢
	BuildEndCarriage     = "CARRIAGE_BUILD_END"     //解锁车厢
	UnLockTrainGoods     = "UNLOCK_TRAIN_GOODS"     //解锁车厢的货品
	PassengerNew         = "PASSENGER_NEW"          //解锁乘客
	PassengerLevelUp     = "PASSENGER_LEVEL_UP"     //乘客升级
	PassengerStarLvUp    = "PASSENGER_STARLV_UP"    //乘客突破
	PassengerCheckIn     = "PASSENGER_CHECK_IN"     //乘客上车
	PassengerCheckOut    = "PASSENGER_CHECK_OUT"    //乘客下车
	PassengerReset       = "PASSENGER_RESET"        //乘客重生
	PassengerTrans       = "PASSENGER_TRANS"        //乘客转换
	ToolMakeSuccess      = "ToolMakeSuccess"        // 工具制作
	ToolChange           = "ToolChange"             // 工具更换
	ToolTableUp          = "ToolTableUp"            // 升级打造台
	GuideStepEnd         = "GUIDE_STEP_END"         //教程步骤结束
	EntrustDispatchStart = "EntrustDispatchStart"   //开始委托(派遣类)
	EntrustComplete      = "EntrustComplete"        //委托完成(完成)
	BeforeUpgradeStorage = "BeforeUpgradeStorage"   //升级仓库之前
	UpgradeStorage       = "UpgradeStorage"         //升级仓库
	TrainMovingPlanet    = "TRAIN_MOVING_PLANET"    //前往某星球
	TrainChangeWork      = "TRAIN_CHANGE_WORK"      //更换工作
	TransportDone        = "TRANSPORT_DONE"         //护送完成
	PlanetUnlock         = "PLANET_UNLOCK"          //解锁星球时
	FieldGainTime        = "FIELD_GAIN_TIME"        //收获时
	FieldCeilUnlock      = "FIELD_CEIL_UNLOCK"      //解锁格子
	FieldPlantTimes      = "FIELD_PLANT_TIMES"      //种植时
	TowerComplete        = "TOWER_COMPLETE"         //爬塔完成
	DeepExploreComplete  = "DEEP_EXPLORE_COMPLETE"  //深度探索完成
	PlanetProfileUnlock  = "PLANET_PROFILE_UNLOCK"  //星球资料解锁
	TransportComplete    = "TRANSPORT_COMPLETE"     //运送完成
	UnlockTheme          = "UNLOCK_THEME"           //解锁主题
)
