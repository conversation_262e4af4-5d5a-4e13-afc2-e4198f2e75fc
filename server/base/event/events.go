package event

import (
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"train/utils/array"
)

type Scripts interface {
	On(eventName string, callback func(...interface{})) func()
	Once(eventName string, callback func(...interface{})) func()
	Emit(eventName string, data ...interface{})
	Off(eventName string)
	OffAll()
}

type eventListener struct {
	callback func(...interface{})
	counter  int
	delete   bool
}

type Events struct {
	listeners  map[string][]*eventListener
	notifyLock deadlock.RWMutex
}

func NewEvents() *Events {
	return &Events{
		listeners: make(map[string][]*eventListener),
	}
}

// 注册后返回一个函数用来取消该监听
func (e *Events) registerListener(eventName string, callback func(...interface{}), counter int) func() {
	// 创建新的listener
	thisListener := &eventListener{
		callback: callback,
		counter:  counter,
		delete:   false,
	}
	e.notifyLock.Lock()
	defer e.notifyLock.Unlock()
	// 保存
	e.listeners[eventName] = append(e.listeners[eventName], thisListener)
	// 返回侦听
	return func() {
		e.notifyLock.Lock()
		defer e.notifyLock.Unlock()

		if _, ok := e.listeners[eventName]; !ok {
			return
		}
		e.listeners[eventName] = array.Filter(e.listeners[eventName], func(l *eventListener, i int) bool {
			return l != thisListener
		})
	}
}

func (e *Events) notify(eventName string, data ...interface{}) {
	// 获取监听列表
	listeners := e.listeners[eventName]
	if listeners == nil {
		// log.Error("No listeners for event '%s'", eventName)
		return
	}
	// 这里只能使用读锁，不然在派发事件a中再派发事件b就会死锁
	e.notifyLock.RLock()
	defer func() {
		e.notifyLock.RUnlock()
		err := recover()
		if err != nil {
			log.Error("派发事件%s,错误:%s", eventName, err)
			return
		}
	}()
	// 标识是否需要删除
	itemsToDelete := false
	// 回调 没有使用协程，所以要注意不要阻塞回调
	for _, listener := range listeners {
		if listener.counter > 0 {
			listener.counter--
		}
		//go listener.callback(data...)
		listener.callback(data...)

		if listener.counter == 0 {
			listener.delete = true
			itemsToDelete = true
		}
	}
	if itemsToDelete == true {
		var newListeners []*eventListener
		for _, listener := range listeners {
			if !listener.delete {
				newListeners = append(newListeners, listener)
			}
		}
		if len(newListeners) > 0 {
			e.listeners[eventName] = newListeners
		} else {
			delete(e.listeners, eventName)
		}
	}
}

// On 当eventName事件被触发时每次回调callback,该方法返回一个函数用来取消该监听
func (e *Events) On(eventName string, callback func(...interface{})) func() {
	return e.registerListener(eventName, callback, -1)
}

// Once 当eventName事件被触发时回调一次callback,该方法返回一个函数用来取消该监听
func (e *Events) Once(eventName string, callback func(...interface{})) func() {
	return e.registerListener(eventName, callback, 1)
}

// Emit 触发eventName事件，调用所有监听回调，内部实现非协程，不要阻塞回调。
func (e *Events) Emit(eventName string, data ...interface{}) {
	e.notify(eventName, data...)
}

// Off 取消eventName所有监听回调
func (e *Events) Off(eventName string) {
	e.notifyLock.Lock()
	defer e.notifyLock.Unlock()
	delete(e.listeners, eventName)
}

// OffAll 取消所有回调
func (e *Events) OffAll() {
	e.notifyLock.Lock()
	defer e.notifyLock.Unlock()
	e.listeners = make(map[string][]*eventListener)
}
