package condition

const (
	GM                   = -1 //一些特殊操作
	NONE                 = 0
	DIAMOND              = 1   // 钻石
	STAR_DUST            = 2   // 星尘
	HEART                = 3   // 爱心
	WORLD_TIME           = 4   //模拟时间
	POWER                = 8   //能量
	TECH                 = 9   // 科技
	PASSENGER            = 10  // 乘客
	PROP                 = 11  //道具
	BUILD_ID             = 12  //设施
	FUNCTION             = 13  //功能(解锁)
	CARRIAGE             = 14  //车厢
	PassengerChip        = 16  // 乘客碎片
	Chest                = 17  // 宝箱
	Seed                 = 18  // 种子
	PLANET_NODE          = 21  // 星球节点
	PLANET_BATTLE_NODE   = 211 // 星球战斗节点
	PLANET               = 22  // 星球
	EQUIP                = 23  // 装备
	PASSENGER_SKIN       = 24  // 乘客皮肤
	PASSENGER_FRAG       = 25  // 乘客碎片
	BLACK_HOLE_CURRENCY  = 26  // 星海币
	ORE_ITEM             = 27  // 矿石
	ARREST_CURRENCY      = 29  // 通缉币
	PASSENGER_PROFILE    = 30  // 乘客资料
	PLANET_PROFILE       = 31  // 星球资料
	DOSING               = 56  // 食材
	PASSENGER_EXPLORE_SP = 101 //乘客探索奖励材料特殊处理
	AD                   = 301 // 广告次数
	JACKPOT_COUNT        = 401 // 累计邀请乘客次数
	TASK_DIALOG          = 501 // 完成对话
)
