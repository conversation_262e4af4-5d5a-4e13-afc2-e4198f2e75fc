package structs

import (
	"train/base/cfg"
	"train/base/enum/wanted_condition_type"
	"train/common/pb"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
)

func NewBurstTaskModule() *BurstTaskModule {
	return &BurstTaskModule{}
}

type BurstTaskModule struct {
	List []*BurstTaskItem `bson:"list"`
	plr  *Player          `bson:"-"`
}

func (b *BurstTaskModule) init(plr *Player) {
	b.plr = plr
	for _, item := range b.List {
		item.init(plr)
	}
}

func (b *BurstTaskModule) Get(index int) *BurstTaskItem {
	if ut.IsIndexOutOfBounds(b.List, index) {
		return nil
	}
	return b.List[index]
}
func (b *BurstTaskModule) Start(index int, roles []int) {
	item := b.List[index]
	item.State = pb.CommonState_InProcess
	item.Roles = roles
	item.EndTime = b.plr.GetNowTime() + item.Json().CostTime*ut.TIME_MINUTE
}

func (b *BurstTaskModule) Complete(index int) {
	item := b.List[index]
	item.State = pb.CommonState_FinishWithReward
}

func (b *BurstTaskModule) Unlock(id int) *BurstTaskItem {
	bean, _ := cfg.BurstTaskItemContainer.GetBeanById(id)
	if bean == nil {
		log.Error("解锁突发失败，没有找到id为 %d 的突发任务。", id)
		return nil
	}
	if lo.ContainsBy(b.List, func(item *BurstTaskItem) bool { return item.Id == id }) {
		return nil
	}
	if b.List == nil {
		b.List = make([]*BurstTaskItem, 0)
	}
	item := b.create(id)
	b.List = append(b.List, item)
	return item
}

func (b *BurstTaskModule) create(id int) *BurstTaskItem {
	item := &BurstTaskItem{
		Id:         id,
		Conditions: make([]*WantedCondition, 0),
	}
	item.init(b.plr)
	bean, _ := cfg.BurstTaskItemContainer.GetBeanById(id)
	item.Rewards = b.plr.GenerateRewards(bean.Reward, nil)

	condAry := cfg.TrainDailyTaskConditionContainer.GetData()
	req := bean.Req
	for i := 0; i < bean.ReqNum; i++ {
		req = lo.Filter(req, func(data *cfg.ChestReward, i int) bool {
			count := lo.CountBy(item.Conditions, func(c *WantedCondition) bool {
				return c.Type == data.Type && c.Value == data.Id
			})
			return data.Num == -1 || count < data.Num
		})
		if len(req) <= 0 {
			break
		}
		idx := ut.RandomIndexByWeight(req, func(d *cfg.ChestReward) int { return d.Weight })
		data := req[idx]

		switch data.Type {
		case wanted_condition_type.QUALITY:
			item.Conditions = append(item.Conditions, &WantedCondition{Type: data.Type, Value: data.Id})
		default:
			matchAry := lo.Filter(condAry, func(d *cfg.TrainDailyTaskCondition[string], i int) bool {
				return d.Type == data.Type
			})
			idx := ut.RandomIndexByWeight(matchAry, func(d *cfg.TrainDailyTaskCondition[string]) int { return d.Weight })
			data := matchAry[idx]
			item.Conditions = append(item.Conditions, &WantedCondition{Type: data.Type, Value: data.Value})
		}
	}
	item.People, _ = b.plr.getMinPeople(item.Conditions)
	train := b.plr.Train
	canDoAry := lo.Filter(bean.Train, func(id int, index int) bool {
		carriage := train.GetCarriageById(id)
		return carriage != nil && carriage.IsBuilt()
	})
	item.TrainId = ut.RandomIn(canDoAry)
	return item
}

func (b *BurstTaskModule) ToPb() *pb.BurstTask {
	return &pb.BurstTask{List: lo.Map(b.List, func(item *BurstTaskItem, i int) *pb.BurstTaskItem { return item.ToPb() })}
}

type BurstTaskItem struct {
	Id         int                     `bson:"id"`
	Rewards    []*Condition            `bson:"rewards"`
	Conditions []*WantedCondition      `bson:"conditions"`
	People     int                     `bson:"people"`
	EndTime    int                     `bson:"endTime"`
	State      pb.CommonState          `bson:"state"`
	Roles      []int                   `bson:"roles"`
	TrainId    int                     `bson:"trainId"`
	plr        *Player                 `bson:"-"`
	json       *cfg.BurstTaskItem[int] `bson:"-"`
}

func (b *BurstTaskItem) init(plr *Player) { b.plr = plr }

func (b *BurstTaskItem) GetSurplusTime() int {
	if b.EndTime <= 0 {
		return 0
	}
	val := b.EndTime - b.plr.GetNowTime()
	if b.State == pb.CommonState_InProcess && val <= 0 {
		b.State = pb.CommonState_DoneWithoutReward
		val = 0
	}
	return val
}

func (b *BurstTaskItem) CanEnd() bool {
	return b.State == pb.CommonState_InProcess && b.GetSurplusTime() <= 0
}

func (b *BurstTaskItem) CanComplete() bool {
	return b.State == pb.CommonState_DoneWithoutReward || b.CanEnd()
}

func (b *BurstTaskItem) Json() *cfg.BurstTaskItem[int] {
	if b.json == nil {
		b.json, _ = cfg.BurstTaskItemContainer.GetBeanById(b.Id)
	}
	return b.json
}

func (b *BurstTaskItem) ToPb() *pb.BurstTaskItem {
	return &pb.BurstTaskItem{
		Id:          int32(b.Id),
		Conditions:  lo.Map(b.Conditions, func(c *WantedCondition, i int) *pb.WantedCondition { return c.ToPb() }),
		Rewards:     ToPbConditions(b.Rewards),
		SurplusTime: int32(b.GetSurplusTime()),
		State:       b.State,
		Roles:       ut.ToInt32(b.Roles),
		People:      int32(b.People),
		TrainId:     int32(b.TrainId),
	}
}
