package structs

import (
	"fmt"
	"math"
	"sort"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum"
	"train/base/enum/build_attr"
	"train/base/enum/equip_effect_type"
	"train/base/event"
	comm "train/common"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"

	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"
)

func (plr *Player) InitPassenger() {
	remove := make([]*Passenger, 0)
	for _, passenger := range plr.Passenger {
		json := passenger.GetJson()
		if json == nil {
			remove = append(remove, passenger)
			continue
		}
		passenger.Init(plr)
	}

	// fix dev环境
	if comm.IsDebug() && len(remove) > 0 {
		lo.ForEach(remove, func(passenger *Passenger, i int) {
			log.Debug("fix 未放出乘客:%d", passenger.Id)
			plr.Passenger = array.Remove(plr.Passenger, passenger)
		})
	}

	plr.UnlockDefaultPassenger()

	//兼容DormIndex
	indexMap := map[int]int{}
	for _, passenger := range plr.Passenger {
		if passenger.isCheckIn() {
			indexMap[passenger.DormId] |= (1 << passenger.DormIndex)
		}
	}
	for _, passenger := range plr.Passenger {
		if passenger.isCheckIn() && passenger.DormIndex == 0 {
			flag := indexMap[passenger.DormId]
			for i := 1; i <= 4; i++ {
				index := (1 << i)
				if flag&index == 0 {
					passenger.DormIndex = i
					flag |= index
					indexMap[passenger.DormId] = flag
					break
				}
			}
		}
	}
}

func (plr *Player) UnlockDefaultPassenger() {
	misc := cfg.Misc_CContainer.GetObj()
	init := misc.INITIAL
	conditions := ToConditions(init)
	passengers := lo.Filter(conditions, func(c *Condition, i int) bool {
		return c.Type == condition.PASSENGER
	})
	if len(passengers) > 0 {
		for _, cp := range passengers {
			id := cast.ToInt(cp.Id)
			passenger := plr.GetPassengerById(id)
			if passenger == nil {
				passenger = plr.UnlockPassenger(id)
				// 初始乘客默认上车车厢 未考虑配置数量超过1013空位
				// 默认入住第一个位置
				plr.PassengerCheckIn(passenger, 1013, 1)
			}
		}
	}
}

func (plr *Player) AddPassengerProfile(id int) {
	plr.PassengerProfiles = append(plr.PassengerProfiles, id)
}

func (plr *Player) RemovePassengerProfile(id int) {
	plr.PassengerProfiles = array.Remove(plr.PassengerProfiles, id)
}

type PassengerPlot struct {
	Id   string                            `bson:"id"`
	Done bool                              `bson:"done"`
	json *cfg.CharacterPlotControl[string] `bson:"-"`
}

func (this *PassengerPlot) GetJson() *cfg.CharacterPlotControl[string] {
	if this.json == nil {
		this.json, _ = cfg.CharacterPlotControlContainer.GetBeanByUnique(this.Id)
	}
	return this.json
}

func (this *PassengerPlot) ToPb() *pb.PassengerPlot {
	return &pb.PassengerPlot{Id: this.Id, Done: this.Done}
}

// Passenger 乘客
type Passenger struct {
	plr          *Player             `bson:"-"`
	json         *cfg.Character[int] `bson:"-"`
	Id           int                 `bson:"id"`
	Level        int                 `bson:"level"`                 // 等级
	StarLv       int                 `bson:"starLv"`                // 星级
	DormId       int                 `bson:"dormId"`                // 入住车厢id
	DormIndex    int                 `bson:"dormIndex"`             //乘客在车厢中的坑位位置
	UnlockTime   int                 `bson:"unlockTime"`            // 乘客获取时间
	HeartOutput  float64             `bson:"heartOutput"`           //爱心积累产出
	StarOutput   float64             `bson:"starOutput"`            //星尘积累产出
	Skills       []*Skill            `bson:"-"`                     //战斗技能
	WorkId       int                 `bson:"workId"`                //工作车厢id
	WorkIndex    int                 `bson:"workIndex"`             //工位顺序
	WaterTime    int                 `bson:"waterTime"`             //
	Plots        []*PassengerPlot    `bson:"plots"`                 //剧情
	UseSkinIndex int                 `bson:"useSkin"`               //使用的皮肤
	Talents      []*PassengerTalent  `bson:"talents"`               //天赋
	ProfileData  map[int]int         `bson:"profileData,omitempty"` // 乘客资料
	Equips       []*EquipItem        `bson:"-"`                     //乘客当前装备，只用于缓存
}

func (this *Passenger) Init(plr *Player) {
	this.plr = plr
	for i := 1; i <= this.Level; i++ {
		this.updateSkillsByLv(i)
	}

	if this.ProfileData == nil {
		this.ProfileData = make(map[int]int)
	}
	this.InitStarLv()
}

func (this *Passenger) InitSkills() {
	//TODO
}

func (this *Passenger) InitStarLv() {
	json := this.GetJson()
	initStartLv := cfg.GetStarLvByQuality(json.Quality)
	this.StarLv = ut.Max(this.StarLv, initStartLv)
}

func (this *Passenger) GetJson() *cfg.Character[int] {
	if this.json == nil {
		this.json, _ = cfg.CharacterContainer.GetBeanById(this.Id)
	}
	return this.json
}

// 是否在车上
func (this *Passenger) isCheckIn() bool {
	return this.DormId != 0
}

func (this *Passenger) GetCheckInBuildAttr() string {
	if this.isCheckIn() {
		return ut.If(this.DormIndex%2 == 1, build_attr.STAR, build_attr.HEART)
	}
	return ""
}

func (this *Passenger) GetSortId() int {
	return this.GetJson().SortId
}

func (this *Passenger) ToPb() *pb.PassengerInfo {
	info := &pb.PassengerInfo{
		Id:           cast.ToInt32(this.Id),
		Level:        cast.ToInt32(this.Level),
		StarLv:       cast.ToInt32(this.StarLv),
		DormId:       cast.ToInt32(this.DormId),
		HeartOutput:  cast.ToInt32(this.HeartOutput),
		StarOutput:   cast.ToInt32(this.StarOutput),
		WorkId:       cast.ToInt32(this.WorkId),
		WorkIndex:    cast.ToInt32(this.WorkIndex),
		Plots:        lo.Map(this.Plots, func(plot *PassengerPlot, i int) *pb.PassengerPlot { return plot.ToPb() }),
		UseSkinIndex: cast.ToInt32(this.UseSkinIndex),
		DormIndex:    cast.ToInt32(this.DormIndex),
		Talents:      lo.Map(this.Talents, func(talent *PassengerTalent, i int) *pb.PassengerTalent { return talent.ToPb() }),
		Profile:      lo.MapEntries(this.ProfileData, func(key int, val int) (int32, int32) { return int32(key), int32(val) }),
	}
	return info
}

func NewPassenger(id int, plr *Player) *Passenger {
	v := &Passenger{
		Id:         id,
		Level:      1,
		StarLv:     1,
		DormId:     0,
		UnlockTime: plr.GetNowTime(),
	}
	// 初始星级读取配置
	v.StarLv = v.GetJson().GetInitStarLv()
	// 初始皮肤
	skinId := fmt.Sprintf("%d-1", id)
	bean, _ := cfg.CharacterSkinContainer.GetBeanByUnique(skinId)
	if bean != nil {
		v.UseSkinIndex = bean.Index
		plr.Skins[id] = make([]*PassengerSkin, 0)
		plr.Skins[id] = append(plr.Skins[id], NewPassengerSkin(bean.Index))
	}
	return v
}

func (plr *Player) PassengerLevelUp(passenger *Passenger) {
	passenger.Level += 1
	passenger.updateSkillsByLv(passenger.Level)
	plr.GetEvent().Emit(event.PassengerLevelUp, passenger)
}

// DoStarLevelUp 乘客升星操作
func (plr *Player) PassengerStarLevelUp(passenger *Passenger) {
	passenger.StarLv += 1
	passenger.updateSkillsByStarLv(passenger.StarLv)
	plr.GetEvent().Emit(event.PassengerStarLvUp, passenger)
}

// PushPassenger 添加解锁的乘客
func (plr *Player) PushPassenger(passenger *Passenger, emit bool) {
	plr.Passenger = append(plr.Passenger, passenger)
	if emit {
		plr.eventCenter.Emit(event.PassengerNew, passenger)
	}
}

// GetPassengerById 使用id获取乘客
func (plr *Player) GetPassengerById(passengerId int) *Passenger {
	if plr.Passenger == nil {
		return nil
	}
	find := array.Find(plr.Passenger, func(p *Passenger) bool {
		return p.Id == passengerId
	})
	return find
}

// 获取入住车厢carriageId的乘客列表
func (plr *Player) GetPassengersByDorm(carriageId int) []*Passenger {
	t := make([]*Passenger, 0)
	carriage := plr.GetCarriageById(carriageId)
	if carriage == nil {
		return t
	}
	t = array.Filter(plr.Passenger, func(p *Passenger, i int) bool {
		return p.DormId == carriageId
	})
	return t
}

func (plr *Player) UnlockPassenger(id int) *Passenger {
	passenger := plr.GetPassengerById(id)
	if passenger != nil {
		return passenger
	}
	passenger = NewPassenger(id, plr)
	passenger.Init(plr)
	plr.PushPassenger(passenger, true)
	plr.PushNew(enum.MARKNEW_ROLE_NEW, []int{id})
	plr.PushNew(enum.MARKNEW_ROLE_UNREAD, []int{id})
	log.Info("[%s] 解锁乘客: %d", plr.GetUid(), id)
	return passenger
}

// 乘客入住车厢
func (plr *Player) PassengerCheckIn(passenger *Passenger, dormId, index int) {
	passenger.DormId = dormId
	passenger.DormIndex = index
	plr.eventCenter.Emit(event.PassengerCheckIn, passenger)
}

// CheckOut 乘客离开车厢
func (plr *Player) PassengerCheckOut(passenger *Passenger) {
	passenger.DormId = 0
	passenger.DormIndex = 0
	plr.eventCenter.Emit(event.PassengerCheckOut, passenger)
}

func (this *Passenger) updateSkillsByStarLv(level int) {
	//lvCfg, _ := cfg.GetCharacterStarLevelData(level)
	//if lvCfg == nil {
	//	return
	//}
	//add := lvCfg.Add
	//if add == nil {
	//	return
	//}
	//
	//if skillId := add["lifeSkill"]; skillId != nil {
	//	Id := cast.ToInt(skillId)
	//	skill, _ := lo.Find(this.lifeSkills, func(skill *Skill) bool {
	//		return skill.Id == Id
	//	})
	//	if skill != nil {
	//		skill.LevelUp()
	//	}
	//}
	//
	//if skillId := add["explorationSkill"]; skillId != nil {
	//	skill := this.exploreSkill
	//	if skill != nil {
	//		skill.LevelUp()
	//	}
	//}
}

func (this *Passenger) updateSkillsByLv(lv int) {
}

func (this *Passenger) getSkillLvByStarLv(starLv int, Type string, skillId int) int {
	sum := 0
	//for lv := 0; lv <= starLv; lv++ {
	//	lvCfg, _ := cfg.GetCharacterStarLevelData(lv)
	//	if lvCfg == nil {
	//		continue
	//	}
	//	add := lvCfg.Add
	//	if add == nil {
	//		continue
	//	}
	//
	//	Id := cast.ToInt(add[Type])
	//	if Id == skillId {
	//		sum++
	//	}
	//}
	return sum
}

func (this *Passenger) Reset() {
	this.Level = 1
	this.Skills = this.Skills[:0]
	this.Init(this.plr)
	this.plr.PassengerRestCdTime = this.plr.GetNowTime() + cfg.Misc_CContainer.GetObj().GetPassengerResetCdTime()
	this.plr.eventCenter.Emit(event.PassengerReset)
}

// GetPassengersWhoHaveBoarded 获取上车的乘客
func (plr *Player) GetCheckInPassengers() []*Passenger {
	return lo.Filter(plr.Passenger, func(passenger *Passenger, idx int) bool {
		return passenger.isCheckIn()
	})
}

// isUnlockSkill 是否解锁技能
func (this *Passenger) IsUnlockSkill(id int) bool {
	_, unlock := lo.Find(this.Skills, func(skill *Skill) bool { return skill.Id == id })
	return unlock
}

func (this *Passenger) GetAttr(attrType string) int {
	return cfg.GetRoleAttr(this.Id, this.Level, this.StarLv, attrType)
}

func (this *Passenger) GetAttack() int {
	return this.GetAttr(enum.RoleAttack)
}

func (this *Passenger) GetHp() int {
	return this.GetAttr(enum.RoleHP)
}

func (this *Passenger) Hire(carriageId int, index int) {
	this.WorkId = carriageId
	this.WorkIndex = index
}

func (this *Passenger) Fire() {
	this.WorkId = 0
	this.WorkIndex = 0
}

func (this *Passenger) IsWork() bool {
	return this.WorkId != 0
}

func (this *Passenger) GetPlot(id string) *PassengerPlot {
	return array.Find(this.Plots, func(plot *PassengerPlot) bool { return plot.Id == id })
}

func (this *Passenger) CompletePlot(id string) {
	plot := this.GetPlot(id)
	if plot == nil {
		plot = &PassengerPlot{Id: id, Done: true}
		this.Plots = append(this.Plots, plot)
	}
}

func (this *Passenger) GetSkins() []*PassengerSkin {
	skins, ok := this.plr.Skins[this.Id]
	if !ok {
		return make([]*PassengerSkin, 0)
	}
	return skins
}

func (this *Passenger) GetSkinByIndex(index int) *PassengerSkin {
	skins := this.GetSkins()
	return array.Find(skins, func(skin *PassengerSkin) bool { return skin.Index == index })
}

func (this *Passenger) GetTalent(id int) *PassengerTalent {
	return array.Find(this.Talents, func(talent *PassengerTalent) bool {
		return talent.Id == id
	})
}

func (this *Passenger) GetTalentLevel(id int) int {
	talent := this.GetTalent(id)
	if talent == nil {
		return 0
	}
	return talent.Level
}

func (this *Passenger) TalentLevelUp(id int, level int) {
	talent := this.GetTalent(id)
	if talent == nil {
		talent = &PassengerTalent{Id: id, Level: 0}
		this.Talents = append(this.Talents, talent)
	}
	talent.Level += level
}

func (p *Passenger) GetTalentMaxLevel(id int) int {
	data, _ := cfg.TalentAttrContainer.GetBeanById(id)
	if data == nil {
		return 0
	}

	if data.Type == equip_effect_type.SKILL {
		isUnlock := false
		for _, skill := range p.Skills {
			if skill.Id == data.Target {
				isUnlock = true
				break
			}
		}
		if !isUnlock {
			return 0
		}
	}

	return 100
}

func (this *Passenger) ToResonance() *Passenger {
	if !this.plr.Resonance.IsBeResonated(this.Id) {
		return this
	}

	Resonance := this.plr.Resonance

	level := Resonance.GetResonanceLv()
	// 创建新的乘客实例
	p := &Passenger{
		Id:     this.Id,
		StarLv: this.StarLv,
		plr:    this.plr,
	}

	p.Equips = this.Equips
	p.Talents = this.Talents

	// 处理等级上限
	maxLevel := cfg.GetMaxLvByStarLv(p.StarLv)
	p.Level = lo.Clamp(level, p.Level, maxLevel)

	p.Init(this.plr)
	return p
}

// GetPassengerAvgLv 获取等级最高的N个乘客平均等级
func (plr *Player) GetPassengerAvgLv(n int) int {
	logic := func(arr []*Passenger) int {
		total := 0
		for _, passenger := range arr {
			total += passenger.Level
		}
		return cast.ToInt(total / len(arr))
	}
	if len(plr.Passenger) <= n {
		return logic(plr.Passenger)
	}
	passengers := array.SliceCopy(plr.Passenger)
	sortFunc := func(i, j int) bool {
		return passengers[i].Level > passengers[j].Level
	}
	sort.Slice(passengers, sortFunc)
	return logic(passengers[:n])
}

func (plr *Player) GetPassengerAvgStarLv(n int) int {
	logic := func(arr []*Passenger) int {
		total := 0
		for _, passenger := range arr {
			total += passenger.StarLv
		}
		return cast.ToInt(total / len(arr))
	}
	if len(plr.Passenger) <= n {
		return logic(plr.Passenger)
	}
	passengers := array.SliceCopy(plr.Passenger)
	sortFunc := func(i, j int) bool {
		return passengers[i].StarLv > passengers[j].StarLv
	}
	sort.Slice(passengers, sortFunc)
	return logic(passengers[:n])
}

func (plr *Player) GetPassengerStarOutput() float64 {
	sum := plr.PassengerStarOutput
	for _, passenegr := range plr.Passenger {
		sum += passenegr.StarOutput
	}
	return sum
}

// 领取产出爱心
func (plr *Player) ClaimPassengerStar(num int) int {
	sum := plr.GetPassengerStarOutput()

	output := cast.ToInt(sum)
	if num > output || num <= 0 {
		num = output
	}

	numF := float64(num)
	originalVal := plr.PassengerStarOutput
	deduct := math.Min(originalVal, numF)
	plr.PassengerStarOutput = originalVal - deduct
	numF -= deduct

	for _, passenegr := range plr.Passenger {
		if numF <= 0 {
			break
		}
		originalVal := passenegr.StarOutput
		deduct := math.Min(originalVal, numF)
		passenegr.StarOutput = originalVal - deduct
		numF -= deduct
	}
	plr.GrantReward(&Condition{Type: condition.STAR_DUST, Num: num}, ta.ResChangeSceneTypeTrainPickUp)
	log.Info("[%s] 领取乘客星尘,本次领取数量:%d, 剩余:%f", plr.GetUid(), num, plr.GetPassengerStarOutput())
	return num
}

func (passenger *Passenger) IsBeResonated() bool {
	return passenger.plr.Resonance.IsBeResonated(passenger.Id)
}

func (passenger *Passenger) GetResonanceLv() int {
	plr := passenger.plr
	lv := passenger.Level
	if passenger.IsBeResonated() {
		lv = lo.Clamp(plr.GetResonanceLv(), lv, cfg.GetMaxLvByStarLv(passenger.StarLv))
	}
	return lv
}

func (passenger *Passenger) GetEquip(index int) *EquipItem {
	equips := passenger.plr.GetEquips()
	return array.Find(equips, func(e *EquipItem) bool {
		return e.Used && e.GetJson().Index == index && e.GetJson().RoleId == passenger.Id
	})
}

func (passenger *Passenger) GetEquips() []*EquipItem {
	if len(passenger.Equips) > 0 {
		return passenger.Equips
	}
	equips := passenger.plr.GetEquips()
	return lo.Filter(equips, func(e *EquipItem, i int) bool {
		return e.Used && e.GetJson().RoleId == passenger.Id
	})
}

func (passenger *Passenger) UnlockProfile(typ, position int) {
	passenger.ProfileData[typ] = position
}

func (plr *Player) GetResonanceRoles() []*Passenger {
	return plr.Resonance.GetResonanceRoles()
}

func (plr *Player) GetResonanceLv() int {
	return plr.Resonance.GetResonanceLv()
}

func (passenger *Passenger) GetQuality() int {
	return cfg.GetQualityByStarLv(passenger.StarLv)
}

func (passenger *Passenger) ToBattleRole() *BattleRole {
	tps := passenger.ToResonance()

	r := &BattleRole{
		Id:      tps.Id,
		Lv:      tps.Level,
		StarLv:  tps.StarLv,
		Talents: tps.Talents,
		Equips:  tps.Equips,
	}
	return r
}
