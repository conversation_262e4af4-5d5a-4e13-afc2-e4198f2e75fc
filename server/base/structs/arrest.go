package structs

import (
	"train/base/cfg"
	"train/base/enum/function_type"
	"train/base/enum/store_type"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func NewArrestModule() *ArrestModule {
	return &ArrestModule{}
}

type ArrestModule struct {
	plr      *Player
	Score    int           `bson:"score"`
	Arrests  []*Arrest     `bson:"arrests"`
	Result   *ArrestResult `bson:"result"`
	Currency int           `bson:"currency"`
}

func (a *ArrestModule) init(plr *Player) {
	a.plr = plr

	store := plr.Store.Get(store_type.ARREST)
	if store != nil && len(store.Goods) <= 0 {
		store.Refresh()
	}
}

// GetOngoingData
/*
 * @description 获取当前能进行的通缉令列表
 * @return []*Arrest
 */
func (a *ArrestModule) GetOngoingData() []*Arrest {
	return lo.Filter(a.Arrests, func(item *Arrest, index int) bool { return !a.IsArrestExpired(item) })
}

func (a *ArrestModule) GetJson() *cfg.ArrestScore[int] {
	datas := cfg.ArrestScoreContainer.GetData()
	index := len(datas)
	for i, data := range datas {
		if data.Id > a.Score {
			index = i
			break
		}
	}
	return datas[index-1]
}

// IsArrestExpired
/*
 * @description 通缉令是否过期
 * @param data
 * @return bool
 */
func (a *ArrestModule) IsArrestExpired(data *Arrest) bool {
	if data == nil || data.State == pb.ArrestState_Finished {
		return true
	}
	return data.ExpirationTime <= a.plr.GetNowTime()
}

func (a *ArrestModule) IsUnlock() bool {
	return a.plr.IsUnlockFunction(function_type.ARREST)
}

// 结算
func (a *ArrestModule) CheckAndUpdateResult() {
	if !a.IsUnlock() {
		return
	}
	a.handleUnClaimRewards()
	//计算上期结果
	a.calcResult()
	a.Refresh()
}

func (a *ArrestModule) Refresh() {
	a.Arrests = make([]*Arrest, 0)
	// 生成新的数据
	scoreBean := a.GetJson()
	tasks := scoreBean.Task
	lvs := make([]int, 0)
	for _, task := range tasks {
		for i := 0; i < task.Count; i++ {
			lvs = append(lvs, task.Lv)
		}
	}
	lvs = ut.RandomArray(lvs)

	for _, lv := range lvs {
		if data := a.Generate(lv); data != nil {
			a.Arrests = append(a.Arrests, data)
		}
	}
}

func (a *ArrestModule) calcResult() {
	arrests := lo.Filter(a.Arrests, func(item *Arrest, index int) bool { return item.State != pb.ArrestState_NotCollected })

	if len(arrests) <= 0 {
		return
	}

	bean := a.GetJson()

	getBeanByCount := func(count int) *cfg.ArrestScoreBattle {
		index := len(bean.Battle)
		for i, battle := range bean.Battle {
			if battle.Count > count {
				index = i
				break
			}
		}
		return bean.Battle[index-1]
	}

	winMap := make(map[int]int)
	failMap := make(map[int]int)
	for _, arrest := range arrests {
		if arrest.State == pb.ArrestState_OnGoing { //未完成
			failMap[arrest.Star]++
		} else {
			winMap[arrest.Star]++
		}
	}

	sum := 0
	for _, arrest := range arrests {
		battleBean := getBeanByCount(arrest.Battle.RoleCnt)
		if battleBean.Count == 0 || bean.Lv <= arrest.Star { //不计算小于当前星级的胜利加成
			sum += battleBean.Score
		}
	}

	datas := cfg.ArrestScoreContainer.GetData()
	getMinScoreByLv := func(lv int) int {
		for _, data := range datas {
			if data.Lv == lv {
				return data.Id
			}
		}
		return a.Score
	}
	minScore := getMinScoreByLv(bean.Lv)
	maxScore := getMinScoreByLv(bean.Lv + 1)
	score := lo.Clamp(a.Score+sum, minScore, maxScore)

	wins := lo.MapToSlice(winMap, func(key int, value int) *ArrestResultDetail { return &ArrestResultDetail{Star: key, Count: value} })
	fails := lo.MapToSlice(failMap, func(key int, value int) *ArrestResultDetail { return &ArrestResultDetail{Star: key, Count: value} })
	a.Result = &ArrestResult{Score: score - a.Score, Wins: wins, Fails: fails}
	a.Score = score
}

func (a *ArrestModule) handleUnClaimRewards() {
	arrest := lo.Filter(a.Arrests, func(item *Arrest, index int) bool { return item.State == pb.ArrestState_DoneNoReward })
	for _, arrest := range arrest {
		a.plr.CreateMail("请收下您未领取的通缉奖励~", "", arrest.Rewards)
		arrest.State = pb.ArrestState_Finished
	}
}

// GetRandomPlanet
/*
 * @description 设置通缉令的目标星球id 星球是要求不重复的，如果不够就放在一起了
 * @param data
 */
func (a *ArrestModule) GetRandomPlanet() *Planet {
	ints := lo.Map(a.GetOngoingData(), func(item *Arrest, index int) int { return item.PlanetId })
	ints = append(ints, 1014)
	planet := a.plr.GetRandomReachedPlanet(ints)
	if planet == nil {
		planet = a.plr.GetRandomReachedPlanet(make([]int, 0))
	}
	return planet
}

func (a *ArrestModule) Generate(lv int) *Arrest {
	arrest := &Arrest{
		Id:     GenUid(),
		Battle: &ArrestBattle{},
	}
	// 目标星球
	planet := a.GetRandomPlanet()
	if planet == nil {
		return arrest
	}
	arrest.PlanetId = planet.Id
	misc := cfg.GetMisc()
	// Arrest.ExpirationTime = ut.GetNextWeekRefreshTime(misc.RefreshTime, a.plr.LastWeekUpdateTime)
	arrest.ExpirationTime = a.plr.GetNowTime() + a.plr.GetNextDaySurplusTime()

	// 星级
	arrest.Star = lv
	arrestLevel, _ := cfg.ArrestLevelContainer.GetBeanById(arrest.Star)

	// 奖励
	arrest.Rewards = make([]*Condition, 0)
	rewards := a.plr.GenerateRewards(arrestLevel.Reward, arrestLevel.RdReward)
	arrest.Rewards = append(arrest.Rewards, rewards...)

	// 怪物数据
	enemies := make([]*BattleRole, 0)
	for j := 0; j < 5; j++ {
		monster := a.plr.randomMonster(5, j, arrestLevel.MonsterLv, enemies)
		enemies = append(enemies, monster)
	}
	arrest.Monsters = enemies

	//罪名
	stories := cfg.ArrestContainer.GetData()
	stories = lo.Filter(stories, func(item *cfg.Arrest[int], index int) bool {
		return !array.Some(a.Arrests, func(arrest *Arrest) bool { return arrest.StoryId == item.Id })
	})
	rdIndex := ut.Random(0, len(stories)-1)
	arrest.StoryId = stories[rdIndex].Id

	//线索
	clues := make([]*ArrestClue, 0)
	//地点
	places := misc.Arrest.Place
	rdIndex = ut.RandomIndexByWeight(places, func(item *cfg.ConfigCondition) int { return item.Weight })
	placeType := places[rdIndex].Type
	clue := &ArrestClue{Type: int(pb.ArrestClueType_PLACE), PlaceType: placeType}
	clues = append(clues, clue)

	if placeType == int(pb.ArrestPlaceType_TEXT) {
		planet := a.GetRandomPlanet()
		if ut.Chance(20) && planet.Id != arrest.PlanetId {
			clue.Planets = append(clue.Planets, planet.Id)
		}
	} else if placeType == int(pb.ArrestPlaceType_PIC) {
		clue.IsHideInfo = ut.Chance(20)
	}

	//时间
	times := misc.Arrest.Time
	rdIndex = ut.RandomIndexByWeight(times, func(item *cfg.ConfigCondition) int { return item.Weight })
	timeType := times[rdIndex].Type
	if timeType != int(pb.ArrestTimeType_ALL) {
		clue = &ArrestClue{Type: int(pb.ArrestClueType_TIME), TimeType: timeType}
		clues = append(clues, clue)
	}

	arrest.Clues = clues

	return arrest
}

func (a *ArrestModule) GetArrestById(id string) *Arrest {
	Arrest, _ := lo.Find(a.Arrests, func(item *Arrest) bool {
		return item.Id == id
	})
	return Arrest
}

func (a *ArrestModule) ToPb() *pb.ArrestModule {
	data := &pb.ArrestModule{
		Arrests:  lo.Map(a.Arrests, func(item *Arrest, index int) *pb.Arrest { return item.ToPb() }),
		Score:    cast.ToInt32(a.Score),
		Currency: cast.ToInt32(a.Currency),
	}
	if a.Result != nil {
		data.Result = a.Result.ToPb()
	}
	return data
}

type ArrestResult struct {
	Score int                   `bson:"score"`
	Wins  []*ArrestResultDetail `bson:"wins"`
	Fails []*ArrestResultDetail `bson:"fails"`
}

func (a *ArrestResult) ToPb() *pb.ArrestResult {
	if a == nil {
		return nil
	}
	return &pb.ArrestResult{
		Score: cast.ToInt32(a.Score),
		Wins:  lo.Map(a.Wins, func(item *ArrestResultDetail, index int) *pb.ArrestResultDetail { return item.ToPb() }),
		Fails: lo.Map(a.Fails, func(item *ArrestResultDetail, index int) *pb.ArrestResultDetail { return item.ToPb() }),
	}
}

type ArrestResultDetail struct {
	Star  int `bson:"star"`
	Count int `bson:"count"`
}

func (a *ArrestResultDetail) ToPb() *pb.ArrestResultDetail {
	return &pb.ArrestResultDetail{
		Star:  cast.ToInt32(a.Star),
		Count: cast.ToInt32(a.Count),
	}
}

type Arrest struct {
	Id             string         `bson:"id"`             // id
	ExpirationTime int            `bson:"expirationTime"` // 过期时间
	PlanetId       int            `bson:"planetId"`       // 通缉怪所在星球id
	State          pb.ArrestState `bson:"state"`          // 当前状态
	Star           int            `bson:"star"`           // 星级
	Monsters       []*BattleRole  `bson:"monsters"`       // 怪物数据
	Rewards        []*Condition   `bson:"rewards"`        // 奖励
	StoryId        int            `bson:"storyId"`
	Clues          []*ArrestClue  `bson:"clues"` //线索
	Battle         *ArrestBattle  `bson:"battle"`
}

func (a *Arrest) ToPb() *pb.Arrest {
	return &pb.Arrest{
		Id:             a.Id,
		ExpirationTime: 0,
		PlanetId:       int32(a.PlanetId),
		State:          a.State,
		Star:           int32(a.Star),
		StoryId:        int32(a.StoryId),
		Monsters:       lo.Map(a.Monsters, func(item *BattleRole, index int) *pb.BattleRole { return item.ToPb() }),
		Rewards:        lo.Map(a.Rewards, func(item *Condition, index int) *pb.Condition { return item.ToPb() }),
		Clues:          lo.Map(a.Clues, func(item *ArrestClue, index int) *pb.ArrestClue { return item.ToPb() }),
	}
}

type ArrestClue struct {
	Type       int   `bson:"type"`
	Planets    []int `bson:"planets"` // 星球id
	TimeType   int   `bson:"timeType"`
	PlaceType  int   `bson:"placeType"`
	IsHideInfo bool  `bson:"isHideInfo"`
}

func (a *ArrestClue) ToPb() *pb.ArrestClue {
	return &pb.ArrestClue{
		Type:       pb.ArrestClueType(a.Type),
		Planets:    ut.ToInt32(a.Planets),
		TimeType:   pb.ArrestTimeType(a.TimeType),
		PlaceType:  pb.ArrestPlaceType(a.PlaceType),
		IsHideInfo: a.IsHideInfo,
	}
}

type ArrestBattle struct {
	FailCnt int `bson:"failCnt"`
	RoleCnt int `bson:"roleCnt"` //存活人数
}
