package structs

import (
	"train/base/enum"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type NewMark struct {
	TypeId int   `bson:"typeId"` //类型id
	AryVal []int `bson:"aryVal"` //一组数据
}

func getTypeLength(typeId int) int {
	switch typeId {
	case enum.MARKNEW_THEME:
		return 2
	default:
		return 1
	}
}
func isEqual(ary []int, i int, aryVal []int) bool {
	for j := 0; j < len(aryVal); j++ {
		if ary[i+j] != aryVal[j] {
			return false
		}
	}
	return true
}
func createNewMark(typeId int, aryVal []int) *NewMark {
	return &NewMark{
		TypeId: typeId,
		AryVal: aryVal,
	}
}

func (this *NewMark) toPb() *pb.NewMarkInfo {
	return &pb.NewMarkInfo{
		TypeId: cast.ToInt32(this.TypeId),
		AryVal: ut.ToInt32(this.AryVal),
	}
}
func (this *NewMark) pushArr(aryVal []int) {
	this.AryVal = append(this.AryVal, aryVal...)
}
func (this *NewMark) removeArr(aryVal []int) bool {
	num := getTypeLength(this.TypeId)
	idx := this.getIndex(num, aryVal)
	if idx < 0 {
		return false
	}
	this.AryVal = array.Splice(this.AryVal, idx, num)
	return true
}
func (this *NewMark) getIndex(num int, aryVal []int) int {
	ary := this.AryVal
	for i := 0; i < len(ary); i += num {
		if isEqual(ary, i, aryVal) {
			return i
		}
	}
	return -1
}

func (plr *Player) ToPbNewMarkList() []*pb.NewMarkInfo {
	temp := make([]*pb.NewMarkInfo, 0)
	lo.ForEach(plr.NewMarkList, func(info *NewMark, index int) {
		temp = append(temp, info.toPb())
	})
	return temp
}
func (plr *Player) GetNewMark(typeId int) *NewMark {
	return array.Find(plr.NewMarkList, func(item *NewMark) bool {
		return item.TypeId == typeId
	})
}
func (plr *Player) PushNew(typeId int, aryVal []int) {
	num := getTypeLength(typeId)
	if num != len(aryVal) {
		log.Error("pushNew error typeId: %d", typeId)
		return
	}
	item := plr.GetNewMark(typeId)
	if item == nil {
		item = createNewMark(typeId, aryVal)
		plr.NewMarkList = append(plr.NewMarkList, item)
		return
	}
	if item.getIndex(num, aryVal) >= 0 {
		return
	}
	item.pushArr(aryVal)
}
func (plr *Player) RemoveNew(typeId int, aryVal []int) {
	item := plr.GetNewMark(typeId)
	if item == nil {
		return
	}
	if !item.removeArr(aryVal) {
		return
	}
	if len(item.AryVal) > 0 {
		return
	}
	plr.NewMarkList = array.Remove(plr.NewMarkList, item)
}
