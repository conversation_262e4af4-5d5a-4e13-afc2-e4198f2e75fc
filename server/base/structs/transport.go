package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum/function_type"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

const StartPlanet = 1007

var RARE_LANGS = []string{
	"transport_desc_rare_text_1",
	"transport_desc_rare_text_2",
}
var NORMAL_LANGS = []string{
	"transport_desc_text_1",
	"transport_desc_text_2",
	"transport_desc_text_3",
	"transport_desc_text_4",
}

func NewTransportModule() *TransportModule {
	return &TransportModule{}
}

type TransportModule struct {
	Exp        int              `bson:"exp"`        // 运送经验值
	List       []*TransportData `bson:"next"`       // 当前运送列表数据
	QuickBack  bool             `bson:"quickBack"`  // 能否快速返回雪星
	MonsterIdx int              `bson:"monsterIdx"` //
	plr        *Player          `bson:"-"`
	maxLv      int              `bson:"-"`
}

func (t *TransportModule) init(plr *Player) {
	t.plr = plr
	for _, item := range t.List {
		item.init(plr)
	}
}

// GetMaxLv 运送最高等级
//
// Returns:
//   - int
func (t *TransportModule) GetMaxLv() int {
	if t.maxLv == 0 {
		// 检查提升难度
		lvData := cfg.TransportLevelContainer.GetData()
		maxBy := lo.MaxBy(lvData, func(a *cfg.TransportLevel[int], b *cfg.TransportLevel[int]) bool {
			return a.Id > b.Id
		})
		t.maxLv = maxBy.Id
	}
	return t.maxLv
}

// GetLv 获取当前等级
//
// Returns:
//   - int
func (t *TransportModule) GetLv() int {
	if t.Exp == 0 {
		return 1
	}
	lvData := cfg.TransportLevelContainer.GetData()
	lv := 1
	exp := t.Exp
	for _, data := range lvData {
		if exp < data.Exp {
			break
		}
		exp -= data.Exp
		lv = data.Id
	}
	return lv
}

// CheckLoadIsValid 检查载重是否合法
//
// Parameters:
//   - add int 要增加的载重
//
// Returns:
//   - bool
func (t *TransportModule) CheckLoadIsValid(add int) bool {
	max := t.plr.Train.GetLoad()
	sum := 0
	lo.ForEach(t.List, func(item *TransportData, index int) {
		if item.State == pb.TransportDataState_Pull {
			sum += item.Load
		}
	})

	return sum+add <= max
}

// GetBattleData 获取已经领取的稀有任务 如果有的话
//
// Returns:
//   - *TransportData
func (t *TransportModule) GetBattleData(planetId int) *TransportData {
	data, _ := lo.Find(t.List, func(item *TransportData) bool {
		return item.End == planetId && item.State == pb.TransportDataState_Pull
	})
	return data
}

func (t *TransportModule) CheckPlanetMoveDone() {
	Planet := t.plr.PlanetData
	lo.ForEach(t.List, func(item *TransportData, index int) {
		if item.State != pb.TransportDataState_Pull {
			return
		}
		if item.End == Planet.CurPlanetId {
			// 如果是航行到护送任务的目标星球 标记成可以完成（未领取奖励）
			item.State = pb.TransportDataState_Done
		}
	})
}

func (t *TransportModule) CheckAndRefresh() {
	if t.IsUnlock() {
		t.CheckAndCompleteTransport()
		t.Refresh()
	}
}

func (t *TransportModule) Unlock() {
	t.plr.Transport.Refresh()
}

// Refresh
/*
 * @description 刷新数据
 * @param force 是否强制刷新，force只刷没被领取和没被完成的任务
 */
func (t *TransportModule) Refresh() {
	t.List = make([]*TransportData, 0)

	transportDefine := cfg.Misc_CContainer.GetObj().Transport
	cnt := transportDefine.Count

	lv := t.GetLv()
	// 必定刷出一个当前货运等级
	bean, _ := cfg.TransportLevelContainer.GetBeanById(lv)

	// 稀有订单处理
	data := cfg.TransportMonsterContainer.GetData()
	usedPlanetIds := make([]int, 0)
	usedKey := make([]string, 0)
	temp, usedPlanetIds := t.TransportDataGenerate(bean, usedPlanetIds, true)
	temp.Key, usedKey = RandomLangKey(true, usedKey)
	monster := data[t.MonsterIdx].Monster
	temp.GenerateTransportBattleData(monster)
	t.List = append(t.List, temp)

	// 必刷一个本级
	temp, usedPlanetIds = t.TransportDataGenerate(bean, usedPlanetIds, false)
	temp.Key, usedKey = RandomLangKey(false, usedKey)

	t.List = append(t.List, temp)
	cnt -= 1
	dMap := map[int]int{
		0:  transportDefine.D0,
		-1: transportDefine.D1,
		-2: transportDefine.D2,
	}
	// 随机刷出其他
	for i := 0; i < cnt; i++ {
		key := ut.RandomIndexByWeightMap(dMap, func(k int, v int) int { return v })
		tmpLv := ut.Max(1, lv+key)
		bean, _ := cfg.TransportLevelContainer.GetBeanById(tmpLv)
		temp, usedPlanetIds = t.TransportDataGenerate(bean, usedPlanetIds, false)
		temp.Key, usedKey = RandomLangKey(false, usedKey)

		t.List = append(t.List, temp)
	}

	//时间之钥
	if t.MonsterIdx >= 1 {
		if !t.plr.HasTimeStoneKey(item_id.TIME_STONE_KEY_3) {
			temp, _ = t.TransportDataGenerate(bean, usedPlanetIds, true)
			temp.Key = "transport_desc_key_text_1"
			temp.GenTimeStoneBoss()
			temp.Rewards = []*Condition{}
			temp.FixedRewards = []*Condition{
				{
					Type:   condition.PROP,
					Id:     item_id.TIME_STONE_KEY_3,
					Num:    1,
					IsHide: true,
				},
			}
			temp.Rare = false
			temp.TimeStoneKey = true
			t.List = array.Splice(t.List, 1, 0, temp)
		}
	}
}

func (t *TransportModule) IsUnlock() bool {
	return t.plr.IsUnlockFunction(function_type.TRANSPORT)
}

// ClearNowTransport
/*
 * @description 清理并刷新当前进行中的任务
 * @return *TransportData
 */
func (t *TransportModule) RefreshTransport() *TransportData {
	transportData, index, _ := lo.FindIndexOf(t.List, func(item *TransportData) bool {
		return item.State == pb.TransportDataState_Pull || item.State == pb.TransportDataState_Done || item.State == pb.TransportDataState_Over
	})
	t.List[index], _ = t.TransportDataGenerate(transportData.GetJson(), nil, false)
	return t.List[index]
}

// ToPb 转换为pb数据
//
// Returns:
//   - *pb.Transport
func (t *TransportModule) ToPb() *pb.Transport {
	return &pb.Transport{
		Exp:  int32(t.Exp),
		List: lo.Map(t.List, func(item *TransportData, index int) *pb.TransportData { return item.ToPb() }),
	}
}

// TransportDataGenerate 使用cfg.TransportLevel生成TransportData
//
// Parameters:
//   - lv *cfg.TransportLevel
//   - plr *Player
//   - excludeMapIds []int 需要排除的星球id
//   - rare bool 是不是稀有任务
//
// Returns:
//   - *TransportData
//   - []int 排出的星球id
func (t *TransportModule) TransportDataGenerate(lv *cfg.TransportLevel[int], excludeMapIds []int, rare bool) (*TransportData, []int) {
	plr := t.plr
	ins := &TransportData{plr: plr}
	ins.Rare = rare
	ins.StarLv = lv.Id
	if rare {
		// 稀有订单 难度加一
		datas := cfg.TransportMonsterContainer.GetData()
		data := datas[t.MonsterIdx]
		ins.StarLv = data.Lv + 1
		lv, _ = cfg.TransportLevelContainer.GetBeanById(ins.StarLv)
	}

	passengers := cfg.CharacterContainer.GetData()
	passengers = lo.Filter(passengers, func(item *cfg.Character[int], index int) bool {
		return !array.Some(plr.Transport.List, func(transport *TransportData) bool {
			return transport.Actor == item.Id
		})
	})
	idx := ut.Random(0, len(passengers)-1)
	ps := passengers[idx]
	ins.Actor = ps.Id

	if !rare {
		// 非稀有重量是三分之一 到 1 随机
		ins.Load = ut.Random(ut.Round(float64(lv.Load)/3), lv.Load)
	} else {
		ins.Load = lv.Load
	}
	// 起点都是雪星
	ins.Start = StartPlanet
	if excludeMapIds == nil {
		excludeMapIds = make([]int, 0)
	}
	// 先走全部排除
	planet := plr.GetRandomReachedPlanet(append(excludeMapIds, ins.Start))
	if planet == nil {
		// 找不到就保底随机一个除开起点的星球
		planet = plr.GetRandomReachedPlanet([]int{ins.Start})
	}
	ins.End = planet.Id
	ins.Rewards = plr.GenerateRewards(lv.Reward, lv.RdReward)

	excludeMapIds = append(excludeMapIds, ins.End)
	return ins, excludeMapIds
}

type TransportData struct {
	Actor        int                      `bson:"actor"`        // 委托人
	Key          string                   `bson:"key"`          // 委托对话
	StarLv       int                      `bson:"starLv"`       // 难度
	Start        int                      `bson:"start"`        // 起点
	End          int                      `bson:"end"`          // 终点
	Rewards      []*Condition             `bson:"rewards"`      // 奖励
	FixedRewards []*Condition             `bson:"fixRewards"`   // 额外必得奖励
	State        pb.TransportDataState    `bson:"state"`        // 状态
	BattleData   *TransportBattleData     `bson:"battleData"`   // 当前战斗数据
	Rare         bool                     `bson:"rare"`         // 是不是稀有任务
	TimeStoneKey bool                     `bson:"timeStoneKey"` // 是否是时间宝石钥匙
	Load         int                      `bson:"load"`         // 货物重量
	json         *cfg.TransportLevel[int] `bson:"-"`
	plr          *Player                  `bson:"-"`
}

func (t *TransportData) init(plr *Player) {
	t.plr = plr
}

func (t *TransportData) IsCanRefresh() bool {
	return false
}

// GetJson
/*
 * @description 获取配置
 * @return *cfg.TransportLevel
 */
func (t *TransportData) GetJson() *cfg.TransportLevel[int] {
	if t.json == nil {
		t.json, _ = cfg.TransportLevelContainer.GetBeanById(t.StarLv)
	}
	return t.json
}

// GetBattleDataByIndex
/*
 * @description 通过索引获取战斗数据
 * @param index
 * @return *TransportBattleData
 */
func (t *TransportData) GetBattleDataByIndex() *TransportBattleData {
	return t.BattleData
}

// ClearBattleData
/*
 * @description 清空战斗数据
 */
func (t *TransportData) ClearBattleData() {
	t.BattleData = nil
}

// CheckAllPass
/*
 * @description 检查是战斗否全部通过
 * @return bool
 */
func (t *TransportData) CheckAllPass() bool {
	if t.BattleData == nil {
		t.State = pb.TransportDataState_Done
		return true
	}
	return false
}

// GenerateTransportBattleData
/*
 * @description 生成护送战斗数据
 * @param start 起点星球
 * @param end 终点星球
 * @return []*pb.TransportBattleData
 */
func (t *TransportData) GenerateTransportBattleData(monster []*cfg.Monster) *pb.TransportBattleData {
	t.ClearBattleData()
	start := t.Start
	end := t.End
	// 遇怪判断
	startBean, _ := cfg.PlanetMoveTimeContainer.GetBeanById(start)
	// 航行时间4倍
	cost := startBean.Time[end] * cfg.Misc_CContainer.GetObj().Transport.TimeRate
	enemies := make([]*BattleRole, 0)
	lo.ForEach(monster, func(item *cfg.Monster, index int) {
		enemies = append(enemies, &BattleRole{
			Id:     item.Id,
			Lv:     item.Level,
			StarLv: item.StarLv,
		})
	})
	l := ut.Round(cast.ToFloat64(cost) * 0.3)
	r := ut.Round(cast.ToFloat64(cost) * 0.5)
	t.BattleData = &TransportBattleData{
		Monsters: enemies,
		Second:   ut.Random(l, r),
	}
	return t.BattleData.ToPb()
}

func (t *TransportData) GenTimeStoneBoss() *pb.TransportBattleData {
	t.ClearBattleData()
	monsters := array.Find(cfg.TimeStoneBossContainer.GetData(), func(item *cfg.TimeStoneBoss[int]) bool {
		return item.Type == function_type.TRANSPORT
	}).Monster
	return t.GenerateTransportBattleData(monsters)
}

// UpdateState
/*
 * @description 更新状态
 */
func (t *TransportData) UpdateState() {
	conditions := lo.Filter(t.Rewards, func(c *Condition, i int) bool {
		return c.Id != item_id.TrainMoveStone && c.Num > 0
	})
	conditions = append(conditions, lo.Filter(t.FixedRewards, func(c *Condition, i int) bool {
		return c.Id != item_id.TrainMoveStone && c.Num > 0
	})...)
	if len(conditions) == 0 {
		t.State = pb.TransportDataState_Failed
	}
}

// randomMonster
/*
 * @description
 * @param tot 总共随机多少个怪物
 * @param index 怪物所在站位位置
 * @param lv 怪物的等级
 */
func (t *TransportData) randomMonster(tot, index, lv int, monsters []*BattleRole) *BattleRole {
	monsterDatas := cfg.PlanetMonsterContainer.GetData()
	noMonsters := GetNoMonsters(tot, index, lv)
	monsterDatas = lo.Filter(monsterDatas, func(data *cfg.PlanetMonster[int], i int) bool {
		if array.Some(monsters, func(m *BattleRole) bool { return m != nil && m.Id == data.ID }) {
			return false
		}
		if array.Has(noMonsters, data.ID) {
			return false
		}
		return true
	})

	rdIndex := ut.Random(0, len(monsterDatas)-1)
	monster := monsterDatas[rdIndex]
	return &BattleRole{
		Id:     monster.ID,
		StarLv: cfg.GetMonsterStarByLevel(lv),
		Lv:     lv,
	}
}

// ToPb
/*
 * @description 转换为pb数据
 * @return *pb.TransportData
 */
func (t *TransportData) ToPb() *pb.TransportData {
	if t == nil {
		return nil
	}
	return &pb.TransportData{
		StarLv:       int32(t.StarLv),
		Start:        int32(t.Start),
		End:          int32(t.End),
		Rewards:      lo.Map(t.Rewards, func(t *Condition, i int) *pb.Condition { return t.ToPb() }),
		FixRewards:   lo.Map(t.FixedRewards, func(t *Condition, i int) *pb.Condition { return t.ToPb() }),
		State:        t.State,
		Load:         int32(t.Load),
		Rare:         t.Rare,
		Actor:        int32(t.Actor),
		Key:          t.Key,
		BattleData:   t.BattleData.ToPb(),
		TimeStoneKey: t.TimeStoneKey,
	}
}

type TransportBattleData struct {
	Monsters []*BattleRole `bson:"monsters"` // 怪物数据
	Second   int           `bson:"second"`   // 航行剩余sec时间时遇怪
	plr      *Player       `bson:"-"`
}

func (t *TransportBattleData) init(plr *Player) {
	t.plr = plr
}

// ToPb
/*
 * @description 转换为pb数据
 * @return *pb.TransportBattleData
 */
func (t *TransportBattleData) ToPb() *pb.TransportBattleData {
	if t == nil {
		return nil
	}
	return &pb.TransportBattleData{
		Monsters: lo.Map(t.Monsters, func(t *BattleRole, i int) *pb.BattleRole { return t.ToPb() }),
		Second:   int32(t.Second),
	}
}

// RandomLangKey 随机委托语言key
//
// Parameters:
//   - rare bool
//   - exclude []string 需要排除的key
//
// Returns:
//   - string 随机的key
//   - []string 新的排除的key列表
func RandomLangKey(rare bool, exclude []string) (string, []string) {
	var ary []string
	if rare {
		ary, _ = lo.Difference(RARE_LANGS, exclude)
	} else {
		ary, _ = lo.Difference(NORMAL_LANGS, exclude)
	}
	if len(ary) == 0 {
		return NORMAL_LANGS[ut.Random(0, len(NORMAL_LANGS)-1)], exclude
	}
	key := ary[ut.Random(0, len(ary)-1)]
	exclude = append(exclude, key)
	return key, exclude
}

func (t *TransportModule) CheckAndCompleteTransport() {
	if t == nil {
		return
	}

	rewards := []*Condition{}
	// 获取所有已完成但未领取奖励的任务
	for _, transport := range t.plr.Transport.List {
		if transport.State == pb.TransportDataState_Done {
			// 收集所有奖励
			rewards = append(rewards, transport.Rewards...)
			rewards = append(rewards, transport.FixedRewards...)
			// 更新状态为已完成
			transport.State = pb.TransportDataState_Over

		}
	}
	// 如果有未领取的奖励，通过邮件发送
	if len(rewards) > 0 {
		rewards = MergeConditions([]*Condition{}, rewards)
		t.plr.CreateMail("请收下您昨日未领取的运送奖励~", "", rewards)
	}
}
