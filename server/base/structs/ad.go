package structs

import (
	"train/base/cfg"
	"train/common/pb"
	ut "train/utils"

	"github.com/spf13/cast"
)

func NewAdModule() *AdModule {
	return &AdModule{}
}

type AdModule struct {
	Data map[pb.AdType]int `bson:"data"`
	plr  *Player           `bson:"-" json:"-"`
}

func (a *AdModule) init(plr *Player) {
	a.plr = plr
	if a.Data == nil {
		a.Data = make(map[pb.AdType]int)
	}

}

func (a *AdModule) ToPb() *pb.Ad {
	data := make(map[int32]int32)
	for k, v := range a.Data {
		data[int32(k)] = cast.ToInt32(v)
	}
	return &pb.Ad{
		Data: data,
	}
}

// 重置广告数据
func (a *AdModule) CheckReset(resetType string) {
	ary := cfg.Misc_CContainer.GetObj().Ad
	for k, _ := range a.Data {
		it := ary[k]
		if it != nil && it.ResetType == resetType {
			a.Data[k] = 0
		}
	}
}

// 周期内 广告最大次数
func (a *AdModule) GetMaxTimes(adType pb.AdType) int {
	ary := cfg.Misc_CContainer.GetObj().Ad
	if ut.IsIndexOutOfBounds(ary, int(adType)) {
		return 0
	}
	it := ary[adType]
	if it == nil {
		return 0
	}
	return it.Count
}

// 周期内 广告剩余次数
func (a *AdModule) GetRemainTimes(adType pb.AdType) int {
	return a.GetMaxTimes(adType) - a.Data[adType]
}
func (a *AdModule) DeductTimes(adType pb.AdType) {
	a.Data[adType] = a.Data[adType] + 1
}
