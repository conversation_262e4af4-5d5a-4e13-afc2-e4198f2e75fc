package structs

import (
	"context"
	"fmt"
	"time"
	"train/base/enum"
	comm "train/common"
	"train/common/ta"
	"train/db"
	ut "train/utils"

	"github.com/golang-jwt/jwt/v4"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// User 用户基础信息
type User struct {
	Session       gate.Session `bson:"-"`             // 会话通道
	Uid           *ut.MongoId  `bson:"-"`             // 用户id
	CreateTime    int          `bson:"createTime"`    // 创建时间
	UserType      string       `bson:"userType"`      // 用户类型，用什么方式登录
	IdCard        string       `bson:"idCard"`        // 身份证号码
	RealName      string       `bson:"realName"`      // 真实姓名
	LastLoginTime int          `bson:"lastLoginTime"` // 上次登录时间
	InviteUid     string       `bson:"inviteUid"`     // 邀请者
	IsSignOuting  bool         `bson:"isSignOuting"`  // 是否处于注销状态
	SignOutTime   int          `bson:"signOutTime"`   // 注销结束时间

	WxAppOpenid string `bson:"wxAppOpenid"` // 微信app openid
	Openid      string `bson:"openid"`      // 微信openid
	UserName    string `bson:"username"`    // 用户名/第三方平台关联id
	Password    string `bson:"password"`    // 密码

	NickName  string `bson:"nickName"`  // 第三方游戏名
	AvatarUrl string `bson:"avatarUrl"` // 第三方头像  不可改变

	// Platform   string `bson:"-"` // 平台 小程序/android/ios
	// Os         string `bson:"-"` // 操作系统 android/ios
	// DistinctId string `bson:"-"` //访客id 就是前端设备id
	// Ver        string `bson:"-"` //游戏版本

}

// Bind 绑定
func (this *User) Bind(session gate.Session) {
	if this.Session == session {
		return
	}
	this.Session = session
	this.Session.Bind(this.GetUid())
	this.Session.Push()
}

// GetAge 获取年龄
func (this *User) GetAge() int32 {
	return cast.ToInt32(comm.GetAge(this.IdCard))
}

func (this *User) GetUid() string {
	if this == nil {
		return ""
	}
	if this.Uid == nil {
		return ""
	}
	return this.Uid.Get()
}

// SaveToDb  更新用户数据到数据库 现在是即时更新，后面需要优化为阻塞队列更新
func (this *User) SaveToDb(v bson.M) error {
	if len(v) == 0 {
		log.Warning("user:%s - SaveToDb,but v is empty :%v", this.GetUid(), v)
		return nil
	}
	sTime := time.Now()
	_, err := db.USER.GetCollection().UpdateOne(context.TODO(), &bson.M{
		"_id": this.Uid.ObjectId(),
	}, &bson.M{
		"$set": &v,
	}, options.Update().SetUpsert(false))
	if err != nil {
		log.Error("user:%s - SaveToDb:%v", this.GetUid(), err.Error())
	}
	log.Debug("user:%s - SaveToDb ,during :%fs", this.GetUid(), time.Since(sTime).Seconds())
	return err
}

// NewUser 创建新用户 用户名重复判断:mongo.IsDuplicateKeyError(err)
func NewUser(u *User) *User {
	u.CreateTime = ut.Now()
	if u.NickName == "" {
		str := cast.ToString(ut.Now())
		u.NickName = fmt.Sprintf("新玩家%s", str[len(str)-5:])
	}
	// 插入数据库
	result, err := db.USER.GetCollection().InsertOne(context.TODO(), u)
	if err != nil {
		panic(err)
	}
	id := result.InsertedID.(primitive.ObjectID)
	u.Uid, err = ut.NewMongoIdFrom(id.Hex())
	if err != nil {
		panic(err)
	}
	log.Info("[%s] signup: %s", u.GetUid(), u.UserName)
	return u
}

// GetUserFromDbById 尝试从数据库中根据uid获取一个user,可能返回nil
func GetUserFromDbById(uid string) *User {
	if ut.IsEmpty(uid) {
		return nil
	}
	// 将id序列化为MongoId
	id, _ := ut.NewMongoIdFrom(uid)
	if id == nil {
		return nil
	}
	user := &User{
		Uid: id,
	}
	err := db.USER.GetCollection().FindOne(context.TODO(), &bson.M{
		"_id": id.ObjectId(),
	}).Decode(user)
	if err != nil {
		return nil
	}
	return user
}

// GetUserFromDbByAccount 尝试从数据库中根据用户名和密码获取一个user,可能返回nil
func GetUserFromDbByAccount(username, password string) *User {
	if ut.IsEmpty(username) || ut.IsEmpty(password) {
		return nil
	}
	user := &User{}

	res := db.USER.GetCollection().FindOne(context.TODO(), &bson.M{
		"username": username,
		"password": password,
	})
	var result bson.M
	err := res.Decode(&result)
	_ = res.Decode(user)
	if err != nil {
		return nil
	}
	oid := result["_id"].(primitive.ObjectID)
	user.Uid, _ = ut.NewMongoIdFrom(oid.Hex())
	return user
}

// GetUserFromDbByUserName 使用第三方id查询用户
func GetUserFromDbByUserName(username string) *User {
	user := &User{}
	res := db.USER.GetCollection().FindOne(context.TODO(), &bson.M{
		"username": username,
	})
	var result bson.M
	err := res.Decode(&result)
	_ = res.Decode(user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil
		}
		panic(err)
	}
	oid := result["_id"].(primitive.ObjectID)
	user.Uid, _ = ut.NewMongoIdFrom(oid.Hex())
	return user
}

// TryUpdateUserCertification 尝试更新用户的实名认证信息 code=0时代表认证成功，age表示年龄
func TryUpdateUserCertification(uid, realName, idCard string) (age, code int) {
	user := GetUserFromDbById(uid)
	if user == nil {
		return 0, -5
	}
	age, code = comm.IdentityVerify(uid, realName, idCard)
	if code != 0 {
		return
	}
	log.Info("实名认证完成，写入数据库。")
	// 实名认证存入数据库
	user.RealName = realName
	user.IdCard = idCard
	toBson := ut.FieldToBsonAuto(user)
	user.SaveToDb(toBson)
	return
}

// TaTrackRegister 数数事件 ta-register
func (this *User) TaTrackRegister() {
	if this.Session == nil {
		log.Warning("数数事件 ta-register 无法上报, 因为Session不存在.")
		return
	}
	distanceId := this.Session.Get(enum.DistanceId)
	ta.Track(this.GetUid(), distanceId, ta.Register, map[string]interface{}{
		"register_time": this.CreateTime,
		"type":          this.UserType,
		"invite_uid":    this.InviteUid,
	})
}

// EnterSignOut 进入注销状态
func (this *User) EnterSignOut() {
	this.IsSignOuting = true
	// 注销有效期7天
	this.SignOutTime = cast.ToInt(time.Now().Add(time.Hour*24*7).UnixNano() / 1e6)
	this.SaveSignOutData()
}

// CancelSignOut 取消注销状态
func (this *User) CancelSignOut() {
	this.IsSignOuting = false
	this.SignOutTime = 0
	this.SaveSignOutData()
}

func (this *User) SaveSignOutData() {
	toBson := ut.FieldToBsonAuto(this)
	this.SaveToDb(toBson)
}

func (this *User) GenToken() string {
	return comm.GetTokenByRsa(&jwt.MapClaims{
		"exp": time.Now().Add(enum.LoginTokenDuringTime).Unix(),
		"id":  this.GetUid(),
	})
}
