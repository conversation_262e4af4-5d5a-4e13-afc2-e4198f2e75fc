package structs

import (
	"train/base/cfg"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type BattleSkillEffect struct {
	Type        string
	Times       int //剩余使用次数， -1代表无限
	ValueType   string
	Value       float64
	Repeats     int
	PerObjType  string
	PerAttrType string
	Pro         int
}

func (this *BattleSkillEffect) Init(effect *cfg.BattleSkillEffect) {
	this.Type = effect.Type
	this.Times = ut.If(effect.Times > 0, effect.Times, -1)
	this.ValueType = effect.ValueType
	this.Value = effect.Value
	this.Repeats = ut.If(effect.Repeats > 0, effect.Repeats, 1)
	this.PerObjType = effect.PerObjectType
	this.PerAttrType = effect.PerAttributeType
	this.Pro = ut.If(effect.Prob > 0, effect.Prob, 100)
}

func (this *BattleSkillEffect) CanUse() bool {
	return this.Times == -1 || this.Times > 0
}
func (this *BattleSkillEffect) Use() bool {
	succ := this.CanUse()
	if succ {
		this.Times--
	}
	return succ
}

type BattleSkill struct {
	Id int
	Lv int

	Trigger *cfg.BattleSkillTrigger
	Object  []*cfg.BattleSkillObject
	Effects []*BattleSkillEffect
}

func (this *BattleSkill) Init(id int, lv int) {
	this.Id = id
	this.Lv = lv

	bean, _ := cfg.BattleSkillContainer.GetBeanByUnique(id)

	this.Trigger = bean.Trigger
	this.Object = bean.Object
	this.Effects = lo.Map(bean.Effect, func(e *cfg.BattleSkillEffect, i int) *BattleSkillEffect {
		effect := &BattleSkillEffect{}
		effect.Init(e)
		return effect
	})
}

func (this *BattleSkill) CanUse() bool {
	return array.Some(this.Effects, func(e *BattleSkillEffect) bool { return e.CanUse() })
}

// export default class BattleSkill extends Skill {
//     protected cfg: BattleSkillCfg

//     public trigger: BattleSkillTriggerType = null
//     public object: BattleSkillObject = null
//     public effects: BattleSkillEffect[] = []

//     public getCfg() { return this.cfg }
//     public getType() { return SkillType.BATTLE }

//     public init(id: number, lv: number = 1) {
//         super.init(id, lv);
//         let cfg = this.getCfg();
//         if (!cfg) {
//             if (lv != 0) {
//                 twlog.error("BattleSkill not found", id, lv)
//             }
//             return
//         }
//         let { trigger, object, effect } = cfg
//         return this.initData(trigger, object, effect)
//     }

//     public levelUp(val: number = 1) {
//         this.level += val;
//         this.init(this.skillId, this.level)
//     }

//     public initData(trigger, object, effects: any[]) {
//         this.trigger = trigger
//         this.object = new BattleSkillObject().init(object)
//         this.effects = effects.filter(e => e.type !== '').map(e => new BattleSkillEffect().init(e))
//         return this
//     }

//     public canUse() {
//         return this.effects.some(e => e.canUse())
//     }

//     public use() {
//         this.effects.forEach(e => e.use())
//     }

//     public getDesc(isBattleShow: boolean = false): Array<{ key: string, params: any[] }> {
//         return gameHelper.getSkillDesc(this.getId(), this.getLevel(), isBattleShow)
//     }
// }

type BattleRole struct {
	Uid      string             `bson:"uid"`
	Id       int                `bson:"id"`
	Lv       int                `bson:"lv,omitempty"`
	StarLv   int                `bson:"starLv,omitempty"`
	Hp       int                `bson:"hp,omitempty"`
	Attack   int                `bson:"attack,omitempty"`
	Talents  []*PassengerTalent `bson:"talents,omitempty"`
	Equips   []*EquipItem       `bson:"equips,omitempty"`
	AttrRate float64            `bson:"attrRate,omitempty"`
	// Skill *BattleSkill `bson:"-"`
}

func (this *BattleRole) ToPb() *pb.BattleRole {
	return &pb.BattleRole{
		Uid:      this.Uid,
		Id:       cast.ToInt32(this.Id),
		Lv:       cast.ToInt32(this.Lv),
		StarLv:   cast.ToInt32(this.StarLv),
		Hp:       cast.ToInt32(this.Hp),
		Attack:   cast.ToInt32(this.Attack),
		Equips:   lo.Map(this.Equips, func(equip *EquipItem, i int) *pb.EquipItem { return equip.ToPb() }),
		Talents:  lo.Map(this.Talents, func(talent *PassengerTalent, i int) *pb.PassengerTalent { return talent.ToPb() }),
		AttrRate: this.AttrRate,
	}
}

func (this *BattleRole) FromPb(r *pb.BattleRole) {
	this.Uid = r.Uid
	this.Id = cast.ToInt(r.Id)
	this.Lv = cast.ToInt(r.Lv)
	this.Hp = cast.ToInt(r.Hp)
	this.Attack = cast.ToInt(r.Attack)
}

type BattleTeam struct {
	Id int `bson:"id"`
	// roles []*BattleRole `bson:"roles"`
	Uids []string `bson:"uids"`
}

func (this *BattleTeam) ToPb() *pb.BattleTeam {
	return &pb.BattleTeam{Id: int32(this.Id), Uids: this.Uids}
}

type Battle struct {
	Teams []*BattleTeam `bson:"teams"`
	plr   *Player       `bson:"-"`
}

func NewBattle() *Battle {
	return &Battle{}
}

func (this *Battle) Init(plr *Player) {
	this.plr = plr
}

func (this *Battle) SetTeam(id int, uids []string) {
	team := this.GetTeam(id)
	if team == nil {
		team = &BattleTeam{Id: id}
		this.Teams = append(this.Teams, team)
	}
	team.Uids = uids
}

// func (this *Battle) SetTeam(id int, roles []*BattleRole) {
// 	team := this.GetTeam(id)
// 	if team == nil {
// 		team = &BattleTeam{Id: id}
// 		this.teams = append(this.teams, team)
// 	}
// 	team.roles = roles
// }

func (this *Battle) GetTeam(Id int) *BattleTeam {
	return array.Find(this.Teams, func(t *BattleTeam) bool { return t.Id == Id })
}

func (this *Battle) ToPb() *pb.Battle {
	return &pb.Battle{
		Teams: lo.Map(this.Teams, func(t *BattleTeam, i int) *pb.BattleTeam { return t.ToPb() }),
	}
}
