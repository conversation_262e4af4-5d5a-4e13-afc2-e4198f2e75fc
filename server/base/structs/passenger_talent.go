package structs

import (
	"train/base/cfg"
	"train/common/pb"

	"github.com/spf13/cast"
)

type PassengerTalent struct {
	Id    int                  `bson:"id"`
	Level int                  `bson:"level"`
	json  *cfg.TalentAttr[int] `bson:"-"`
}

func (t *PassengerTalent) GetType() int {
	return t.GetJson().Type
}

func (t *PassengerTalent) GetTarget() int {
	return t.GetJson().Target
}

func (t *PassengerTalent) GetJson() *cfg.TalentAttr[int] {
	if t.json == nil {
		t.json, _ = cfg.TalentAttrContainer.GetBeanById(t.Id)
	}
	return t.json
}

func (t *PassengerTalent) ToPb() *pb.PassengerTalent {
	return &pb.PassengerTalent{
		Id:    cast.ToInt32(t.Id),
		Level: cast.ToInt32(t.Level),
	}
}
