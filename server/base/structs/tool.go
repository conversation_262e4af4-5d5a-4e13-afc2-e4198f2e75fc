package structs

import (
	"fmt"
	"train/base/cfg"
	"train/base/event"
	"train/common/pb"
	"train/common/ta"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type Tool struct {
	Type     int                    `bson:"type"` // 类型
	Lv       int                    `bson:"lv"`   // 等级
	levelCfg *cfg.ToolLevel[string] `bson:"-"`
	toolCfg  *cfg.Tool[int]         `bson:"-"`
}

func NewTool(typ, level int) *Tool {
	return &Tool{
		Type: typ,
		Lv:   level,
	}
}

func (t *Tool) Atk() int   { return t.GetLevelCfg().Attack }
func (t *Tool) Amp() int   { return t.GetLevelCfg().Amp }
func (t *Tool) Break() int { return t.GetLevelCfg().Break }
func (t *Tool) Hit() int   { return t.GetLevelCfg().Hit }

func (t *Tool) GetLevelCfg() *cfg.ToolLevel[string] {
	if t.levelCfg == nil {
		t.levelCfg, _ = cfg.ToolLevelContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", t.Type, t.Lv))
	}
	return t.levelCfg
}

// GetCfg 工具配置
func (t *Tool) GetCfg() *cfg.Tool[int] {
	if t.toolCfg == nil {
		bean := t.GetLevelCfg()
		t.toolCfg, _ = cfg.ToolContainer.GetBeanById(bean.ToolId)
	}
	return t.toolCfg
}

// ToPb 转pb数据
func (t *Tool) ToPb() *pb.ToolInfo {
	if t == nil {
		return nil
	}
	return &pb.ToolInfo{
		Type: cast.ToInt32(t.Type),
		Lv:   cast.ToInt32(t.Lv),
	}
}

type ToolModel struct {
	Tools      map[int]*Tool `bson:"tools"`
	Lv         int           `bson:"lv"`         // 打造台等级
	BlessCount int           `bson:"blessCount"` // 祝福剩余次数
	BlessId    string        `bson:"BlessId"`    // 祝福id
}

func NewToolModel() *ToolModel {
	model := &ToolModel{
		Tools: make(map[int]*Tool),
		Lv:    1,
	}
	return model
}

func (t *ToolModel) Init() {
	// 默认添加初始化工具
	data := cfg.Misc_CContainer.GetObj().ToolInitial
	for _, e := range data {
		if _, ok := t.Tools[e.Type]; !ok {
			t.Tools[e.Type] = NewTool(e.Type, e.Lv)
		}
	}
}

// GetTool 获取使用中的工具 [enum.ToolPlantCollect, enum.ToolMineralCollect, enum.ToolPartsCollect]
func (this *ToolModel) GetTool(_type int) *Tool {
	return this.Tools[_type]
}

func (this *ToolModel) ChangeBlessCount(count int) {
	this.BlessCount += count
}

// ToPb 转protobuf数据
func (this *ToolModel) ToPb() *pb.ToolModel {
	m := make(map[int32]*pb.ToolInfo)
	for k, v := range this.Tools {
		m[cast.ToInt32(k)] = v.ToPb()
	}

	inf := &pb.ToolModel{
		Lv:         cast.ToInt32(this.Lv),
		Tools:      m,
		BlessCount: cast.ToInt32(this.BlessCount),
		BlessId:    this.BlessId,
	}
	return inf
}

// GetToolByType 根据类型获取工具 _type = [enum.ToolPlantCollect, enum.ToolMineralCollect, enum.ToolPartsCollect]
func (plr *Player) GetToolByType(_type int) *Tool {
	return plr.Tool.GetTool(_type)
}

// 判断所有工具等级
func (plr *Player) checkToolLevel(num int) bool {
	for _, tool := range plr.Tool.Tools {
		if tool.Lv < num {
			return false
		}
	}

	return true
}

// 判断所有工具品质
func (plr *Player) checkToolQuality(num int) bool {
	for _, tool := range plr.Tool.Tools {
		if tool.GetLevelCfg().Quality < num {
			return false
		}
	}
	return true
}

// ToolMakeLogic 工具打造逻辑
func (plr *Player) ToolMakeLogic(_type int) int32 {
	toolVo := plr.Tool
	if toolVo == nil {
		toolVo = NewToolModel()
	}
	// 当前工具
	tool := plr.GetToolByType(_type)
	if tool == nil {
		return 2
	}
	var cost []*Condition
	if tool.Lv == 0 {
		bean, _ := lo.Find(cfg.Misc_CContainer.GetObj().ToolInitial, func(e *cfg.ToolInitial) bool {
			return e.Lv == tool.Lv && e.Type == tool.Type
		})
		// 不应该出现的情况
		if bean == nil {
			return 2
		}
		cost = ToConditions(bean.BuyCost)
	}
	if tool.Lv > 0 {
		cost = ToConditions(tool.GetLevelCfg().BuyCost)
	}
	// 检查升级消耗
	if failed := plr.CheckConditions(cost); len(failed) > 0 {
		return 1
	}
	// 打造台等级
	level := toolVo.Lv
	toolTableBean, _ := cfg.ToolTableContainer.GetBeanById(level)
	if toolTableBean.MaxToolLv <= tool.Lv {
		return 3
	}
	// 扣除消耗
	plr.DeductCosts(cost, ta.ResChangeSceneTypeTool)
	tool.Lv += 1
	// 提交一次打造结果
	plr.eventCenter.Emit(event.ToolMakeSuccess, _type)
	return 0
}
