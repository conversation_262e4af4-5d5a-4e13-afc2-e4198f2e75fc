package structs

import (
	"train/base/enum/battle_skill_trigger_type"
)

var TRIGGER_PRIORITY = map[string]int{
	battle_skill_trigger_type.ATTACK_AFTER:       1,
	battle_skill_trigger_type.HIT:                1,
	battle_skill_trigger_type.SUMMON_ANY:         1,
	battle_skill_trigger_type.ATTACK_AFTER_AHEAD: 1,
	battle_skill_trigger_type.DEATH:              1,
	battle_skill_trigger_type.DEATH_AHEAD:        1,
	battle_skill_trigger_type.DEATH_ANY:          1,
}

type BattleLog struct {
	Type     int
	Params   string
	Sender   string
	Receiver string
	Next     []*BattleLog
}

type BattleAction struct {
	Func func(...any)
	Args []any
	Next []*BattleAction
	Dep  int

	Battle *BattleLogic
}

func (this *BattleAction) Init(battle *BattleLogic, Func func(...any), args ...any) {
	this.Battle = battle
	this.Func = Func
	this.Args = args
}

// func cmpPriority(act1 *BattleAction, act2 *BattleAction) int {
// 	cmpFuncs := act1.getPriorityFuncs()
// 	for _, fn := range cmpFuncs {
// 		pri1 := fn.call(act1)
// 		pri2 := fn.call(act2)
// 		if pri1 != pri2 {
// 			return pri1 - pri2 //从小到大
// 		}
// 	}
// 	return 0
// }

// func (this *BattleAction) getPriorityFuncs() []func() int {
// 	return []func() int{this.getDepPriority, this.getEffectPriority, this.getTriggerPriority, this.getCampPriority, this.getIndexPriority}
// }

// func (this *BattleAction) getDepPriority() int { //dep小的优先
// 	return this.Dep
// }

// func (this *BattleAction) getEffectPriority() int { //增益 > 减益
// 	role := this.Args[0].(*BattleRole)
// 	isNegativeSkill := this.Battle.isNegativeSkill(role.Skill)
// 	return ut.If(isNegativeSkill, 1, 0)
// }

// func (this *BattleAction) getTriggerPriority() int {
// 	role := this.Args[0].(*BattleRole)
// 	trigger := role.skill.trigger
// 	pri := TRIGGER_PRIORITY[trigger]
// 	if !pri {
// 		pri = 1000
// 	}
// 	return pri
// }

// func (this *BattleAction) getCampPriority() int {
// 	role := this.Args[0].(*BattleRole)
// 	return role.Type
// }

// func (this *BattleAction) getIndexPriority() int {
// 	role := this.Args[0].(*BattleRole)
// 	index := this.battle.getRoleIndex(role)
// 	return index
// }

type BattleLogic struct {
}
