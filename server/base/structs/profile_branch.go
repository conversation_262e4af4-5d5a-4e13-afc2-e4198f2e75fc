package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type ProfileBranchLevel struct {
	Id     int                `bson:"id"` //关卡id
	NodeId int                `bson:"nodeId"`
	Nodes  []*MapMineItemData `bson:"nodes"`
	Unlock bool               `bson:"unlock"`
	plr    *Player
}

func (this *ProfileBranchLevel) Init(plr *Player) {
	this.plr = plr
}

func (this *ProfileBranchLevel) GetNode(nodeId int) *MapMineItemData {
	return this.Nodes[nodeId-1]
}

func (this *ProfileBranchLevel) GetCurNode() *MapMineItemData {
	return this.GetNode(this.NodeId)
}

func (this *ProfileBranchLevel) ToPb() *pb.ProfileBranchLevel {
	if this == nil {
		return nil
	}
	return &pb.ProfileBranchLevel{
		Id:     cast.ToInt32(this.Id),
		NodeId: cast.ToInt32(this.NodeId),
		Nodes: lo.Map(this.Nodes, func(node *MapMineItemData, _ int) *pb.MapMineItemData {
			return node.ToPb()
		}),
		Unlock: this.Unlock,
	}
}

func NewProfileBranch() *ProfileBranch {
	return &ProfileBranch{
		Levels: make([]*ProfileBranchLevel, 0),
		Energy: 0,
	}
}

type ProfileBranch struct {
	Id                   int                   `bson:"id"`                   //当前关卡id
	Levels               []*ProfileBranchLevel `bson:"levels"`               //关卡列表
	Energy               int                   `bson:"energy"`               //体力
	LastUpdateEnergyTime int                   `bson:"lastUpdateEnergyTime"` //上次更新时间
	plr                  *Player
}

func (p *ProfileBranch) Init(plr *Player) {
	p.plr = plr
	p.Unlock()
	for _, level := range p.Levels {
		level.Init(plr)
	}
}

func (p *ProfileBranch) Unlock() {
	if len(p.Levels) > 0 {
		return
	}
	data := cfg.ProfileBranchLevelContainer.GetData()[0]
	p.Id = data.Id
	level := p.GenLevel(data.Id)
	p.Levels = append(p.Levels, level)
	// 多生成一个
	p.Levels = append(p.Levels, p.GenLevel(data.Id+1))
	p.ResetEnergy()
}

func (p *ProfileBranch) ResetEnergy() {
	misc := cfg.GetMisc()
	p.Energy = misc.ProfileBranch.MaxEnergy
	p.LastUpdateEnergyTime = p.plr.GetNowTime()
}

func (p *ProfileBranch) UpdateEnergy() {
	now := p.plr.GetNowTime()
	passTime := now - p.LastUpdateEnergyTime
	misc := cfg.GetMisc()
	recoverTime := p.GetEnergyRecoverTime()
	energy := passTime / recoverTime
	p.Energy = ut.Min(p.Energy+energy, misc.ProfileBranch.MaxEnergy)
	p.LastUpdateEnergyTime += energy * recoverTime
}

func (p *ProfileBranch) ChangeEnergy(energy int) {
	misc := cfg.GetMisc()
	preEnergy := p.Energy
	maxEnergy := misc.ProfileBranch.MaxEnergy
	p.Energy += energy
	if preEnergy >= maxEnergy && p.Energy < maxEnergy {
		p.LastUpdateEnergyTime = p.plr.GetNowTime()
	}
}

func (p *ProfileBranch) GetEnergySurplusTime() int {
	misc := cfg.GetMisc()
	if p.Energy >= misc.ProfileBranch.MaxEnergy {
		return 0
	}
	recoverTime := p.GetEnergyRecoverTime()
	now := p.plr.GetNowTime()
	passTime := now - p.LastUpdateEnergyTime
	return ut.Max(0, recoverTime-passTime)
}

func (p *ProfileBranch) GetEnergyRecoverTime() int {
	return cfg.GetMisc().ProfileBranch.RecoverTime * ut.TIME_SECOND
}

func (p *ProfileBranch) ToPb() *pb.ProfileBranch {
	return &pb.ProfileBranch{
		Levels: lo.Map(p.Levels, func(level *ProfileBranchLevel, _ int) *pb.ProfileBranchLevel {
			return level.ToPb()
		}),
		Energy:      cast.ToInt32(p.Energy),
		Id:          cast.ToInt32(p.Id),
		SurplusTime: cast.ToInt32(p.GetEnergySurplusTime()),
	}
}

// Gen 生成关卡配置
func (p *ProfileBranch) GenLevel(id int) *ProfileBranchLevel {
	level := &ProfileBranchLevel{
		Id:     id,
		NodeId: 1,
		plr:    p.plr,
	}
	levelCfg, _ := cfg.ProfileBranchLevelContainer.GetBeanById(id)

	profiles := array.SliceCopy(levelCfg.Profiles)

	// 计算这一关需要的总贴纸数量
	totalProfileCnt := 0
	for _, question := range levelCfg.Questions {
		totalProfileCnt += question.Profile
	}

	ownedProfiles := p.plr.PassengerProfiles

	// 构建已拥有贴纸的 map，用于快速查找
	ownedProfilesMap := make(map[int]bool)
	for _, profileId := range ownedProfiles {
		ownedProfilesMap[profileId] = true
	}

	// 构建按 CharacterId 分组的贴纸 map
	characterProfilesMap := make(map[int][]*cfg.CharacterProfile[int])
	for _, profile := range cfg.CharacterProfileContainer.GetData() {
		characterProfilesMap[profile.CharacterId] = append(characterProfilesMap[profile.CharacterId], profile)
	}

	// 计算需要随机生成的贴纸数量
	needRandomProfiles := totalProfileCnt - len(levelCfg.Profiles)
	if needRandomProfiles > 0 {
		weights := make([]int, 0)
		profileIds := make([]int, 0)

		// 遍历所有乘客
		for _, character := range cfg.CharacterContainer.GetData() {
			// 获取该乘客的所有贴纸
			characterProfiles := characterProfilesMap[character.Id]
			passenger := p.plr.GetPassengerById(character.Id)
			jackCfg, _ := cfg.JackpotContainer.GetBeanById(character.Quality)

			// 过滤出未获得的贴纸
			availableProfiles := lo.Filter(characterProfiles, func(profile *cfg.CharacterProfile[int], _ int) bool {
				// 检查是否在已拥有的贴纸列表中
				if ownedProfilesMap[profile.Id] {
					return false
				}
				// 检查是否在乘客的 ProfileData 中
				if passenger != nil {
					_, hasProfile := passenger.ProfileData[profile.Type]
					if hasProfile {
						return false
					}
				}
				if lo.Contains(profiles, profile.Id) {
					return false
				}
				return true
			})

			if len(availableProfiles) == 0 {
				continue
			}

			// 计算未获得的专属资料和通用资料数量
			var exclusiveProfiles, commonProfiles int
			for _, profile := range availableProfiles {
				if profile.IsCommon() {
					commonProfiles++
				} else {
					exclusiveProfiles++
				}
			}

			// 为每个未获得的贴纸计算权重
			// 乘客权重 / 该乘客的可用贴纸数量
			w := float64(jackCfg.Weight) / (1 + float64(exclusiveProfiles) + float64(commonProfiles)*0.25) / float64(len(availableProfiles))
			weight := ut.Round(w * 10000)

			for _, profile := range availableProfiles {
				weights = append(weights, weight)
				profileIds = append(profileIds, profile.Id)
			}
		}

		// 根据权重随机选择贴纸
		for i := 0; i < needRandomProfiles; i++ {
			if len(weights) == 0 {
				break
			}
			selectedIndex := ut.RandomIndexByWeight(weights, func(w int) int { return w })
			profiles = append(profiles, profileIds[selectedIndex])
			profileIds = array.Splice(profileIds, selectedIndex, 1)
			weights = array.Splice(weights, selectedIndex, 1)
		}
	}

	index := 0
	for _, question := range levelCfg.Questions {
		node := &MapMineItemData{}
		node.Type = enum.QUESTION
		for i := 0; i < question.Profile; i++ {
			node.Reward = append(node.Reward, &Condition{
				Type: condition.PASSENGER_PROFILE,
				Id:   profiles[index],
				Num:  1,
			})
			index++
		}
		level.Nodes = append(level.Nodes, node)
	}
	return level
}

func (p *ProfileBranch) GetCurLevel() *ProfileBranchLevel {
	return p.Levels[p.Id-1]
}

func (p *ProfileBranch) GetNextLevel() *ProfileBranchLevel {
	index := p.Id
	if ut.IsIndexOutOfBounds(p.Levels, index) {
		return nil
	}
	return p.Levels[index]
}

func (p *ProfileBranch) GetProgress(id int) int {
	level, _ := lo.Find(p.Levels, func(level *ProfileBranchLevel) bool { return level.Id == id })
	if level == nil {
		return 0
	}
	if level.NodeId > len(level.Nodes) {
		return 100
	}
	return level.NodeId / len(level.Nodes)
}

func (p *ProfileBranch) GetCurNode() *MapMineItemData {
	return p.GetCurLevel().GetCurNode()
}

func (p *ProfileBranch) PassNode(nodeId int) {
	level := p.GetCurLevel()
	level.NodeId = nodeId + 1

	if nodeId >= len(level.Nodes) {
		nextId := p.Id + 2
		bean, _ := cfg.ProfileBranchLevelContainer.GetBeanById(nextId)
		if bean != nil {
			p.Id++
			level = p.GenLevel(nextId)
			p.Levels = append(p.Levels, level)
		}
	}
}
