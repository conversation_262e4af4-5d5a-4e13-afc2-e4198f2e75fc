package structs

import (
	"train/base/cfg"
	"train/common/pb"
	ut "train/utils"

	"github.com/samber/lo"
)

type EnergyModel struct {
	CostRecoverNum      int          `bson:"costRecoverNum"` //剩余钻石恢复次数
	FreeRecoverNum      int          `bson:"freeRecoverNum"` //剩余免费恢复次数
	Energy              int          `bson:"energy"`         //存的是毫秒数
	json                *cfg.SpeedUp `bson:"-"`              //配置
	plr                 *Player      `bson:"-"`
	IsUnlockSpeedUpAuto bool         `bson:"isUnlockSpeedUpAuto"` //是否解锁自动加速
}

func NewEnergy() *EnergyModel {
	model := &EnergyModel{Energy: -1}
	return model
}

func (this *EnergyModel) GetJson() *cfg.SpeedUp {
	return this.json
}

func (this *EnergyModel) init() {
	if this.Energy < 0 {
		this.RecoverEnergy()
		this.ResetRecoverNum()
	}
}

func (this *EnergyModel) ToPb() *pb.Energy {
	return &pb.Energy{
		Energy:              int32(this.Energy),
		CostRecoverNum:      int32(this.CostRecoverNum),
		FreeRecoverNum:      int32(this.FreeRecoverNum),
		IsUnlockSpeedUpAuto: this.IsUnlockSpeedUpAuto,
	}
}

// RecoverEnergy
/*
 * @description : 恢复能量
 */
func (this *EnergyModel) RecoverEnergy() {
	cfg := this.GetJson()
	base := lo.If(this.IsUnlockSpeedUpAuto, cfg.AutoMax).Else(cfg.Max)
	this.Energy = base * ut.TIME_SECOND
}

func (this *EnergyModel) ResetRecoverNum() {
	this.CostRecoverNum = this.GetJson().RecoverBuy
	this.FreeRecoverNum = this.GetJson().RecoverFree
}

type AutoRecoverEnergy struct {
	*EnergyModel    `bson:",inline"`
	LastSpeedUpTime int `bson:"lastSpeedUpTime"` //上次加速时间
}

func NewAutoRecoverEnergy() *AutoRecoverEnergy {
	model := &AutoRecoverEnergy{EnergyModel: NewEnergy()}
	return model
}

func (this *AutoRecoverEnergy) Init(plr *Player) {
	if this.EnergyModel == nil {
		this.EnergyModel = NewEnergy()
	}
	this.json = cfg.Misc_CContainer.GetObj().SpeedUp
	this.plr = plr
	this.init()
}

func (this *AutoRecoverEnergy) IsSpeedUp() bool {
	return this.LastSpeedUpTime > 0
}

func (plr *Player) EnergyToPb() *pb.Energy {
	plr.UpdateSpeedUp()
	pb := plr.Energy.ToPb()
	pb.Used = plr.SpeedUpTime > 0
	pb.IsSpeedUp = plr.Energy.IsSpeedUp()
	return pb
}
