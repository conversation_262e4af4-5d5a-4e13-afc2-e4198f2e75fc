package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"

	"github.com/spf13/cast"
)

type SpaceStone struct {
	LastUseTime int     `bson:"lastUseTime"` // 上一次使用的时间
	Marks       []int   `bson:"marks"`       // 标记
	Lv          int     `bson:"lv"`          // 等级
	Energy      int     `bson:"energy"`      //剩余能量
	plr         *Player `bson:"-"`
}

func NewSpaceStone() *SpaceStone {
	return &SpaceStone{}
}

func (t *SpaceStone) init(plr *Player) {
	t.plr = plr
}

func (t *SpaceStone) GetMaxMarkCnt() int {
	return t.GetJson().MarkCnt
}

func (t *SpaceStone) GetMaxEnergy() int {
	if t.Lv == 0 {
		return 0
	}
	return t.GetJson().Energy
}

func (t *SpaceStone) ToPb() *pb.SpaceStone {
	return &pb.SpaceStone{
		Marks:  ut.ToInt32(t.Marks),
		Energy: cast.ToInt32(t.Energy),
		Lv:     cast.ToInt32(t.Lv),
	}
}

func (t *SpaceStone) HasMark(id int) bool {
	return array.Has(t.Marks, id)
}

func (t *SpaceStone) Mark(id int) {
	t.Marks = append(t.Marks, id)
}

func (t *SpaceStone) LvUp() {
	preMaxEnergy := t.GetMaxEnergy()

	t.Lv++
	maxEnergy := t.GetMaxEnergy()
	t.Energy += maxEnergy - preMaxEnergy

	if t.Lv == 1 {
		cond := &Condition{Type: condition.PROP, Id: item_id.SPACE_STONE, Num: 1}
		if !t.plr.CheckCondition(cond) {
			t.plr.GrantReward(cond, ta.ResChangeSceneTypeSpaceStone)
		}
		t.Refresh()
	}
}

func (t *SpaceStone) Refresh() {
	if t.Lv > 0 {
		t.Energy = t.GetMaxEnergy()
	}
}

func (t *SpaceStone) RemoveMark(id int) {
	t.Marks = array.Remove(t.Marks, id)
}

func (t *SpaceStone) GetJson() *cfg.SpaceStone[int] {
	bean, _ := cfg.SpaceStoneContainer.GetBeanById(t.Lv)
	return bean
}
