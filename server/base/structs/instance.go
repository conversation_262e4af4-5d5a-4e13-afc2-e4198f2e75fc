package structs

import (
	"train/base/cfg"
	"train/common/pb"
)

func NewInstanceModule() *InstanceModule {
	return &InstanceModule{}
}

type InstanceModule struct {
	Level            int     `bson:"level"`            // 最后一个通关的关卡
	IsUnlock         bool    `bson:"isUnlock"`         // 是否解锁
	IsCompletePuzzle bool    `bson:"isCompletePuzzle"` // 是否完成解密
	plr              *Player `bson:"-" json:"-"`
}

func (i *InstanceModule) init(plr *Player) {
	i.plr = plr
}

func (i *InstanceModule) ToPb() *pb.Instance {
	return &pb.Instance{
		Level:            int32(i.Level),
		IsUnlock:         i.IsUnlock,
		IsCompletePuzzle: i.IsCompletePuzzle,
	}
}

// GetCurCfg
/*
 * @description 获取当前关卡配置
 * @return *cfg.InstanceLevel
 */
func (i *InstanceModule) GetCurCfg() *cfg.InstanceLevel[int] {
	bean, _ := cfg.InstanceLevelContainer.GetBeanById(i.Level)
	return bean
}

// GetNextCfg
/*
 * @description 获取下一个关卡配置
 * @return *cfg.InstanceLevel
 */
func (i *InstanceModule) GetNextCfg() *cfg.InstanceLevel[int] {
	bean, _ := cfg.InstanceLevelContainer.GetBeanById(i.Level + 1)
	return bean
}

func (i *InstanceModule) Unlock() {
	if i.IsUnlock {
		return
	}
	i.IsUnlock = true
}

func (i *InstanceModule) CompletePuzzle() {
	if i.IsCompletePuzzle {
		return
	}
	i.IsCompletePuzzle = true
}
