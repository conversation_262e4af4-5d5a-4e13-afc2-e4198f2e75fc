package structs

import (
	"train/base/cfg"
	"train/base/event"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type GuideInfo struct {
	Id        int   `bson:"id"`        // 所属模块id
	KeySteps  []int `bson:"keySteps"`  // 已经完成的关键步骤id
	CurStepId int   `bson:"curStepId"` //当前步骤
	IsOver    bool  `bson:"isOver"`    //此模块已结束(解决"当配置修改后，会重新开始引导此模块，从而导致流程卡死"的问题)
}

// Contains 已经完成的步骤包不包含step
func (g *GuideInfo) hasKeyStep(step int) bool {
	return array.Has(g.KeySteps, step)
}

// Push 放入一个step
func (g *GuideInfo) addKeyStep(step int) bool {
	if g.hasKeyStep(step) {
		return false
	}
	g.KeySteps = append(g.KeySteps, step)
	return true
}

func (g *GuideInfo) ToPbKeysSteps() []int32 {
	steps := g.GetAllSkipStep()
	if !g.IsOver {
		g.setOverByMax(steps)
		g.setOverByKeys(steps)
	}
	if g.IsOver {
		return lo.MapToSlice(steps, func(k int, v bool) int32 { return cast.ToInt32(k) })
	}
	return lo.Map(g.KeySteps, func(id int, i int) int32 { return cast.ToInt32(id) })
}

func (g *GuideInfo) ToPbGuideInfo() *pb.GuideInfo {
	return &pb.GuideInfo{
		Id:       cast.ToInt32(g.Id),
		KeySteps: g.ToPbKeysSteps(),
	}
}

func (g *GuideInfo) GetAllSkipStep() map[int]bool {
	dic := map[int]bool{}
	guideId := g.Id
	datas := cfg.GuideContainer.GetData()
	for _, data := range datas {
		if data.GuideId == guideId {
			dic[data.Skip] = true
		}
	}
	return dic
}

func (g *GuideInfo) setOverByMax(steps map[int]bool) {
	if g.IsOver || len(steps) == 0 {
		return
	}
	max := g.CurStepId
	for skip, _ := range steps {
		if skip > max {
			return
		}
	}
	g.IsOver = true
}

func (g *GuideInfo) setOverByKeys(steps map[int]bool) {
	if g.IsOver || len(steps) == 0 {
		return
	}
	for skip, _ := range steps {
		if !g.hasKeyStep(skip) {
			return
		}
	}
	g.IsOver = true
}

// ToPbGuideInfo guideInfo 转pb数据
func (plr *Player) ToPbGuideInfo() []*pb.GuideInfo {
	temp := make([]*pb.GuideInfo, 0)
	lo.ForEach(plr.GuideList, func(guideInfo *GuideInfo, index int) {
		temp = append(temp, guideInfo.ToPbGuideInfo())
	})
	return temp
}

// GetPlayerGuideInfo 获取用户指定引导模块的数据
func (plr *Player) GetGuideInfo(id int) *GuideInfo {
	info, _ := lo.Find(plr.GuideList, func(guide *GuideInfo) bool {
		return guide.Id == id
	})
	return info
}

func IsKeyStepByDic(stepId int, datas map[int]bool) bool {
	for skip, _ := range datas {
		if skip == stepId {
			return true
		}
	}
	return false
}

func GetGuideIdByStep(stepId int) int {
	return stepId / 100
}

func (plr *Player) isStepEndByUnlockFunc(unlockFunc string) bool {
	return plr.isStepEndBy(func(data *cfg.Guide[int]) bool {
		return data.UnlockFunc == unlockFunc
	})
}

func (plr *Player) IsStepEndByMark(mark string) bool {
	return plr.isStepEndBy(func(data *cfg.Guide[int]) bool {
		return data.Mark == mark
	})
}

func (plr *Player) isStepEndBy(filter func(item *cfg.Guide[int]) bool) bool {
	datas := cfg.GuideContainer.GetData()
	for _, data := range datas {
		if filter(data) {
			return plr.isStepEnd(data.Id)
		}
	}
	return false
}

func (plr *Player) isStepEnd(stepId int) bool {
	if plr.CloseGuide {
		return true
	}
	stepCfg, _ := cfg.GuideContainer.GetBeanById(stepId)
	guideId := GetGuideIdByStep(stepId)
	info := plr.GetGuideInfo(guideId)
	if info == nil {
		return false
	}
	return info.CurStepId >= stepId || array.Has(info.KeySteps, stepCfg.Skip)
}

// RecordGuideInfo 记录引导数据
func (plr *Player) RecordGuideInfo(guideId, stepId int) {
	info := plr.GetGuideInfo(guideId)
	if info == nil {
		info = &GuideInfo{
			Id:       guideId,
			KeySteps: make([]int, 0),
		}
		plr.GuideList = append(plr.GuideList, info)
	}
	plr.GuideId = guideId

	steps := info.GetAllSkipStep()

	if IsKeyStepByDic(stepId, steps) {
		info.addKeyStep(stepId)
		info.setOverByKeys(steps)
	}
	if info.CurStepId < stepId {
		plr.checkGuideReward(stepId)
	}
	info.CurStepId = ut.Max(info.CurStepId, stepId)
	log.Info("[%s] RecordGuideInfo %d %d", plr.GetUid(), info.CurStepId, stepId)

	plr.eventCenter.Emit(event.GuideStepEnd, stepId)

}

func (plr *Player) checkGuideReward(stepId int) {
	stepCfg, _ := cfg.GuideContainer.GetBeanById(stepId)
	plr.setFakeTimeBy(stepCfg)
}

func (plr *Player) isRealTime() bool {
	id := plr.getFakeTimeEndId()
	return plr.isStepEnd(id)
}

func (plr *Player) setFakeTimeBy(stepCfg *cfg.Guide[int]) {
	gameTime := stepCfg.GameTime
	if gameTime == nil {
		return
	}
	plr.WorldTime = gameTime.Hour*ut.TIME_HOUR + gameTime.Min*ut.TIME_MINUTE
	plr.LastUpdateWorldTime = plr.GetNowTime()

	if stepCfg.Id == plr.getFakeTimeEndId() {
		plr.ResetLastOutputTime()
	}
}

func (plr *Player) getFakeTimeEndId() int {
	datas := cfg.GuideContainer.GetData()
	id := 0
	for _, data := range datas {
		if data.GameTime != nil {
			id = data.Id
		}
	}
	return id
}
