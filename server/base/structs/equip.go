package structs

import (
	"fmt"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum"
	"train/base/enum/equip_effect_target"
	"train/base/enum/equip_effect_type"
	"train/common/pb"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func NewEquipModule() *EquipModule {
	return &EquipModule{
		Data: make([]*EquipItem, 0),
	}
}

type EquipModule struct {
	Size        int            `bson:"size"`        // 装备容量
	Data        []*EquipItem   `bson:"data"`        // 装备数据
	Proficiency map[string]int `bson:"proficiency"` // 打造台熟练度
}

func (e *EquipModule) Init() {
	e.Data = lo.Filter(e.Data, func(equip *EquipItem, index int) bool {
		return equip.GetJson() != nil
	})
	if e.Proficiency == nil {
		e.Proficiency = make(map[string]int)
	}
}

// Push
/*
 * @description 添加装备到装备池
 * @param item 要添加的装备
 */
func (e *EquipModule) Push(item ...*EquipItem) {
	e.Data = append(e.Data, item...)
}

// Get
/*
 * @description 获取装备, 只是获取，不会从装备池中移除
 * @param uid 装备唯一id
 * @return equip *Equip
 */
func (e *EquipModule) Get(uid string) (equip *EquipItem) {
	equip, _ = lo.Find(e.Data, func(equip *EquipItem) bool {
		return equip.Uid == uid
	})
	return
}

// Pull
/*
 * @description 获取装备, 并从装备池中移除
 * @param uid 装备唯一id
 * @return equip *Equip
 */
func (e *EquipModule) Pull(uid string) (equip *EquipItem) {
	equip, index, _ := lo.FindIndexOf(e.Data, func(equip *EquipItem) bool {
		return equip.Uid == uid
	})
	if index != -1 {
		// 移除现有的装备
		temp := e.Data[:index]
		temp = append(temp, e.Data[index+1:]...)
		e.Data = temp
	}
	return
}

func (e *EquipModule) MakeByTable(id int, tableId int) *EquipItem {
	pro := e.GetProficiency(id, tableId)
	pLv := cfg.GetProficiencyLv(tableId, pro)
	makeBean, _ := cfg.EquipMakeContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", tableId, pLv))
	lv := makeBean.Level
	return e.Make(id, lv)
}

func (e *EquipModule) GetProficiencyKey(equipId int, level int) string {
	bean, _ := cfg.EquipContainer.GetBeanById(equipId)
	return fmt.Sprintf("%d-%d", level, bean.Index)
}

func (e *EquipModule) Make(id int, level int) (equip *EquipItem) {
	key := e.GetProficiencyKey(id, level)
	pro := e.Proficiency[key]
	effects := cfg.GetEquipEffectsLv(id, level, pro)

	equip = &EquipItem{
		Uid:     GenUid(),
		Id:      id,
		Level:   level,
		Effects: make([]*EquipEffect, 0),
	}

	for _, effect := range effects {
		lv := ut.Random(effect.Min, effect.Max)
		if effect.Type == equip_effect_type.ATTR {
			lvs := make([]int, 0)
			attrType := enum.RoleAttack
			if effect.Target == equip_effect_target.HP {
				attrType = enum.RoleHP
			}
			for i := effect.Min; i <= effect.Max; i++ {
				if cfg.GetEquipLv(id, i, attrType) == i {
					lvs = append(lvs, i)
				}
			}
			idx := ut.Random(0, len(lvs)-1)
			lv = lvs[idx]
		}
		// 属性0 不予添加
		if lv == 0 {
			continue
		}
		attr := cfg.GetEquipEffectAttrByLv(id, effect, lv)
		equip.Effects = append(equip.Effects, &EquipEffect{Attr: attr, Id: effect.ID, Level: lv})
	}

	e.Push(equip)
	pro += 1

	e.Proficiency[key] = pro
	return
}

func (e *EquipModule) GetProficiency(id int, level int) int {
	key := e.GetProficiencyKey(id, level)
	return e.Proficiency[key]
}

func (e *EquipModule) Buy(id int, level int) (equip *EquipItem) {
	effects := cfg.GetEquipEffectsLv(id, level, 0)
	equip = &EquipItem{
		Uid:   GenUid(),
		Id:    id,
		Level: level,
		Effects: lo.Map(effects, func(effect *cfg.EquipEffect[int], i int) *EquipEffect {
			lv := effect.Store
			attr := cfg.GetEquipEffectAttrByLv(id, effect, lv)
			return &EquipEffect{Id: effect.ID, Attr: attr, Level: lv}
		}),
	}
	e.Push(equip)
	return
}

func (plr *Player) GetEquipByUid(uid string) *EquipItem {
	return plr.Equip.Get(uid)
}

func (e *EquipModule) ToPb() *pb.Equip {
	return &pb.Equip{
		Data: lo.Map(e.Data, func(equip *EquipItem, i int) *pb.EquipItem {
			return equip.ToPb()
		}),
		Proficiency: lo.MapEntries(e.Proficiency, func(key string, val int) (string, int32) {
			return key, int32(val)
		}),
	}
}

// 装备词条
type EquipEffect struct {
	Id int `bson:"id"`
	// Type   int `bson:"type"`   // 词条类型
	// Target int `bson:"target"` // 作用对象
	Attr  int                   `bson:"attr"` // 属性数值
	Level int                   `bson:"level"`
	json  *cfg.EquipEffect[int] `bson:"-" json:"-"`
}

func (e *EquipEffect) GetJson() *cfg.EquipEffect[int] {
	if e.json == nil {
		e.json, _ = cfg.EquipEffectContainer.GetBeanById(e.Id)
	}
	return e.json
}

type EquipItem struct {
	json    *cfg.Equip[int] `bson:"-"`       // 配置
	Uid     string          `bson:"uid"`     // 唯一id
	Id      int             `bson:"id"`      // 配置id
	Used    bool            `bson:"used"`    // 是否使用中
	Level   int             `bson:"level"`   // 等级
	Effects []*EquipEffect  `bson:"effects"` //词条
}

// GetJson
/*
 * @description 装备配置
 * @return *cfg.EquipLevel
 */
func (e *EquipItem) GetJson() *cfg.Equip[int] {
	if e.json == nil {
		e.json, _ = cfg.EquipContainer.GetBeanById(e.Id)
	}
	return e.json
}

func (e *EquipItem) GetUid() string {
	return e.Uid
}

func (e *EquipItem) GetId() int {
	return e.Id
}

func (e *EquipItem) GetIndex() int {
	return e.GetJson().Index
}

func (e *EquipItem) GetRoleId() int {
	return e.GetJson().RoleId
}

func (e *EquipItem) ToPb() *pb.EquipItem {
	return &pb.EquipItem{
		Uid:   e.Uid,
		Id:    cast.ToInt32(e.Id),
		Used:  e.Used,
		Level: cast.ToInt32(e.Level),
		Effects: lo.Map(e.Effects, func(effect *EquipEffect, i int) *pb.EquipEffect {
			return &pb.EquipEffect{
				Id:    cast.ToInt32(effect.Id),
				Attr:  cast.ToInt32(effect.Attr),
				Level: cast.ToInt32(effect.Level),
			}
		}),
	}
}

func (e *EquipItem) GetEffectsLevel() int {
	lv := 0
	for _, effect := range e.Effects {
		if effect.GetJson().Type == equip_effect_type.SKILL {
			lv += effect.Level * 5
		} else {
			lv += effect.Level
		}
	}
	return lv
}

// ToCondition
/*
 * @description 装备数据转换为通用条件 主要数据放在了Extra里面
 * @return *pb.EquipItem
 */
func (e *EquipItem) ToCondition() *Condition {
	return &Condition{
		Type: condition.EQUIP,
		Num:  1,
		Id:   e.Id,
		Extra: map[string]interface{}{
			"uid":   e.Uid,
			"level": e.Level,
			"effects": lo.Map(e.Effects, func(effect *EquipEffect, i int) interface{} {
				return map[string]interface{}{
					"id":    effect.Id,
					"attr":  effect.Attr,
					"level": effect.Level,
				}
			}),
		},
	}
}

// AddEquip
/*
 * @description 从condition的extra添加装备
 * @param extra
 */
func (plr *Player) AddEquip(cond *Condition) {
	e := &EquipItem{
		Id: cast.ToInt(cond.Id),
	}
	for k, v := range cond.Extra {
		switch k {
		case "uid":
			e.Uid = cast.ToString(v)
		case "id":
			e.Id = cast.ToInt(v)
		case "level":
			e.Level = cast.ToInt(v)
		case "effects":
			e.Effects = lo.Map(v.([]interface{}), func(effect interface{}, i int) *EquipEffect {
				return &EquipEffect{
					Id:    cast.ToInt(effect.(map[string]interface{})["id"]),
					Attr:  cast.ToInt(effect.(map[string]interface{})["attr"]),
					Level: cast.ToInt(effect.(map[string]interface{})["level"]),
				}
			})
		default:
			log.Warning("AddEquip: 未知的装备属性字段 %s", k)
		}
	}

	plr.Equip.Push(e)
}

func (plr *Player) GetEquips() []*EquipItem {
	return plr.Equip.Data
}
