package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/event"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// GetRandomPlanetArea
/*
 * @description 获取随机星球区域
 * @param planetId
 * @return int
 */
func GetRandomPlanetArea(planetId int) int {
	planetAreas := lo.Filter(cfg.PlanetAreaContainer.GetData(), func(item *cfg.PlanetArea[string], index int) bool {
		return item.PlanetId == planetId
	})
	if len(planetAreas) == 0 {
		return 1
	}
	idx := ut.Random(0, len(planetAreas)-1)
	return planetAreas[idx].Index
}

// ExploreTeam 探索队伍
type ExploreTeam struct {
	PlanetId int          `bson:"planetId"` // 探索的星球ID
	Roles    []int        `bson:"roles"`    // 探索的乘客ID列表
	EndTime  int          `bson:"endTime"`  // 结束探索时间
	Rewards  []*Condition `bson:"rewards"`  // 探索奖励
	plr      *Player      `bson:"-"`
}

func (e *ExploreTeam) ToPb() *pb.ExploreTeam {
	return &pb.ExploreTeam{
		PlanetId:    cast.ToInt32(e.PlanetId),
		Roles:       ut.ToInt32(e.Roles),
		SurplusTime: cast.ToInt32(e.GetSurplusTime()),
		Rewards:     ToPbConditions(e.Rewards),
	}
}

func (e *ExploreTeam) GetSurplusTime() int {
	if e.EndTime <= 0 {
		return 0
	}
	return e.EndTime - e.plr.GetNowTime()
}

func (e *ExploreTeam) CanComplete() bool {
	return e.GetSurplusTime() <= 0
}

// ExploreModule 探索模块
type ExploreModule struct {
	Teams []*ExploreTeam `bson:"teams"` // 所有探索队伍
	Area  map[int]int    `bson:"area"`  // 区域记录
	plr   *Player        `bson:"-"`
}

func NewExploreModule() *ExploreModule {
	module := &ExploreModule{
		Teams: []*ExploreTeam{},
	}
	return module
}

func (e *ExploreModule) Init(plr *Player) {
	e.plr = plr
	for _, team := range e.Teams {
		team.plr = plr
	}
	if e.Area == nil {
		e.Area = make(map[int]int)
		for _, planet := range plr.PlanetData.Planets {
			if planet.IsDone() {
				e.Area[planet.Id] = GetRandomPlanetArea(planet.Id)
			}
		}
	}
	// 监听星球完成事件 生成随机区域
	plr.eventCenter.On(event.PlanetComplete, func(val ...interface{}) {
		planetId := cast.ToInt(val[0])
		e.Area[planetId] = GetRandomPlanetArea(planetId)
	})
}

func (e *ExploreModule) ToPb() *pb.Explore {
	m := make(map[int32]int32)
	for k, v := range e.Area {
		m[cast.ToInt32(k)] = cast.ToInt32(v)
	}
	return &pb.Explore{
		Teams: lo.Map(e.Teams, func(t *ExploreTeam, i int) *pb.ExploreTeam {
			return t.ToPb()
		}),
	}
}

// StartExplore 开始探索
func (e *ExploreModule) StartExplore(planetId int, roles []int) (int32, []*pb.Condition) {
	// 检查是否达到队伍上限
	if len(e.Teams) >= e.GetMaxTeamCount() {
		return 1, nil
	}

	// 检查星球是否已经在探索中
	if e.IsPlanetExploring(planetId) {
		return 2, nil
	}

	// 检查乘客是否已经在其他星球探索
	for _, roleId := range roles {
		if e.IsRoleExploring(roleId) {
			return 3, nil
		}
	}

	// 获取星球配置
	planetCfg, ok := cfg.PlanetContainer.GetBeanById(planetId)
	if !ok {
		return 4, nil
	}

	// 检查队伍人数是否符合要求
	if len(roles) != 5 {
		return 5, nil
	}

	// 检查乘客是否存在
	for _, roleId := range roles {
		if e.plr.GetPassengerById(roleId) == nil {
			return 6, nil
		}
	}

	// 创建探索队伍
	team := &ExploreTeam{
		PlanetId: planetId,
		Roles:    roles,
		EndTime:  e.plr.GetNowTime() + planetCfg.Explore.Time*ut.TIME_HOUR,
		plr:      e.plr,
	}

	// 生成探索奖励
	team.Rewards = e.plr.GenerateRewards(planetCfg.Explore.Rewards, planetCfg.Explore.RdRewards)

	// 标记已经拥有的
	profileMap := make(map[int]bool)
	for _, profile := range e.plr.PlanetProfiles {
		profileMap[profile] = true
	}
	// 标记已经激活的
	for _, planet := range e.plr.PlanetData.Planets {
		if len(planet.ProfileData) == 0 {
			continue
		}
		for profile, _ := range planet.ProfileData {
			profileMap[profile] = true
		}
	}

	weightAry := make([]ut.DataWeight, 0)
	weightSum := 0
	for _, profileCfg := range cfg.PlanetProfileContainer.GetData() {
		if profileMap[profileCfg.Id] {
			continue
		}
		if profileCfg.PlanetId == planetId && profileCfg.Weight > 0 {
			checkFlag := true
			for _, target := range profileCfg.Target {
				if target.Type == condition.PASSENGER {
					if !lo.Contains(roles, cast.ToInt(target.Id)) {
						checkFlag = false
						break
					}
				}
				if !checkFlag {
					break
				}
			}
			if checkFlag {
				weightAry = append(weightAry, profileCfg)
				weightSum += profileCfg.Weight
			}
		}
	}

	if weightSum < 100 {
		weightAry = append(weightAry, &cfg.PlanetProfile[int]{
			Id:     -1,
			Weight: 100 - weightSum,
		})
	}

	index := ut.RandomIndexByDataWeight(weightAry)

	if index > -1 {
		if profile, ok := weightAry[index].(*cfg.PlanetProfile[int]); ok {
			if profile.Id != -1 {
				team.Rewards = append(team.Rewards, &Condition{
					Type: condition.PLANET_PROFILE,
					Id:   profile.Id,
					Num:  1,
				})
			}
		}
	}

	e.Teams = append(e.Teams, team)

	return 0, ToPbConditions(team.Rewards)
}

// ClaimRewards 领取奖励
func (e *ExploreModule) ClaimRewards(planetId int) (int32, int64) {
	team := e.GetTeamByPlanetId(planetId)
	if team == nil {
		return 1, 0
	}

	if !team.CanComplete() {
		return 2, int64(team.GetSurplusTime())
	}

	// 发放奖励
	e.plr.GrantRewards(team.Rewards, ta.ResChangeSceneTypeDeepExplore)

	// 从队伍列表中移除
	e.Teams = lo.Filter(e.Teams, func(t *ExploreTeam, i int) bool {
		return t.PlanetId != planetId
	})
	delete(e.Area, planetId)
	e.plr.eventCenter.Emit(event.DeepExploreComplete)
	return 0, 0
}

// IsPlanetExploring 检查星球是否在探索中
func (e *ExploreModule) IsPlanetExploring(planetId int) bool {
	return lo.ContainsBy(e.Teams, func(t *ExploreTeam) bool {
		return t.PlanetId == planetId
	})
}

// IsRoleExploring 检查乘客是否在探索中
func (e *ExploreModule) IsRoleExploring(roleId int) bool {
	return lo.ContainsBy(e.Teams, func(t *ExploreTeam) bool {
		return lo.Contains(t.Roles, roleId)
	})
}

// GetTeamByPlanetId 根据星球ID获取探索队伍
func (e *ExploreModule) GetTeamByPlanetId(planetId int) *ExploreTeam {
	return lo.FindOrElse(e.Teams, nil, func(t *ExploreTeam) bool {
		return t.PlanetId == planetId
	})
}

// SyncExplore 同步探索状态
func (e *ExploreModule) SyncExplore(planetId int) (int32, int64) {
	team := e.GetTeamByPlanetId(planetId)
	if team == nil {
		return 1, 0
	}
	return 0, int64(team.GetSurplusTime())
}

func (e *ExploreModule) GetMaxTeamCount() int {
	return e.plr.Tech.GetShip()
}
