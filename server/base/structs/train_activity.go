package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum/function_type"
	"train/common/pb"
	ut "train/utils"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

var day_weight = map[int]int{
	7:  15,
	8:  35,
	9:  35,
	10: 15,
}

func NewTrainActivityModule() *TrainActivityModule {
	return &TrainActivityModule{}
}

type TrainActivityModule struct {
	ArrangeWorldTime int64                       `bson:"arrangeWorldTime"` // 安排时的世界时间
	TodayIsRefresh   bool                        `bson:"todayIsRefresh"`   // 是否今天刷新过
	List             []*TrainActivityItem        `bson:"list"`             // 活动列表
	UngetRewards     []*TrainActivityUngetReward `bson:"ungetRewards"`     // 未领取奖励
	plr              *Player                     `bson:"-" json:"-"`
}

func (t *TrainActivityModule) init(plr *Player) {
	t.plr = plr
	for _, v := range t.List {
		v.plr = plr
	}
	t.CheckAndRefresh()
}

func (t *TrainActivityModule) ToPb() *pb.TrainActivity {
	if t == nil {
		return nil
	}
	list := make([]*pb.TrainActivityItem, 0)
	for _, v := range t.List {
		list = append(list, v.ToPb())
	}

	ungetRewards := make([]*pb.TrainActivityUngetReward, 0)

	for _, v := range t.UngetRewards {
		ungetRewards = append(ungetRewards, v.ToPb()...)
	}
	return &pb.TrainActivity{
		ArrangeWorldTime: t.ArrangeWorldTime,
		List:             list,
		UngetRewards:     ungetRewards,
	}
}

func (t *TrainActivityModule) IsTodayArranged() bool { return t.ArrangeWorldTime > 0 }
func (t *TrainActivityModule) CheckAndRefresh() {
	if !t.plr.IsUnlockFunction(function_type.TRAIN_ACTIVITY) {
		return
	}
	tmp := make([]*TrainActivityItem, 0)
	canRefresh := true
	for _, v := range t.List {
		tmp = append(tmp, v)
		if canRefresh && v.IsActiving() {
			// 还有进行中的活动 不可以刷新
			canRefresh = false
		}
	}
	if !canRefresh || t.TodayIsRefresh {
		t.List = tmp
		t.UpdateState()
		return
	}
	// 重新刷新
	t.Refresh()
	t.TodayIsRefresh = true
}

func (t *TrainActivityModule) Unlock() {
	t.Refresh()
	t.TodayIsRefresh = true
}

func (t *TrainActivityModule) UpdateState() {
	if t.ArrangeWorldTime == 0 || len(t.List) == 0 {
		return
	}
	for i := range t.List {
		item := t.List[i]
		if item.State != pb.CommonState_InProcess {
			continue
		}
		if i == 0 {
			item.State = pb.CommonState_DoneWithoutReward
			continue
		}
		preItem := t.List[i-1]
		if preItem.GetSurplusTime() > 0 {
			break
		}
		item.State = pb.CommonState_DoneWithoutReward
	}
}

func (t *TrainActivityModule) Refresh() {
	if t.UngetRewards == nil {
		t.UngetRewards = make([]*TrainActivityUngetReward, 0)
	}

	for _, val := range t.List {
		if val.State == pb.CommonState_DoneWithoutReward {
			// 车顶的活动奖励 也放在一号寝室
			if val.TrainId == 0 {
				val.TrainId = 1013
			}
			item, _ := lo.Find(t.UngetRewards, func(item *TrainActivityUngetReward) bool { return item.TrainId == val.TrainId })
			if item == nil {
				item = &TrainActivityUngetReward{
					TrainId: val.TrainId,
					Rewards: make([][]*Condition, 0),
				}
				t.UngetRewards = append(t.UngetRewards, item)
			}
			item.Rewards = append(item.Rewards, val.Rewards)
		}
	}

	t.ArrangeWorldTime = 0
	t.List = make([]*TrainActivityItem, 0)
	items := cfg.TrainActivityItemContainer.GetData()
	items = lo.Filter(items, func(item *cfg.TrainActivityItem[int], _ int) bool {
		if len(item.TrainId) == 0 {
			return true
		}
		return len(lo.Filter(item.TrainId, func(id int, _ int) bool {
			carriage := t.plr.Train.GetCarriageById(id)
			return carriage != nil && carriage.IsBuilt()
		})) > 0
	})
	misc := cfg.Misc_CContainer.GetObj()
	rdAry := misc.TrainActivity.RewardRandom
	plr := t.plr
	// 玩家可以产出的资源类型
	outputConds := make([]*Condition, 0)
	for _, r := range rdAry {
		if lo.ContainsBy(outputConds, func(cond *Condition) bool { return cond.Type == r.Type && cond.Id == r.Id }) {
			continue
		}
		if IsOutputCondType(r.Type, cast.ToInt(r.Id)) {
			cond := &Condition{Type: r.Type, Id: r.Id}
			// 车厢无产出 则不刷新这种奖励
			if plr.Train.GetAttrByCond(cond) <= 0 {
				continue
			}
			outputConds = append(outputConds, cond)
		}
	}

	if len(outputConds) == 0 {
		// 这里信号测试的时候没有任何产出数据  加个爱心和星尘测试
		outputConds = append(outputConds, &Condition{Type: condition.STAR_DUST, Id: 0})
		outputConds = append(outputConds, &Condition{Type: condition.HEART, Id: 0})
	}
	ary := make([]*TrainActivityItem, 0)
	usedIds := make([]int, 0)
	usedTrainIds := make([]int, 0)
	cnt := ut.Random(misc.TrainActivity.DailyCntMin, misc.TrainActivity.DailyCntMax)
	for {
		if cnt < 0 {
			break
		}
		matchTrainActivityItemAry := lo.Filter(items, func(item *cfg.TrainActivityItem[int], _ int) bool {
			// 1.尽量让活动表现不重复出现
			if lo.Contains(usedIds, item.Id) {
				return false
			}
			// // 2.车顶被占用
			// if len(item.TrainId) == 0 && lo.Contains(usedTrainIds, 0) {
			// 	return false
			// }
			// // 3.尽量选择没有活动&修建完成的车厢 - 车厢
			// if len(item.TrainId) > 0 {
			// 	after := lo.Filter(item.TrainId, func(id int, _ int) bool {
			// 		carriage := t.plr.Train.GetCarriageById(id)
			// 		return carriage != nil && carriage.IsBuilt() && !lo.Contains(usedTrainIds, id)
			// 	})
			// 	if len(after) == 0 {
			// 		return false
			// 	}
			// }
			return true
		})
		// 兼容车厢过少 error
		if len(matchTrainActivityItemAry) == 0 {
			matchTrainActivityItemAry = items
		}
		itemCfg := ut.RandomIn(matchTrainActivityItemAry)
		trainIds := itemCfg.TrainId
		// 默认是车顶的活动
		trainId := 0
		if len(trainIds) > 0 {
			// 随机车厢
			before := trainIds
			trainIds = lo.Filter(trainIds, func(id int, _ int) bool {
				carriage := t.plr.Train.GetCarriageById(id)
				return carriage != nil && carriage.IsBuilt() && !lo.Contains(usedTrainIds, id)
			})
			// 兼容车厢过少 error
			if len(trainIds) == 0 {
				trainIds = lo.Filter(before, func(id int, _ int) bool {
					carriage := t.plr.Train.GetCarriageById(id)
					return carriage != nil && carriage.IsBuilt()
				})
			}
			trainId = ut.RandomIn(trainIds)
		}

		usedIds = append(usedIds, itemCfg.Id)
		usedTrainIds = append(usedTrainIds, trainId)
		// 随机奖励数据
		filterAry := lo.Filter(rdAry, func(item *cfg.ChestReward, _ int) bool {
			return lo.ContainsBy(outputConds, func(cond *Condition) bool { return cond.Type == item.Type && cond.Id == item.Id })
		})
		rewards := plr.GenerateRewards(nil, filterAry)
		costDay := ut.RandomIndexByWeightMap(day_weight, func(k int, v int) int { return v })
		lo.ForEach(rewards, func(cond *Condition, _ int) { cond.Num *= costDay })
		ary = append(ary, &TrainActivityItem{
			Id:      len(ary),
			CfgId:   itemCfg.Id,
			TrainId: trainId,
			Rewards: rewards,
			EndTime: -1,
			CostDay: costDay,
			plr:     plr,
		})
		cnt--
	}
	t.List = ary
}

type TrainActivityUngetReward struct {
	TrainId int            `bson:"trainId"` // 车厢id
	Rewards [][]*Condition `bson:"rewards"` // 奖励
}

func (t *TrainActivityUngetReward) ToPb() []*pb.TrainActivityUngetReward {
	if t == nil || len(t.Rewards) == 0 {
		return nil
	}
	ary := make([]*pb.TrainActivityUngetReward, 0)
	for _, v := range t.Rewards {
		ary = append(ary, &pb.TrainActivityUngetReward{
			TrainId: int32(t.TrainId),
			Rewards: ToPbConditions(v),
		})
	}
	return ary
}

type TrainActivityItem struct {
	Id      int            `bson:"id"`      // id
	CfgId   int            `bson:"cfgId"`   // 配置id
	TrainId int            `bson:"trainId"` // 作用车厢id
	Rewards []*Condition   `bson:"rewards"` // 奖励
	EndTime int64          `bson:"endTime"` // 结束时间
	CostDay int            `bson:"costDay"` // 消耗天数
	State   pb.CommonState `bson:"state"`   // 状态
	plr     *Player        `bson:"-" json:"-"`
}

func (t *TrainActivityItem) Json() *cfg.TrainActivityItem[int] {
	return cfg.TrainActivityItemContainer.GetBean(t.Id)
}

func (t *TrainActivityItem) GetSurplusTime() int64 {
	if t.EndTime <= 0 {
		return 0
	}
	return t.EndTime - int64(t.plr.GetWorldTime())
}

func (t *TrainActivityItem) CanArrange() bool { return t.EndTime == -1 }

// 活动表现进行中
func (t *TrainActivityItem) IsActiving() bool { return t.GetSurplusTime() > 0 }

func (t *TrainActivityItem) ToPb() *pb.TrainActivityItem {
	if t == nil {
		return nil
	}
	return &pb.TrainActivityItem{
		Id:          int32(t.Id),
		CfgId:       int32(t.CfgId),
		TrainId:     int32(t.TrainId),
		Rewards:     ToPbConditions(t.Rewards),
		SurplusTime: t.GetSurplusTime(),
		CostDay:     int32(t.CostDay),
		State:       t.State,
	}
}
