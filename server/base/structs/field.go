package structs

import (
	"fmt"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum"
	"train/base/enum/field_ceil_type"
	"train/base/event"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// NewEmptyFieldModule
/*
 * @description : 初始化农场模块
 * @return *FieldModule
 */
func NewEmptyFieldModule() *FieldModule {
	f := &FieldModule{
		Level:    1,
		CeilData: make([]*Ceil, 0),
		SeedData: make([]*Seed, 0),
	}
	return f
}

type FieldModule struct {
	Level     int            `bson:"level"`     // 农场等级
	CeilData  []*Ceil        `bson:"ceilData"`  // 格子数据
	SeedData  []*Seed        `bson:"seedData"`  // 种子数据
	LevelCond map[string]int `bson:"levelCond"` // 升级条件
	plr       *Player        `bson:"-"`
}

func (f *FieldModule) Unlock() {
	reward := cfg.Misc_CContainer.GetObj().FieldUnlockGet
	if reward != nil {
		ary := ConfigConditionConvert(reward...).All()
		f.plr.GrantRewards(ary, ta.ResChangeSceneTypeField)
	}
}

func (f *FieldModule) init(plr *Player) {
	f.plr = plr
	if f.Level <= 0 {
		f.Level = 1
	}

	if f.LevelCond == nil {
		f.LevelCond = make(map[string]int)
	}

	for _, ceil := range f.CeilData {
		ceil.plr = f.plr
	}

	f.initUnlock()

	eventCenter := f.plr.eventCenter
	// 种植
	eventCenter.On(event.FieldPlantTimes, func(i ...interface{}) {
	})
	// 收获
	eventCenter.On(event.FieldGainTime, func(i ...interface{}) {
		v, _ := f.LevelCond[event.FieldGainTime]
		f.LevelCond[event.FieldGainTime] = v + 1
		f.checkLevelUp()
		f.checkLevelUp()
	})
	if plr.IsNew {
	}
	// 进行一次升级检查
	f.checkLevelUp()

}

func (f *FieldModule) initUnlock() {
	misc := cfg.GetMisc()
	start := len(f.GetCeils(field_ceil_type.Food))
	for i := start; i < misc.Field.FoodLandUnlock; i++ {
		f.unlockCeil(i, field_ceil_type.Food)
	}
	start = len(f.GetCeils(field_ceil_type.Fruit))
	for i := start; i < misc.Field.FruitLandUnlock; i++ {
		f.unlockCeil(8+i, field_ceil_type.Fruit)
	}
}

func (f *FieldModule) checkLevelUp() {
	levelBean, _ := cfg.FieldLevelContainer.GetBeanById(f.Level)
	if levelBean.Target == nil {
		return
	}
	wait := len(levelBean.Target)

	if wait == 0 {
		return
	}

	for _, target := range levelBean.Target {
		cnt, ok := f.LevelCond[target.Type]
		if !ok {
			break
		}
		if cnt >= target.Num {
			wait -= 1
		}
	}
	if wait <= 0 {
		f.Level += 1
		f.LevelCond = make(map[string]int)
	}
}

func (f *FieldModule) ToPb() *pb.Field {
	cond := make(map[string]int32)
	for k, v := range f.LevelCond {
		cond[k] = cast.ToInt32(v)
	}

	return &pb.Field{
		CeilData:  lo.Map(f.CeilData, func(t *Ceil, i int) *pb.FieldCeil { return t.ToPb() }),
		SeedData:  lo.Map(f.SeedData, func(t *Seed, i int) *pb.Condition { return t.ToPb() }),
		Level:     cast.ToInt32(f.Level),
		LevelCond: cond,
	}
}

// GetSeed
/*
 * @description : 获取种子
 * @param id
 * @return *Seed
 */
func (f *FieldModule) GetSeed(id int) *Seed {
	seed, _ := lo.Find(f.SeedData, func(seed *Seed) bool {
		return seed.Id == id
	})
	return seed
}

// UnlockCeil
/*
 * @description : 解锁格子
 * @param id 格子id
 * @param ceilType 格子类型 field_ceil_type.Food or field_ceil_type.Fruit
 * @return *Ceil
 */
func (f *FieldModule) UnlockCeil(id int, ceilType int) (ceil *Ceil, code pb.CeilOperationCode) {
	// 按顺序解锁
	if id != 0 {
		_, last := lo.Find(f.CeilData, func(ceil *Ceil) bool {
			return ceil.Id+1 == id
		})
		if !last {
			return nil, pb.CeilOperationCode_SureCeilSort
		}
	}

	ceil = f.GetCeil(id)
	if ceil != nil {
		// 已经解锁了
		return nil, pb.CeilOperationCode_CeilAlreadyUnlock
	}
	levelBean, _ := cfg.FieldLevelContainer.GetBeanById(f.Level)

	typeOfTargetCeils := f.GetCeils(ceilType)

	switch ceilType {
	case field_ceil_type.Food:
		if len(typeOfTargetCeils) >= levelBean.Vegetable {
			return nil, pb.CeilOperationCode_CeilMax
		}
	case field_ceil_type.Fruit:
		if len(typeOfTargetCeils) >= levelBean.Fruit {
			return nil, pb.CeilOperationCode_CeilMax
		}
	default:
		panic(fmt.Sprintf("UnlockCeil error id:%v, type:%d", id, ceilType))
	}

	cost := &Condition{
		Type: condition.STAR_DUST,
		Num:  f.GetUnlockCost(ceilType),
	}
	if !f.plr.CheckCondition(cost) {
		return nil, pb.CeilOperationCode_NotEnoughCostForUnlock
	}
	f.plr.DeductCost(cost, ta.ResChangeSceneTypeField)
	ceil = f.unlockCeil(id, ceilType)
	return ceil, pb.CeilOperationCode_Success
}

func (f *FieldModule) unlockCeil(id int, ceilType int) *Ceil {
	ceil := f.GetCeil(id)
	if ceil != nil {
		// 已经解锁了
		return ceil
	}
	ceil = &Ceil{
		Id:      id,
		Type:    ceilType,
		State:   pb.FieldCeilState_Empty,
		EndTime: -1,
		PlantId: -1,
		plr:     f.plr,
	}
	f.CeilData = append(f.CeilData, ceil)
	f.plr.eventCenter.Emit(event.FieldCeilUnlock, ceil)
	return ceil
}

func (f *FieldModule) GetCeils(ceilType int) []*Ceil {
	return lo.Filter(f.CeilData, func(ceil *Ceil, i int) bool {
		return ceil.Type == ceilType
	})
}

func GetFieldInitUnlockCount(ceilType int) int {
	misc := cfg.GetMisc()
	return ut.If(ceilType == field_ceil_type.Food, misc.Field.FoodLandUnlock, misc.Field.FruitLandUnlock)
}

func (f *FieldModule) GetUnlockCost(ceilType int) int {
	ceils := f.GetCeils(ceilType)
	index := len(ceils) - GetFieldInitUnlockCount(ceilType)
	misc := cfg.GetMisc()
	costs := ut.If(ceilType == field_ceil_type.Food, misc.Field.FoodLandUnlockCost, misc.Field.FruitLandUnlockCost)
	if len(costs) < index {
		index = len(costs) - 1
	}
	return costs[index]
}

// PlanetCeil
/*
 * @description : 播种
 * @param id 格子id
 * @param plantId 作物id
 * @return code
 */
func (f *FieldModule) PlanetCeil(id, plantId int) (code pb.CeilOperationCode) {
	ceil := f.GetCeil(id)
	if ceil == nil {
		return pb.CeilOperationCode_CeilNotUnlock
	}
	if ceil.HasPlanet() {
		return pb.CeilOperationCode_CeilAlreadyPlant
	}
	seed, _ := cfg.FieldSeedContainer.GetBeanById(plantId)
	if seed == nil || seed.Type != ceil.Type {
		return pb.CeilOperationCode_CeilPlantIdError
	}
	targetSeed := f.GetSeed(plantId)
	if targetSeed == nil {
		return pb.CeilOperationCode_NotEnoughCostForPlant
	}
	f.plr.DeductCost(&Condition{
		Type: condition.Seed,
		Id:   plantId,
		Num:  1,
	}, ta.ResChangeSceneTypeField)
	ceil.PlantId = plantId
	ceil.State = pb.FieldCeilState_NotWater
	// 种下事件
	f.plr.eventCenter.Emit(event.FieldPlantTimes, ceil)
	return pb.CeilOperationCode_Success
}

// WaterCeil
/*
 * @description : 浇水
 * @param id 格子id
 * @return code
 */
func (f *FieldModule) WaterCeil(id int) (code pb.CeilOperationCode) {
	ceil := f.GetCeil(id)
	if ceil == nil {
		return pb.CeilOperationCode_CeilNotUnlock
	}
	if ceil.State != pb.FieldCeilState_NotWater {
		return pb.CeilOperationCode_CeilNeedNotWater
	}
	seed, _ := cfg.FieldSeedContainer.GetBeanById(ceil.PlantId)
	ceil.EndTime = f.plr.GetNowTime() + seed.Time*ut.TIME_MINUTE
	ceil.State = pb.FieldCeilState_Growing
	return pb.CeilOperationCode_Success
}

// FertilizerCeil
/*
 * @description : 施肥
 * @param id 格子id
 * @param value 肥料id
 * @return code
 */
func (f *FieldModule) FertilizerCeil(id, value int) (code pb.CeilOperationCode) {
	ceil := f.GetCeil(id)
	if ceil == nil {
		return pb.CeilOperationCode_CeilNotUnlock
	}
	if ceil.State != pb.FieldCeilState_Growing {
		return pb.CeilOperationCode_CeilNeedNotFertilizer
	}
	cost := &Condition{
		Type: condition.PROP,
		Id:   value,
		Num:  1,
	}
	enough := f.plr.CheckCondition(cost)
	if !enough {
		return pb.CeilOperationCode_NotEnoughCostForFertilizer
	}
	// 肥料缩短的时间
	dfTime := cfg.Misc_CContainer.GetObj().GetFertilizerTime(value)
	if dfTime < 0 {
		ceil.EndTime = 0
	} else {
		ceil.EndTime -= dfTime
	}
	f.plr.DeductCost(cost, ta.ResChangeSceneTypeField)
	return pb.CeilOperationCode_Success
}

// HarvestCeil
/*
 * @description : 收获
 * @param id 格子id
 * @return code
 */
func (f *FieldModule) HarvestCeil(id int) (rewards []*Condition, code pb.CeilOperationCode) {
	ceil := f.GetCeil(id)
	if ceil == nil {
		return nil, pb.CeilOperationCode_CeilNotUnlock
	}

	ceil.GetSurplusTime()
	if ceil.State != pb.FieldCeilState_GrowDone {
		return nil, pb.CeilOperationCode_CeilCanNotHarvest
	}
	seed, _ := cfg.FieldSeedContainer.GetBeanById(ceil.PlantId)
	rewards = ConfigConditionConvert(seed.Reward...).All()
	f.plr.GrantRewards(rewards, ta.ResChangeSceneTypeField)
	f.plr.eventCenter.Emit(event.FieldGainTime, ceil)
	ceil.Clear()
	return rewards, pb.CeilOperationCode_Success
}

// GetCeil
/*
 * @description : 获取格子
 * @param id
 * @return *Ceil
 */
func (f *FieldModule) GetCeil(id int) *Ceil {
	ceil, _ := lo.Find(f.CeilData, func(ceil *Ceil) bool {
		return ceil.Id == id
	})
	return ceil
}

func (f *FieldModule) HandleCeilOperation(typ pb.CeilOperationType, id, value int) (rewards []*Condition, code pb.CeilOperationCode) {
	switch typ {
	case pb.CeilOperationType_Unlock:
		// 解锁格子
		_, code = f.UnlockCeil(id, value)
	case pb.CeilOperationType_Sow:
		// 种植
		code = f.PlanetCeil(id, value)
	case pb.CeilOperationType_Water:
		// 浇水
		code = f.WaterCeil(id)
	case pb.CeilOperationType_Fertilizer:
		// 施肥
		code = f.FertilizerCeil(id, value)
	case pb.CeilOperationType_Harvest:
		// 收获
		rewards, code = f.HarvestCeil(id)
	}
	return
}

type Ceil struct {
	Id      int               `bson:"id"`      // 格子id 也是顺序
	Type    int               `bson:"type"`    // 格子类型 field_ceil_type.Food or field_ceil_type.Fruit
	State   pb.FieldCeilState `bson:"state"`   // 状态
	EndTime int               `bson:"endTime"` // 成熟时间
	PlantId int               `bson:"plantId"` // 作物id
	plr     *Player           `bson:"-"`
}

func (c *Ceil) ToPb() *pb.FieldCeil {
	if c == nil {
		return nil
	}
	return &pb.FieldCeil{
		Id:          int32(c.Id),
		Type:        int32(c.Type),
		SurplusTime: int32(c.GetSurplusTime()),
		State:       c.State,
		PlantId:     int32(c.PlantId),
	}
}

// GetSurplusTime
/*
 * @description : 获取作物成长剩余时间
 * @return int
 */
func (c *Ceil) GetSurplusTime() int {
	if c.State == pb.FieldCeilState_Growing {
		time := ut.Max(0, c.EndTime-c.plr.GetNowTime())
		if time <= 0 {
			c.State = pb.FieldCeilState_GrowDone
		}
		return time
	}
	return -1
}

// HasPlanet
/*
 * @description : 是否有作物
 * @return bool
 */
func (c *Ceil) HasPlanet() bool {
	return c.State != pb.FieldCeilState_Empty
}

// Clear
/*
 * @description : 清理格子
 */
func (c *Ceil) Clear() {
	c.EndTime = -1
	c.PlantId = -1
	c.State = pb.FieldCeilState_Empty
}

func NewSeed(id, num int) *Seed {
	return &Seed{
		Id:  id,
		Num: num,
	}
}

type Seed struct {
	Id  int `bson:"id"`  // 种子id
	Num int `bson:"num"` // 数量
}

func (s *Seed) ToPb() *pb.Condition {
	return &pb.Condition{
		Id:   int32(s.Id),
		Num:  int32(s.Num),
		Type: condition.Seed,
	}
}

// ChangeSeed
/*
 * @description : 修改种子数量
 * @param id
 * @param num
 */
func (plr *Player) ChangeSeed(id, num int) {
	mod := plr.FieldModule
	seed := mod.GetSeed(id)
	if seed == nil {
		if num <= 0 {
			panic("操作了一个不存在的作物种子，并且数量是负数？")
		}
		seed = NewSeed(id, num)
		mod.SeedData = append(mod.SeedData, seed)
		plr.PushNew(enum.MARKNEW_SEED, []int{id})
		return
	}
	if seed.Num+num < 0 {
		panic("种子数量不足,都被扣成负数了！")
	}
	seed.Num += num
	mod.SeedData = lo.Filter(mod.SeedData, func(seed *Seed, i int) bool { return seed.Num > 0 })
}

// GetSeedNum
/*
 * @description : 获取指定id种子数量
 * @param id
 * @return int
 */
func (plr *Player) GetSeedNum(id int) int {
	mod := plr.FieldModule
	if mod == nil {
		return 0
	}
	seed := mod.GetSeed(id)
	if seed == nil {
		return 0
	}
	return seed.Num
}
