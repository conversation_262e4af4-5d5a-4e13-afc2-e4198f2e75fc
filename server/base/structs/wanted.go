package structs

import (
	"fmt"
	"sort"
	"train/base/cfg"
	"train/base/enum/wanted_condition_type"
	"train/base/enum/wanted_state"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// 车厢背景
var NormalBg = []int{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

// 车厢事件 打扫，灭火，陨石
var NormalEvt = []int{0, 1, 2}

// 星球背景
var BattleBg = []int{0, 1, 2, 3}

// 星球事件
var BattleEvt = []int{0}

type WantedCondition struct {
	Type  int `bson:"type"`
	Value int `bson:"value"`
}

func (w *WantedCondition) ToPb() *pb.WantedCondition {
	return &pb.WantedCondition{
		Type:  cast.ToInt32(w.Type),
		Value: cast.ToInt32(w.Value),
	}
}

func (w *WantedCondition) String() string {
	return fmt.Sprintf("%d-%d", w.Type, w.Value)
}

type Wanted struct {
	Name       int                `bson:"name"`
	Level      int                `bson:"level"`
	Conditions []*WantedCondition `bson:"conditions"`
	People     int                `bson:"people"`
	Rewards    []*Condition       `bson:"rewards"`
	EndTime    int                `bson:"endTime"`
	State      int                `bson:"state"`     //0:未开始，1:进行中，2:已结束(未领奖) 3: 已完成(领了奖)
	Roles      []int              `bson:"roles"`     //进行中的角色
	Publisher  int                `bson:"publisher"` //发布者
	Type       int                `bson:"type"`      //0:普通事件，1:战斗事件
	Bg         int                `bson:"bg"`        //背景
	BgArg      int                `bson:"bgArg"`     //背景参数
	plr        *Player            `bson:"-"`
}

type GenWantedOpt struct {
	Auto  bool
	Level int
}

func (w *Wanted) GetSurplusTime() int {
	if w.EndTime <= 0 {
		return 0
	}
	return w.EndTime - w.plr.GetNowTime()
}

func (w *Wanted) CanEnd() bool {
	return w.State == wanted_state.START && w.GetSurplusTime() <= 0
}

func (w *Wanted) CanComplete() bool {
	return w.State == wanted_state.END || w.CanEnd()
}

func (w *Wanted) ToPb() *pb.WantedInfo {
	return &pb.WantedInfo{
		Name:        cast.ToInt32(w.Name),
		Level:       cast.ToInt32(w.Level),
		People:      cast.ToInt32(w.People),
		Rewards:     ToPbConditions(w.Rewards),
		SurplusTime: cast.ToInt32(w.GetSurplusTime()),
		State:       cast.ToInt32(w.State),
		Conditions: lo.Map(w.Conditions, func(c *WantedCondition, i int) *pb.WantedCondition {
			return c.ToPb()
		}),
		Roles:     ut.ToInt32(w.Roles),
		Publisher: cast.ToInt32(w.Publisher),
		Type:      cast.ToInt32(w.Type),
		Bg:        cast.ToInt32(w.Bg),
		BgArg:     cast.ToInt32(w.BgArg),
	}
}

type WantedModule struct {
	Wanteds       []*Wanted `bson:"wanteds"`
	Point         int       `bson:"point"`
	DailyBattleId string    `bson:"daliyBattleId"` //每日战斗节点id，用于计算一些随机产出奖励；记的是节点的TypeId
	plr           *Player   `bson:"-"`
}

func (w *WantedModule) ToPb() *pb.Wanted {
	return &pb.Wanted{
		List: lo.Map(w.Wanteds, func(c *Wanted, i int) *pb.WantedInfo {
			return c.ToPb()
		}),
	}
}

func (w *WantedModule) Init(plr *Player) {
	w.plr = plr
	for _, wanted := range w.Wanteds {
		wanted.plr = plr
	}
	if len(w.Wanteds) < 6 {
		w.CheckAndRefresh()
	}
}

func (w *WantedModule) CheckAndRefresh() {
	if w == nil {
		return
	}
	// if !w.plr.IsUnlockFunction(function_type.PLAY_WANTED) {
	// 	return
	// }
	//w.Refresh()
}

func (w *WantedModule) Refresh() {
	node := w.plr.PlanetData.GetLastBattleNode(false)
	w.DailyBattleId = node.TypeId

	w.CheckAndCompleteWanteds()

	first := len(w.Wanteds) == 0
	misc := cfg.GetMisc()

	ary := cfg.WantedLevelContainer.GetData()
	lv := 0
	for _, levelData := range ary {
		if levelData.NeedPoint > w.Point {
			break
		}
		lv = levelData.ID
	}
	levelData, _ := cfg.WantedLevelContainer.GetBeanById(lv)

	w.Wanteds = w.Wanteds[:0]
	for i := 0; i < levelData.TaskCnt; i++ {
		opt := GenWantedOpt{Auto: true}
		if first {
			opt.Level = misc.Wanted.InitialLevel[i]
		}
		wanted := w.genWanted(opt)
		wanted.plr = w.plr
		w.Wanteds = append(w.Wanteds, wanted)
	}
}

func (w *WantedModule) CheckAndCompleteWanteds() {
	rewards := []*Condition{}
	for i, wanted := range w.Wanteds {
		if wanted.CanComplete() {
			rewards = append(rewards, wanted.Rewards...)
			wanted.Rewards = wanted.Rewards[:0]
			w.Complete(i)
		}
	}
	if len(rewards) > 0 {
		w.plr.CreateMail("请收下您昨日未领取的委托奖励~", "", rewards)
	}
}

func (w *WantedModule) ManualRefresh(index int) *Wanted {
	w.Wanteds = array.RemoveIndex(w.Wanteds, index)
	// log.Debug("before %d %+v", index, lo.Map(w.Wanteds, func(c *Wanted, i int) int {
	// 	return c.Name
	// }))
	wanted := w.genWanted(GenWantedOpt{Auto: false})
	wanted.plr = w.plr
	w.Wanteds = array.Insert(w.Wanteds, index, wanted)
	// log.Debug("after %d %+v", index, lo.Map(w.Wanteds, func(c *Wanted, i int) int {
	// 	return c.Name
	// }))
	return wanted
}

func (w *WantedModule) genWanted(opt GenWantedOpt) *Wanted {
	wanted := &Wanted{}
	auto := opt.Auto
	level := opt.Level

	// 发布者
	wanted.Publisher = 1005
	ary := lo.Filter(w.plr.Passenger, func(p *Passenger, i int) bool {
		return !lo.ContainsBy(w.plr.Wanted.Wanteds, func(w *Wanted) bool { return w.Publisher == p.Id })
	})
	if len(ary) > 0 {
		index := ut.Random(0, len(ary)-1)
		wanted.Publisher = ary[index].Id
	} else if len(w.plr.Passenger) > 1 {
		index := ut.Random(0, len(w.plr.Passenger)-1)
		wanted.Publisher = w.plr.Passenger[index].Id
	}
	// 事件背景
	wanted.Type = ut.Random(0, 1)
	switch wanted.Type {
	case 0:
		wanted.Bg = ut.RandomIn(NormalBg)
		wanted.BgArg = ut.RandomIn(NormalEvt)
		switch wanted.BgArg {
		case 0:
			wanted.Name = 1
		case 1:
			wanted.Name = 2
		case 2:
			wanted.Name = 4
		}
	case 1:
		wanted.Bg = ut.RandomIn(BattleBg)
		wanted.BgArg = ut.RandomIn(BattleEvt)
		wanted.Name = 3
	}

	//难度
	var levelData *cfg.WantedLevel[int]
	if level == 0 {
		levelDatas := cfg.WantedLevelContainer.GetData()
		levelDatas = lo.Filter(levelDatas, func(d *cfg.WantedLevel[int], i int) bool {
			num := lo.CountBy(w.Wanteds, func(wanted *Wanted) bool { return wanted.Level == d.ID })
			return d.NeedPoint <= w.Point && (!auto || d.NumMax > num)
		})
		idx := ut.RandomIndexByWeight(levelDatas, func(d *cfg.WantedLevel[int]) int { return d.Weight })
		levelData = levelDatas[idx]
	} else {
		levelData, _ = cfg.WantedLevelContainer.GetBeanById(level)
	}
	wanted.Level = levelData.ID

	condAry := cfg.WantedConditionContainer.GetData()
	req := levelData.Req
	// 条件数量
	for i := 0; i < levelData.Normal.Num; i++ {
		req = lo.Filter(req, func(data *cfg.ChestReward, i int) bool {
			count := lo.CountBy(wanted.Conditions, func(c *WantedCondition) bool {
				return c.Type == data.Type && c.Value == data.Id
			})
			return data.Num == -1 || count < data.Num
		})
		if len(req) <= 0 {
			log.Error("genWanted fail %v", &wanted)
			return nil
		}
		idx := ut.RandomIndexByWeight(req, func(d *cfg.ChestReward) int { return d.Weight })
		data := req[idx]

		switch true {
		case data.Type == wanted_condition_type.QUALITY:
			wanted.Conditions = append(wanted.Conditions, &WantedCondition{Type: data.Type, Value: data.Id})
		default:
			matchAry := lo.Filter(condAry, func(d *cfg.WantedCondition[string], i int) bool {
				return d.Type == data.Type
			})
			idx := ut.RandomIndexByWeight(matchAry, func(d *cfg.WantedCondition[string]) int { return d.Weight })
			data := matchAry[idx]
			wanted.Conditions = append(wanted.Conditions, &WantedCondition{Type: data.Type, Value: data.Value})
		}
	}

	//人数
	people, needAdd := w.plr.getMinPeople(wanted.Conditions)
	if needAdd {
		people += levelData.AddPeople
	}
	wanted.People = people

	//奖励
	rewards := w.plr.GenerateRewards(levelData.Rewards, nil)
	rdRewards := levelData.RdRewards
	for _, rdReward := range rdRewards {
		if ut.Chance(rdReward.Pro) {
			rewards = append(rewards, w.plr.ChestRewardToCondition(rdReward))
		}
	}
	wanted.Rewards = rewards
	return wanted
}

func (w *WantedModule) TestGetMinPeople(ary []*WantedCondition) (int, bool) {
	return w.plr.getMinPeople(ary)
}

// getMinPeople 根据条件获取最小需求人数
//
// Parameters:
//   - ary []*WantedCondition
//
// Returns:
//   - int 人数
//   - bool 需要需要宽裕
func (plr *Player) getMinPeople(ary []*WantedCondition) (int, bool) {
	// 复制角色和条件数组
	// characters := array.SliceCopy(cfg.CharacterContainer.GetData())
	characters := lo.Filter(cfg.CharacterContainer.GetData(), func(c *cfg.Character[int], i int) bool {
		return plr.GetPassengerById(c.Id) != nil
	})
	curCondAry := array.SliceCopy(ary)
	people := 0

	// 将品质条件按照品质值从高到低排序
	qualityConds := lo.Filter(curCondAry, func(cond *WantedCondition, _ int) bool {
		return cond.Type == wanted_condition_type.QUALITY
	})
	sort.Slice(qualityConds, func(i, j int) bool {
		return qualityConds[i].Value > qualityConds[j].Value // 品质值大的排在前面
	})
	// 如果全是品质要求  直接返回人数  并且不需要宽裕
	if people = len(qualityConds); people == len(curCondAry) {
		return people, false
	}

	// 所有可用乘客
	match := make(map[*cfg.Character[int]]bool)
	lo.ForEach(characters, func(char *cfg.Character[int], i int) {
		if match[char] {
			return
		}
		if len(qualityConds) == 0 {
			match[char] = true
			return
		}
		lo.ForEach(qualityConds, func(c *WantedCondition, i int) {
			if char.Quality < c.Value {
				return
			}
			match[char] = true
		})
	})

	/** 因为品质条件是要求乘客唯一的，所以上面先处理了品质条件，满足品质条件的乘客再拿下来判断其他条件 **/

	otherCondAry := lo.Filter(curCondAry, func(cond *WantedCondition, _ int) bool { return cond.Type != wanted_condition_type.QUALITY })

	for len(otherCondAry) > 0 {
		maxMatchCount := 0
		var bestChar *cfg.Character[int]
		var bestMatchConds []*WantedCondition

		for role := range match {
			ary := GetSatisfyCondAry(role, otherCondAry)
			curMatchCount := len(ary)
			if curMatchCount > maxMatchCount {
				maxMatchCount = curMatchCount
				bestChar = role
				bestMatchConds = ary
			}
		}

		// 无法匹配任何条件
		if maxMatchCount == 0 || bestChar == nil || bestMatchConds == nil {
			return people, true
		}
		otherCondAry = lo.Filter(otherCondAry, func(cond *WantedCondition, _ int) bool {
			return !lo.Contains(bestMatchConds, cond)
		})
		people++
	}

	return people, false
}

func (w *WantedModule) Start(index int, roles []int) {
	wanted := w.Wanteds[index]
	wanted.State = wanted_state.START
	wanted.Roles = roles
	levelData, _ := cfg.WantedLevelContainer.GetBeanById(wanted.Level)
	wanted.EndTime = w.plr.GetNowTime() + levelData.CostTime*ut.TIME_MINUTE
}

func (w *WantedModule) Complete(index int) {
	wanted := w.Wanteds[index]
	wanted.State = wanted_state.COMPLETE
	levelData, _ := cfg.WantedLevelContainer.GetBeanById(wanted.Level)
	w.Point += levelData.Point
	w.plr.GrantRewards(wanted.Rewards, ta.ResChangeSceneTypeWanted)
}

func (w *WantedModule) Get(index int) *Wanted {
	if len(w.Wanteds) <= index {
		return nil
	}
	return w.Wanteds[index]
}

func GetSatisfyCondAry(role *cfg.Character[int], ary []*WantedCondition) []*WantedCondition {
	result := make([]*WantedCondition, 0)
	matched := make([]int, 0)
	for _, cond := range ary {
		// 同一个角色只能满足同类型条件一次
		if lo.Contains(matched, cond.Type) {
			continue
		}
		yes := false
		switch cond.Type {
		case wanted_condition_type.QUALITY:
			if role.Quality >= cond.Value {
				yes = true
			}
		case wanted_condition_type.ANIMAL_TYPE:
			if role.AnimalType == cond.Value {
				yes = true
			}
		case wanted_condition_type.BATTLE_TYPE:
			if role.BattleType == cond.Value {
				yes = true
			}
		}
		if yes {
			result = append(result, cond)
			matched = append(matched, cond.Type)
		}
	}
	return result
}
