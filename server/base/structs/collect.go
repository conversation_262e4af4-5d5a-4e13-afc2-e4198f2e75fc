package structs

import (
	"math"
	"train/base/cfg"
	"train/base/enum/daily_task_type"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func NewCollectModule(plr *Player) *CollectModule {
	c := &CollectModule{}
	return c
}

type CollectModule struct {
	Mine []*MapMineItemData `bson:"mine"` // 采集物数据
	plr  *Player            `bson:"-"`
}

type MapMineItemData struct {
	Id       int          `bson:"id"`
	Type     int          `bson:"type"`
	Unique   string       `bson:"unique"`
	Reward   []*Condition `bson:"reward"`
	Position *pb.Point    `bson:"position"`
	Scale    float64      `bson:"scale"`
	PlanetId int          `bson:"planetId"`
}

func (c *CollectModule) init(plr *Player) {
	c.plr = plr

	c.Mine = lo.Filter(c.<PERSON>, func(item *MapMineItemData, index int) bool {
		data, _ := cfg.CollectMineContainer.GetBeanById(item.Id)
		return data != nil
	})
}

func (c *CollectModule) RefreshMap() {
	mineData := cfg.CollectMineContainer.GetData()

	group := lo.GroupBy(mineData, func(item *cfg.CollectMine[int]) int { return item.PlanetId })
	belongPlanetIds := lo.Keys(group)

	targetMap := make(map[int]map[int]int)
	tasks := c.plr.DailyTask.Tasks
	for _, task := range tasks {
		if task.GetJson().Type == daily_task_type.COLLECT {
			for _, target := range task.Target {
				id := cast.ToInt(target.Id)
				data := cfg.CollectMineContainer.GetBean(id)
				if data == nil {
					continue
				}
				v := targetMap[data.PlanetId]
				if v == nil {
					v = make(map[int]int)
					targetMap[data.PlanetId] = v
				}
				v[id] += target.GetNum()
			}
		}
	}

	c.Mine = c.Mine[:0]
	for _, planetId := range belongPlanetIds {
		mines := make([]*MapMineItemData, 0)
		// 必需品
		v := targetMap[planetId]
		mines = append(mines, c.genMust(v)...)
		// 生成额外的干扰物
		extra := make([]int, 0)
		switch planetId {
		case 1001:
			extra = append(extra, 1003)
		case 1021:
			extra = append(extra, 1052)
		}
		if len(extra) > 0 {
			mines = append(mines, c.genInterference(extra...)...)
		}

		// 生成基础干扰物
		interferences := lo.Filter(mineData, func(item *cfg.CollectMine[int], index int) bool {
			return item.PlanetId == planetId && v[item.Id] <= 0 && !lo.Contains(extra, item.Id)
		})
		if len(interferences) > 0 {
			interferCnt := 140 - len(mines)
			for i := 0; i < interferCnt; i++ {
				idx := ut.Random(0, len(interferences)-1)
				data := interferences[idx]
				mines = append(mines, &MapMineItemData{
					Id: data.Id,
				})
			}
		}

		for _, mine := range mines {
			point := c.getPoint(mine, 200.0, 100)
			mine.Position = point
			mine.PlanetId = planetId
			mine.Unique = GenUid()
			mine.Scale = ut.RandomFloat64(0.9, 1.1) * float64(ut.If(ut.Chance(50), 1, -1))
			c.Mine = append(c.Mine, mine)
		}
	}
}

func (c *CollectModule) genMust(targetMap map[int]int) []*MapMineItemData {
	mines := make([]*MapMineItemData, 0)
	for id, num := range targetMap {
		item, _ := cfg.CollectMineContainer.GetBeanById(id)
		for i := 0; i < num; {
			rewards := ConfigConditionConvert(item.RandomGetRewards()...).All()
			i += rewards[0].GetNum()
			mines = append(mines, &MapMineItemData{
				Id:     item.Id,
				Reward: rewards,
			})
		}
	}
	return mines
}

func (c *CollectModule) genInterference(id ...int) []*MapMineItemData {
	mines := make([]*MapMineItemData, 0)
	if len(id) == 0 {
		return mines
	}
	treeId := ut.RandomIn(id)
	treeCount := ut.Random(10, 15)
	for i := 0; i < treeCount; i++ {
		mines = append(mines, &MapMineItemData{
			Id: treeId,
		})
	}
	return mines
}

func (c *CollectModule) CollectMine(unique string) *MapMineItemData {
	mine, index, _ := lo.FindIndexOf(c.Mine, func(item *MapMineItemData) bool {
		return item.Unique == unique
	})
	if mine == nil {
		return nil
	}
	c.plr.GrantRewards(mine.Reward, ta.ResChangeSceneTypeCollect)
	c.Mine = array.Splice(c.Mine, index, 1)
	return mine
}

func (c *CollectModule) getPoint(mine *MapMineItemData, dis float64, randTimes int) *pb.Point {
	misc := cfg.GetMisc()
	size := misc.Collect.Map
	paddingX, paddingY := 200.0, 200.0
	// 计算区域范围
	halfWidth := float64(size.Width) * 0.5
	halfHeight := float64(size.Height) * 0.5
	minX := -halfWidth + paddingX
	maxX := halfWidth - paddingX
	minY := -halfHeight + paddingY
	maxY := halfHeight - paddingY

	dis *= dis
	item, _ := cfg.CollectMineContainer.GetBeanById(mine.Id)
	w := item.Width
	h := item.Height
	bestPoint := (*pb.Point)(nil)
	bestDist := -1.0
	lastPoint := (*pb.Point)(nil)

	for i := 0; i < randTimes; i++ {
		x := ut.RandomFloat64(minX+w*0.5, maxX-w*0.5)
		y := ut.RandomFloat64(minY, maxY-h)
		curRect := &ut.Rect{
			Cx: x,
			Cy: y + h*0.5,
			W:  w,
			H:  h,
		}
		overlap := false
		minDist := 1e9
		for _, m := range c.Mine {
			item2, _ := cfg.CollectMineContainer.GetBeanById(m.Id)
			rect := &ut.Rect{
				Cx: float64(m.Position.X),
				Cy: float64(m.Position.Y) + item2.Height*0.5,
				W:  item2.Width,
				H:  item2.Height,
			}
			interArea := ut.RectIntersectArea(curRect, rect)
			minArea := ut.Min(curRect.W*curRect.H, rect.W*rect.H)
			if interArea > minArea*0.3 {
				overlap = true
			}
			dist := math.Pow(curRect.Cx-rect.Cx, 2) + math.Pow(curRect.Cy-rect.Cy, 2)
			if dist < minDist {
				minDist = dist
			}
			if overlap {
				break
			}
		}
		pt := &pb.Point{X: cast.ToInt32(x), Y: cast.ToInt32(y)}
		if !overlap && minDist > dis {
			return pt
		}
		if !overlap && minDist > bestDist {
			bestDist = minDist
			bestPoint = pt
		}
		lastPoint = pt
	}
	if bestPoint != nil {
		// log.Debug("bestPoint: %v", bestPoint)
		return bestPoint
	}
	// log.Debug("lastPoint: %v", lastPoint)
	return lastPoint
}

func (c *CollectModule) ToPb() *pb.Collect {
	return &pb.Collect{
		Mine: lo.Map(c.Mine, func(item *MapMineItemData, index int) *pb.MapMineItemData {
			return item.ToPb()
		}),
	}
}

func (n *MapMineItemData) ToPb() *pb.MapMineItemData {
	return &pb.MapMineItemData{
		Id: int32(n.Id),
		Reward: lo.Map(n.Reward, func(item *Condition, index int) *pb.Condition {
			return item.ToPb()
		}),
		Position: n.Position,
		Uid:      n.Unique,
		Type:     int32(n.Type),
		Scale:    n.Scale,
		PlanetId: int32(n.PlanetId),
	}
}
