package structs

import (
	"encoding/json"
	"reflect"
	"sort"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/common/pb"
	ut "train/utils"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// Condition 通用的条件判断类
type Condition struct {
	Id     interface{}    `bson:"id" json:"id"`         // 条件需求id
	Num    int            `bson:"num" json:"num"`       // 条件需求数量
	Type   int            `bson:"type" json:"type"`     // 条件类型 参考 condition_type.go
	IsHide bool           `bson:"isHide" json:"isHide"` // 是否隐藏
	Extra  map[string]any `bson:"extra" json:"extra"`   // 扩展数据
}

// GetId 获取条件需求id
func (this *Condition) GetId() interface{} { return this.Id }

// GetNum 获取条件需求数量
func (this *Condition) GetNum() int { return this.Num }

// GetType 获取条件类型
func (this *Condition) GetType() int { return this.Type }

// SetFromMap 从map配置里初始化一个Condition
func (this *Condition) SetFromMap(data map[string]interface{}) {
	id, exists := data["id"]
	if exists {
		if reflect.TypeOf(id).Name() == "string" {
			this.Id = id
		} else {
			this.Id = cast.ToInt(id)
		}
	} else {
		this.Id = -1
	}

	this.Num = cast.ToInt(data["num"])
	this.Type = cast.ToInt(data["type"])

	// extra扩展字段转换， 有可能是string，也有可能是map[string]any  其他的未兼容
	if extra, ok := data["extra"]; ok {
		switch extra.(type) {
		case string:
			if extra != "" {
				json.Unmarshal([]byte(extra.(string)), &this.Extra)
			}
		case map[string]any:
		case map[any]any:
			this.Extra = make(map[string]any)
			for k, v := range extra.(map[any]any) {
				this.Extra[cast.ToString(k)] = v
			}
		}
	} else {
		this.Extra = make(map[string]any)
	}

	//v := reflect.ValueOf(this).Elem()
	//
	//for i := 0; i < v.NumField(); i++ {
	//	field := v.Field(i)
	//	tag := v.Type().Field(i).Tag.Get("de")
	//	if tag == "" && tag != "-" {
	//		log.Warning("解析Condition是遇到了一个错误标记, 第%d个字段.", i)
	//		continue
	//	}
	//	if val, ok := data[tag]; ok {
	//		field.SetInt(cast.ToInt64(val))
	//	}
	//}
}
func (this *Condition) ToPb() *pb.Condition {
	if this == nil {
		return nil
	}
	extra := ""
	if this.Extra != nil {
		bytes, _ := json.Marshal(this.Extra)
		extra = string(bytes)
	}
	return &pb.Condition{
		Id:     cast.ToInt32(this.Id),
		Num:    cast.ToInt32(this.Num),
		Type:   cast.ToInt32(this.Type),
		IsHide: this.IsHide,
		Extra:  extra,
	}
}

func (this *Condition) FromPb(pb *pb.Condition) *Condition {
	this.Id = cast.ToInt(pb.Id)
	this.Num = cast.ToInt(pb.Num)
	this.Type = cast.ToInt(pb.Type)
	if pb.Extra != "" {
		json.Unmarshal([]byte(pb.Extra), &this.Extra)
	}
	if this.Type == condition.EQUIP {
		if this.Id == 0 {
			this.Id = this.Extra["id"]
		}
	}
	return this
}

func (this *Condition) IsSame(cond *Condition) bool {
	return this.Type == cond.Type && cast.ToString(this.Id) == cast.ToString(cond.Id)
}

func ConditionsFromPb(pbConditions []*pb.Condition) []*Condition {
	out := make([]*Condition, 0)
	for _, pbCondition := range pbConditions {
		temp := &Condition{}
		out = append(out, temp.FromPb(pbCondition))
	}
	return out
}

// ToCondition 配置表转condition
func ToCondition(data map[string]interface{}) *Condition {
	val := &Condition{}
	val.SetFromMap(data)
	return val
}

// ToConditions 配置表转condition组
func ToConditions(datas []map[string]interface{}) (cs []*Condition) {
	if datas == nil {
		return make([]*Condition, 0)
	}
	for _, data := range datas {
		cs = append(cs, ToCondition(data))
	}
	return
}

// MergeConditions
/*
 * @description 合并condition
 * @param orgCons 原始
 * @param newConds 新加
 * @return []*Condition
 */
func MergeConditions(orgCons []*Condition, newConds []*Condition) []*Condition {
	if orgCons == nil {
		orgCons = make([]*Condition, 0)
	}
	for _, cond := range newConds {
		// 装备不可合并叠加
		if cond.Type == condition.EQUIP {
			orgCons = append(orgCons, cond)
			continue
		}
		c, _ := lo.Find(orgCons, func(c *Condition) bool {
			return c.Type == cond.Type && cast.ToString(c.Id) == cast.ToString(cond.Id)
		})
		if c == nil {
			orgCons = append(orgCons, ut.Clone(cond).(*Condition))
		} else {
			c.Num += cond.Num
		}
	}
	// 根据type sort一下
	sort.Slice(orgCons, func(i, j int) bool {
		a := orgCons[i]
		b := orgCons[j]
		return a.Type < b.Type
	})
	return orgCons
}

func ToPbConditions(orgCons []*Condition) []*pb.Condition {
	out := make([]*pb.Condition, 0)
	for _, con := range orgCons {
		out = append(out, con.ToPb())
	}
	return out
}

func (plr *Player) GrantReward(cond *Condition, sceneType int) int {
	return plr.ChangeCostByCond(cond, 1, sceneType)
}

// 发放奖励
func (plr *Player) GrantRewards(conds []*Condition, sceneType int) {
	plr.ChangeCostByConds(conds, 1, sceneType)
}

func (plr *Player) DeductCost(cond *Condition, sceneType int) {
	plr.ChangeCostByCond(cond, -1, sceneType)
}

// 扣除消耗
func (plr *Player) DeductCosts(conds []*Condition, sceneType int) {
	plr.ChangeCostByConds(conds, -1, sceneType)
}

// 优先用上面两个接口；如果有需要发放n倍奖励的时候，可以用这个，change表示倍数
func (plr *Player) ChangeCostByConds(conds []*Condition, change int, sceneType int) {
	for _, to := range conds {
		plr.ChangeCostByCond(to, change, sceneType)
	}
}

func (plr *Player) ChangeCostByCond(cond *Condition, change int, sceneType int) int {
	num := cond.GetNum()
	num *= change
	_type := cond.GetType()
	id := cond.GetId()
	condClone := ut.Clone(cond).(*Condition)
	condClone.Num = num
	// 上报数据
	switch _type {
	case condition.DIAMOND:
		fallthrough
	case condition.STAR_DUST:
		fallthrough
	case condition.HEART:
		plr.changeCurrency(_type, num)
	case condition.WORLD_TIME:
		time := num * ut.TIME_MINUTE
		plr.SpeedUpTime += time
		plr.Train.AddOutputTime(TransToRealTime(time))
	case condition.PROP:
		plr.ChangeItemByCond(condClone)
	case condition.PASSENGER:
		plr.UnlockPassenger(cast.ToInt(id))
	case condition.BUILD_ID:
		plr.UnlockBuildById(id.(string))
	case condition.Chest:
		plr.AddChest(cast.ToInt(id), num)
	case condition.EQUIP:
		if num > 0 {
			plr.AddEquip(cond)
		} else {
			uid := cast.ToString(cond.Extra["uid"])
			plr.Equip.Pull(uid)
		}
	case condition.PASSENGER_SKIN:
		plr.UnlockSkin(id.(string))
	case condition.Seed:
		plr.ChangeSeed(cast.ToInt(id), num)
	case condition.PASSENGER_FRAG:
		idToInt := cast.ToInt(id)
		old := plr.FragItems[idToInt]
		old += num
		plr.FragItems[idToInt] = old
		if old <= 0 {
			delete(plr.FragItems, idToInt)
		}

	case condition.BLACK_HOLE_CURRENCY:
		plr.BlackHole.Currency += num
	case condition.ORE_ITEM:
		plr.Ore.changeItem(cast.ToInt(id), num)
	case condition.PASSENGER_PROFILE:
		if num >= 0 {
			plr.AddPassengerProfile(cast.ToInt(id))
		} else {
			plr.RemovePassengerProfile(cast.ToInt(id))
		}
	case condition.PLANET_PROFILE:
		if num >= 0 {
			plr.AddPlanetProfile(cast.ToInt(id))
		} else {
			plr.RemovePlanetProfile(cast.ToInt(id))
		}
	}
	plr.TaTrackResChange(cond, change, sceneType)
	return num
}

func (plr *Player) GetNumByCondition(cond *Condition) int {
	_type := cond.GetType()
	id := cond.GetId()
	switch _type {
	case condition.DIAMOND:
		return plr.GetCurrency(_type)
	case condition.STAR_DUST:
		return plr.GetCurrency(_type)
	case condition.HEART:
		return plr.GetCurrency(_type)
	case condition.PROP:
		return plr.GetItemNum(cast.ToInt(id))
	case condition.Seed:
		plr.GetSeedNum(cast.ToInt(id))
	case condition.PASSENGER_FRAG:
		return plr.FragItems[cast.ToInt(id)]
	case condition.BLACK_HOLE_CURRENCY:
		return plr.BlackHole.Currency
	case condition.ORE_ITEM:
		val, ok := plr.Ore.OreItems[cast.ToInt(id)]
		if ok {
			return val
		}
	case condition.EQUIP:
		if cond.Extra != nil {
			uid, ok := cond.Extra["uid"]
			if ok && plr.GetEquipByUid(uid.(string)) != nil {
				return 1
			}
		}
	case condition.ARREST_CURRENCY:
		return plr.ArrestModule.Currency
	case condition.JACKPOT_COUNT:
		return plr.JackpotMod.JackpotTotalCount
	}
	return 0
}

func (plr *Player) CheckCondition(cond *Condition) bool {
	if cond == nil {
		return true
	}
	curNum := plr.GetNumByCondition(cond)
	return curNum >= cond.GetNum()
}

func (plr *Player) CheckConditions(conds []*Condition) (failed []*Condition) {
	failed = make([]*Condition, 0)
	if conds == nil || len(conds) == 0 {
		return failed
	}
	for _, cond := range conds {
		if !plr.CheckCondition(cond) {
			failed = append(failed, cond)
		}
	}
	return
}

func EquipsToConditions(equips []*EquipItem) (conds []*Condition) {
	conds = make([]*Condition, 0)
	for _, equip := range equips {
		conds = append(conds, equip.ToCondition())
	}
	return
}

// ChestRewardToCondition
/*
 * @description ChestReward配置转换成Condition
 * @param reward
 * @return *Condition
 */
func (plr *Player) ChestRewardToCondition(reward *cfg.ChestReward) *Condition {
	cond := &Condition{
		Type: reward.Type,
		Id:   reward.Id,
	}
	num := reward.Num
	if reward.MaxNum > 0 {
		num = ut.Random(reward.MinNum, reward.MaxNum)
	}
	if IsOutputCond(cond) && reward.Rate != 0 {
		num = ut.Round(cast.ToFloat64(plr.Train.GetAttrByCond(cond)) * reward.Rate)
	}
	num += reward.Base
	cond.Num = num
	if IsOutputCond(cond) {
		num := ut.Round(float64(cond.Num) * ut.RandomFloat64(-0.1, 0.1))
		num /= 10
		cond.Num += cast.ToInt(num) * 10
	}
	if cond.Type == condition.PROP {
		bean, ok := cfg.ItemContainer.GetBeanById(cast.ToInt(cond.Id))
		if ok {
			if bean.IsUnique == 1 {
				cond.Extra = map[string]any{
					"uid": GenUid(),
				}
			}
		}
	}
	return cond
}

func IsOutputCondType(typ, id int) bool {
	switch true {
	case typ == condition.STAR_DUST:
		fallthrough
	case typ == condition.HEART:
		return true
	case typ == condition.PROP:
		return id == item_id.ELECTRIC || id == item_id.WATER || id == item_id.Vitality
	}
	return false
}

func IsOutputCond(cond *Condition) bool { return IsOutputCondType(cond.Type, cast.ToInt(cond.Id)) }

// ConfigConditionConvert
/*
 * @description 用于转换配置数据中的ConfigCondition
 * @param inputConfigConditions
 * @return *configConditionConvertResult
 */
func ConfigConditionConvert(inputConfigConditions ...*cfg.ConfigCondition) *configConditionConvertResult {
	conditions := make([]*Condition, 0)
	for _, inputConfigCondition := range inputConfigConditions {
		conditions = MergeConditions(conditions, []*Condition{
			{
				Id:   inputConfigCondition.Id,
				Num:  inputConfigCondition.Num,
				Type: inputConfigCondition.Type,
			},
		})
	}
	return &configConditionConvertResult{conditions: conditions}
}

type configConditionConvertResult struct {
	conditions []*Condition
}

func (c *configConditionConvertResult) One() *Condition {
	if len(c.conditions) == 0 {
		return nil
	}
	return c.conditions[0]
}
func (c *configConditionConvertResult) All() []*Condition {
	return c.conditions
}

func NewCondition(id any, typ, num int) *Condition {
	return &Condition{
		Id:   id,
		Num:  num,
		Type: typ,
	}
}
