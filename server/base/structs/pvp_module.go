package structs

import (
	"context"
	"train/base/cfg"
	"train/common/pb"
	"train/db"
)

func NewPvpModule() *PvpModule {
	return &PvpModule{}
}

type PvpModule struct {
	TicketInfo map[pb.PvpType]int `bson:"info"`
	plr        *Player            `bson:"-"`
}

func (p *PvpModule) init(plr *Player) {
	p.plr = plr
	if p.TicketInfo == nil {
		p.TicketInfo = make(map[pb.PvpType]int)
	}
}

// 重置挑战次数
func (p *PvpModule) ResetTicket(typ pb.PvpType) {
	idx := int(typ)
	data := cfg.Misc_CContainer.GetObj().Pvp
	if idx >= len(data) {
		return
	}
	p.TicketInfo[typ] = data[idx].TicketMax
}

func (p *PvpModule) Unlock(typ pb.PvpType) {
	if p.TicketInfo == nil {
		p.TicketInfo = make(map[pb.PvpType]int)
	}
	if _, ok := p.TicketInfo[typ]; ok {
		return
	}
	p.ResetTicket(typ)
}

func (p *PvpModule) GetTicket(typ pb.PvpType) int {
	return p.TicketInfo[typ]
}

func (p *PvpModule) AddTicket(typ pb.PvpType, num int) int {
	v := p.GetTicket(typ) + num
	p.TicketInfo[typ] = v
	return v
}

func (p *PvpModule) ToPb() *pb.PvpModuleData {
	if p == nil {
		return nil
	}
	// misc := cfg.Misc_CContainer.GetObj()
	data := &pb.PvpModuleData{}
	data.Ticket = make(map[int32]int32)
	data.Duration = make(map[int32]int32)

	for T, V := range p.TicketInfo {
		data.Ticket[int32(T)] = int32(V)
		if T == pb.PvpType_NORMAL {
			duration := db.GetRedis().TTL(context.TODO(), db.RKPvpNormalRankSeason()).Val()
			if duration < 0 {
				duration = 0
			}
			data.Duration[int32(T)] = int32(duration.Milliseconds())
		}
	}
	return data
}
