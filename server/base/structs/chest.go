package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum/build_attr"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func InitChestModule() *ChestModule {
	return &ChestModule{
		Data:  make(map[int][]*Box),
		Medal: 0,
		Step:  0,
	}
}

type Box struct {
	Id  int `bson:"id"`
	Num int `bson:"num"`
}

func (b *Box) ToPb() *pb.BoxInfo {
	return &pb.BoxInfo{
		Id:  cast.ToInt32(b.Id),
		Num: cast.ToInt32(b.Num),
	}
}

// ChestModule 宝箱模块
type ChestModule struct {
	Data  map[int][]*Box `bson:"data"`
	Medal int            `bson:"medal"` // 积分
	Step  int            `bson:"cur"`   // 当前积分档位
}

func (c *ChestModule) ToPb() *pb.Chest {
	data := make(map[int32]*pb.BoxInfoArray, 0)
	for key, boxes := range c.Data {
		data[cast.ToInt32(key)] = &pb.BoxInfoArray{
			Data: lo.Map(boxes, func(box *Box, i int) *pb.BoxInfo {
				return box.ToPb()
			}),
		}
	}
	return &pb.Chest{
		Data:  data,
		Medal: cast.ToInt32(c.Medal),
		Step:  cast.ToInt32(c.Step),
	}
}

// GetNum 获取对应宝箱的数量
func (c *ChestModule) GetNum(typ int) int {
	i, ok := c.Data[typ]
	if !ok {
		return 0
	}
	cnt := 0
	for _, box := range i {
		cnt += box.Num
	}
	return cnt
}

func (c *ChestModule) GetBoxes(typ int) []*Box {
	return c.Data[typ]
}

func (plr *Player) AddChest(id int, cnt int) {
	bean, _ := cfg.ChestContainer.GetBeanById(id)
	if bean == nil {
		log.Error("AddChest, chest not found: %d", id)
		return
	}
	typ := bean.Type
	boxes := plr.Chest.GetBoxes(typ)
	if boxes != nil {
		box, _ := lo.Find(boxes, func(box *Box) bool {
			return box.Id == id
		})
		if box != nil {
			box.Num += cnt
			return
		}
		plr.Chest.Data[typ] = append(plr.Chest.Data[typ], &Box{Id: id, Num: cnt})
		return
	}
	plr.Chest.Data[typ] = []*Box{{Id: id, Num: cnt}}
}

func (plr *Player) ChestSingleOpen(boxId int) []*Condition {
	// 奖励配置列表
	rewards := make([]*cfg.ChestReward, 0)
	bean, _ := cfg.ChestContainer.GetBeanById(boxId)
	// 必得奖励
	if bean.Reward != nil {
		rewards = append(rewards, bean.Reward...)
	}
	// 随机奖励
	rewards = append(rewards, bean.GetRandomReward()...)
	output := make([]*Condition, 0)
	node := plr.PlanetData.GetLastBattleNode(false)
	config, _ := cfg.ChapterPlanetMonsterContainer.GetBeanByUnique(node.TypeId)
	if config == nil {
		log.Error("暂时加一个error log，如果没有通关记录这里会出错")
	}
	isFudai := bean.IsFudai()
	lo.ForEach(rewards, func(reward *cfg.ChestReward, i int) {
		R := &Condition{
			Type: reward.Type,
			Id:   reward.Id,
			Num:  reward.Num,
		}
		switch reward.Type {
		case condition.PASSENGER: // 随机获得一位乘客
			pid := cast.ToInt(R.Id)
			if pid == 0 {
				R = plr.CheckChangePassenger(plr.RandomPassenger(), output)
			}
		case condition.PassengerChip: // 乘客碎片
			if cast.ToInt(R.Id) == 0 {
				// 碎片 而不是本体
				R.Id = plr.RandomPassenger().Id
			}
			R.Type = condition.PROP
		case condition.STAR_DUST: // 星尘
			R.Num = lo.If(!isFudai, config.GetBox(build_attr.STAR)).Else(plr.Train.GetAttrByCond(R))
		case condition.HEART: // 爱心
			R.Num = lo.If(!isFudai, config.GetBox(build_attr.HEART)).Else(plr.Train.GetAttrByCond(R))
		case condition.PROP: // 处理水/电
			if reward.Id == item_id.ELECTRIC {
				R.Num = lo.If(!isFudai, config.GetBox(build_attr.ELECTRICITY)).Else(plr.Train.GetAttrByCond(R))
			}
			if reward.Id == item_id.WATER {
				R.Num = lo.If(!isFudai, config.GetBox(build_attr.WATER)).Else(plr.Train.GetAttrByCond(R))
			}
		}
		// 倍率
		if reward.Rate != 0 {
			R.Num = cast.ToInt(reward.Rate * float64(R.Num))
		}
		if R.Num > 0 {
			output = append(output, R)
		}
	})
	// 增加积分 已经弃用，暂时不删除，万一哪天又用上了
	plr.Chest.Medal += bean.Medal
	return output
}

// ChestOpen 开宝箱
func (plr *Player) ChestOpen(typ, cnt int) (int, []*Condition) {
	num := plr.Chest.GetNum(typ)
	if num < cnt || cnt <= 0 {
		return 1, nil
	}
	boxes := plr.Chest.GetBoxes(typ)
	output := make([]*Condition, 0)

	randomBoxes := lo.Filter(boxes, func(box *Box, i int) bool {
		return box.Num > 0
	})
	removed := false
	for {
		if cnt <= 0 {
			break
		}
		idx := ut.Random(0, len(randomBoxes)-1)
		box := randomBoxes[idx]
		result := plr.ChestSingleOpen(box.Id)
		plr.GrantRewards(result, ta.ResChangeSceneTypeChest)
		output = append(output, result...)
		box.Num--
		if box.Num <= 0 {
			randomBoxes = append(randomBoxes[:idx], randomBoxes[idx+1:]...)
			removed = true
		}
		cnt--
	}
	if removed {
		boxes = lo.Filter(boxes, func(box *Box, i int) bool { return box.Num > 0 })
		if len(boxes) == 0 {
			delete(plr.Chest.Data, typ)
		} else {
			plr.Chest.Data[typ] = boxes
		}
	}
	return 0, output
}
