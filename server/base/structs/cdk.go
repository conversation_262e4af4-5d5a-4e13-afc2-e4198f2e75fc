package structs

import (
	"context"
	"train/common/ta"
	"train/db"
	ut "train/utils"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type CdkRecord struct {
	Uid    string   `bson:"uid"`    // 玩家id
	Record []string `bson:"record"` // 兑换记录
}

type Cdk struct {
	Code      string       `bson:"code"`
	Limit     int          `bson:"limit"`  // 兑换次数限制
	GetNum    int          `bson:"getNum"` // 已领取次数
	StartTime int          `bson:"startTime"`
	EndTime   int          `bson:"endTime"`
	Rewards   []*Condition `bson:"rewards"`
}

func (plr *Player) IsUseCdk(code string) bool {
	countDocuments, _ := db.CDK_RECORD.GetCollection().CountDocuments(context.TODO(), bson.M{"uid": plr.Id, "record": bson.M{"$in": []string{code}}})
	return countDocuments != 0
}

// UseCdk 使用兑换码
func (plr *Player) UseCdk(code string) (int32, []*Condition) {
	// 自己已经兑换过了
	if plr.IsUseCdk(code) {
		return 1, nil
	}
	singleResult := db.CDK.GetCollection().FindOne(context.TODO(), bson.M{"code": code})
	var cdk *Cdk
	err = singleResult.Decode(&cdk)
	// 兑换码不存在
	if err != nil || cdk == nil {
		return 2, nil
	}
	// 兑换码已结束 || 兑换码未开始
	if cdk.EndTime < ut.Now() || cdk.StartTime > ut.Now() {
		return 3, nil
	}
	// 开始兑换
	plr.GrantRewards(cdk.Rewards, ta.ResChangeSceneTypeCdk)
	// 存储自身使用次数
	_, _ = db.CDK_RECORD.GetCollection().UpdateOne(context.TODO(), bson.M{"uid": plr.Id}, bson.M{"$push": bson.M{"record": bson.M{"$each": []string{code}}}}, options.Update().SetUpsert(true))
	// 存储兑换码被兑换次数
	_, _ = db.CDK.GetCollection().UpdateOne(context.TODO(), bson.M{"code": code}, bson.M{"$inc": bson.M{"getNum": 1}})
	return 0, cdk.Rewards
}
