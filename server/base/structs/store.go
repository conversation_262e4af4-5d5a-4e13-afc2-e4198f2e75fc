package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum/function_type"
	"train/base/enum/item_type"
	"train/base/enum/store_type"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func NewStoreMod(plr *Player) *StoreMod {
	mod := &StoreMod{
		plr:  plr,
		List: make(map[int]*BaseStoreData),
	}
	return mod
}

type StoreMod struct {
	plr  *Player                `bson:"-"`
	List map[int]*BaseStoreData `bson:"list"`
}

func (s *StoreMod) ToPb() *pb.Store {
	return &pb.Store{
		List: lo.MapToSlice(s.List, func(k int, v *BaseStoreData) *pb.StoreInfo { return v.ToPb() }),
	}
}

func (s *StoreMod) init(plr *Player) {
	s.plr = plr
	for _, store := range s.List {
		store.plr = plr
	}
	// 默认黑市
	s.AddStore(store_type.BLACK)
	s.AddStore(store_type.BLACK_HOLE)
	s.AddStore(store_type.ARREST)
	s.AddStore(store_type.RESOURCE)

}

// Get
/*
 * @description 获取商店
 * @param id
 * @return *BaseStoreData
 */
func (s *StoreMod) Get(id int) *BaseStoreData {
	return s.List[id]
}

// DailyUpdateCheck
/*
 * @description 每日刷新检查 预留检查7天刷新 or 30天刷新的逻辑 可以先不管
 */
func (s *StoreMod) DailyUpdateCheck() {
	for _, data := range s.List {
		if data.IsCanUpdate() {
			data.Refresh()
		}
	}
}

// AddStore
/*
 * @description 添加商店
 * @param id 商店类型
 * @return bool 是否添加成功
 */
func (s *StoreMod) AddStore(id int) *BaseStoreData {
	store, _ := s.List[id]
	if store == nil {
		store = &BaseStoreData{Id: id, plr: s.plr}
		s.List[id] = store
		if id == store_type.RESOURCE {
			store.Refresh()
		}
	}
	return store
}

type BaseStoreData struct {
	Id             int              `bson:"id"`             // 商店id
	RefreshCount   int              `bson:"refreshCount"`   // 刷新次数
	Goods          []*GoodData      `bson:"goods"`          // 商品
	LastUpdateTime int              `bson:"lastUpdateTime"` // 上次自动刷新时间
	plr            *Player          `bson:"-"`
	json           *cfg.StoreConfig `bson:"-"`
}

func (b *BaseStoreData) GetJson() *cfg.StoreConfig {
	if b.json == nil {
		b.json = cfg.GetMisc().Stores[b.Id-1]
	}
	return b.json
}

func (b *BaseStoreData) IsCanUpdate() bool {
	if b.GetRefreshSurplusTime() > 0 {
		return false
	}
	switch b.Id {
	case store_type.BLACK_HOLE:
		// 解锁后可以刷新
		return b.plr.isStepEndByUnlockFunc(function_type.BLACK_HOLE)
	default:
		return true
	}
}

func (b *BaseStoreData) GetRefreshSurplusTime() int {
	bean := b.GetJson()
	refreshTime := ut.If(bean.RefreshTime == 0, 1, bean.RefreshTime)
	return b.plr.GetToDaySurplusTime(b.LastUpdateTime, refreshTime)
}

func (b *BaseStoreData) Refresh() {
	b.DoUpdate()
	b.RefreshCount = 0
	b.LastUpdateTime = b.plr.GetNowTime()
}

func (b *BaseStoreData) ManualRefresh() {
	b.DoUpdate()
	b.RefreshCount++
}

// DoUpdate
/*
 * @description 刷新商店
 * @param plr
 * @return bool 是否刷新成功
 */
func (b *BaseStoreData) DoUpdate() {
	b.Goods = make([]*GoodData, 0)
	plr := b.plr
	offs := lo.Filter(cfg.StoreOffContainer.GetData(), func(storeOff *cfg.StoreOff[string], index int) bool { return storeOff.StoreId == b.Id })
	// 参与折扣的商店位
	discountIdx := make([]int, 0)
	for _, off := range offs {
		// 只有一个位置 必定打折
		if len(off.Goods) == 1 {
			discountIdx = append(discountIdx, off.Goods[0])
			continue
		}
		pos := off.GetRandomOffGoods()
		discountIdx = append(discountIdx, pos...)
	}

	storeData := lo.Filter(cfg.StoreContainer.GetData(), func(store *cfg.Store[string], index int) bool {
		if store.StoreId != b.Id {
			return false
		}
		return true
	})

	//时间之钥1
	if b.Id == store_type.BLACK_HOLE {
		if !plr.HasTimeStoneKey(item_id.TIME_STONE_KEY_1) {
			orgData := storeData[len(storeData)-1]
			storeData[len(storeData)-1] = &cfg.Store[string]{
				StoreId: store_type.BLACK_HOLE,
				PId:     orgData.PId,
				GoodsId: orgData.GoodsId,
				Goods:   &cfg.ConfigCondition{Type: condition.PROP, Id: 401, Num: 1, Weight: 1},
				Price:   orgData.Price,
				Stock:   1,
			}
			discountIdx = lo.Filter(discountIdx, func(i int, _ int) bool { return i != orgData.PId })
		}
	}

	// 已经添加过的商店位
	added := make([]int, 0)
	// 上一次添加的商店位
	lastAddPos := -1
	for _, data := range storeData {
		// 上一次添加的商店位和当前商店位相同  fail
		if lastAddPos == data.PId {
			continue
		}
		// 已经添加过的商店位  fail
		_, exists := lo.Find(added, func(i int) bool { return data.PId == i })
		if exists {
			continue
		}
		lastAddPos = data.PId
		goodData := &GoodData{}
		// 判断当前商店位是否参与折扣
		_, isDiscount := lo.Find(discountIdx, func(i int) bool { return data.PId == i })
		// 当前位置随机出一个商品
		allStoreGoods := make([]ut.DataWeight, 0)
		lo.ForEach(storeData, func(store *cfg.Store[string], i int) {
			if store.PId == data.PId {
				allStoreGoods = append(allStoreGoods, store)
			}
		})
		randomIdx := ut.RandomIndexByDataWeight(allStoreGoods)
		randomGoods := allStoreGoods[randomIdx].(*cfg.Store[string])

		goodId := cast.ToInt(randomGoods.Goods.Id)

		switch randomGoods.Goods.Type {
		case condition.PROP:
			if randomGoods.Goods.SubType == item_type.ROLE_TICKET {
				arr := lo.Filter(cfg.CharacterContainer.GetData(), func(character *cfg.Character[int], i int) bool {
					return character.Quality == goodId && !array.Some(b.Goods, func(good *GoodData) bool {
						return good.Goods.Type == randomGoods.Goods.Type && cast.ToInt(good.Goods.Id) == character.Id
					})
				})
				rdIdx := ut.Random(0, len(arr)-1)
				goodId = cast.ToInt(arr[rdIdx].Id)
			}
		}

		good := &Condition{
			Type: randomGoods.Goods.Type,
			Id:   goodId,
			Num:  randomGoods.Goods.Num,
		}
		cost := &Condition{
			Type: randomGoods.Price.Type,
			Id:   randomGoods.Price.Id,
		}

		switch good.Type {
		case condition.EQUIP:
			// 装备价格
			bean, _ := cfg.EquipContainer.GetBeanById(goodId)
			cost.Num = bean.GetPrice(randomGoods.Price.Type)
			// 装备默认是1级装备
			// good = b.plr.Equip.Make(goodId, 10).ToCondition()
		case condition.Chest:
			chestBean, _ := cfg.ChestContainer.GetBeanById(goodId)
			itemBean, _ := cfg.ItemContainer.GetBeanById(chestBean.Type)
			cost.Num = itemBean.GetPrice(randomGoods.Price.Type)
		default:
			// item价格
			bean, _ := cfg.ItemContainer.GetBeanById(goodId)
			cost.Num = bean.GetPrice(randomGoods.Price.Type)
		}
		// 默认1折 （不打折）
		goodData.Discount = 100
		if isDiscount {
			offBean, _ := cfg.OffContainer.GetBeanById(randomGoods.OffId)
			dis := offBean.RandomOff()
			goodData.Discount = cast.ToInt(dis * 100)
			cost.Num = ut.Ceil(cast.ToFloat64(cost.Num) * dis)
		}
		// 最终价格= 折扣价 * 物品数量
		cost.Num *= lo.If(good.Num <= 0, 1).Else(good.Num)
		goodData.Cost = cost
		goodData.Goods = good
		goodData.Stock = randomGoods.Stock
		b.Goods = append(b.Goods, goodData)
	}
}

func (b *BaseStoreData) ToPb() *pb.StoreInfo {
	return &pb.StoreInfo{
		Id:           int32(b.Id),
		RefreshCount: int32(b.RefreshCount),
		Goods:        lo.Map(b.Goods, func(good *GoodData, index int) *pb.Goods { return good.ToPb() }),
		RefreshTime:  int32(b.GetRefreshSurplusTime()),
	}
}

type GoodData struct {
	Stock    int        `bson:"stock"`    // 库存
	Discount int        `bson:"discount"` // 折扣
	Cost     *Condition `bson:"cost"`     // 消耗
	Goods    *Condition `bson:"goods"`    // 商品
}

func (g *GoodData) ToPb() *pb.Goods {
	if g == nil {
		return nil
	}
	return &pb.Goods{
		Stock:    int32(g.Stock),
		Discount: int32(g.Discount),
		Cost:     g.Cost.ToPb(),
		Item:     g.Goods.ToPb(),
	}
}

func (g *GoodData) SetSellDone() {
	g.Stock = 0
}

func (g *GoodData) IsSellDone() bool {
	return g.Stock <= 0
}

func BuildGoodData() *GoodData {
	return (&GoodData{}).SetDiscount(100)
}

func (g *GoodData) SetStock(stock int) *GoodData {
	g.Stock = stock
	return g
}

func (g *GoodData) SetDiscount(discount int) *GoodData {
	g.Discount = discount
	return g
}

func (g *GoodData) SetCost(cost *Condition) *GoodData {
	g.Cost = cost
	return g
}

func (g *GoodData) SetGoods(goods *Condition) *GoodData {
	g.Goods = goods
	return g
}

func (g *GoodData) DeductStock(cnt int) {
	g.Stock -= cnt
	if g.Stock <= 0 {
		g.Stock = 0
	}
}

// CheckGoodsBuy
/*
 * @description 检查商品购买
 * @param plr 玩家
 * @param goods 商品
 * @param buyCnt 购买数量
 * @param getCode 1:数据错误，2:商品没有库存了，3:购买消耗不足
 */
func CheckGoodsBuy(plr *Player, goods *GoodData, buyCnt int) (getCode func() int, buy func()) {
	code := 0
	getCode = func() int {
		return code
	}
	if plr == nil || goods == nil {
		code = 1
		return
	}
	buyCnt = ut.Max(1, buyCnt)
	if goods.IsSellDone() || goods.Stock < buyCnt {
		code = 2
		return
	}
	// 计算消耗
	if goods.Cost != nil {
		goods.Cost.Num *= buyCnt
	}
	if !plr.CheckCondition(goods.Cost) {
		code = 3
		return
	}
	buy = func() {
		plr.DeductCost(goods.Cost, ta.ResChangeSceneTypeStore)
		goods.Goods.Num *= buyCnt
		// 发放奖励
		plr.GrantReward(goods.Goods, ta.ResChangeSceneTypeStore)
		// 扣除库存
		goods.DeductStock(buyCnt)
		buy = nil
	}
	return
}
