package structs

import (
	"context"
	"train/db"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Area struct {
	Id       int    `bson:"id"`       // 区服id,需要创建唯一索引
	Name     string `bson:"name"`     // 区服名称
	OpenTime int    `bson:"openTime"` // 开区时间,也是创建时间.
	State    int    `bson:"state"`    // 区服状态
	Desc     string `bson:"desc"`     // 区服备注
}

// Save 数据保存
func (this *Area) Save() {
	db.GetCollection(db.AREA).UpdateOne(context.TODO(), &bson.M{
		"_id": this.Id,
	}, &bson.M{
		"$set": this,
	}, options.Update().SetUpsert(true))
}
