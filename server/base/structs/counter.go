package structs

import (
	"context"
	"train/db"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Counter 这是一个计数器
type Counter struct {
	Id   string           `bson:"id"`  // id
	seq  int              `bson:"seq"` //计数
	lock deadlock.RWMutex `bson:"-"`   // 锁
}

// IncrementAndGet 增加一次计数并当前返回当前计数,save代表着是否需要立即保存进db,通常情况下不需要,由job来决定更新.
func (this *Counter) IncrementAndGet(save bool) int {
	this.lock.Lock()
	defer this.lock.Unlock()
	this.seq++
	if save {
		this.Save()
	}
	return this.seq
}

// DecrementAndGet 减少一次计数并返回当前计数,save代表着是否需要立即保存进db,通常情况下不需要,由job来决定更新.
func (this *Counter) DecrementAndGet(save bool) int {
	this.lock.Lock()
	defer this.lock.Unlock()
	this.seq--
	if save {
		this.Save()
	}
	return this.seq
}

// Get 获取当前计数
func (this *Counter) Get() int {
	this.lock.Lock()
	defer this.lock.Unlock()
	return this.seq
}

// CompareValue 返回与compareObj相差的值
func (this *Counter) CompareValue(compareObj *Counter) int {
	this.lock.Lock()
	defer this.lock.Unlock()
	return this.Get() - compareObj.Get()
}

// Save 保存counter计数到db
func (this *Counter) Save() bool {
	this.lock.Lock()
	defer this.lock.Unlock()
	_, err := db.COUNTER.GetCollection().UpdateOne(context.TODO(), &bson.M{
		"id": this.Id,
	}, this, options.Update().SetUpsert(true))
	if err != nil {
		log.Error("Counter - Save error:%s", err.Error())
		return false
	}
	return true
}
