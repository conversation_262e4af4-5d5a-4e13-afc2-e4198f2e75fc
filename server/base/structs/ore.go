package structs

import (
	"time"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum"
	"train/base/enum/function_type"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"
	"train/utils/bfs"
	"train/utils/dfs"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// MinDepth 至少需要有几层
const MinDepth = 18

// ViewMinDepth 视图上需要有几层，即剩多少层时开始移动 从0开始计算
const ViewMinDepth = 8
const ViewRow = 6
const ViewCol = 9

// boss位置，现在是固定的
var bossX, bossY = 4, 2

// boss关联位置
var bossRef = []bfs.Point{
	{X: -1, Y: 0},
	{X: 1, Y: 0},
	{X: 0, Y: -1},
	{X: 0, Y: 1},
	{X: -1, Y: 1},
	{X: 1, Y: 1},
	{X: -1, Y: -1},
	{X: 1, Y: -1},
}

func NewOreModule(plr *Player) *OreModule {
	mod := &OreModule{
		LevelData: make([]*OreLevelData, 0),
	}
	return mod
}

type OreModule struct {
	OreItems  map[int]int     `bson:"oreItems"`  // 矿石数据
	LevelData []*OreLevelData `bson:"levelData"` // 难度对应的矿洞数据
	IsUnlock  bool            `bson:"isUnlock"`  // 是否解锁
	plr       *Player         `bson:"-" json:"-"`
}

func (o *OreModule) Init(plr *Player) {
	o.plr = plr
	if o.OreItems == nil {
		o.OreItems = make(map[int]int)
	}
	if o.LevelData != nil {
		for _, levelData := range o.LevelData {
			levelData.init(plr)
		}
	}
}

func (o *OreModule) Unlock() {
	if o.IsUnlock {
		return
	}
	o.IsUnlock = true
	o.plr.PushNew(enum.MARKNEW_NPC_DIALOG, []int{4004})

	// 功能模块解锁时发放满镐子
	oreConfig := cfg.Misc_CContainer.GetObj().Ore
	o.plr.GrantReward(&Condition{Type: condition.PROP, Id: item_id.OreBreakItem, Num: oreConfig.BreakMaxNum}, ta.ResChangeSceneTypeOre)
}

// GetLevelData
/*
 * @description 获取难度数据
 * @param level
 * @return *OreLevelData
 */
func (o *OreModule) GetLevelData(level int) *OreLevelData {
	levelData, _ := lo.Find(o.LevelData, func(data *OreLevelData) bool {
		return data.Level == level
	})
	return levelData
}

// UnlockLevelData
/*
 * @description 解锁新难度数据
 * @param level
 * @return *OreLevelData
 */
func (o *OreModule) UnlockLevelData(level int) *OreLevelData {
	levelData := &OreLevelData{Level: level, LastGenNextCeil: 2}
	levelData.init(o.plr)
	levelData.Generate(true)
	o.LevelData = append(o.LevelData, levelData)
	return levelData
}

// changeItem
/*
 * @description 修改矿石数量
 * @param id
 * @param num
 */
func (o *OreModule) changeItem(id, num int) {
	oldVal, ok := o.OreItems[id]
	if !ok {
		o.OreItems[id] = num
	} else {
		o.OreItems[id] = oldVal + num
	}
	log.Info("[%s] 矿石:%d, 数量:%d -> %d", o.plr.GetUid(), id, oldVal, o.OreItems[id])
}

// UpdateBreakItem
/*
 * @description 更新的时候恢复镐子
 * @param am 是不是凌晨的刷新
 */
func (o *OreModule) UpdateBreakItem(am bool) {
	if !o.plr.IsUnlockFunction(function_type.ORE) {
		return
	}
	oreConfig := cfg.Misc_CContainer.GetObj().Ore
	num := o.plr.GetItemNum(item_id.OreBreakItem)
	sendCnt := 0
	// 补差回复
	cnt := ut.Min(num, oreConfig.BreakMaxNum)
	sendCnt = oreConfig.BreakMaxNum - cnt
	if sendCnt > 0 {
		o.plr.GrantReward(&Condition{Type: condition.PROP, Id: item_id.OreBreakItem, Num: sendCnt}, ta.ResChangeSceneTypeOre)
	}
}

// NextRecoverTime
/*
 * @description 下一次回复稿子的剩余时间
 * @return int
 */
func (o *OreModule) NextRecoverTime() int {
	oreConfig := cfg.Misc_CContainer.GetObj().Ore
	num := o.plr.GetItemNum(item_id.OreBreakItem)
	// 超过上限，不发时间
	if num >= oreConfig.BreakMaxNum {
		return 0
	}

	plr := o.plr
	misc := cfg.Misc_CContainer.GetObj()
	am := ut.TimeUntilNextRefresh(plr.LastUpdateTime, plr.GetNowTime(), misc.RefreshTime, 1)
	pm := ut.TimeUntilNextRefresh(plr.LastUpdateTime, plr.GetNowTime(), misc.RefreshTimePm, 1)
	return ut.Min(am, pm)
}

func (o *OreModule) ToPb() *pb.Ore {
	ore := new(pb.Ore)
	ore.RecoverTime = int32(o.NextRecoverTime())
	ore.OreItems = make(map[int32]int32)
	for k, v := range o.OreItems {
		ore.OreItems[int32(k)] = int32(v)
	}
	ore.Data = lo.Map(o.LevelData, func(t *OreLevelData, i int) *pb.OreLevelData {
		return t.ToPb()
	})
	ore.IsUnlock = o.IsUnlock
	return ore
}

type OreLevelData struct {
	Level           int                     `bson:"level"`           // 难度等级
	Depth           int                     `bson:"depth"`           // 当前深度
	Data            [][]*OreCeil            `bson:"data"`            // 地块数据
	IsSpecialArea   bool                    `bson:"isSpecialArea"`   // 是否处于特殊区域层
	GenNextPageType pb.OrePageNextType      `bson:"genNextPageType"` // 下一页生成的类型
	LastGenNextCeil int                     `bson:"lastGenNextCeil"` // 上一页生成的传送门位置
	Records         map[int]*OreLevelRecord `bson:"records"`         //记录
	plr             *Player                 `bson:"-" json:"-"`
}

func (o *OreLevelData) init(plr *Player) {
	o.plr = plr
	if o.Records == nil {
		o.Records = make(map[int]*OreLevelRecord)
	}
}

func getRandomNext() (int, int) {
	// 只在最后一列出现
	return ViewCol - 1, ut.Random(0, ViewRow-1)
}

func getRandomSpecialNext() (int, int) {
	ary := []int{4, 5}
	// 只在前1-6列，第4&5行出现
	return ut.Random(1, ViewCol-3), ary[ut.Random(0, 1)]
}

// getRandomNextSpecial
/*
 * @description 获取随机特殊区域传送门的格子 只会在下面两排 避开第一列，免得和起点冲突
 * @param x 需要排除的x  用来保证不和下一层传送门重复
 * @param y 需要排除对y  用来保证不和下一层传送门重复
 * @return int
 * @return int
 */
func getRandomNextSpecial(x, y int) (int, int) {
	a, b := getRandomSpecialNext()
	if a == x && b == y {
		return getRandomSpecialNext()
	}
	return a, b
}

// getRandomBack
/*
 * @description 从特殊区域返回，只会在第一行和第二行
 * @return int
 * @return int
 */
// func getRandomBack() (int, int) {
// 	ary := []int{0, 1}
// 	return ut.Random(1, ViewCol-1), ary[ut.Random(0, 1)]
// }

// Generate
/*
 * @description 生成地块
 */
func (o *OreLevelData) Generate(init bool) [][]*OreCeil {
	depth := MinDepth
	if o.Data == nil {
		o.Data = make([][]*OreCeil, 0)
	}
	if len(o.Data) >= MinDepth {
		return nil
	}
	depth = depth - len(o.Data)

	currentDepth := o.Depth
	startDepth := currentDepth + len(o.Data)
	cnt := 0
	debugStartTime := time.Now()
	page := lo.If(init, 2).Else(1)
	log.Info("开始生成地块，从%d开始，生成%d层(%d页)，难度：%d", startDepth, depth, page, o.Level)
	randomResult := OreGenerateLogic(page, startDepth, o.Level, false, o, false, 0, 0)
	o.Data = append(o.Data, randomResult...)
	log.Debug("[%s] ore generate, retry times：%d, dur: %fs", o.plr.Id, cnt, time.Since(debugStartTime).Seconds())
	return randomResult
}

func (o *OreLevelData) GetTerrian(depth int) int {
	misc := cfg.GetMisc()
	terrianBeans := misc.Ore.Terrian
	noBoss := false
	boss := o.GetTimeStoneBoss()
	if boss == nil {
		noBoss = true
	} else {
		monster := boss.Monster[0]
		lv := monster.Level
		node := o.plr.PlanetData.GetLastBattleNode(false)
		if node != nil {
			battleId := node.TypeId
			bean, _ := cfg.ChapterPlanetMonsterContainer.GetBeanByUnique(battleId)
			if lv-10 > bean.Monster[0].Level {
				noBoss = true
			}
		} else {
			noBoss = true
		}
	}
	if noBoss {
		data := array.Find(terrianBeans, func(t *cfg.ConfigCondition) bool {
			return t.Type == 4
		})
		terrianBeans = lo.Filter(terrianBeans, func(t *cfg.ConfigCondition, i int) bool {
			return t.Type != 4
		})
		index := array.FindIndex(terrianBeans, func(t *cfg.ConfigCondition) bool {
			return t.Type == 0
		})
		if index != -1 {
			terrianBeans[index] = &cfg.ConfigCondition{Type: 0, Weight: terrianBeans[index].Weight + data.Weight}
		}
	}
	weightSum := lo.Reduce(terrianBeans, func(sum int, t *cfg.ConfigCondition, i int) int { return sum + t.Weight }, 0)
	terrains := make([]int, weightSum)
	max := lo.MaxBy(terrianBeans, func(a *cfg.ConfigCondition, b *cfg.ConfigCondition) bool { return a.Weight > b.Weight })
	maxWeight := max.Weight
	maxSplit := make([]int, 0)
	remain := weightSum - maxWeight
	if remain < maxWeight {
		maxSplit = ut.NumAvgSplit(maxWeight, remain)
		maxWeight = remain
		weightSum = maxWeight + remain
	}
	typeArr := make([]int, weightSum)
	posArr := make([]int, weightSum)
	for i := 0; i < weightSum; i++ {
		posArr[i] = i
	}
	for _, bean := range terrianBeans {
		interval := 1
		weight := 1
		if bean == max {
			weight = maxWeight
		} else {
			weight = bean.Weight
		}
		interval = ut.Floor(float64(len(posArr)) / float64(weight))
		del := make([]int, 0)
		for i := 0; len(del) < weight; i += interval {
			index := posArr[i]
			typeArr[index] = bean.Type
			del = append(del, i)
		}
		for i := len(del) - 1; i >= 0; i-- {
			posArr = array.Splice(posArr, del[i])
		}
	}
	index := 0
	maxIndex := 0
	for _, t := range typeArr {
		if t == max.Type && len(maxSplit) > 0 {
			for j := 0; j < maxSplit[maxIndex]; j++ {
				terrains[index] = t
				index++
			}
			maxIndex++
		} else {
			terrains[index] = t
			index++
		}
	}

	page := ut.Floor(float64(depth) / float64(ViewCol))
	page %= len(terrains)
	return terrains[page]
}

// GetTerrianTrans
/*
 * @description
 * @param depth
 * @return pageNextType 下一页的类型
 * @return specialWayType 本页特殊传送门类型，-1就是无
 */
func (o *OreLevelData) GetTerrianTrans(depth int) (pageNextType pb.OrePageNextType, specialWayType pb.OreSpecialAreaType) {
	oreMaps := lo.Filter(cfg.OreMapContainer.GetData(), func(item *cfg.OreMap[int], index int) bool {
		return item.Level == o.Level && item.Page <= 10
	})
	minDepth := len(oreMaps) / ViewRow * ViewCol
	if depth > minDepth {
		depth -= minDepth
	}

	i := o.GetTerrian(depth)
	switch i {
	case 0:
		return pb.OrePageNextType_NormalNext, -1
	case 1:
		return pb.OrePageNextType_BlueNext, -1
	case 2:
		return pb.OrePageNextType_GrayNext, -1
	case 3:
		return pb.OrePageNextType_NormalNext, pb.OreSpecialAreaType_OreArea
	case 4:
		return pb.OrePageNextType_NormalNext, pb.OreSpecialAreaType_MonsterQunit
	}
	return pb.OrePageNextType_NormalNext, -1
}

func (o *OreLevelData) getOreLayerConfig() *cfg.OreLayer[int] {
	data := cfg.OreLayerContainer.GetData()

	times := 0
	for _, record := range o.Records {
		times += record.Times
	}
	layer := array.Find(data, func(layer *cfg.OreLayer[int]) bool {
		return layer.Layer >= times && layer.Level == o.Level
	})
	if layer == nil {
		layer = data[len(data)-1]
	}
	return layer
}

func (o *OreLevelData) ToPb() *pb.OreLevelData {
	if o == nil {
		return nil
	}
	oreLevelData := &pb.OreLevelData{
		Depth:         int32(o.Depth),
		Level:         int32(o.Level),
		IsSpecialArea: o.IsSpecialArea,
	}
	for _, row := range o.Data {
		oreRow := new(pb.OreRowData)
		oreRow.Data = make([]*pb.OreCeilData, 0)
		for _, ceil := range row {
			oreRow.Data = append(oreRow.Data, ceil.ToPb())
		}
		oreLevelData.Data = append(oreLevelData.Data, oreRow)
	}
	return oreLevelData
}

// GetData
/*
 * @description 获取矿洞格子数据
 * @param curPage 是否只获取当前页
 * @param getSpecial 是不是获取特殊区域数据(会影响bfs相关操作)，有特殊区域的page页是左右各一页，getSpecial=false获取左边正常页，getSpecial=true获取右边特殊页。
 * @return [][]*OreCeil
 * @return int 起点位置
 */
func (o *OreLevelData) GetData(curPage, getSpecial bool) ([][]*OreCeil, int, int) {
	data := o.Data
	// 只获取当前页
	if curPage {
		data = data[:ViewCol]
	}
	data = lo.RepeatBy(len(data), func(index int) []*OreCeil {
		// 获取特殊区域
		if getSpecial {
			return data[index][ViewRow:]
		}
		return data[index][:ViewRow]
	})
	startX, startY := -1, -1
	for y, col := range data {
		for x, item := range col {
			if getSpecial && item.Type == pb.OreCeilType_BlockBack {
				startY = y
				startX = x
				break
			} else if item.Type == pb.OreCeilType_BlockStartPs {
				startY = y
				startX = x
			}
		}
	}
	if startX == -1 || startY == -1 {
		log.Error("找不到起点？？？")
	}
	return data, startX, startY
}

// checkArrive
/*
 * @description 检查是否可以到达目标地块
 * @param tx
 * @param ty
 * @return bool
 */
func (o *OreLevelData) checkArrive(tx, ty int) bool {
	// 如果tx或者ty超出单页，就只拉取特殊区域数据
	data, x, y := o.GetData(true, tx >= ViewRow || ty >= ViewCol)
	startPos := &bfs.Point{X: x, Y: y}
	if tx >= ViewRow {
		tx -= ViewRow
	}
	if ty >= ViewCol {
		ty -= ViewCol
	}

	row := data[ty]
	ceil := row[tx]
	// 有引用的格子检查可达
	if ceil.RefCeil != nil {
		if len(ceil.RefCeil) == 1 {
			ref := ceil.RefCeil[0]
			ceil = data[ref.GetX()][ref.GetY()]
		}
		for _, ref := range ceil.RefCeil {
			gx := ref.GetX()
			gy := ref.GetY()
			grid := toGridArray(data, func(ceil *OreCeil, x, y int) int {
				if ceil == nil {
					return 1
				}
				if ceil.TypeCanPass() || gx == x && gy == y {
					return 0
				}
				return 1
			})
			if bfs.IsReachableWithStart(grid, startPos, &bfs.Point{X: gx, Y: gy}) {
				return true
			}
		}
	}

	grid := toGridArray(data, func(ceil *OreCeil, x, y int) int {
		if ceil == nil {
			return 1
		}
		if ceil.TypeCanPass() || tx == x && ty == y {
			return 0
		}
		return 1
	})
	return bfs.IsReachableWithStart(grid, startPos, &bfs.Point{X: tx, Y: ty})
}

// 转换格子数据，用于路径计算，callback返回1表示不可通过，0表示可通过
func toGridArray(data [][]*OreCeil, callback func(ceil *OreCeil, x, y int) int) [][]int {
	grid := make([][]int, len(data))
	for i, row := range data {
		grid[i] = make([]int, len(row))
		for j, ceil := range row {
			grid[i][j] = callback(ceil, j, i)
		}
	}
	return grid
}

// 转换成地块格子数组
func (o *OreLevelData) toGridArray(curPage, isSpecial bool) [][]int {
	data, _, _ := o.GetData(curPage, isSpecial)
	page := toGridArray(data, func(ceil *OreCeil, x, y int) int {
		if ceil == nil {
			return 1
		}
		if ceil.TypeCanPass() {
			return 0
		}
		if ceil.RefCeil != nil {
			return 1
		}
		return 1
	})

	return page
}

// Action
/*
 * @description 地块操作
 * @param mod 矿洞数据模块
 * @param x 地块x坐标
 * @param y 地块y坐标
 * @param typ 操作类型 实际上没使用
 * @param extra 扩展参数，精英怪战斗上传的奖励计算次数
 * @return pb.OreCeilActionCode 操作响应code
 * @return [][]*OreCeil 新生成的地块（如果有的话）
 * @return []*Condition 获得的奖励（如果有的话），这里没发放给玩家
 */
func (o *OreLevelData) Action(mod *OreModule, x, y int, typ pb.OreCeilActionType, extra int) (pb.OreCeilActionCode, [][]*OreCeil, []*Condition) {
	data := o.Data
	if y < 0 || y >= len(data) {
		return pb.OreCeilActionCode_InvalidPosition, nil, nil
	}
	row := data[y]
	if x < 0 || x >= len(row) {
		return pb.OreCeilActionCode_InvalidPosition, nil, nil
	}

	// 检查格子是否可达
	if !o.checkArrive(x, y) {
		return pb.OreCeilActionCode_InvalidPosition, nil, nil
	}
	// 特殊区域不参与深度计算
	_, depthValueBefore := bfs.FindDeepestPoint(o.toGridArray(true, false))
	ceil := row[x]
	plr := o.plr
	clearView := false
	rewards := make([]*Condition, 0)
	switch ceil.Type {
	case pb.OreCeilType_BlockNone:
		fallthrough
	case pb.OreCeilType_BlockStartPs:
		if ceil.OreExtra != nil {
			rewards = ceil.OreExtra
			plr.GrantRewards(ceil.OreExtra, ta.ResChangeSceneTypeOre)
			ceil.clear()
		} else {
			return pb.OreCeilActionCode_InvalidActionType, nil, nil
		}
	case pb.OreCeilType_BlockSingle:
		fallthrough
	case pb.OreCeilType_BlockDoubleBreakage:
		fallthrough
	case pb.OreCeilType_BlockDouble:
		// 检查普通镐子
		cost := NewCondition(item_id.OreBreakItem, condition.PROP, 1)
		if !plr.CheckCondition(cost) {
			// 检查黄金稿子
			cost = NewCondition(item_id.OreBreakItemPlus, condition.PROP, 1)
			if !plr.CheckCondition(cost) {
				return pb.OreCeilActionCode_ItemNotEnough, nil, nil
			}
		}
		plr.DeductCost(cost, ta.ResChangeSceneTypeOre)
		o.handleBreak(ceil)
	case pb.OreCeilType_BlockMonster:
		fallthrough
	case pb.OreCeilType_BlockMonsterQunit:
		o.DestroyCeil(ceil)
	case pb.OreCeilType_BlockNext:
		if ceil.Type != pb.OreCeilType_BlockNext {
			return pb.OreCeilActionCode_InvalidActionType, nil, nil
		}
		ceil.Type = pb.OreCeilType_BlockNone
		clearView = true
	case pb.OreCeilType_BlockNextSpecial:
		//o.DestroyCeil(ceil)
		o.IsSpecialArea = true
	case pb.OreCeilType_BlockBack:
		o.IsSpecialArea = false
	case pb.OreCeilType_BlockMonsterRunAway:
		// 逃跑地精
		cnt := cfg.Misc_CContainer.GetObj().Ore.BossCondition * extra
		layerConfig := o.getOreLayerConfig()
		for i := 0; i < cnt; i++ {
			reward := ToCondition(layerConfig.RandomOreReward().MapData())
			rewards = append(rewards, reward)
		}
		// 发放奖励
		if ceil.OreExtra != nil {
			rewards = append(rewards, ceil.OreExtra...)
		}
		rewards = MergeConditions([]*Condition{}, rewards)
		plr.GrantRewards(rewards, ta.ResChangeSceneTypeOre)
		o.Record(ceil)
		o.DestroyCeil(ceil)
	case pb.OreCeilType_BlockItemBoom:
		o.handleBomb(x, y)
	case pb.OreCeilType_BlockItemDrill:
		o.handleDrill(x, y)
	default:
		return pb.OreCeilActionCode_InvalidActionType, nil, nil
	}
	return pb.OreCeilActionCode_OnSuccess, o.checkMoveDepth(depthValueBefore, clearView), rewards
}

// CheckMoveDepth
/*
 * @description 检查是否需要进行深度下移，同时消掉顶层地块行
 * @param before 操作前最深点
 */
func (o *OreLevelData) checkMoveDepth(before int, clearView bool) (data [][]*OreCeil) {
	// 不再移动
	//// 大于等于视图显示深度时才开始移动
	//if y >= ViewMinDepth {
	//	// 只移动一层a
	//	o.Data = o.Data[y-(ViewMinDepth-1):]
	//}

	if clearView {
		if o.Depth < before {
			o.Depth = before
		}
		o.Depth += ViewCol - before
		// 清理掉当前视图内的9层
		o.Data = o.Data[ViewCol:]
		data = o.Generate(false)
		before = 0
	}

	x, y := bfs.FindDeepestPoint(o.toGridArray(true, false))
	if x == -1 || y == before {
		return data
	}

	o.Depth += y - before
	return data
}

// BombGrid 炸弹的生效点位
var BombGrid = []dfs.Point{
	{X: 0, Y: -2},
	{X: -1, Y: -1}, {X: 0, Y: -1}, {X: 1, Y: -1},
	{X: -2, Y: 0}, {X: -1, Y: 0}, {X: 1, Y: 0}, {X: 2, Y: 0},
	{X: -1, Y: 1}, {X: 0, Y: 1}, {X: 1, Y: 1},
	{X: 0, Y: 2},
}

// 使用炸弹
func (o *OreLevelData) handleBomb(x, y int) {
	getSpecial := x >= ViewRow || y >= ViewCol
	data, _, _ := o.GetData(true, getSpecial)
	if x >= ViewRow {
		x -= ViewRow
	}
	if y >= ViewCol {
		y -= ViewCol
	}
	orgGrid := toGridArray(data, func(ceil *OreCeil, _x, _y int) int {
		if _x == x && _y == y {
			return 0
		}
		_, exists := lo.Find(BombGrid, func(item dfs.Point) bool {
			return item.X+x == _x && item.Y+y == _y
		})
		if !exists {
			return 1
		}
		if ceil.Type == pb.OreCeilType_BlockMonster {
			return 1
		}
		return 0
	})
	// 需要后续连续触发的
	continuousDealItems := make([]*bfs.Point, 0)
	start := &bfs.Point{X: x, Y: y}
	for _, point := range BombGrid {
		_y := y + point.Y
		if _y >= len(data) || _y < 0 {
			continue
		}
		row := data[_y]
		if row == nil {
			continue
		}
		_x := x + point.X
		if _x >= len(row) || _x < 0 {
			continue
		}
		ceil := row[_x]
		if ceil == nil {
			continue
		}
		if bfs.IsReachableWithStart(orgGrid, start, &bfs.Point{X: _x, Y: _y}) {
			if ceil.TypeNotCanDestroyByItem() {
				continue
			}
			// 连续触发
			if ceil.Type == pb.OreCeilType_BlockItemDrill || ceil.Type == pb.OreCeilType_BlockItemBoom {
				continuousDealItems = append(continuousDealItems, &bfs.Point{
					X: lo.If(getSpecial, _x+ViewRow).Else(_x),
					Y: lo.If(getSpecial, _y+ViewCol).Else(_y),
				})
				continue
			}
			o.DestroyCeil(ceil)
		}
	}
	// 销毁自己
	o.DestroyCeil(data[y][x])
	o.handleContinuous(continuousDealItems)
}

// 使用钻头
func (o *OreLevelData) handleDrill(x, y int) {
	getSpecial := x >= ViewRow || y >= ViewCol
	data, _, _ := o.GetData(true, getSpecial)
	if x >= ViewRow {
		x -= ViewRow
	}
	if y >= ViewCol {
		y -= ViewCol
	}
	self := data[y][x]
	dir := self.Direction
	var moveDo func()
	var moveCondition func() bool
	sx, sy := x, y
	switch dir {
	case pb.OreBlockItemDrillDirection_Left:
		moveDo, moveCondition = func() { sy-- }, func() bool { return sy >= 0 }
	case pb.OreBlockItemDrillDirection_Top:
		moveDo, moveCondition = func() { sx-- }, func() bool { return sx >= 0 }
	case pb.OreBlockItemDrillDirection_Right:
		moveDo, moveCondition = func() { sy++ }, func() bool { return sy < ViewCol }
	case pb.OreBlockItemDrillDirection_Bottom:
		moveDo, moveCondition = func() { sx++ }, func() bool { return sx < ViewRow }
	}
	// 销毁自己并移动一次
	o.DestroyCeil(self)
	moveDo()
	// 需要后续连续触发的
	continuousDealItems := make([]*bfs.Point, 0)
	for ; moveCondition(); moveDo() {
		row := data[sy]
		ceil := row[sx]
		if ceil == nil {
			log.Error("为什么会出现空格子？？？")
			continue
		}
		// 无法穿过，就停止
		if ceil.Type == pb.OreCeilType_BlockMonster {
			break
		}
		// 能穿过，但是不能销毁
		if ceil.TypeNotCanDestroyByItem() {
			continue
		}
		// 连续触发
		if ceil.Type == pb.OreCeilType_BlockItemDrill || ceil.Type == pb.OreCeilType_BlockItemBoom {
			continuousDealItems = append(continuousDealItems, &bfs.Point{
				X: lo.If(getSpecial, sx+ViewRow).Else(sx),
				Y: lo.If(getSpecial, sy+ViewCol).Else(sy),
			})
			continue
		}
		o.DestroyCeil(ceil)
	}
	o.handleContinuous(continuousDealItems)
}

// 道具连续触发
func (o *OreLevelData) handleContinuous(data []*bfs.Point) {
	lo.ForEach(data, func(item *bfs.Point, index int) {
		o.Action(nil, item.X, item.Y, 0, 0)
	})
}

// handleBreak
/*
 * @description 进行一次地块破坏
 * @param ceil 目标地块
 * @return bool 是否破坏完成
 */
func (o *OreLevelData) handleBreak(ceil *OreCeil) bool {
	if ceil.Type == pb.OreCeilType_BlockDouble {
		// 变成破损状态
		ceil.Type = pb.OreCeilType_BlockDoubleBreakage
		return false
	}
	o.DestroyCeil(ceil)
	return true
}

// DestroyCeil
/*
 * @description 地块被摧毁，变成空地
 */
func (o *OreLevelData) DestroyCeil(ceil *OreCeil) {
	// 如果有奖励 需要发放
	switch ceil.Type {
	case pb.OreCeilType_BlockSingle:
		fallthrough
	case pb.OreCeilType_BlockDouble:
		fallthrough
	case pb.OreCeilType_BlockDoubleBreakage:
		fallthrough
	case pb.OreCeilType_BlockMonster:
		if ceil.OreExtra != nil {
			o.plr.GrantRewards(ceil.OreExtra, ta.ResChangeSceneTypeOre)
			o.Record(ceil)
		}
	case pb.OreCeilType_BlockMonsterQunit:
		// 精英怪
		pageData, _, _ := o.GetData(true, true)
		lo.ForEach(ceil.RefCeil, func(ref *OreCeilRef, index int) {
			o.DestroyCeil(pageData[ref.GetX()][ref.GetY()])
		})
		if ceil.OreExtra != nil {
			o.plr.GrantRewards(ceil.OreExtra, ta.ResChangeSceneTypeOre)
			o.Record(ceil)
		}
	}
	ceil.Type = pb.OreCeilType_BlockNone
	ceil.clear()
}

func (o *OreLevelData) Record(ceil *OreCeil) {
	if ceil.OreExtra == nil {
		return
	}
	rewards := ceil.OreExtra
	for _, reward := range rewards {
		if reward.GetType() == condition.ORE_ITEM {
			id := cast.ToInt(reward.GetId())
			record := o.Records[id]
			if record == nil {
				record = &OreLevelRecord{}
			}
			record.Times++
			record.Num += reward.GetNum()
			o.Records[id] = record
		}
	}
	// times := 0
	// for _, record := range o.Records {
	// 	times += record.Times
	// }
	// log.Debug("ore level data record times:%d", times)
}

type OreCeilRef struct {
	X int `bson:"x"`
	Y int `bson:"y"`
}

func (or *OreCeilRef) GetX() int {
	return or.X
}

func (or *OreCeilRef) GetY() int {
	return or.Y - ViewRow
}

type OreCeil struct {
	Type      pb.OreCeilType                `bson:"type"`      // 地块类型
	OreExtra  []*Condition                  `bson:"oreExtra"`  // 矿石数据 or 奖励数据
	Monsters  []*BattleRole                 `bson:"monsters"`  // 怪物数据
	RefCeil   []*OreCeilRef                 `bson:"refCeil"`   // 引用关联
	NextType  pb.OrePageNextType            `bson:"nextType"`  // 下一层矿区类型
	Direction pb.OreBlockItemDrillDirection `bson:"direction"` // 钻头方向
}

func (o *OreCeil) clear() {
	o.OreExtra = nil
	o.Monsters = nil
	o.RefCeil = nil
}

// TypeCanPass
/*
 * @description 空地，起点，特殊区域的返回，特殊区域的传送，传送门
 * @return bool
 */
func (o *OreCeil) TypeCanPass() bool {
	return o.Type == pb.OreCeilType_BlockNone ||
		o.Type == pb.OreCeilType_BlockStartPs ||
		o.Type == pb.OreCeilType_BlockBack ||
		o.Type == pb.OreCeilType_BlockNextSpecial ||
		o.Type == pb.OreCeilType_BlockNext
}

// TypeNotCanDestroyByItem
/*
 * @description 能被道具摧毁的地块
 * @return bool
 */
func (o *OreCeil) TypeNotCanDestroyByItem() bool {
	bol := o.Type == pb.OreCeilType_BlockMonster ||
		o.Type == pb.OreCeilType_BlockMonsterRunAway ||
		o.Type == pb.OreCeilType_BlockNextSpecial ||
		o.Type == pb.OreCeilType_BlockNext ||
		o.Type == pb.OreCeilType_BlockBack ||
		o.Type == pb.OreCeilType_BlockMonsterQunit ||
		o.Type == pb.OreCeilType_BlockStartPs
	return bol
}

// InitWithConfig
/*
 * @description 根据配置初始化地块
 * @param config
 * @param level 当前难度等级
 * @param depth 当前深度
 * @param x 位置
 * @param y 位置
 * @param canNotIsSpecialWay 不能是特殊区域传送门
 * @param canNotIsBattle 不能是战斗格子（紫色）
 * @param canNotIsSpecial 不能是不可敲击格子（黑色）
 * @param specialWay 使用特殊概率((非战斗)特殊区域传送门使用)
 * @param orePageType pb.OrePageNextType 日常矿区类型 提高对应矿石概率
 * @param blankHaseOre 空地能不能有矿石
 * @return *OreCeil
 */
func (o *OreCeil) InitWithConfig(config *cfg.OreLayer[int], level, depth, x, y int, canNotIsSpecialWay, canNotIsBattle, canNotIsSpecial, specialWay bool, orePageType pb.OrePageNextType, blankHaseOre bool, levelData *OreLevelData) *OreCeil {
	if specialWay {
		canNotIsBattle = true
		canNotIsSpecial = true
	}
	_cfg := cfg.Misc_CContainer.GetObj().Ore
	// 矿石生成概率提升
	genRate := 1
	genRateMax := 1.0

	// (非战斗)特殊区域的矿石概率提升
	if specialWay {
		genRate = _cfg.TreasureValue
		genRateMax = _cfg.TreasureValueMax
	}

	w := config.GetWeightBy(func(lucky, blank, purple, blue, gray, black, boom, drill int) []int {
		if canNotIsSpecialWay {
			// 空地概率增加
			blank *= 10
		}
		if orePageType == pb.OrePageNextType_GrayNext {
			// 黑色概率增加
			black = ut.Round(float64(blue + gray))
		}
		if orePageType == pb.OrePageNextType_BlueNext {
			blue *= 10
		}
		if canNotIsBattle {
			purple = 0
		}
		if canNotIsSpecial {
			black = 0
		}
		// 首列不放空地
		if y%ViewCol == 0 {
			blank = 0
		}
		// 最后一列不放钻头
		if y == ViewCol-1 {
			drill = 0
		}
		weights := []int{blank, gray, blue, purple, black, 0, 0, 0, 0, 0, 0, boom, drill, 0}
		return weights
	})
	idx := ut.RandomIndexByTotalWeight(w)
	o.Type = pb.OreCeilType(idx)
	switch o.Type {
	case pb.OreCeilType_BlockNone:
		fallthrough
	case pb.OreCeilType_BlockStartPs:
		o.setAsBlank(config, blankHaseOre, genRate, genRateMax)
	case pb.OreCeilType_BlockSingle:
		o.setAsSingle(config, genRate, genRateMax)
	case pb.OreCeilType_BlockDouble:
		o.setAsSingle(config, genRate, genRateMax)
	case pb.OreCeilType_BlockMonster:
		o.setAsMonster(config, levelData)
	case pb.OreCeilType_BlockItemDrill:
		o.setDirection(x, y)
	}
	return o
}

// setAsBlank
/*
 * @description 作为空地格子
 * @param config
 * @param canHasOre 空地上能否产生宝石
 */
func (o *OreCeil) setAsBlank(config *cfg.OreLayer[int], canHasOre bool, rate int, max float64) {
	o.OreExtra = nil
	o.Monsters = nil
	if canHasOre {
		if config.RandomHasOre(rate, max) {
			ore := config.RandomOreReward()
			o.OreExtra = make([]*Condition, 0)
			o.OreExtra = append(o.OreExtra, ToCondition(ore.MapData()))
		}
	}
}

// setAsSingle
/*
 * @description 作为矿石格子 随机矿石数据
 * @param config
 * @param rate 概率需要翻n倍
 * @param max 概率不超过多少 范围 0-1
 */
func (o *OreCeil) setAsSingle(config *cfg.OreLayer[int], rate int, max float64) {
	if config.RandomHasOre(rate, max) {
		ore := config.RandomOreReward()
		o.OreExtra = make([]*Condition, 0)
		o.OreExtra = append(o.OreExtra, ToCondition(ore.MapData()))
	}
}

func (o *OreCeil) randomMonster(tot, index, lv int, monsters []*BattleRole) *BattleRole {
	monsterDatas := cfg.PlanetMonsterContainer.GetData()
	noMonsters := GetNoMonsters(tot, index, lv)
	monsterDatas = lo.Filter(monsterDatas, func(data *cfg.PlanetMonster[int], i int) bool {
		if array.Some(monsters, func(m *BattleRole) bool { return m != nil && m.Id == data.ID }) {
			return false
		}
		if array.Has(noMonsters, data.ID) {
			return false
		}
		return true
	})

	rdIndex := ut.Random(0, len(monsterDatas)-1)
	monster := monsterDatas[rdIndex]
	return &BattleRole{
		Id:     monster.ID,
		StarLv: cfg.GetMonsterStarByLevel(lv),
		Lv:     lv,
	}
}

// setAsMonster
/*
 * @description 作为怪物格子
 * @param config
 */
func (o *OreCeil) setAsMonster(config *cfg.OreLayer[int], levelData *OreLevelData) {
	plr := levelData.plr
	node := plr.PlanetData.GetLastBattleNode(false)
	monsterInfos := make([]*cfg.Monster, 0)
	if node != nil {
		battleId := node.TypeId
		bean, _ := cfg.ChapterPlanetMonsterContainer.GetBeanByUnique(battleId)
		monsterInfos = lo.Map(bean.Monster, func(monster *cfg.Monster, _ int) *cfg.Monster {
			return &cfg.Monster{Level: monster.Level, StarLv: monster.StarLv}
		})
	}

	// 看看要不要产生逃跑地精 暂时固定
	isMonsterRunAway := false
	count := 5
	enemies := make([]*BattleRole, count)
	for i := 0; i < 3; i++ {
		if ut.Chance(15) {
			monster := &BattleRole{
				Id: 2101,
				Lv: 1,
			}
			enemies[i] = monster
			isMonsterRunAway = true
		}
	}
	if isMonsterRunAway {
		o.Type = pb.OreCeilType_BlockMonsterRunAway
		enemies = ut.RandomArray(enemies)
	}
	for j := 0; j < count; j++ {
		if enemies[j] == nil {
			enemies[j] = o.randomMonster(count, j, 1, enemies)
		}
		if j < len(monsterInfos) {
			enemies[j].Lv = monsterInfos[j].Level
			enemies[j].StarLv = monsterInfos[j].StarLv
		}
	}
	o.OreExtra = genRandomBattleReward(config)
	// 随机奖励
	o.Monsters = enemies
}

// setDirection
/*
 * @description 随机设置钻头方向
 * @param x 格子所在位置
 * @param y 格子所在位置
 */
func (o *OreCeil) setDirection(x, y int) {
	dir := map[pb.OreBlockItemDrillDirection]int{
		pb.OreBlockItemDrillDirection_Left:   y,               // 左边还有y个格子
		pb.OreBlockItemDrillDirection_Right:  ViewCol - 1 - y, // 右边还有ViewCol-1-y个格子
		pb.OreBlockItemDrillDirection_Top:    x,               // 上边还有x个格子
		pb.OreBlockItemDrillDirection_Bottom: ViewRow - 1 - x, // 下边还有ViewRow-1-x个格子
	}

	// 首列方向不能是向左
	if y == 0 {
		delete(dir, pb.OreBlockItemDrillDirection_Left)
	}
	// 末列方向不能是向右
	if y == ViewCol-1 {
		delete(dir, pb.OreBlockItemDrillDirection_Right)
	}
	// 首行方向不能是向上
	if x == 0 {
		delete(dir, pb.OreBlockItemDrillDirection_Top)
	}
	// 末行方向不能是向下
	if x == ViewRow-1 {
		delete(dir, pb.OreBlockItemDrillDirection_Bottom)
	}

	maxWeight := 0
	var maxDir pb.OreBlockItemDrillDirection
	for d, weight := range dir {
		if weight > maxWeight {
			maxWeight = weight
			maxDir = d
		}
	}

	o.Direction = maxDir
}

// genRandomBattleReward
/*
 * @description 生成战斗随机奖励
 * @param config 矿区当前层数对应配置
 * @param cnt 随机次数，精英怪使用
 * @return []*Condition
 */
func genRandomBattleReward(config *cfg.OreLayer[int]) []*Condition {
	// 随机奖励
	randomReward := config.GetRandomReward()
	random := ConfigConditionConvert(randomReward).One()
	// 兼容，保底1次
	random.Num = ut.Max(1, random.Num)
	rewards := make([]*Condition, 0)
	for i := 0; i < random.Num; i++ {
		oreReward := config.RandomOreReward()
		rewards = append(rewards, ToCondition(oreReward.MapData()))
	}
	rewards = MergeConditions([]*Condition{}, rewards)
	return rewards
}

func (o *OreCeil) setAsMonsterQunit(page [][]*OreCeil, levelData *OreLevelData) {
	o.RefCeil = make([]*OreCeilRef, 0)
	o.Type = pb.OreCeilType_BlockMonsterQunit
	for _, point := range bossRef {
		x := bossX + point.X
		y := bossY + point.Y
		ceil := page[x][y]
		ceil.clear()
		ceil.Type = pb.OreCeilType_BlockMonster
		ceil.RefCeil = []*OreCeilRef{{bossX, bossY + ViewRow}}
		o.RefCeil = append(o.RefCeil, &OreCeilRef{X: x, Y: y + ViewRow})
	}
	boss := levelData.GetTimeStoneBoss()
	o.Monsters = lo.Map(boss.Monster, func(monster *cfg.Monster, _ int) *BattleRole {
		return &BattleRole{
			Id:     monster.Id,
			Lv:     monster.Level,
			StarLv: monster.StarLv,
		}
	})
	o.OreExtra = ToConditions(boss.Reward)
}

func (o *OreCeil) ToPb() *pb.OreCeilData {
	return &pb.OreCeilData{
		Type:     o.Type,
		OreExtra: ToPbConditions(o.OreExtra),
		Monsters: lo.Map(o.Monsters, func(t *BattleRole, i int) *pb.BattleRole {
			if t == nil {
				return nil
			}
			return t.ToPb()
		}),
		Ref: lo.Map(o.RefCeil, func(t *OreCeilRef, i int) *pb.Point {
			if t == nil {
				return nil
			}
			return &pb.Point{X: int32(t.X), Y: int32(t.Y)}
		}),
		NextType:  o.NextType,
		Direction: o.Direction,
	}
}

// CheckOreGenerateConfig
/*
 * @description  通过配置创建矿洞格子数据
 * @param curPage 当前页
 * @param level
 * @param depth
 * @return [][]*OreCeil
 */
func CheckOreGenerateConfig(curPage, level, depth int, levelData *OreLevelData) [][]*OreCeil {
	oreMapData := cfg.OreMapContainer.GetData()
	oreMaps := lo.Filter(oreMapData, func(item *cfg.OreMap[int], index int) bool {
		return item.Level == level && item.Page == curPage
	})
	if len(oreMaps) == 0 {
		return nil
	}
	log.Debug("使用配置生成：%d页", curPage)
	pageData := make([][]*OreCeil, 0)
	for k := 0; k < ViewCol; k++ {
		row := make([]*OreCeil, 0)
		depth++
		for j := 0; j < ViewRow; j++ {
			oreMap := oreMaps[j]
			config := oreMap.Ceil[k]
			ceil := new(OreCeil)
			ceil.Type = pb.OreCeilType(config.Type)
			if config.Ore != nil {
				ceil.OreExtra = ConfigConditionConvert(config.Ore).All()
			}
			if config.Reward != nil {
				ceil.OreExtra = ConfigConditionConvert(config.Reward).All()
			}
			switch ceil.Type {
			case pb.OreCeilType_BlockMonster:
				layerConfig := cfg.OreLayerContainer.GetData()[0]
				ceil.setAsMonster(layerConfig, levelData)
			case pb.OreCeilType_BlockItemDrill:
				ceil.Direction = config.DrillDirection
			}
			row = append(row, ceil)
		}
		pageData = append(pageData, row)
	}
	return pageData
}

// OreGenerateLogic
/*
 * @description 矿洞页生成
 * @param page 生成几页
 * @param currentDepth 当前深度
 * @param level 当前难度
 * @param noSpecialWay 不可以生成特殊区域传送门, 同时本页也不会生成下一层, 说明当前是特殊层的生成
 * @param backFloor 生成返回传送门的位置
 * @param backCeil 生成返回传送门的位置
 * @return [][]*OreCeil
 */
func OreGenerateLogic(page, currentDepth, level int, noSpecialWay bool, levelData *OreLevelData, blankHaseOre bool, backFloor, backCeil int) [][]*OreCeil {
	pageData := make([][]*OreCeil, 0)
	oreLevelConfig := levelData.getOreLayerConfig()
	// 当前生成的深度
	depth := currentDepth
	genLogicCnt := 0
	for i := 0; i < page; i++ {
		genLogicCnt++
		curGeneratePage := (depth)/ViewCol + 1
		pdata := CheckOreGenerateConfig(curGeneratePage, level, depth, levelData)
		if pdata != nil {
			pageData = append(pageData, pdata...)
			depth += ViewCol
			continue
		}
		// 当前页的类型
		curOrePageType := levelData.GenNextPageType
		// 预生成下一页的类型
		pageNextType, specialWayType := levelData.GetTerrianTrans(depth)

		// 当前页是否要生成特殊传送门
		tagIsSpecialWay := specialWayType != -1
		// 已经触发特殊区域 就不用再生成日常特殊矿区了
		if noSpecialWay {
			curOrePageType = pb.OrePageNextType_NormalNext
			tagIsSpecialWay = false
			specialWayType = -1
		}
		col := make([][]*OreCeil, 0)
		tagUpdateLastGenNextCeil := false
		// 当前页的下一层传送门位置
		genNextFloor, genNextCeil := getRandomNext()
		// 当前页的特殊传送门位置
		genNextSpecialFloor, genNextSpecialCeil := getRandomNextSpecial(genNextFloor, genNextCeil)

		if !noSpecialWay {
			oreMapData := cfg.OreMapContainer.GetData()
			oreMaps := lo.Filter(oreMapData, func(item *cfg.OreMap[int], index int) bool {
				return item.Level == level && item.Page == curGeneratePage+1
			})
			// 如果下一页是固定页
			if len(oreMaps) > 0 {
				pageNextType = pb.OrePageNextType_NormalNext
				for j := 0; j < ViewRow; j++ {
					oreMap := oreMaps[j]
					config := oreMap.Ceil[0]
					if config.Type == int(pb.OreCeilType_BlockStartPs) {
						genNextCeil = j
						break
					}
				}
			}
		}

		for j := 0; j < ViewCol; j++ {
			depth++
			row := make([]*OreCeil, 0)
			for k := 0; k < ViewRow; k++ {
				ceil := new(OreCeil).InitWithConfig(oreLevelConfig, level, depth, k, j, noSpecialWay, false, false, noSpecialWay, curOrePageType, blankHaseOre, levelData)
				// 入口空地
				if !noSpecialWay && j%ViewCol == 0 {
					// 本层起点，要和上一层传送门位置对应连通
					if k == levelData.LastGenNextCeil {
						ceil.Type = pb.OreCeilType_BlockStartPs
						ceil.clear()
					}
				}
				// 下一页传送门
				if !noSpecialWay && j == genNextFloor && k == genNextCeil {
					ceil.Type = pb.OreCeilType_BlockNext
					ceil.NextType = pageNextType
					tagUpdateLastGenNextCeil = true
					ceil.clear()
				}
				// 特殊传送门
				if tagIsSpecialWay && j == genNextSpecialFloor && k == genNextSpecialCeil {
					ceil.Type = pb.OreCeilType_BlockNextSpecial
					ceil.clear()
				}
				row = append(row, ceil)
			}

			col = append(col, row)
		}
		// 路线求解,保证传送门能够到达
		if !noSpecialWay {
			// 重新生成当页
			retry := !pathFindForPage(col, levelData.LastGenNextCeil)
			if !retry && genLogicCnt < 64 {
				// 黑色区域，尝试保证矿石可达
				if curOrePageType == pb.OrePageNextType_GrayNext {
					orgGrid := toGridArray(col, func(ceil *OreCeil, x, y int) int {
						if ceil != nil && ceil.Type == pb.OreCeilType_BlockSpecial {
							return 1
						}
						return 0
					})
					startPos := &bfs.Point{X: levelData.LastGenNextCeil, Y: 0}

					for y, row := range col {
						for x, ceil := range row {
							if len(ceil.OreExtra) > 0 {
								if !bfs.IsReachableWithStart(orgGrid, startPos, &bfs.Point{X: x, Y: y}) {
									retry = true
									break
								}
							}
						}
					}

				}
			}
			if retry {
				i--
				depth -= ViewCol
				continue
			}
		}
		// 晚更新位置，因为上面的重随会使用最新的LastGenNextCeil值，如果提前更新，会导致和上一层不衔接
		if tagUpdateLastGenNextCeil {
			levelData.LastGenNextCeil = genNextCeil
			levelData.GenNextPageType = pageNextType
		}
		// 当前页传送门处理
		if tagIsSpecialWay {
			genBackFloor, genBackCeil := 1, 1
			// 传送门类型 怪or矿
			var sp [][]*OreCeil
			switch specialWayType {
			case pb.OreSpecialAreaType_MonsterQunit:
				sp = make([][]*OreCeil, 0)
				for a := 0; a < ViewCol; a++ {
					tRow := make([]*OreCeil, 0)
					for b := 0; b < ViewRow; b++ {
						ceil := new(OreCeil)
						ceil.Type = pb.OreCeilType_BlockNone
						if b == 2 && a == 0 {
							ceil.Type = pb.OreCeilType_BlockStartPs
						}
						tRow = append(tRow, ceil)
					}
					sp = append(sp, tRow)
				}
				ceil := sp[bossX][bossY]
				ceil.setAsMonsterQunit(sp, levelData)
			case pb.OreSpecialAreaType_OreArea:
				// 生成一页特殊区域
				sp = OreGenerateLogic(1, depth, level, true, levelData, true, backFloor, backCeil)
			}
			if sp != nil {
				ceil := sp[genBackFloor][genBackCeil]
				ceil.Type = pb.OreCeilType_BlockBack
				ceil.clear()
				for l := 0; l < ViewCol; l++ {
					col[l] = append(col[l], sp[l]...)
				}
			}
		}
		pageData = append(pageData, col...)
	}
	log.Debug("生成地块结束，页数:%d,实际生成次数:%d", page, genLogicCnt)
	return pageData
}

func pathFindForPage(page [][]*OreCeil, startX int) bool {
	if len(page) == 0 {
		return true
	}
	startPos := &bfs.Point{X: startX, Y: 0}
	suc := len(page) / ViewCol
	checkEndTo := func(cur [][]*OreCeil, endType pb.OreCeilType) bool {
		var end *bfs.Point = nil
		for y, a := range cur {
			for x, ceil := range a {
				if ceil.Type == endType {
					end = &bfs.Point{X: x, Y: y}
					break
				}
			}
			if end != nil {
				break
			}
		}
		if end == nil {
			return true
		}
		grid := toGridArray(cur, func(ceil *OreCeil, x, y int) int {
			if ceil != nil && ceil.Type == pb.OreCeilType_BlockSpecial {
				return 1
			}
			return 0
		})
		return bfs.IsReachableWithStart(grid, startPos, end)
	}
	for i := 0; i < len(page); {
		temp := page[i : i+ViewCol]
		if checkEndTo(temp, pb.OreCeilType_BlockNext) && checkEndTo(temp, pb.OreCeilType_BlockNextSpecial) {
			suc--
		}
		i += ViewCol
	}
	return suc <= 0
}

func (o *OreLevelData) GetTimeStoneBoss() *cfg.TimeStoneBoss[int] {
	bossList := lo.Filter(cfg.TimeStoneBossContainer.GetData(), func(item *cfg.TimeStoneBoss[int], _ int) bool {
		return item.Type == function_type.ORE
	})
	if len(bossList) == 0 {
		return nil
	}
	count := o.plr.GetTimeStoneKey2FragCount()
	return bossList[count]
}

type OreLevelRecord struct {
	Times int `bson:"times"` //挖到的次数
	Num   int `bson:"num"`   //矿的数量
}
