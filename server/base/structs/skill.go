package structs

import (
	"fmt"
	"strings"
	"train/base/cfg"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type Skill struct {
	Id   int                    `bson:"id"`
	Bean *cfg.GrowSkill[string] `bson:"-" json:"-"`
	Lv   int
}

func (this *Skill) Init(id int, lv int) {
	this.Id = id
	this.Lv = lv
	bean, _ := cfg.GrowSkillContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", id, lv))
	this.Bean = bean
}

func (this *Skill) LevelUp() {
	this.Init(this.Id, this.Lv+1)
}

type ExploreSkill struct {
	Id   int                        `bson:"id"`
	Bean *CharacterExplorationSkill `bson:"-" json:"-"`
	Lv   int
}

func (this *ExploreSkill) GetTargetIndex(targetType int) int {
	objects := this.Bean.Object
	for i := 0; i < len(objects); i++ {
		object := objects[i]
		Type := object.Type
		if strings.Contains(Type, "PLANET_") {
			if "PLANET_"+cast.ToString(targetType) == Type {
				return i
			}
		}
	}
	return -1
}

func (this *ExploreSkill) GetEffect(targetType int) *CharacterExplorationSkillEffect {
	index := this.GetTargetIndex(targetType)
	if index < 0 {
		return nil
	}
	effect := this.Bean.Effect[index]
	return effect
}

func (this *ExploreSkill) GetEffectValByType(effectType string) float64 {
	if this == nil {
		return 0
	}
	effects := this.Bean.Effect
	effect, _ := lo.Find(effects, func(e *CharacterExplorationSkillEffect) bool {
		return e.Type == effectType
	})
	if effect != nil {
		return effect.Value
	}
	return 0
}

type CharacterExplorationSkillObject struct {
	Type string `json:"type"`
}

type CharacterExplorationSkillEffect struct {
	Type  string  `json:"type"`
	Value float64 `json:"value"`
	Pro   int     `json:"pro"`
}

type CharacterExplorationSkill struct {
	Id     string                             `json:"id"`
	Object []*CharacterExplorationSkillObject `json:"object"`
	Effect []*CharacterExplorationSkillEffect `json:"effect"`
}
