package structs

import (
	"context"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum/time_stone_evt"
	"train/common/pb"
	"train/common/ta"
	"train/db"
	ut "train/utils"

	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const MaxRecords = 10

func NewTimeStone() *TimeStone {
	return &TimeStone{}
}

type TimeStone struct {
	LastUseTime int              `bson:"lastUseTime"` // 上一次使用的时间
	TotalUseCnt int              `bson:"totalUseCnt"` // 累计使用次数
	TSR         *TimeStoneRecord `bson:"-" json:"-"`  // 记录列表
	plr         *Player          `bson:"-"`
	UseId       string           `bson:"useId" json:"-"`
}

func (t *TimeStone) init(plr *Player) {
	t.plr = plr
}

// UseStone
/*
 * @description 使用时间宝石
 */
func (t *TimeStone) UseStone(id string) int32 {
	if t.TSR == nil {
		t.TSR = GetTimeStoneRecord(t.plr.Id)
	}
	if t.TSR == nil || len(t.TSR.Records) == 0 {
		// 模块没得数据？
		return 1
	}
	recordData, exists := lo.Find(t.TSR.Records, func(item *TimeStoneRecordData) bool {
		return item.Id == id
	})
	if !exists || recordData.Use {
		// 没对对应的可以回溯的记录
		return 2
	}
	//e := json.Unmarshal([]byte(recordData.Data), &t.plr)
	//if e != nil {
	//	log.Error(e.Error())
	//	return 4
	//}
	t.UseId = id
	recordData.Use = true
	t.LastUseTime = ut.Now()
	t.TotalUseCnt += 1
	return 0

	//logic := func(arg interface{}) bool {
	//	e := json.Unmarshal([]byte(recordData.Arg), arg)
	//	if e != nil {
	//		return false
	//	}
	//	return true
	//}
	//var r int32 = 99
	//switch recordData.Type {
	//case time_stone_record_type.Jackpot:
	//	arg := &time_stone_evt.Jackpot{}
	//	if logic(arg) {
	//		r = t.RollBackJackpot(arg)
	//	}
	//case time_stone_record_type.EquipMake:
	//	arg := &time_stone_evt.EquipMake{}
	//	if logic(arg) {
	//		r = t.RollBackEquipMake(arg)
	//	}
	//}
	//if r == 0 {
	//	recordData.Use = true
	//	t.LastUseTime = ut.Now()
	//	t.TotalUseCnt += 1
	//}
	//return r
}

// RollBackEquipMake
/*
 * @description 回溯装备打造
 * @param arg
 * @return int32 错误段 11-20
 */
func (t *TimeStone) RollBackEquipMake(arg *time_stone_evt.EquipMake) int32 {
	plr := t.plr
	equipMod := plr.Equip
	// 移除装备
	equip := equipMod.Pull(arg.EquipId)
	if equip == nil {
		return 0
	}
	// 返还资源
	makeBean, _ := cfg.EquipMakeContainer.GetBeanById(arg.TableId)
	if makeBean == nil {
		return 11
	}
	conds := ConfigConditionConvert(makeBean.Cost...).All()
	plr.GrantRewards(conds, ta.ResChangeSceneTypeUnknown)
	return 0
}

// RollBackJackpot
/*
 * @description 回溯抽卡
 * @param arg
 * @return int32 错误段 1-10
 */
func (t *TimeStone) RollBackJackpot(arg *time_stone_evt.Jackpot) int32 {
	lose := make([]*Condition, 0)
	get := make([]*Condition, 0)
	plr := t.plr

	passenger := plr.GetPassengerById(arg.DrawPid)
	characterFrag, _ := lo.Find(cfg.CharacterFragContainer.GetData(), func(frag *cfg.CharacterFrag[int]) bool {
		return frag.CharacterId == arg.DrawPid && frag.Quality == passenger.GetJson().Quality
	})
	// 优先扣碎片
	tmp := &Condition{
		Type: condition.PASSENGER_FRAG,
		Id:   characterFrag.Id,
		Num:  1,
	}
	suc := plr.CheckCondition(tmp)
	if suc {
		// 背包碎片足够，就直接扣除
		lose = append(lose, tmp)
	} else {
		// 检查能不能降低星级，不能降低就扣除乘客
		if passenger.StarLv-passenger.GetJson().GetInitStarLv() > 0 {
			starBean, _ := cfg.StarUpContainer.GetBeanById(passenger.StarLv - 1)
			cs := &Condition{
				Type: condition.PASSENGER_FRAG,
				Id:   characterFrag.Id,
				Num:  starBean.UpCost - 1,
			}
			if cs.Num > 0 {
				get = append(get, cs)
			}
			passenger.StarLv -= 1
		} else {
			// 取消入住
			plr.PassengerCheckOut(passenger)
			// 取消工作
			passenger.Fire()
			// 移除
			plr.Passenger = lo.Filter(plr.Passenger, func(item *Passenger, index int) bool {
				return item != passenger
			})
		}
	}

	diamondOrTicket := &Condition{}
	diamondOrTicket.Id = arg.DrawPid
	diamondOrTicket.Type = condition.PROP
	diamondOrTicket.Num = 1
	if arg.DrawType == 2 {
		diamondOrTicket.Type = condition.DIAMOND
		miscC := cfg.Misc_CContainer.GetObj()
		diamondOrTicket.Num = miscC.JackPotPrice
		if arg.IsDiamondDiscount {
			diamondOrTicket.Num = ut.Floor(miscC.JackPotDiscount * float64(diamondOrTicket.Num))
		}
	}
	get = append(get, diamondOrTicket)
	plr.DeductCosts(lose, ta.ResChangeSceneTypeUnknown)
	plr.GrantRewards(get, ta.ResChangeSceneTypeUnknown)
	return 0
}

// DoTimeStoneRecord
/*
 * @description 时间宝石记录
 */
func (plr *Player) DoTimeStoneRecord(typ string, arg ...any) {
	// timeStone := plr.TimeStone
	// if timeStone == nil {
	// 	return
	// }
	// if timeStone.TSR == nil {
	// 	timeStone.TSR = GetTimeStoneRecord(timeStone.plr.Id)
	// }
	// bytes1, _ := json.Marshal(plr)
	// records := timeStone.TSR.Records
	// if records == nil {
	// 	records = make([]*TimeStoneRecordData, 0)
	// }
	// records = append(records, &TimeStoneRecordData{
	// 	Id:   cast.ToString(GenUid()),
	// 	Data: string(bytes1),
	// 	Type: typ,
	// })
	// // 只保留 x 条
	// if len(records) > MaxRecords {
	// 	records = records[1:]
	// }
	// timeStone.TSR.Records = records
}

func (t *TimeStone) RollBackBattle() {

}

// SaveTimeStoneRecord
/*
 * @description 保存时间宝石记录
 */
func (plr *Player) SaveTimeStoneRecord() {
	if plr.TimeStone == nil || plr.TimeStone.TSR == nil || plr.TimeStone.TSR.Records == nil {
		return
	}
	db.TIME_STONE_RECORD.GetCollection().FindOneAndUpdate(
		context.TODO(),
		bson.M{"uid": plr.Id},
		bson.M{"$set": bson.M{"records": plr.TimeStone.TSR.Records}},
		options.FindOneAndUpdate().SetUpsert(true),
	)
}

type TimeStoneRecordData struct {
	Id   string `bson:"id"`
	Data string `bson:"data"`
	Arg  string `bson:"arg"`
	Use  bool   `bson:"use"`
	Type string `bson:"type"`
}

func (t *TimeStoneRecordData) ToPb() *pb.TimeStoneRecordData {
	return &pb.TimeStoneRecordData{
		Id:   t.Id,
		Use:  t.Use,
		Type: t.Type,
		Arg:  t.Arg,
	}
}

type TimeStoneRecord struct {
	Records []*TimeStoneRecordData `bson:"records"`
}

func (t *TimeStoneRecord) ToPb() *pb.TimeStoneRecord {
	return &pb.TimeStoneRecord{
		Records: lo.Map(t.Records, func(item *TimeStoneRecordData, index int) *pb.TimeStoneRecordData { return item.ToPb() }),
	}
}

func GetTimeStoneRecord(uid string) *TimeStoneRecord {
	singleResult := db.TIME_STONE_RECORD.GetCollection().FindOne(context.TODO(), &bson.M{"uid": uid})
	tsr := &TimeStoneRecord{}
	_ = singleResult.Decode(tsr)
	if tsr == nil {
		tsr = &TimeStoneRecord{
			Records: make([]*TimeStoneRecordData, 0),
		}
	}
	return tsr
}
