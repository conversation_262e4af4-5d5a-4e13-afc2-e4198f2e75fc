package structs

import (
	"train/base/cfg"
	"train/common/pb"
	"train/utils/array"
)

type Tower struct {
	CheckPointId string  `bson:"checkPointId"` //当前关卡id
	isEnd        bool    `bson:"isEnd"`        //全打完
	plr          *Player `bson:"-"`
}

func NewTower() *Tower {
	return &Tower{}
}

func (t *Tower) Init(plr *Player) {
	t.plr = plr

	if t.CheckPointId == "" {
		beans := cfg.TowerMonsterContainer.GetData()
		t.CheckPointId = beans[0].Id
	}
}

func (t *Tower) Unlock() {
}

func (t *Tower) GetIndex() int {
	beans := cfg.TowerMonsterContainer.GetData()
	index := array.FindIndex(beans, func(b *cfg.TowerMonster[string]) bool { return b.Id == t.CheckPointId })
	return index + 1
}

func (t *Tower) ToNext() {
	if t.isEnd {
		return
	}
	beans := cfg.TowerMonsterContainer.GetData()
	index := array.FindIndex(beans, func(b *cfg.TowerMonster[string]) bool { return b.Id == t.CheckPointId })
	if index+1 >= len(beans) {
		t.isEnd = true
	} else {
		t.CheckPointId = beans[index+1].Id
	}
}

func (t *Tower) ToPb() *pb.Tower {
	return &pb.Tower{
		CheckPointId: t.CheckPointId,
	}
}
