package structs

import (
	"context"
	"train/common/pb"
	"train/common/ta"
	"train/db"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MailModule struct {
	Uid              string  `bson:"-"` // 玩家id
	List             []*Mail `bson:"-"` // 邮件列表
	deadlock.RWMutex `bson:"-"`
}

// 初始化一个空的邮件模块
func emptyMailModule(plrId string) *MailModule {
	return &MailModule{
		Uid:  plrId,
		List: make([]*Mail, 0),
	}
}

func (m *MailModule) Init() {
	list := make([]*Mail, 0)
	loadMore := func(filter bson.M) bool {
		var temp []*Mail
		cursor, _ := db.MAIL.GetCollection().Find(context.TODO(), filter, options.Find().SetLimit(100))
		defer cursor.Close(context.TODO())
		cursor.All(context.TODO(), &temp)
		list = append(list, temp...)
		if len(temp) < 100 {
			return false
		}
		lastId := temp[len(temp)-1].DOCID
		filter["_id"] = bson.M{"$gt": lastId}
		return true
	}

	for {
		if !loadMore(bson.M{"uid": m.Uid}) {
			break
		}
	}
	m.List = list
}

func (m *MailModule) Save() {
	if m == nil {
		return
	}
	m.Lock()
	defer m.Unlock()
	if len(m.List) == 0 {
		db.MAIL.GetCollection().DeleteMany(context.TODO(), bson.M{"uid": m.Uid})
		return
	}

	ary := m.List
	operations := make([]mongo.WriteModel, 0)
	//
	for i, v := range ary {
		if v.write {
			// 新增 or 更新
			if !v.DOCID.IsZero() {
				operation := mongo.NewUpdateOneModel()
				filter := bson.M{"_id": v.DOCID}
				operation.SetFilter(&filter)

				updateDoc := bson.M{}
				bytes, _ := bson.Marshal(v)
				bson.Unmarshal(bytes, &updateDoc)
				delete(updateDoc, "_id")
				operation.SetUpdate(&bson.M{"$set": &updateDoc})
				operation.SetUpsert(false)
				operations = append(operations, operation)
			} else {
				operation := mongo.NewInsertOneModel()
				operation.SetDocument(v)
				operations = append(operations, operation)
			}
			v.write = false
		} else if v.del {
			// 删除
			operation := mongo.NewDeleteOneModel()
			operation.SetFilter(&bson.M{"id": v.Id})
			operations = append(operations, operation)
		}

		if len(operations) >= 1000 || (i == len(ary)-1 && len(operations) != 0) {
			_, er := db.MAIL.GetCollection().BulkWrite(context.TODO(), operations, options.BulkWrite().SetOrdered(true))
			if er != nil {
				log.Error("邮件批量写入出错: %v", er.Error())
			}
			operations = operations[:0]
		}
	}

	m.List = lo.Filter(m.List, func(mail *Mail, i int) bool { return !mail.del })

}

type Mail struct {
	DOCID   primitive.ObjectID `bson:"_id,omitempty"` // MongoDB的_id
	PlrId   string             `bson:"uid"`           // 玩家id
	Id      string             `bson:"id"`            // id
	Time    int                `bson:"time"`          // 邮件生成时间
	Title   string             `bson:"title"`         // 邮件标题
	Content string             `bson:"content"`       // 邮件内容
	Rewards []*Condition       `bson:"rewards"`       // 附件列表
	Read    bool               `bson:"read"`          // 是否已经读取
	Attach  bool               `bson:"attach"`        // 是否已经领取附件
	write   bool               `bson:"-"`             // 是否需要更新写入db
	del     bool               `bson:"-"`             // 是否需要删除写入db
}

func NewMail(title, content, uid string, rewards []*Condition) *Mail {
	title = lo.If(ut.IsEmpty(title), "来自系统的邮件").Else(title)
	content = lo.If(ut.IsEmpty(content), "").Else(content)
	m := &Mail{
		Id:      GenUid(),
		PlrId:   uid,
		Time:    ut.Now(),
		Title:   title,
		Content: content,
		Rewards: rewards,
		Read:    false,
		Attach:  false,
	}
	if len(rewards) == 0 {
		m.Attach = true
	}
	m.write = true
	return m
}

// TpPbMailInfo 只需要id title和read 三个值,前端就能渲染界面, content和reward不需要直接渲染
func (m *Mail) TpPbMailInfo(detail bool) *pb.MailInfo {
	if !detail {
		return &pb.MailInfo{
			Id:     m.Id,
			Title:  m.Title,
			Read:   m.Read,
			Attach: m.Attach,
		}
	}
	return &pb.MailInfo{
		Id:      m.Id,
		Title:   m.Title,
		Content: m.Content,
		Rewards: m.TpPbConditionInfo(),
		Read:    m.Read,
		Attach:  m.Attach,
		Time:    uint64(m.Time),
	}
}

func (m *Mail) TpPbConditionInfo() []*pb.Condition {
	f := make([]*pb.Condition, 0)
	for _, condition := range m.Rewards {
		f = append(f, condition.ToPb())
	}
	return f
}

func (plr *Player) ListToPbMailInfo(detail bool) []*pb.MailInfo {
	plr.InitMailModule()
	temp := make([]*pb.MailInfo, 0)
	lo.ForEach(plr.Mail.List, func(mail *Mail, index int) {
		if mail.del {
			return
		}
		temp = append(temp, mail.TpPbMailInfo(detail))
	})
	return temp
}

// CreateMail 新邮件
func (plr *Player) CreateMail(title, content string, rewards []*Condition) *Mail {
	plr.InitMailModule()
	mail := NewMail(title, content, plr.Id, rewards)
	mail.Time += plr.OffsetTime
	plr.Mail.List = append(plr.Mail.List, mail)
	// 发送消息
	plr.TellPlayerMsg(pb.S2COnNewMailMessage, &pb.S2C_OnNewMailMessage{
		Mail: mail.TpPbMailInfo(false),
	})
	return mail
}

// DeleteMailById 删除邮件
func (plr *Player) DeleteMailById(mailId string) {
	plr.InitMailModule()
	mail, b := lo.Find(plr.Mail.List, func(mail *Mail) bool {
		return mail.Id == mailId
	})
	if b {
		mail.write = false
		mail.del = true
	}
}

// DeleteReadMail 删除已读/无未提取附件的邮件
func (plr *Player) DeleteReadMail() {
	plr.InitMailModule()
	for _, mail := range plr.Mail.List {
		if !mail.Read {
			continue
		}
		if len(mail.Rewards) > 0 && !mail.Attach {
			continue
		}
		mail.write = false
		mail.del = true
	}
}

// DetailMail 查看邮件详情
func (plr *Player) DetailMail(mailId string) *Mail {
	plr.InitMailModule()
	mail, _ := lo.Find(plr.Mail.List, func(mail *Mail) bool {
		return mail.Id == mailId
	})
	if mail != nil {
		// 设置成已读
		mail.Read = true
		mail.write = true
	}
	return mail
}

// AttachMail 领取附件
func (plr *Player) AttachMail(mailId string) (int, []*pb.Condition, []string) {
	rewards := make([]*Condition, 0)
	plr.InitMailModule()
	// 可以领取附件的邮件列表
	canGetMails := make([]*Mail, 0)
	mailIds := make([]string, 0)
	switch mailId {
	case "-1":
		for _, mail := range plr.Mail.List {
			if len(mail.Rewards) == 0 {
				continue
			}
			if mail.Attach {
				continue
			}
			canGetMails = append(canGetMails, mail)
		}
	default:
		mail, _ := lo.Find(plr.Mail.List, func(mail *Mail) bool {
			return mail.Id == mailId
		})
		if mail != nil && mail.Rewards != nil && len(mail.Rewards) > 0 {
			canGetMails = append(canGetMails, mail)
		}
	}
	for _, mail := range canGetMails {
		mail.Read = true
		rewards = MergeConditions(rewards, mail.Rewards)
		mailIds = append(mailIds, cast.ToString(mail.Id))
		plr.GrantRewards(mail.Rewards, ta.ResChangeSceneTypeMail)
		mail.Attach = true
		mail.write = true
	}

	return 0, ToPbConditions(rewards), mailIds
}

func (plr *Player) InitMailModule() {
	if plr.Mail != nil {
		return
	}
	plr.Mail = emptyMailModule(plr.Id)
	plr.Mail.Init()
}
