package structs

import (
	"strings"
	"train/base/cfg"
	"train/base/enum"
	"train/base/enum/function_type"
	"train/base/enum/guide_mark"
	"train/base/enum/task_type"
	"train/base/event"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// TaskCondition 任务条件类
type TaskCondition struct {
	Id  string `bson:"id"`  // 条件目标数组索引
	Num int    `bson:"num"` // 当前数量id
}
type TaskInfo struct {
	Id           string            `bson:"id"`           // 任务id
	AcceptedTime int               `bson:"acceptedTime"` // 领取时间
	Targets      []*TaskCondition  `bson:"targets"`      //目标进度
	json         *cfg.Task[string] `bson:"-"`            //任务配置
}

func NewTaskInfo(id string) *TaskInfo {
	bean := &TaskInfo{
		Id:           id,
		AcceptedTime: ut.Now(),
		Targets:      make([]*TaskCondition, 0),
	}
	return bean
}

func (this *TaskInfo) toPb() *pb.Task {
	return &pb.Task{
		Id: this.Id,
		Targets: lo.Map(this.Targets, func(target *TaskCondition, i int) *pb.TaskTarget {
			return &pb.TaskTarget{Id: target.Id, Num: cast.ToInt32(target.Num)}
		}),
	}
}

func (this *TaskInfo) GetJson() *cfg.Task[string] {
	if this.json == nil {
		json, _ := cfg.TaskContainer.GetBeanByUnique(this.Id)
		this.json = json
		if json == nil {
			log.Error("TaskInfo GetJson fail:", this.Id)
		}
	}
	return this.json
}

func (this *TaskInfo) setTargetProgress(idx int, num int) {
	key := cast.ToString(idx)
	target := array.Find(this.Targets, func(target *TaskCondition) bool { return target.Id == key })
	if target == nil {
		target = &TaskCondition{Id: key, Num: num}
		this.Targets = append(this.Targets, target)
	} else {
		target.Num = num
	}
}

func (this *TaskInfo) addTargetProgress(idx int, addNum int) {
	key := cast.ToString(idx)
	target := array.Find(this.Targets, func(target *TaskCondition) bool { return target.Id == key })
	if target == nil {
		target = &TaskCondition{Id: key, Num: addNum}
		this.Targets = append(this.Targets, target)
	} else {
		target.Num += addNum
	}
}

// 根据数组索引获取进度
func (this *TaskInfo) GetTargetProgress(idx int) int {
	curTarget := array.Find(this.Targets, func(target *TaskCondition) bool { return target.Id == cast.ToString(idx) })
	if curTarget == nil {
		return 0
	}
	return curTarget.Num
}

func (this *TaskInfo) haveTargetProgress(tagId int) bool {
	curTarget := array.Find(this.Targets, func(target *TaskCondition) bool { return target.Id == cast.ToString(tagId) })
	return curTarget != nil
}

// 获取数组索引
func (this *TaskInfo) GetTargetIdxById(tagId interface{}) int {
	json := this.GetJson()
	if json == nil {
		return -1
	}
	return array.FindIndex(json.Target, func(need *cfg.TaskTarget) bool { return cast.ToString(need.Id) == cast.ToString(tagId) })
}

// 获取数组索引
func (this *TaskInfo) GetTargetIdxByType(tagType interface{}) int {
	json := this.GetJson()
	if json == nil {
		return -1
	}
	return array.FindIndex(json.Target, func(need *cfg.TaskTarget) bool { return cast.ToInt(need.Type) == cast.ToInt(tagType) })
}

func (this *TaskInfo) checkConditionId(tagId interface{}) bool {
	return this.GetTargetIdxById(tagId) >= 0
}

func (this *TaskInfo) checkConditionType(tagType interface{}) bool {
	return this.GetTargetIdxByType(tagType) >= 0
}

// 根据记录的进度判断是否完成
func (this *TaskInfo) CheckByTargets() bool {
	json := this.GetJson()
	if json == nil {
		return false
	}
	for i, need := range json.Target {
		if this.GetTargetProgress(i) < need.Num {
			return false
		}
	}
	return true
}

func (this *TaskInfo) CheckHaveTargets() bool {
	json := this.GetJson()
	if json == nil {
		return false
	}
	for _, need := range json.Target {
		if !this.haveTargetProgress(cast.ToInt(need.Id)) {
			return false
		}
	}
	return true
}

func (this *TaskInfo) GetConfigTarget() []*cfg.TaskTarget {
	return this.GetJson().Target
}
func (this *TaskInfo) GetTaskType() string {
	return this.GetJson().Type
}

type CompletedTaskInfo struct {
	Id           string `bson:"id"`           // 任务id
	AcceptedTime int    `bson:"acceptedTime"` // 领取时间
	FinishedTime int    `bson:"finishedTime"` // 完成时间
}

func NewCompleteTaskInfo(task *TaskInfo) *CompletedTaskInfo {
	bean := &CompletedTaskInfo{
		Id:           task.Id,
		AcceptedTime: task.AcceptedTime,
		FinishedTime: ut.Now(),
	}
	return bean
}

func (this *CompletedTaskInfo) toPb() string {
	return this.Id
}

// PlayerTask 任务集合
type PlayerTask struct {
	Curs       map[string]*TaskInfo          `bson:"tasks"`          //当前任务
	Completeds map[string]*CompletedTaskInfo `bson:"completedTasks"` //已完成的任务
}

func NewPlayerTaskData() *PlayerTask {
	return &PlayerTask{
		Curs:       make(map[string]*TaskInfo),
		Completeds: make(map[string]*CompletedTaskInfo),
	}
}

func (this *PlayerTask) Init(plr *Player) {
	if this.Curs == nil {
		this.Curs = make(map[string]*TaskInfo)
	}
	if this.Completeds == nil {
		this.Completeds = make(map[string]*CompletedTaskInfo)
	}
	plr.initTaskListener()
	plr.TriggerTasks()
}

func (this *PlayerTask) toPb() *pb.TaskInfo {
	return &pb.TaskInfo{
		Tasks: lo.MapToSlice(this.Curs, func(id string, val *TaskInfo) *pb.Task {
			return val.toPb()
		}),
		Completes: lo.MapToSlice(this.Completeds, func(id string, val *CompletedTaskInfo) string {
			return val.toPb()
		}),
	}
}

func (plr *Player) initTaskListener() {
	eventCenter := plr.eventCenter
	//trigger
	eventCenter.On(event.GuideStepEnd, plr.TriggerTasks)
	eventCenter.On(event.CreateNewCarriage, plr.TriggerTasks)
	eventCenter.On(event.PlanetNodeComplete, plr.TriggerTasks)
	eventCenter.On(event.PlanetComplete, plr.TriggerTasks)
	eventCenter.On(event.UnLockBuild, plr.TriggerTasks)

	//update progress
	onAddJustType := func(taskType string) {
		plr.onAddTask(taskType, nil, nil)
		plr.onAddAchievementTask(taskType, nil, nil)
	}
	onAddByOneTagId := func(taskType string, tagId interface{}) {
		plr.onAddTask(taskType, func(task *TaskInfo) bool { return task.checkConditionId(tagId) }, nil)
		plr.onAddAchievementTask(taskType, func(achievement *AchievementTaskInfo) bool { return achievement.IsTargetExists(tagId) }, nil)
	}
	onAddByNum := func(taskType string, addNum int) {
		if addNum > 0 {
			plr.onAddTask(taskType, nil, func(task *TaskInfo) { task.addTargetProgress(0, addNum) })
			plr.onAddAchievementTask(taskType, nil, func(achievement *AchievementTaskInfo) { achievement.addTargetProgress(0, addNum) })
		}
	}
	onCheckId0 := func(task CommonTaskObj, giveId int) bool {
		needs := task.GetConfigTarget()
		id := needs[0].Id
		if id != nil {
			return cast.ToInt(id) == giveId
		}
		return true
	}
	onAddByCheckId0 := func(taskType string, giveId int) {
		plr.onAddTask(taskType, func(task *TaskInfo) bool { return onCheckId0(task, giveId) }, func(task *TaskInfo) { task.addTargetProgress(0, 1) })
		plr.onAddAchievementTask(taskType, func(achievement *AchievementTaskInfo) bool { return onCheckId0(achievement, giveId) }, func(achievement *AchievementTaskInfo) { achievement.addTargetProgress(0, 1) })
	}
	eventCenter.On(event.TrainMovingPlanet, func(val ...interface{}) { onAddByOneTagId(task_type.GotoPlanet, val[0]) })
	eventCenter.On(event.PlanetNodeComplete, func(val ...interface{}) { onAddByOneTagId(task_type.ExplorePlanet, val[0]) })
	eventCenter.On(event.PlanetComplete, func(val ...interface{}) { onAddByOneTagId(task_type.ExplorePlanetComplete, val[0]) })
	eventCenter.On(event.UnLockBuild, func(val ...interface{}) { onAddByOneTagId(task_type.BuildTrainItem, val[0]) })
	eventCenter.On(event.LevelUpBuild, func(val ...interface{}) {
		item := val[0].(*Build)
		onAddByOneTagId(task_type.TrainItemLevelUp, item.GetCarriageOrder())
		onAddByOneTagId(task_type.TrainItemLevelUp2, item.carriage.Id)
	})
	eventCenter.On(event.UnLockAllBuild, func(val ...interface{}) { onAddByOneTagId(task_type.BuildTrainItemLevel, val[0]) })
	eventCenter.On(event.UnLockTrainGoods, func(val ...interface{}) { onAddByOneTagId(task_type.GoodsUnlock, val[0]) })
	eventCenter.On(event.BuildEndCarriage, func(val ...interface{}) {
		onAddJustType(task_type.BuildTrainIndex)
		onAddJustType(task_type.BuildTrain)
	})
	eventCenter.On(event.PassengerLevelUp, func(val ...interface{}) {
		onAddJustType(task_type.CharacterLevel)
		onAddJustType(task_type.CharacterLevelUp)
	})
	eventCenter.On(event.PassengerStarLvUp, func(val ...interface{}) {
		onAddJustType(task_type.CharacterRank)
		onAddJustType(task_type.CharacterRankUp)
	})
	eventCenter.On(event.PassengerNew, func(val ...interface{}) { onAddJustType(task_type.CharacterIndex) })
	eventCenter.On(event.ChangeNumProp, func(val ...interface{}) {
		item := val[0].(*Item)
		changeNum := cast.ToInt(val[1])
		if changeNum > 0 {
			plr.onAddTask(task_type.CollectItem, func(task *TaskInfo) bool { return task.checkConditionId(item.Id) }, func(task *TaskInfo) { task.addTargetProgress(task.GetTargetIdxById(item.Id), changeNum) })
			plr.onAddAchievementTask(task_type.CollectItem, func(achievement *AchievementTaskInfo) bool { return achievement.IsTargetExists(item.Id) }, func(achievement *AchievementTaskInfo) { achievement.addTargetProgressByTargetId(item.Id, changeNum) })
		}
	})
	eventCenter.On(event.UpdateCurrency, func(val ...interface{}) {
		currencyType := cast.ToInt(val[0])
		changeNum := cast.ToInt(val[1])
		if changeNum > 0 {
			plr.onAddTask(task_type.CollectItem, func(task *TaskInfo) bool { return task.checkConditionType(currencyType) }, func(task *TaskInfo) { task.addTargetProgress(task.GetTargetIdxByType(currencyType), changeNum) })
			plr.onAddAchievementTask(task_type.CollectItem, func(achievement *AchievementTaskInfo) bool { return achievement.checkConditionType(currencyType) }, func(achievement *AchievementTaskInfo) {
				achievement.addTargetProgress(achievement.GetTargetIdxByType(currencyType), changeNum)
			})
		}
	})
	eventCenter.On(event.PassengerCheckIn, func(val ...interface{}) {
		plr.onAddTask(task_type.CharacterGetOn, nil, func(task *TaskInfo) { plr.setProgressCharacterGetOn(task) })
		onAddJustType(task_type.CharacterGetOn2)
	})
	eventCenter.On(event.PassengerCheckOut, func(val ...interface{}) {
		plr.onAddTask(task_type.CharacterGetOn, nil, func(task *TaskInfo) { plr.setProgressCharacterGetOn(task) })
		onAddJustType(task_type.CharacterGetOn2)
	})
	eventCenter.On(event.TrainChangeWork, func(val ...interface{}) {
		plr.onAddTask(task_type.GotoWork, nil, func(task *TaskInfo) { plr.setProgressTrainWork(task) })
	})
	eventCenter.On(event.ToolMakeSuccess, func(val ...interface{}) {
		toolType := cast.ToInt(val[0])
		onAddJustType(task_type.ToolLevelUp)
		onAddJustType(task_type.ToolRankUp)
		onAddByNum(task_type.ToolBuild, 1)
		onAddByCheckId0(task_type.ToolLevelUp2, toolType)
	})
	eventCenter.On(event.ToolTableUp, func(val ...interface{}) { onAddJustType(task_type.ToolTableUp) })
	eventCenter.On(event.DeepExploreComplete, func(val ...interface{}) {
		onAddByNum(task_type.DEEP_EXPLORE, 1)
	})
	eventCenter.On(event.PlanetProfileUnlock, func(val ...interface{}) {
		onAddByNum(task_type.PROFILE, 1)
	})
	eventCenter.On(event.TowerComplete, func(val ...interface{}) {
		onAddJustType(task_type.TOWER)
	})
	eventCenter.On(event.TransportComplete, func(val ...interface{}) {
		onAddByNum(task_type.TRANSPORT, 1)
	})
	eventCenter.On(event.UnlockTheme, func(val ...interface{}) {
		onAddJustType(task_type.UNLOCK_THEME)
	})
}

func (plr *Player) UnlockTask(id string) {
	log.Info("[%s] UnlockTask: %s", plr.GetUid(), id)
	task := NewTaskInfo(id)
	task.AcceptedTime = plr.GetNowTime()
	plr.Task.Curs[task.Id] = task
}

func (plr *Player) CompleteTask(id string) {
	task := plr.GetCurTask(id)
	if task == nil {
		return
	}
	t := NewCompleteTaskInfo(task)
	t.FinishedTime = plr.GetNowTime()
	plr.Task.Completeds[id] = t

	delete(plr.Task.Curs, id)
	log.Info("[%s] CompleteTask: %s", plr.GetUid(), id)
	plr.TriggerTasks()
}

func (plr *Player) TriggerTasks(...interface{}) {
	datas := cfg.TaskContainer.GetData()
	datas = array.Filter(datas, func(data *cfg.Task[string], i int) bool { //过滤掉当前的和已完成的
		return !plr.IsTaskCompleted(data.Id) && plr.Task.Curs[data.Id] == nil
	})
	for _, data := range datas {
		if plr.CheckTriggerTask(data) {
			plr.UnlockTask(data.Id)
		}
	}
	plr.CheckAutoCompleteTasks()
}

func (plr *Player) CheckTriggerTask(task *cfg.Task[string]) bool {
	if !ut.IsEmpty(task.PreTask) && !plr.IsTaskCompleted(task.PreTask) {
		return false
	}
	trigger := task.Trigger
	if trigger != nil {
		switch trigger.Type {
		case enum.TriggerGuideUnlockFunc:
			return plr.isStepEndByUnlockFunc(cast.ToString(trigger.Id))
		case enum.TriggerGuideComplateId:
			return plr.isStepEnd(cast.ToInt(trigger.Id))
		case enum.TriggerBuildCarriage:
			return plr.GetCarriageById(cast.ToInt(trigger.Id)) != nil
		case enum.TriggerComplatePlanet:
			planet := plr.PlanetData.GetPlanet(cast.ToInt(trigger.Id))
			return planet != nil && planet.IsDone()
		case enum.TriggerComplatePlanetId:
			return plr.PlanetData.isPassNode(cast.ToString(trigger.Id))
		case enum.TriggerComplateBattle:
			return plr.PlanetData.isPassBattle(cast.ToString(trigger.Id))
		case enum.TriggerReachPlanet:
			return plr.PlanetData.GetPlanet(cast.ToInt(trigger.Id)) != nil
		case enum.TriggerUnlockFunc:
			return plr.IsUnlockFunction(cast.ToString(trigger.Id))
		case enum.TriggerCompleteTheme:
			ids := strings.Split(cast.ToString(trigger.Id), "-")
			carriageId := cast.ToInt(ids[0])
			themeId := cast.ToInt(ids[1])
			carriage := plr.GetCarriageById(carriageId)
			return carriage != nil && carriage.isAllBuildsMaxLv(themeId)
		}
	}
	return true
}

func (plr *Player) GetCurTask(id string) *TaskInfo {
	return plr.Task.Curs[id]
}

func (plr *Player) IsTaskUnlock(id string) bool {
	return plr.Task.Curs[id] != nil
}

func (plr *Player) IsTaskCompleted(id string) bool {
	return plr.Task.Completeds[id] != nil
}

func (plr *Player) onAddTask(taskType string, conditionFun func(task *TaskInfo) bool, addFun func(task *TaskInfo)) {
	tasks := plr.Task.Curs
	autoOver := false
	for _, task := range tasks {
		json := task.GetJson()
		if json != nil && json.Type == taskType && (conditionFun == nil || conditionFun(task)) {
			if addFun != nil {
				addFun(task)
			}
			if !autoOver && plr.CheckTaskCanAutoComplete(task) {
				autoOver = true
			}
		}
	}
	if autoOver {
		plr.CheckAutoCompleteTasks()
	}
}

// 没奖励的自动完成
func (plr *Player) CheckAutoCompleteTasks() {
	for _, task := range plr.Task.Curs {
		if plr.CheckTaskCanAutoComplete(task) {
			plr.CompleteTask(task.Id)
		}
	}
}

func (plr *Player) CheckTaskCanAutoComplete(task *TaskInfo) bool {
	json := task.GetJson()
	if json == nil {
		return false
	}
	return len(json.Reward) <= 0 && plr.CheckTaskDone(task)
}

func (plr *Player) CheckTaskDone(task CommonTaskObj) bool {
	typ := task.GetTaskType()
	needs := task.GetConfigTarget()
	switch typ {
	case task_type.GotoPlanet:
		planetId := cast.ToInt(needs[0].Id)
		if plr.PlanetData.TargetPlanetId == planetId || plr.PlanetData.CurPlanetId == planetId {
			return true
		}
		planet := plr.PlanetData.GetPlanet(planetId)
		return planet != nil && planet.IsDone()
	case task_type.ExplorePlanet:
		oneTag := needs[0]
		planet := plr.PlanetData.GetPlanet(cast.ToInt(oneTag.Id))
		return planet != nil && planet.getPercent() >= oneTag.Num
	case task_type.ExplorePlanetComplete:
		oneTag := needs[0]
		planet := plr.PlanetData.GetPlanet(cast.ToInt(oneTag.Id))
		return planet != nil && planet.IsDone()
	case task_type.BuildTrainItem:
		for _, oneTag := range needs {
			if !plr.IsUnlockBuildById(cast.ToString(oneTag.Id)) {
				return false
			}
		}
		return true
	case task_type.BuildTrainIndex:
		return len(plr.Train.Carriages) >= needs[0].Num
	case task_type.BuildTrain:
		for _, oneTag := range needs {
			if !plr.isCarriageOverBuilt(cast.ToInt(oneTag.Id)) {
				return false
			}
		}
		return true
	case task_type.CollectItem:
		return task.CheckByTargets()
	case task_type.CharacterLevel:
		cnt, needCnt := 0, needs[0].Num
		for _, passenger := range plr.Passenger {
			cnt += passenger.Level
		}
		return cnt >= needCnt
	case task_type.CharacterRank:
		cnt, needCnt := 0, needs[0].Num
		for _, passenger := range plr.Passenger {
			cnt += passenger.StarLv
		}
		return cnt >= needCnt
	case task_type.CharacterLevelUp:
		cnt, needCnt, needLv := 0, needs[0].Num, needs[1].Num
		for _, passenger := range plr.Passenger {
			if passenger.Level >= needLv {
				cnt++
			}
		}
		return cnt >= needCnt
	case task_type.CharacterRankUp:
		cnt, needCnt, needLv := 0, needs[0].Num, needs[1].Num
		for _, passenger := range plr.Passenger {
			if passenger.StarLv >= needLv {
				cnt++
			}
		}
		return cnt >= needCnt
	case task_type.CharacterGetOn2:
		x := cast.ToInt(needs[0].Id)
		p := plr.GetPassengerById(x)
		if p != nil {
			if len(needs) > 1 {
				return p.DormId == cast.ToInt(needs[1].Id)
			}
			return p.isCheckIn()
		}
		return false
	case task_type.CharacterGetOn:
		return task.CheckByTargets()
	case task_type.CharacterIndex:
		return len(plr.Passenger) >= needs[0].Num
	case task_type.ToolBuild:
		return task.CheckByTargets()
	case task_type.ToolChange:
		return task.CheckHaveTargets()
	case task_type.ToolLevelUp2:
		return task.CheckByTargets()
	case task_type.ToolLevelUp:
		id := needs[0].Id
		num := needs[0].Num
		if id != nil {
			tool := plr.GetToolByType(cast.ToInt(id))
			return tool != nil && tool.Lv >= num
		}
		return plr.checkToolLevel(num)
	case task_type.ToolRankUp:
		return plr.checkToolQuality(needs[0].Num)
	case task_type.ToolTableUp:
		return plr.Tool.Lv >= needs[0].Num
	case task_type.EntrustComplete:
		fallthrough
	case task_type.EntrustStart:
		return task.CheckByTargets()
	case task_type.BuildTrainItemLevel:
		targetTrainId, targetLv := cast.ToInt(needs[0].Id), needs[0].Num
		carriage := plr.GetCarriageById(targetTrainId)
		if carriage == nil {
			return false
		}
		return carriage.isAllBuildsMaxLv(targetLv)
	case task_type.TrainItemLevelUp:
		key, max := cast.ToString(needs[0].Id), needs[0].Num
		strs := strings.Split(key, "-")
		carriage := plr.GetCarriageById(cast.ToInt(strs[0]))
		if carriage != nil {
			build := carriage.GetBuildByOrder(cast.ToInt(strs[1]))
			if build != nil {
				return build.Lv >= max
			}
		}
		return false
	case task_type.TrainItemLevelUp2:
		carriageId := cast.ToInt(needs[0].Id)
		carriage := plr.GetCarriageById(carriageId)
		if carriage == nil {
			return false
		}
		targetLv := needs[0].Num
		beans := lo.Filter(cfg.TrainItemContainer.GetData(), func(d *cfg.TrainItem[string], i int) bool {
			return d.CarriageId == carriageId && d.Skin == 1 && d.Show == 1
		})
		for _, v := range beans {
			build := carriage.GetBuildByOrder(v.Order)
			if build == nil {
				return false
			}
			if build.Lv < targetLv {
				return false
			}
		}
		return true
	case task_type.GotoWork:
		if task.CheckByTargets() {
			return true
		}
		oneTag := needs[0]
		carriage := plr.GetCarriageById(cast.ToInt(oneTag.Id))
		if carriage == nil {
			return false
		}
		return len(carriage.GetWorkers()) >= oneTag.Num
	case task_type.GoodsUnlock:
		trainId := cast.ToInt(needs[0].Id)
		carriage := plr.GetCarriageById(trainId)
		if carriage == nil {
			return false
		}
		max := needs[0].Num
		if max == 0 {
			beans := cfg.TrainGoodsContainer.GetData()
			max = lo.CountBy(beans, func(bean *cfg.TrainGoods[string]) bool { return bean.TrainId == trainId })
		}
		return len(carriage.Goods) >= max
	case task_type.PlanetBattleId:
		battleId := cast.ToString(needs[0].Id)
		return plr.PlanetData.isPassBattle(battleId)
	case task_type.TO_DEEP_EXPLORE:
		return plr.IsStepEndByMark(guide_mark.DEEP_EXPLORE_START)
	case task_type.TOWER:
		return plr.Tower.GetIndex() > needs[0].Num
	case task_type.GOTO_PLANET_ENTRY_1009:
		return plr.IsStepEndByMark(guide_mark.START_ORE_PUZZLE)
	case task_type.GOTO_PLANET_ENTRY_1007:
		return plr.isStepEndByUnlockFunc(function_type.PLAY_SPACE_STONE)
	case task_type.TO_TRANSPORT:
		return plr.isStepEndByUnlockFunc(function_type.TRANSPORT)
	case task_type.ORE_PUZZLE:
		return plr.IsUnlockFunction(function_type.ORE)
	case task_type.INSTANCE_PUZZLE:
		return plr.IsUnlockFunction(function_type.INSTANCE)
	case task_type.EXPLORE_PLANET_AREA:
		ids := strings.Split(cast.ToString(needs[0].Id), "-")
		planetId := cast.ToInt(ids[0])
		index := cast.ToInt(ids[1])
		planet := plr.PlanetData.GetPlanet(planetId)
		if planet == nil {
			return false
		}
		return planet.getMapPercent(index) >= needs[0].Num
	case task_type.DEEP_EXPLORE:
		fallthrough
	case task_type.PROFILE:
		fallthrough
	case task_type.TRANSPORT:
		return task.CheckByTargets()
	case task_type.UNLOCK_THEME:
		ids := strings.Split(cast.ToString(needs[0].Id), "-")
		carriageId := cast.ToInt(ids[0])
		themeId := cast.ToInt(ids[1])
		carriage := plr.GetCarriageById(carriageId)
		return carriage != nil && carriage.ThemeLv >= themeId
	}
	return false
}

func (plr *Player) setProgressCharacterGetOn(task *TaskInfo) {
	if plr.CheckTaskDone(task) {
		return //进度满了就不会再下降了
	}
	passenegrs := lo.Filter(plr.Passenger, func(passenger *Passenger, i int) bool { return passenger.isCheckIn() })
	task.setTargetProgress(0, len(passenegrs))
}

func (plr *Player) setProgressTrainWork(task *TaskInfo) {
	if plr.CheckTaskDone(task) {
		return //进度满了就不会再下降了
	}
	needs := task.GetConfigTarget()
	oneTag := needs[0]
	carriage := plr.GetCarriageById(cast.ToInt(oneTag.Id))
	if carriage == nil {
		return
	}
	task.setTargetProgress(0, len(carriage.GetWorkers()))
}

func (plr *Player) CheckTaskSpReward(mark string) {
	if mark == "HUTAO_TIP" { //车头喝茶小费

		passenger := plr.GetPassengerById(1006)
		// 如果是用命令跳过，这里可能就没有解锁胡桃夹子
		if passenger != nil {
			// bean := passenger.GetJson()
			// passenger.StarOutput += bean.GetAttrWithLevel(enum.RoleStar, 1) * (1 + bean.GetAttrWithStarLv(enum.RoleStar, 1))
			misc := cfg.Misc_CContainer.GetObj()
			passenger.StarOutput += cast.ToFloat64(misc.Guide.HeadstockStarNum)
			log.Info("[%s] CheckTaskSpReward %s %f", plr.GetUid(), mark, passenger.StarOutput)
		}
	}
}
