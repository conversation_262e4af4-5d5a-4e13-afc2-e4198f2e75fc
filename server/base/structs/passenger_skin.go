package structs

import (
	"train/base/cfg"
	"train/common/pb"
)

func NewPassengerSkin(index int) *PassengerSkin {
	return &PassengerSkin{
		Index: index,
	}
}

type PassengerSkin struct {
	Index int `bson:"index"`
}

func (p *PassengerSkin) ToPb() *pb.PassengerSkin {
	return &pb.PassengerSkin{
		Index: int32(p.Index),
	}
}

func (plr *Player) UnlockSkin(skinId string) {
	bean, _ := cfg.CharacterSkinContainer.GetBeanByUnique(skinId)
	plr.UnlockSkinByCfgBean(bean)
}

// UnlockSkinByCfgBean
/*
 * @description 解锁皮肤
 * @param bean
 */
func (plr *Player) UnlockSkinByCfgBean(bean *cfg.CharacterSkin[string]) {
	if bean != nil {
		skin, ok := plr.Skins[bean.CharacterId]
		if !ok {
			skin = make([]*PassengerSkin, 0)
		}
		skin = append(skin, NewPassengerSkin(bean.Index))
		plr.Skins[bean.CharacterId] = skin
	}
}

// GetSkinCharacterId
/*
 * @description 获取指定角色id拥有的皮肤
 * @param id
 * @return map[int]bool
 */
func (plr *Player) GetSkinCharacterId(id int) []*PassengerSkin {
	skin, ok := plr.Skins[id]
	if !ok {
		skin = make([]*PassengerSkin, 0)
		plr.Skins[id] = skin
	}
	return skin
}
