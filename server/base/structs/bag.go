package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum"
	"train/base/enum/item_type"
	"train/base/event"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"

	"github.com/bwmarrin/snowflake"
	"github.com/spf13/cast"
)

type Item struct {
	Uid     string         `bson:"uid"`     // 唯一id
	Id      int            `bson:"Id"`      // 物品配置id
	Num     int            `bson:"num"`     // 物品数量
	EndTime int            `bson:"endTime"` // 结束时间
	json    *cfg.Item[int] `bson:"-"`
	plr     *Player        `bson:"-"`
}

// AddNum 增加数量
func (this *Item) AddNum(num int) {
	this.Num += num
}

// GetNum 获取数量
func (this *Item) GetNum() int {
	return this.Num
}

func (this *Item) GetJson() *cfg.Item[int] {
	if this.json == nil {
		this.json, _ = cfg.ItemContainer.GetBeanById(this.Id)
	}
	return this.json
}

func (this *Item) GetSurplusTime() int {
	return ut.Max(0, this.EndTime-this.plr.GetNowTime())
}

// ToItemInfo 转换为pb.ItemInfo
func (this *Item) ToItemInfo() *pb.ItemInfo {
	return &pb.ItemInfo{
		Uid:         this.Uid,
		Id:          cast.ToInt32(this.Id),
		Num:         cast.ToInt32(this.Num),
		SurplusTime: cast.ToInt32(this.GetSurplusTime()),
	}
}

var snowflakeNode, err = snowflake.NewNode(1)

// GenUid 获取一个新的物品uid
func GenUid() string {
	return cast.ToString(snowflakeNode.Generate().Int64())
}

func (plr *Player) InitItems() {
	items := plr.Bag
	for _, item := range items {
		item.plr = plr
	}
}

// NewItem 使用物品配置id创建一个item,数量为num
func (plr *Player) NewItem(id int, num int) (item *Item) {
	item = &Item{
		Id:  id,
		Num: num,
		plr: plr,
	}
	bean, _ := cfg.ItemContainer.GetBeanById(id)
	if bean.IsUnique == 1 {
		item.Uid = GenUid()
	}
	if bean.Type == item_type.TIME_BOX {
		misc := cfg.GetMisc()
		timeBoxBean := array.Find(misc.TimeBox, func(item *cfg.TimeBoxConfig) bool {
			return item.Id == bean.Id
		})
		if timeBoxBean != nil {
			item.EndTime = plr.GetNowTime() + ut.Round(timeBoxBean.Time*ut.TIME_HOUR)
		}
	}
	return
}

func (plr *Player) ChangeItemByCond(cond *Condition) {
	id := cast.ToInt(cond.Id)
	num := cond.Num
	uid := cast.ToString(cond.Extra["uid"])
	bean, _ := cfg.ItemContainer.GetBeanById(id)
	if bean.IsUnique == 1 {
		var item *Item
		if uid != "" {
			item = plr.GetItemByUid(uid)
		}
		if item != nil && num > 0 {
			log.Error("[%s] ChangeItem, item already exists: %s %d", plr.GetUid(), uid, id)
			return
		} else if item == nil && num <= 0 {
			log.Error("[%s] ChangeItem, item not found: %s %d", plr.GetUid(), uid, id)
			return
		}
		if num > 0 {
			item = plr.NewItem(id, 0)
			if uid != "" {
				item.Uid = uid
			}
			plr.Bag = append(plr.Bag, item)
		}
		plr.handleChange(item, num)
	} else {
		plr.ChangeItem(id, num)
	}
}

func (plr *Player) ChangeItem(id int, num int) {
	item := plr.GetItem(id)
	if item == nil {
		if num <= 0 {
			log.Error("[%s] ChangeItem, item not found: %d", plr.GetUid(), id)
			return
		}
		item = plr.NewItem(id, 0)
		plr.Bag = append(plr.Bag, item)
	}
	plr.handleChange(item, num)
}

func (plr *Player) handleChange(item *Item, num int) {
	bean := item.GetJson()
	oldVal := item.GetNum()
	if oldVal == 0 {
		if bean.IsShow > 0 {
			if bean.IsUse > 0 || bean.Type == 16 {
				plr.PushNew(enum.MARKNEW_PROP_USE, []int{item.Id})
			}
		}
	}
	if num > 0 {
		plr.AddAccCount(&Condition{Type: condition.PROP, Id: item.Id, Num: num})
	}
	item.AddNum(num)
	curVal := item.GetNum()
	log.Info("[%s] 玩家物品:%d, 数量:%d -> %d %s", plr.GetUid(), item.Id, oldVal, curVal, item.Uid)
	plr.eventCenter.Emit(event.ChangeNumProp, item, curVal-oldVal)
	if curVal == 0 {
		plr.delItem(item)
		curVal = 0
	}
}

func (plr *Player) delItem(item *Item) {
	plr.Bag = array.Remove(plr.Bag, item)
}

func (plr *Player) GetItem(id int) *Item {
	return array.Find(plr.Bag, func(item *Item) bool {
		return item.Id == id
	})
}

func (plr *Player) GetItemByUid(uid string) *Item {
	return array.Find(plr.Bag, func(item *Item) bool {
		return item.Uid == uid
	})
}

func (plr *Player) GetItemNum(id int) int {
	item := plr.GetItem(id)
	if item == nil {
		return 0
	}
	return item.GetNum()
}

func (plr *Player) GetItemNumByUid(uid string) int {
	item := plr.GetItemByUid(uid)
	if item == nil {
		return 0
	}
	return item.GetNum()
}

func (plr *Player) HasTimeStoneKey(id int) bool {
	return plr.GetNumByCondition(&Condition{Type: condition.PROP, Id: id}) > 0
}

func (plr *Player) GetTimeStoneKey2FragCount() int {
	items := lo.Filter(plr.Bag, func(item *Item, _ int) bool {
		return item.GetJson().Type == item_type.TIME_STONE_KEY_2_FRAG
	})
	return len(items)
}

func (plr *Player) GenRandomBoxRewards(id int) []*Condition {
	misc := cfg.GetMisc()
	randomBoxBean := array.Find(misc.RandomBox, func(item *cfg.RandomBoxConfig) bool {
		return item.Id == id
	})
	idx := ut.RandomIndexByWeight(randomBoxBean.Rewards, func(r *cfg.ChestReward) int { return r.Weight })
	fixCount := idx + 1
	fixReward := randomBoxBean.Rewards[idx]
	numList := ut.NumAvgSplit(fixReward.Num, fixCount)
	rewards := lo.Map(numList, func(num int, _ int) *Condition {
		return &Condition{Type: fixReward.Type, Id: fixReward.Id, Num: num}
	})
	for i := fixCount; i < 3; i++ {
		rewards = append(rewards, plr.GenerateRewards(nil, randomBoxBean.RewardRandom)...)
	}
	rewards = ut.RandomArray(rewards)
	return rewards
}
