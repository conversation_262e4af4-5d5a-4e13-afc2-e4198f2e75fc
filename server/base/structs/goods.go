package structs

import (
	"fmt"
	"train/base/cfg"
	"train/base/event"
	"train/common/pb"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
)

type Goods struct {
	Id    string `bson:"id"`    // 货物id
	Level int    `bson:"level"` // 等级
}

func NewGoods(id string) *Goods { return &Goods{Id: id} }

func (g *Goods) ToPb() *pb.CarriageGoodsInfo {
	if g == nil {
		return nil
	}
	// 兼容之前的数据
	if g.Level == 0 {
		g.Level = 1
	}
	return &pb.CarriageGoodsInfo{
		Id: g.Id,
		Lv: int32(g.Level),
	}
}

// GetAttr
/*
 * @description 获取货物属性
 * @param attr
 * @return int
 */
func (g *Goods) GetAttr() int {
	json := g.GetJson()
	if json == nil {
		return 0
	}
	return json.AttrValue
}

// GetJson
/*
 * @description 获取货物等级配置json
 * @return *cfg.TrainGoodsLevel
 */
func (g *Goods) GetJson() *cfg.TrainGoodsLevel[string] {
	lvJson, _ := cfg.TrainGoodsLevelContainer.GetBeanByUnique(fmt.Sprintf("%s-%d", g.Id, g.Level))
	return lvJson
}

// GetGoodsJson
/*
 * @description 获取货物配置json
 * @return *cfg.TrainGoods
 */
func (g *Goods) GetGoodsJson() *cfg.TrainGoods[string] {
	goodsJson, _ := cfg.TrainGoodsContainer.GetBeanByUnique(g.Id)
	return goodsJson
}

func (g *Goods) IsMaxLevel() bool {
	_, exists := cfg.TrainGoodsLevelContainer.GetBeanByUnique(fmt.Sprintf("%s-%d", g.Id, g.Level+1))
	return !exists
}

// IsGoodsCanUnlock 是否能解锁/升级食物
func (c *Carriage) IsGoodsCanUnlock(id string, level int) bool {
	good, exists := lo.Find(c.Goods, func(g *Goods) bool {
		return g.Id == id
	})
	if exists {
		return level-good.Level == 1
	} else {
		return level == 1
	}
}

func (c *Carriage) UnlockGoods(id string, lv int) {
	goods, _ := lo.Find(c.Goods, func(goods *Goods) bool {
		return goods.Id == id
	})
	if goods == nil {
		goods = NewGoods(id)
		goods.Level = lv
		c.Goods = append(c.Goods, goods)
	} else {
		if !c.IsGoodsCanUnlock(id, lv) {
			return
		}
		goods.Level = lv
	}
}

func (c *Carriage) GetGoods(goodsId string) *Goods {
	goods, _ := lo.Find(c.Goods, func(goods *Goods) bool {
		return goods.Id == goodsId
	})
	return goods
}

func (plr *Player) UnlockGoods(carriage *Carriage, id string, lv int) {
	carriage.UnlockGoods(id, lv)
	plr.eventCenter.Emit(event.UnLockTrainGoods, carriage.Id)
	log.Info("[%s] UnlockGoods %s-%d", plr.GetUid(), id, lv)
}
