package structs

import (
	"train/base/cfg"
	"train/common/pb"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func NewAchievement() *Achievement {
	return &Achievement{}
}

type Achievement struct {
	Curs      map[string]*AchievementTaskInfo `bson:"tasks"`         //当前任务
	Completes map[string]*CompletedTaskInfo   `bson:"completeTasks"` //已完成的任务
	plr       *Player                         `bson:"-"`
}

func (a *Achievement) Init(plr *Player) {
	a.plr = plr
	if a.Curs == nil {
		a.Curs = make(map[string]*AchievementTaskInfo)
	}
	if a.Completes == nil {
		a.Completes = make(map[string]*CompletedTaskInfo)
	}
	for _, achievementTypeCfg := range cfg.AchievementTypeContainer.GetData() {
		achievements := cfg.AchievementGroupMap[achievementTypeCfg.Id]
		for _, achievementCfg := range achievements {
			// 该类型有正在进行的就返回
			if a.IsDoing(achievementCfg.GetUnique()) {
				info := a.CurGet(achievementCfg.GetUnique())
				if info.json == nil {
					info.json = achievementCfg
				}
				break
			}
			// 该成就被完成就判断下一个
			if a.IsCompleted(achievementCfg.GetUnique()) {
				continue
			}
			// 当前类型不存在 就创建
			a.Curs[achievementCfg.GetUnique()] = &AchievementTaskInfo{
				Id:           achievementCfg.GetUnique(),
				AcceptedTime: a.plr.GetNowTime(),
				json:         achievementCfg,
			}
			break
		}
	}
}

// CurGet 从当前成就任务中获取
func (a *Achievement) CurGet(id string) *AchievementTaskInfo {
	return a.Curs[id]
}

// CompleteGet 从完成的成就任务中获取
func (a *Achievement) CompleteGet(id string) *CompletedTaskInfo {
	return a.Completes[id]
}

// IsDoing 成就任务是否正在进行中
func (a *Achievement) IsDoing(id string) bool {
	return a.CurGet(id) != nil
}

// IsCompleted 成就任务是否被完成
func (a *Achievement) IsCompleted(id string) bool {
	return a.CompleteGet(id) != nil
}

func (this *Achievement) ToPb() *pb.AchievementInfo {
	return &pb.AchievementInfo{
		Tasks: lo.MapToSlice(this.Curs, func(id string, val *AchievementTaskInfo) *pb.Task {
			return val.toPb()
		}),
		Completes: lo.MapToSlice(this.Completes, func(id string, val *CompletedTaskInfo) string {
			return val.toPb()
		}),
		CompleteTime: lo.MapToSlice(this.Completes, func(id string, val *CompletedTaskInfo) uint64 {
			return cast.ToUint64(val.FinishedTime)
		}),
	}
}

type AchievementTaskInfo struct {
	Id           string                   `bson:"id"`           // 任务id
	AcceptedTime int                      `bson:"acceptedTime"` // 领取时间
	Targets      []*TaskCondition         `bson:"targets"`      // 条件列表
	json         *cfg.Achievement[string] `bson:"-"`            // 配置
}

// IsHasNext 是否有下一个成就
func (this *AchievementTaskInfo) IsHasNext() bool {
	return this.json.Next() == nil
}

// GetNext 下一个成就
func (this *AchievementTaskInfo) GetNext(plr *Player) *AchievementTaskInfo {
	next := this.json.Next()
	if next == nil {
		return nil
	}
	return &AchievementTaskInfo{
		Id:           next.GetUnique(),
		AcceptedTime: plr.GetNowTime(),
		json:         next,
	}
}

// GetTargetIdxById 获取任务目标中 指定条件tagId 所在位置
func (this *AchievementTaskInfo) GetTargetIdxById(tagId any) int {
	if this.json == nil {
		return -1
	}
	return array.FindIndex(this.json.Target, func(need *cfg.TaskTarget) bool { return cast.ToString(need.Id) == cast.ToString(tagId) })
}

// 获取数组索引
func (this *AchievementTaskInfo) GetTargetIdxByType(tagType interface{}) int {
	if this.json == nil {
		return -1
	}
	return array.FindIndex(this.json.Target, func(need *cfg.TaskTarget) bool { return cast.ToInt(need.Type) == cast.ToInt(tagType) })
}

func (this *AchievementTaskInfo) checkConditionType(tagType interface{}) bool {
	return this.GetTargetIdxByType(tagType) >= 0
}

// IsTargetExists 判断条件是否存在
func (this *AchievementTaskInfo) IsTargetExists(tagId any) bool {
	return this.GetTargetIdxById(tagId) >= 0
}

// 增加指定位置条件的进度
func (this *AchievementTaskInfo) addTargetProgress(idx, addNum int) {
	key := cast.ToString(idx)
	target := array.Find(this.Targets, func(target *TaskCondition) bool { return target.Id == key })
	if target == nil {
		target = &TaskCondition{Id: key, Num: addNum}
		this.Targets = append(this.Targets, target)
	} else {
		target.Num += addNum
	}
}
func (this *AchievementTaskInfo) addTargetProgressByTargetId(tagId any, addNum int) {
	this.addTargetProgress(this.GetTargetIdxById(tagId), addNum)
}

func (plr *Player) onAddAchievementTask(taskType string, condition func(achievement *AchievementTaskInfo) bool, add func(achievement *AchievementTaskInfo)) {
	for _, info := range plr.Achievement.Curs {
		if info.json != nil && info.json.ConditionType == taskType && (condition == nil || condition(info)) {
			if add != nil {
				add(info)
			}
		}
	}
}
func (this *AchievementTaskInfo) GetJson() *cfg.Achievement[string] {
	if this.json == nil {
		json, _ := cfg.AchievementContainer.GetBeanByUnique(this.Id)
		this.json = json
		if json == nil {
			log.Error("AchievementTaskInfo GetJson fail:", this.Id)
		}
	}
	return this.json
}
func (this *AchievementTaskInfo) toPb() *pb.Task {
	return &pb.Task{
		Id: this.Id,
		Targets: lo.Map(this.Targets, func(target *TaskCondition, i int) *pb.TaskTarget {
			return &pb.TaskTarget{Id: target.Id, Num: cast.ToInt32(target.Num)}
		}),
	}
}

func (this *AchievementTaskInfo) GetTargetProgress(idx int) int {
	curTarget := array.Find(this.Targets, func(target *TaskCondition) bool { return target.Id == cast.ToString(idx) })
	if curTarget == nil {
		return 0
	}
	return curTarget.Num
}

func (this *AchievementTaskInfo) haveTargetProgress(tagId int) bool {
	curTarget := array.Find(this.Targets, func(target *TaskCondition) bool { return target.Id == cast.ToString(tagId) })
	return curTarget != nil
}

func (this *AchievementTaskInfo) GetConfigTarget() []*cfg.TaskTarget {
	return this.GetJson().Target
}
func (this *AchievementTaskInfo) GetTaskType() string {
	return this.GetJson().ConditionType
}

func (this *AchievementTaskInfo) CheckByTargets() bool {
	json := this.GetJson()
	if json == nil {
		return false
	}
	for i, need := range json.Target {
		if this.GetTargetProgress(i) < need.Num {
			return false
		}
	}
	return true
}
func (this *AchievementTaskInfo) CheckHaveTargets() bool {
	json := this.GetJson()
	if json == nil {
		return false
	}
	for _, need := range json.Target {
		if !this.haveTargetProgress(cast.ToInt(need.Id)) {
			return false
		}
	}
	return true
}

func (plr *Player) CompleteAchievementTask(taskId string) {
	task := plr.Achievement.CurGet(taskId)
	if task == nil {
		return
	}
	plr.Achievement.Completes[taskId] = &CompletedTaskInfo{
		Id:           task.Id,
		AcceptedTime: task.AcceptedTime,
		FinishedTime: plr.GetNowTime(),
	}
	log.Info("[%s] 完成成就任务:%s", plr.GetUid(), taskId)
	if next := task.GetNext(plr); next != nil {
		plr.Achievement.Curs[next.Id] = next
		next.Targets = task.Targets
		log.Info("[%s] 触发下一个成就任务:%s", plr.GetUid(), next.Id)
	}
	delete(plr.Achievement.Curs, taskId)
}

type CommonTaskObj interface {
	GetTaskType() string
	GetConfigTarget() []*cfg.TaskTarget // 获取json配置的目标
	CheckByTargets() bool               // 直接用targets里面的数据判断
	CheckHaveTargets() bool             //
}
