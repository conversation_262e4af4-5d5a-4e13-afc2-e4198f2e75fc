package structs

import (
	"context"
	"train/common/pb"
	"train/db"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	ORDER_STATE_NOT_PAY = iota //未支付
	ORDER_STATE_PAY            //已支付未发放
	ORDER_STATE_FINISH         //已发放
	ORDER_STATE_REFUND         //已退款
)

type PayModule struct {
	NotFinishOrders []*NotFinishOrder `bson:"notFinishOrders"` // 未完成订单列表
	PayCountMap     map[string]int    `bson:"payCountMap"`     // 订单购买次数
	write           bool              `bson:"-"`               // 是否需要更新db
}

type PayOrder struct {
	UID          string  `json:"uid" bson:"uid"`                     //订单号
	OrderId      string  `json:"order_id" bson:"order_id"`           //外部订单号
	UserId       string  `json:"user_id" bson:"user_id"`             //玩家id
	State        int     `json:"state" bson:"state"`                 //状态
	ProductId    string  `json:"product_id" bson:"product_id"`       //商品id
	Platform     string  `json:"platform" bson:"platform"`           //支付方式
	CreateTime   int     `json:"create_time" bson:"create_time"`     //创建时间
	PurchaseTime int     `json:"purchase_time" bson:"purchase_time"` //支付时间
	FinishTime   int     `json:"finish_time" bson:"finish_time"`     //完成时间
	CurrencyType string  `json:"currency_type" bson:"currency_type"` //支付币种
	PayAmount    float64 `json:"pay_amount" bson:"pay_amount"`       //支付金额
	Quantity     int     `json:"quantity" bson:"quantity"`           //数量
}

type NotFinishOrder struct {
	CpOrderId string `json:"cp_order_id" bson:"cp_order_id"` //订单号
	ProductId string `json:"product_id" bson:"product_id"`   //商品id
	Quantity  int    `json:"quantity" bson:"quantity"`       //数量
	Platform  string `json:"platform" bson:"platform"`       //平台
}

// 未完成订单转为pb
func (n *NotFinishOrder) ToPb() *pb.NotFinishPayOrder {
	return &pb.NotFinishPayOrder{
		CpOrderId: n.CpOrderId,
		ProductId: n.ProductId,
		Quantity:  int32(n.Quantity),
		Platform:  n.Platform,
	}
}

// 创建订单
func (p *PayModule) CreateOrder(userId string, productId string, platform string) (orderData *PayOrder, err string) {
	log.Info("CreateOrder userId: %v, productId: %v, platform: %v", userId, productId, platform)
	uid := cast.ToString(GenUid())
	orderData = &PayOrder{
		UID:        uid,
		UserId:     userId,
		ProductId:  productId,
		Platform:   platform,
		State:      ORDER_STATE_NOT_PAY,
		CreateTime: ut.Now(),
	}
	if _, e := db.PAY.GetCollection().InsertOne(context.TODO(), orderData); e != nil {
		err = e.Error()
		log.Error("CreateOrder userId: %v, productId: %v, err: %v", userId, productId, err)
	}
	return
}

// 查询订单
func (p *PayModule) FindByUid(uid string) (data PayOrder, err string) {
	if e := db.PAY.GetCollection().FindOne(context.TODO(), bson.M{"uid": uid}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 查询订单 外部订单号
func (p *PayModule) FindByOrderId(orderId, platform string) (data PayOrder, err string) {
	if e := db.PAY.GetCollection().FindOne(context.TODO(), bson.M{"order_id": orderId, "platform": platform}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 查询最近未验证订单
func (p *PayModule) FindByUserNotVerifyOrder(userId, productId, platform string) (data PayOrder, err string) {
	opts := options.FindOne().SetSort(bson.M{"create_time": -1})
	if e := db.PAY.GetCollection().FindOne(context.TODO(), bson.M{"user_id": userId, "product_id": productId, "platform": platform, "state": ORDER_STATE_NOT_PAY}, opts).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 更新验证订单
func (p *PayModule) UpdateVerifiedOrder(uid, orderId, currencyType string, purchaseTime int, payAmount float64, quantity int) (err string) {
	if _, e := db.PAY.GetCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"state": ORDER_STATE_PAY, "order_id": orderId, "purchase_time": purchaseTime, "currency_type": currencyType, "pay_amount": payAmount, "quantity": quantity}}); e != nil {
		err = e.Error()
		log.Error("UpdateVerifiedOrder", err)
	}
	return
}

// 更新完成订单
func (p *PayModule) UpdateFinishedOrder(uid string) (err string) {
	if _, e := db.PAY.GetCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"state": ORDER_STATE_FINISH, "finish_time": ut.Now()}}); e != nil {
		err = e.Error()
		log.Error("UpdateVerifiedOrder", err)
	}
	return
}

// 更新退款订单
func (p *PayModule) UpdateRefundOrder(uid string) (err string) {
	if _, e := db.PAY.GetCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"state": ORDER_STATE_REFUND}}); e != nil {
		err = e.Error()
		log.Error("UpdateRefundOrder", err)
	}
	return
}

// 更新一条
func (p *PayModule) UpdateOne(uid string, key string, value interface{}) (err string) {
	if _, e := db.PAY.GetCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{key: value}}); e != nil {
		err = e.Error()
		log.Error("UpdateOrder", err)
	}
	return
}

// 更新订单
func (p *PayModule) UpdateData(uid string, data bson.M) (err string) {
	if _, e := db.PAY.GetCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": data}); e != nil {
		err = e.Error()
		log.Error("UpdateOrder", err)
	}
	return
}

// 获取指定用户已完成订单数量
func (p *PayModule) GetUserFinishOrderCount(userId string) (count int, err string) {
	if c, e := db.PAY.GetCollection().CountDocuments(context.TODO(), bson.M{"user_id": userId, "state": ORDER_STATE_FINISH}); e != nil {
		err = e.Error()
		if e != mongo.ErrNoDocuments {
			log.Error("GetUserFinishOrderCount userId: %v, err: %v", userId, err)
		}
	} else {
		count = int(c)
	}
	return
}

func NewPayModule() *PayModule {
	return &PayModule{
		NotFinishOrders: make([]*NotFinishOrder, 0),
	}
}

func (p *PayModule) Init(plr *Player) {
}

// 是不是首购
func (p *PayModule) IsFirstPay(productIds string) bool {
	count := p.PayCountMap[productIds]
	return count == 0
}

func (p *PayModule) ToPb() *pb.Pay {
	return &pb.Pay{
		NotFinishOrders: lo.Map(p.NotFinishOrders, func(o *NotFinishOrder, i int) *pb.NotFinishPayOrder { return o.ToPb() }),
		PayCountMap:     lo.MapEntries(p.PayCountMap, func(key string, val int) (string, int32) { return key, int32(val) }),
	}
}
