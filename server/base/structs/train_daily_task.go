package structs

import (
	"train/base/cfg"
	"train/base/enum/function_type"
	"train/base/enum/wanted_condition_type"
	"train/common/pb"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func NewTrainDailyTaskModule() *TrainDailyTaskModule {
	return &TrainDailyTaskModule{}
}

type TrainDailyTaskModule struct {
	List  []*TrainDailyTaskItem `bson:"list"`
	Point int                   `bson:"point"`
	plr   *Player
}

func (t *TrainDailyTaskModule) init(plr *Player) {
	t.plr = plr
	for _, item := range t.List {
		item.init(plr)
	}
}

func (t *TrainDailyTaskModule) Unlock() { t.Refresh() }

func (t *TrainDailyTaskModule) CheckAndRefresh() {
	if !t.plr.IsUnlockFunction(function_type.PLAY_TRAIN_DAILY_TASK) {
		return
	}
	t.Refresh()
}

func (t *TrainDailyTaskModule) Get(index int) *TrainDailyTaskItem {
	if ut.IsIndexOutOfBounds(t.List, index) {
		return nil
	}
	return t.List[index]
}

// 开始
func (t *TrainDailyTaskModule) Start(index int, roles []int) {
	item := t.List[index]
	item.State = pb.CommonState_InProcess
	item.Roles = roles
	item.EndTime = t.plr.GetNowTime() + item.LevelJson().CostTime*ut.TIME_MINUTE
}

func (t *TrainDailyTaskModule) Complete(index int) {
	item := t.List[index]
	item.State = pb.CommonState_FinishWithReward
	t.Point += item.LevelJson().Point
}

func (t *TrainDailyTaskModule) Refresh() {
	ary := make([]*TrainDailyTaskItem, 0)
	train := t.plr.Train

	inProcessList := lo.Filter(t.List, func(item *TrainDailyTaskItem, index int) bool {
		return item.State == pb.CommonState_InProcess
	})

	tmpList := cfg.TrainDailyTaskItemContainer.GetData()

	isVaildTrain := func(id int) bool {
		if id == 0 {
			return true
		}
		carriage := train.GetCarriageById(id)
		if carriage == nil || !carriage.IsBuilt() {
			return false
		}
		return !lo.SomeBy(inProcessList, func(item *TrainDailyTaskItem) bool { return item.TrainId == id })
	}

	lv := t.GetLevel()
	levelData, _ := cfg.TrainDailyTaskContainer.GetBeanById(lv)
	taskCnt := levelData.TaskCnt + t.plr.Tech.GetTrainDailyTask()
	for i := 0; i < taskCnt; i++ {
		idx := ut.RandomIndexByWeight(levelData.Level, func(data *cfg.ConfigCondition) int { return data.Weight })
		// 任务星级
		level := cast.ToInt(levelData.Level[idx].Id)
		// 根据星级去taskLevel表筛选
		levelCfg, _ := cfg.TrainDailyTaskLevelContainer.GetBeanById(level)
		// 任务表现
		tmpList = lo.Filter(tmpList, func(item *cfg.TrainDailyTaskItem[int], index int) bool {
			if len(item.Train) == 0 {
				return true
			}
			return lo.SomeBy(item.Train, isVaildTrain)
		})
		itemCfg := ut.RandomIn(tmpList)
		current := &TrainDailyTaskItem{
			Id:         itemCfg.Id,
			Level:      level,
			Conditions: []*WantedCondition{},
		}
		condAry := cfg.TrainDailyTaskConditionContainer.GetData()
		req := levelCfg.Req
		// 条件数量
		for i := 0; i < levelCfg.Normal.Num; i++ {
			req = lo.Filter(req, func(data *cfg.ChestReward, i int) bool {
				count := lo.CountBy(current.Conditions, func(c *WantedCondition) bool {
					return c.Type == data.Type && c.Value == data.Id
				})
				return data.Num == -1 || count < data.Num
			})
			if len(req) <= 0 {
				log.Error("%d gen train_daily_task fail %v %v", t.plr.GetUid(), levelCfg.Id, len(current.Conditions))
				break
			}
			idx := ut.RandomIndexByWeight(req, func(d *cfg.ChestReward) int { return d.Weight })
			data := req[idx]

			switch data.Type {
			case wanted_condition_type.QUALITY:
				current.Conditions = append(current.Conditions, &WantedCondition{Type: data.Type, Value: data.Id})
			default:
				matchAry := lo.Filter(condAry, func(d *cfg.TrainDailyTaskCondition[string], i int) bool {
					return d.Type == data.Type
				})
				idx := ut.RandomIndexByWeight(matchAry, func(d *cfg.TrainDailyTaskCondition[string]) int { return d.Weight })
				data := matchAry[idx]
				current.Conditions = append(current.Conditions, &WantedCondition{Type: data.Type, Value: data.Value})
			}
		}

		people, needAdd := t.plr.getMinPeople(current.Conditions)
		if needAdd {
			people += levelData.AddPeople
		}
		current.People = people
		// 奖励
		rewards := t.plr.GenerateRewards(levelCfg.Reward, levelCfg.RewardRandom, &cfg.GenerateRewardsOpt{RandomCnt: &levelCfg.RandomCnt})
		current.Rewards = rewards
		// 事件车厢
		if len(itemCfg.Train) > 0 {
			idAry := lo.Filter(itemCfg.Train, func(id int, index int) bool {
				return isVaildTrain(id)
			})
			if len(idAry) <= 0 {
				// 没有可用车厢时跳过此任务，而不是回退到原始配置
				log.Info("No available carriage for task %d (player %d), skipping", itemCfg.Id, t.plr.GetUid())
				continue
			}
			idx := ut.Random(0, len(idAry)-1)
			current.TrainId = idAry[idx]
		}
		ary = append(ary, current)
		inProcessList = append(inProcessList, current)
	}

	if t.List == nil {
		t.List = ary
		return
	}
	// 保留的任务
	// 之前的任务
	// 可以完成的不清理 进行中的不清理
	reserveList := lo.Filter(t.List, func(item *TrainDailyTaskItem, index int) bool {
		return item.CanComplete() || item.State == pb.CommonState_InProcess
	})
	t.List = append(reserveList, ary...)
}

func (t *TrainDailyTaskModule) GetLevel() int {
	lv := 0
	for _, v := range cfg.TrainDailyTaskContainer.GetData() {
		if v.NeedPoint > t.Point {
			break
		}
		lv = v.Id
	}
	return lv
}

func (t *TrainDailyTaskModule) ToPb() *pb.TrainDailyTask {
	return &pb.TrainDailyTask{
		List: lo.Map(t.List, func(c *TrainDailyTaskItem, i int) *pb.TrainDailyTaskItem { return c.ToPb() }),
	}
}

type TrainDailyTaskItem struct {
	Id         int                           `bson:"id"`
	Conditions []*WantedCondition            `bson:"conditions"`
	People     int                           `bson:"people"`
	Rewards    []*Condition                  `bson:"rewards"`
	EndTime    int                           `bson:"endTime"`
	State      pb.CommonState                `bson:"state"`
	Roles      []int                         `bson:"roles"` //进行中的角色
	TrainId    int                           `bson:"trainId"`
	Level      int                           `bson:"level"`
	itemJson   *cfg.TrainDailyTaskItem[int]  `bson:"-"`
	levelJson  *cfg.TrainDailyTaskLevel[int] `bson:"-"`
	plr        *Player                       `bson:"-"`
}

func (t *TrainDailyTaskItem) init(plr *Player) { t.plr = plr }

func (t *TrainDailyTaskItem) ItemJson() *cfg.TrainDailyTaskItem[int] {
	if t.itemJson == nil {
		t.itemJson, _ = cfg.TrainDailyTaskItemContainer.GetBeanById(t.Id)
	}
	return t.itemJson
}
func (t *TrainDailyTaskItem) LevelJson() *cfg.TrainDailyTaskLevel[int] {
	if t.levelJson == nil {
		t.levelJson, _ = cfg.TrainDailyTaskLevelContainer.GetBeanById(t.Level)
	}
	return t.levelJson
}

func (t *TrainDailyTaskItem) GetSurplusTime() int {
	if t.EndTime <= 0 {
		return 0
	}
	val := t.EndTime - t.plr.GetNowTime()
	if t.State == pb.CommonState_InProcess && val <= 0 {
		t.State = pb.CommonState_DoneWithoutReward
		val = 0
	}
	return val
}

func (t *TrainDailyTaskItem) CanEnd() bool {
	return t.State == pb.CommonState_InProcess && t.GetSurplusTime() <= 0
}

func (t *TrainDailyTaskItem) CanComplete() bool {
	return t.State == pb.CommonState_DoneWithoutReward || t.CanEnd()
}

func (t *TrainDailyTaskItem) ToPb() *pb.TrainDailyTaskItem {
	return &pb.TrainDailyTaskItem{
		Id:          int32(t.Id),
		Conditions:  lo.Map(t.Conditions, func(c *WantedCondition, i int) *pb.WantedCondition { return c.ToPb() }),
		Rewards:     ToPbConditions(t.Rewards),
		SurplusTime: int32(t.GetSurplusTime()),
		State:       t.State,
		Roles:       ut.ToInt32(t.Roles),
		People:      int32(t.People),
		TrainId:     int32(t.TrainId),
		Level:       int32(t.Level),
	}
}
