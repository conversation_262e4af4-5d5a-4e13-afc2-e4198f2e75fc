package script

import (
	"crypto/md5"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"io/fs"
	"math"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
	"train/base/enum"
	comm "train/common"
	ut "train/utils"

	"github.com/samber/lo"

	"github.com/huyangv/vmqant/conf"
	basegate "github.com/huyangv/vmqant/gate/base"
	"github.com/huyangv/vmqant/log"
	logs "github.com/huyangv/vmqant/log/beego"
	"github.com/huyangv/vmqant/module"
	"github.com/spf13/cast"
)

var ServerType string // 当前进程服务器类型
var Sid int           // 如果是game的话，sid代表区服
var Mark int          // 服务器标记

const reset = "\u001B[0m"

var ConfigMd5 map[string]string

// CallBeforeApplicationLaunchedSync 程序启动
func CallBeforeApplicationLaunchedSync() {
	LoadSeverConfig()
	SetLogger()
	// 解析启动参数
	flag.Parse()
	// 启动死锁监听
	comm.InitDeadlockListener()
	// 判断启动参数
	if ut.IsEmpty(ServerType) {
		flag.PrintDefaults()
		os.Exit(-1)
	}

	switch ServerType {
	case enum.ServerTypeLogin:
		Mark = enum.MarkLogin
	case enum.ServerTypeGame:
		Mark = enum.MarkGame
	case enum.ServerTypeGate:
		Mark = enum.MarkGate
	case enum.ServerTypeHttp:
		Mark = enum.MarkHttp
	case enum.ServerTypeTask:
		Mark = enum.MarkTask
	case enum.ServerTypeDev:
		Mark |= enum.MarkLogin | enum.MarkGame | enum.MarkGate | enum.MarkHttp | enum.MarkTask
	}
	log.Info("ServerType:%s", ServerType)
	if ServerType == enum.ServerTypeGame || ServerType == enum.ServerTypeLogin || ServerType == enum.ServerTypeDev {
		if Sid == -1 {
			fmt.Printf("请传入参数:区服序号,例: -sid 1, 区服必须是数据库中存在一条区服数据的. \n")
			os.Exit(-1)
		}
		log.Info("Sid:%d", Sid)
	}

	if comm.IsDebug() {
		calculateJsonFileMd5()
	}
}

func SetLogger() {
	isDebug := comm.IsDebug()
	// 设置日志级别
	level := ut.If(isDebug, logs.LevelDebug, logs.LevelInformational)
	log.LogBeego().SetLevel(level)
	// 正式环境开启异步日志
	if !isDebug {
		log.LogBeego().Async()
	}
	// 不再格式化时间，由下面的SetFormatFunc去格式化消息
	logs.FormatTime = ""
	// 写入log file时附带颜色
	logs.WriteLogFileColor = true

	log.LogBeego().SetFormatFunc(func(when time.Time, span *logs.BeegoTraceSpan, logLevel int, msg string, v ...interface{}) (string, error) {
		timestr := when.Format("2006/01/02 - 15:04:05")
		color := reset
		if logLevel == logs.LevelAlert {
			color = "\u001B[97;42m" //绿色背景 白字
		}
		if logLevel == logs.LevelError {
			color = "\u001B[97;41m" // 红色背景 白字
		}
		if logLevel == logs.LevelDebug {
			color = "\u001B[97;45m" // 浅紫色背景 白字
		}
		if logLevel == logs.LevelWarning {
			color = "\u001B[97;43m" // 黄色背景 白字
		}
		msg = fmt.Sprintf(msg, v...)
		// 是否打印堆栈信息
		withStack := isDebug
		if logLevel > logs.LevelInformational {
			withStack = true
		}
		if withStack {
			_, file, line, _ := runtime.Caller(4)
			i := math.Max(cast.ToFloat64(strings.LastIndex(file, "/"))+1, 0)
			msg = fmt.Sprintf("%s%-22s %s[%s] %s: %s %s", reset, fmt.Sprintf("%s:%d", file[cast.ToInt(i):], line), color, ServerType, timestr, msg, reset)
		} else {
			msg = fmt.Sprintf("%s[%s] %s: %s %s", color, ServerType, timestr, msg, reset)
		}
		return msg, nil
	})
}

// AddRPCSerializeOrNot 因为只有gate模块才会注入Session反序列化逻辑，所以这里需要手动为其他模块注入
func AddRPCSerializeOrNot(app module.App) {
	serialize := app.GetRPCSerialize()
	if serialize["gate"] != nil {
		return
	}
	gate := new(basegate.Gate)
	// 这句话是必须的
	gate.App = app
	log.Info("Adding session structures to serialize interfaces.")
	if err := app.AddRPCSerialize("gate", gate); err != nil {
		log.Warning("Adding session structures failed to serialize interfaces %s", err.Error())
	}
}

// LaunchAllOrNotAtDev  1 dev环境需要全部模块启动 2 game和login环境下需要修改setting.ID
func LaunchAllOrNotAtDev(app module.App) {
	if ServerType == enum.ServerTypeDev {
		mods := app.GetSettings().Module
		setProcessID(mods[enum.ServerTypeGate])
		setProcessID(mods[enum.ServerTypeLogin])
		setProcessID(mods[enum.ServerTypeGame])
		setProcessID(mods[enum.ServerTypeHttp])
		setProcessID(mods[enum.ServerTypeTask])
	}
	//setID(enum.ServerTypeGame, app)
	//setID(enum.ServerTypeLogin, app)
}

func setID(t string, app module.App) {
	if ServerType != enum.ServerTypeDev && ServerType != t {
		return
	}
	mod := app.GetSettings().Module[t]
	if mod == nil {
		log.Error("no %s node found at app.GetSettings.", t)
		os.Exit(-1)
	}
	for _, settings := range mod {
		settings.ID = fmt.Sprintf("%s&%d", settings.ID, Sid)
	}
}

func setProcessID(settings []*conf.ModuleSettings) {
	if settings == nil {
		return
	}
	mut := len(settings) <= 1
	for i, setting := range settings {
		setting.ProcessID = lo.If(mut, "dev").Else(fmt.Sprintf("dev&%d", i))
	}
}

func LoadSeverConfig() {
	bytes, err := ut.LoadJsonByPath("server", "/bin/conf/")
	if err != nil {
		panic(fmt.Sprintf("加载server.json出错:%s", err.Error()))
	}
	err = json.Unmarshal(bytes, &comm.ServerConfig)
	if err != nil {
		panic(fmt.Sprintf("解析server.json出错:%s", err.Error()))
	}
}

func calculateJsonFileMd5() {
	ConfigMd5 = make(map[string]string)
	configPath := fmt.Sprintf("%s/bin/conf/json", ut.WorkDir())

	// 读取copy_conf.go 只计算需要拷贝的json文件  防止一些bug
	bytes, err := os.ReadFile(fmt.Sprintf("%s/bin/conf/json/copy_conf.go", ut.WorkDir()))
	if err != nil {
		panic(err)
	}
	// 提取copy_conf.go中填写config切片
	sTag := "var configs = map[string]string{"
	eTag := "}"
	body := string(bytes)
	sIdx := strings.Index(body, sTag)
	if sIdx == -1 {
		panic("未找到config配置数据切片")
	}
	body = body[sIdx+len(sTag):]
	eIdx := strings.Index(body, eTag)
	if eIdx == -1 {
		panic("未找到config配置数据切片")
	}
	body = body[:eIdx]
	// 去掉go编译器符号
	body = strings.ReplaceAll(body, "\"", "")
	body = strings.ReplaceAll(body, "\n", "")
	body = strings.ReplaceAll(body, "\t", "")
	// 去掉空格
	body = strings.TrimSpace(body)
	configs := make([]string, 0)
	for _, str := range strings.Split(body, ",") {
		arg := strings.Split(str, ":")
		if len(arg) != 2 || arg[0] == "" {
			continue
		}
		configs = append(configs, arg[0])
	}
	filepath.Walk(configPath, func(path string, info fs.FileInfo, err error) error {
		if path == configPath || info.IsDir() || !strings.HasSuffix(path, ".json") || strings.HasPrefix(path, ".") {
			return nil
		}
		_, exists := lo.Find(configs, func(config string) bool {
			return strings.TrimSpace(config) == strings.Replace(info.Name(), ".json", "", 1)
		})
		if !exists {
			return nil
		}
		//cmd := exec.Command("node", "sync.js", path)
		//cmd.Dir = ut.WorkDir()
		//output, err := cmd.Output()
		//md5 := string(output)
		//if md5 == "" {
		//	//panic("计算md5出错:" + err.Error())
		//	log.Info("计算md5出错:" + err.Error())
		//}
		//md5 = strings.ReplaceAll(md5, "\n", "")
		hash := md5.New()
		file, err := os.Open(path)
		if _, err := io.Copy(hash, file); err != nil {
			panic(err)
		}
		// 将哈希值转换为16进制字符串
		md5 := fmt.Sprintf("%x", hash.Sum(nil))
		ConfigMd5[info.Name()] = md5
		return nil
	})
}
