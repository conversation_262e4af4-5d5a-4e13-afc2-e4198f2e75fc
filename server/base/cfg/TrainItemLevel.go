package cfg

const ConstTrainItemLevel = "TrainItemLevel"

type TrainItemLevel[K string] struct {
	Id         K              `json:"id"`
	Lv         int            `json:"level"`
	CarriageId int            `json:"carriageId"`
	Order      int            `json:"order"`
	Add        map[string]any `json:"add"` // 属性
	BuyCost []map[string]interface{} `json:"buyCost"` // 解锁消耗
}

func (this *TrainItemLevel[K]) GetName() string { return ConstTrainItemLevel }

func (this *TrainItemLevel[K]) GetUnique() K { return this.Id }

func (this *TrainItemLevel[K]) OnInit() {
}
