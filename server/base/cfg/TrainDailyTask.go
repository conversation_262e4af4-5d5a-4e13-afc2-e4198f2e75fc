package cfg

const ConstTrainDailyTask = "TrainDailyTask"

type TrainDailyTask[K int] struct {
	Id        K                  `json:"id"`
	NeedPoint int                `json:"needPoint"`
	TaskCnt   int                `json:"taskCnt"`
	AddPeople int                `json:"addPeople"`
	Level     []*ConfigCondition `json:"level"`
}

func (this *TrainDailyTask[K]) GetName() string { return ConstTrainDailyTask }

func (this *TrainDailyTask[K]) GetUnique() K { return this.Id }

func (this *TrainDailyTask[K]) OnInit() {
}
