package cfg

import (
	"github.com/samber/lo"
)

const ConstItem = "Item"

type Item[K int] struct {
	Id       K                  `json:"id"`
	Type     int                `json:"type"`     // 类型
	IsShow   int                `json:"isShow"`   // 是否显示在背包
	IsUse    int                `json:"isUse"`    // 是否能使用
	Price    []*ConfigCondition `json:"price"`    // 商店价格配置
	IsUnique int                `json:"isUnique"` // 是否唯一
}

func (this *Item[K]) GetName() string { return ConstItem }

func (this *Item[K]) GetUnique() K { return this.Id }

func (this *Item[K]) OnInit() {

}

func (this *Item[K]) IsMaterial() bool {
	return this.Type == 1 || this.Type == 2 || this.Type == 3
}

func (this *Item[K]) GetPrice(priceType int) int {
	price, _ := lo.Find(this.Price, func(t *ConfigCondition) bool { return t.Type == priceType })
	return price.Num
}
