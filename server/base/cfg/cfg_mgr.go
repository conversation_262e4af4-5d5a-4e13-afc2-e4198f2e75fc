package cfg

import (
	"encoding/json"
	"fmt"
	"reflect"
	ut "train/utils"
)

var configs = make(map[string]any, 0)

// ConfigLoad load配置文件
func ConfigLoad() {
	LoadConfig()
	for _, config := range configs {
		valueOf := reflect.ValueOf(config)
		initFunc := valueOf.MethodByName("OnInit")
		initFunc.Call(nil)
	}
	Init()
}

// 请不要手动调用该方法
func localNewContainer[K Unique, V Config[K]](name string) *ConfigContainer[K, V] {
	temp := &ConfigContainer[K, V]{
		name: name,
		data: make([]V, 0),
	}
	temp.initConfigData()
	configs[temp.name] = temp
	return temp
}

type ConfigContainer[K Unique, V Config[K]] struct {
	name         string  // 配置名称
	data         []V     // 大部分配置表都是数组配置
	obj          V       // 有些配置表是对象配置
	init         bool    // 是否已经初始化
	mapData      map[K]V // 包装数据 用于快速使用unique获取
	isTypeArray  bool
	isTypeObject bool
}

// GetContainerName 配置容器获取名称
func (c *ConfigContainer[K, V]) GetContainerName() string { return c.name }

// GetBeanById 使用id获取配置实体类, 实体类必须实现structs.Config接口,和GetBeanByUnique并无明显区别,只要有err就是配置没找到
func (c *ConfigContainer[K, V]) GetBeanById(unique K) (cfg V, exists bool) {
	return c.GetBeanByUnique(unique)
}

func (c *ConfigContainer[K, V]) GetBean(unique K) V {
	v, _ := c.GetBeanByUnique(unique)
	return v
}

func (c *ConfigContainer[K, V]) GetOne() V {
	var zero V
	if c.isTypeArray {
		if len(c.data) > 0 {
			return c.data[0]
		}
	}
	if c.isTypeObject {
		return c.obj
	}
	return zero
}

// GetBeanByUnique 使用Unique获取配置实体类, 实体类必须实现structs.Config接口,和GetBeanById并无明显区别,只要有err就是配置没找到
func (c *ConfigContainer[K, V]) GetBeanByUnique(unique K) (obj V, exists bool) {
	if c.isTypeArray {
		obj, exists = c.mapData[unique]
	}
	return
}

func (c *ConfigContainer[K, V]) initConfigData() {
	if c.init {
		return
	}
	bytes, err := ut.LoadConfig(c.GetContainerName())
	if err != nil {
		panic(fmt.Sprintf("加载配置文件[ %s ]出错:%s", c.GetContainerName(), err.Error()))
	}
	if len(bytes) > 0 {
		if bytes[0] == 91 {
			err = json.Unmarshal(bytes, &c.data)
			c.isTypeArray = true
			if err != nil {
				panic(fmt.Sprintf("配置文件[ %s ]初始化出错:%s", c.GetContainerName(), err.Error()))
			}
			c.mapData = make(map[K]V)
			for _, v := range c.data {
				c.mapData[v.GetUnique()] = v
			}
		}
		if bytes[0] == 123 {
			err = json.Unmarshal(bytes, &c.obj)
			c.isTypeObject = true
			if err != nil {
				panic(fmt.Sprintf("配置文件[ %s ]初始化出错:%s", c.GetContainerName(), err.Error()))
			}
		}
		c.init = true
	}
}

func (c *ConfigContainer[K, V]) OnInit() {
	if c.init {
		if c.isTypeArray {
			for _, v := range c.mapData {
				v.OnInit()
			}
		}
		if c.isTypeObject {
			c.obj.OnInit()
		}
	}
}

// GetData 获取配置表数据，前提是配置表是数组，可以使用IsTypeArray 判断
func (c *ConfigContainer[K, V]) GetData() []V {
	if !c.isTypeArray {
		panic("错误的调用方式，配置container不是一个数组")
	}
	return c.data
}

// GetObj 获取配置表数据，前提是配置表是对象，可以使用IsTypeObject 判断
func (c *ConfigContainer[K, V]) GetObj() (obj V) {
	if !c.isTypeObject {
		panic("错误的调用方式，配置container不是一个对象")
	}
	obj = c.obj
	return
}

// // GetConfigs 获取所有的配置容器
//
//	func GetConfigs() cmap.ConcurrentMap[string, interface{}] {
//		return configs
//	}
//
// // GetConfigByName 使用名称获取
func GetConfigByName(key string) interface{} {
	get := configs[key]
	return get
}
