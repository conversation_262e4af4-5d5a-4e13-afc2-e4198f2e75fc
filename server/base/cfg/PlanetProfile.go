package cfg

const ConstPlanetProfile = "PlanetProfile"

type PlanetProfile[K int] struct {
	Id       K                  `json:"id"`
	Type     int                `json:"type"`
	PlanetId int                `json:"planetId"`
	Target   []*ConfigCondition `json:"target"`
	Weight   int                `json:"weight"`
	Area     int                `json:"area"`
}

func (this *PlanetProfile[K]) GetName() string { return ConstPlanetProfile }

func (this *PlanetProfile[K]) GetUnique() K { return this.Id }

func (this *PlanetProfile[K]) OnInit() {
}

func (this *PlanetProfile[K]) GetWeight() int {
	return this.Weight
}
