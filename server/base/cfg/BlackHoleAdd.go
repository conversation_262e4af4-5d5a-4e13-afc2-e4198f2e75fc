package cfg

const ConstBlackHoleAdd = "BlackHoleAdd"

type BlackHoleAdd[K string] struct {
	ID   K                      `json:"id"`
	Type int                    `json:"type"`
	Add  map[string]interface{} `json:"add"`
}

type BlackHoleBuff struct {
	Weight int `json:"weight"`
	Type   int `json:"type"`
	Count  int `json:"count"`
	Attack int `json:"attack"`
	Hp     int `json:"hp"`
}

func (this *BlackHoleAdd[K]) GetName() string { return ConstBlackHoleAdd }

func (this *BlackHoleAdd[K]) GetUnique() K { return this.ID }

func (this *BlackHoleAdd[K]) OnInit() {
}
