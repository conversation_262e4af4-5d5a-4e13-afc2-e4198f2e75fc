package cfg

import (
	"train/base/data/condition"
	"train/common/pb"

	"github.com/samber/lo"
)

const ConstOreMap = "OreMap"

type OreMap[K int] struct {
	Id    K               `json:"id"`
	Level int             `json:"level"`
	Page  int             `json:"page"`
	Row   int             `json:"row"`
	Ceil  []*OreMapConfig `json:"ceil"`
}

func (this *OreMap[K]) GetName() string { return ConstOreMap }
func (this *OreMap[K]) GetUnique() K    { return this.Id }

func (this *OreMap[K]) OnInit() {
	if this.Ceil != nil {
		lo.ForEach(this.Ceil, func(item *OreMapConfig, index int) {
			if item.Ore != nil {
				item.Ore.Type = condition.ORE_ITEM
			}
			if item.Type == int(pb.OreCeilType_BlockItemDrill) {
				item.DrillDirection = pb.OreBlockItemDrillDirection_Right
			}
		})
	}
}

type OreMapConfig struct {
	Type           int                           `json:"type"`
	Ore            *ConfigCondition              `json:"ore"`
	DrillDirection pb.OreBlockItemDrillDirection `json:"drillDirection"` //暂时没配置  默认向右
	Reward         *ConfigCondition              `json:"reward"`
}
