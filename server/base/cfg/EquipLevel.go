package cfg

const ConstEquipLevel = "EquipLevel"

type equipEffectData struct {
	ID    int `json:"id"`
	Min   int `json:"min"`
	Max   int `json:"max"`
	Store int `json:"store"`
}

type effectRange struct {
	Min int `json:"min"`
	Max int `json:"max"`
}

type EquipLevel[K int] struct {
	ID      K                  `json:"id"`
	Quality int                `json:"quality"`
	Effect  []*equipEffectData `json:"effect"`
	Seq     struct {
		Times []int `json:"times"`
	} `json:"seq"`
	effectRangeData map[int][]*effectRange
	Money           int `json:"money"`
}

func (this *EquipLevel[K]) GetName() string { return ConstEquipLevel }

func (this *EquipLevel[K]) GetUnique() K { return this.ID }

func (this *EquipLevel[K]) OnInit() {
}
