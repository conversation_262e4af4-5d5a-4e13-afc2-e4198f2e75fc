package cfg

const ConstDynamicStep = "DynamicStep"

type DynamicStep[K int] struct {
	Id         K     `json:"id"`
	Trigger    int   `json:"trigger"`
	TriggerArg []any `json:"triggerArg"`
	Function   int   `json:"function"`
	FuncArg    []any `json:"funcArg"`
}

func (this *DynamicStep[K]) GetName() string { return ConstDynamicStep }

func (this *DynamicStep[K]) GetUnique() K { return this.Id }

func (this *DynamicStep[K]) OnInit() {
}
