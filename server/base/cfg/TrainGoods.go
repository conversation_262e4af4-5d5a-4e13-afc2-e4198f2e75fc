package cfg

const ConstTrainGoods = "TrainGoods"

type TrainGoods[K string] struct {
	Id      K               `json:"id"`
	TrainId int             `json:"trainId"`
	Type    int             `json:"type"`
	SortId  int             `json:"sortId"`
	Unlock  []*ConfigUnlock `json:"unlock"`
}

func (this *TrainGoods[K]) GetName() string { return ConstTrainGoods }

func (this *TrainGoods[K]) GetUnique() K { return this.Id }

func (this *TrainGoods[K]) OnInit() {
}
