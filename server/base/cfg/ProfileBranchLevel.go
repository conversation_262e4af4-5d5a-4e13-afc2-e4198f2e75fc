package cfg

const ConstProfileBranchLevel = "ProfileBranchLevel"

// 小关配置
type ProfileBranchQuestion struct {
	Profile int `json:"profile"` //贴纸数量
}

type ProfileBranchLevel[K int] struct {
	Id        K                       `json:"id"`
	Profiles  []int                   `json:"profiles"`  //指定贴纸
	Questions []ProfileBranchQuestion `json:"questions"` //小关
	Request   []*ConfigCondition      `json:"request"`   //开启条件
}

func (this *ProfileBranchLevel[K]) GetName() string { return ConstProfileBranchLevel }

func (this *ProfileBranchLevel[K]) GetUnique() K { return this.Id }

func (this *ProfileBranchLevel[K]) OnInit() {
}
