package cfg

import (
	"strings"

	"github.com/spf13/cast"
)

const ConstToolLevel = "ToolLevel"

type ToolLevel[K string] struct {
	Id      K                        `json:"id"`
	ToolId  int                      `json:"toolId"`  // 工具id
	Quality int                      `json:"quality"` // 品质
	Attack  int                      `json:"attack"`  // 攻击力
	Hit     int                      `json:"hit"`     // 命中
	Amp     int                      `json:"amp"`     // 重伤
	Break   int                      `json:"break"`   // 破甲
	BuyCost []map[string]interface{} `json:"buyCost"`
	Tid     int                      `json:"-"`
	Tlv     int                      `json:"-"`
}

func (this *ToolLevel[K]) GetName() string { return ConstToolLevel }

func (this *ToolLevel[K]) GetUnique() K { return this.Id }

func (this *ToolLevel[K]) OnInit() {
	split := strings.Split(string(this.Id), "-")
	this.Tid = cast.ToInt(split[0])
	this.Tlv = cast.ToInt(split[1])
}
