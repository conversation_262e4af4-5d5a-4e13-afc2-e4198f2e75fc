package cfg

const ConstTowerMonster = "TowerMonster"

type TowerMonster[K string] struct {
	Id      K                        `json:"id"`
	Monster []*Monster               `json:"monster"`
	Index   int                      `json:"index"`
	Reward  []map[string]interface{} `json:"reward"`
}

func (this *TowerMonster[K]) GetName() string { return ConstTowerMonster }

func (this *TowerMonster[K]) GetUnique() K { return this.Id }

func (this *TowerMonster[K]) OnInit() {
}

func (this *TowerMonster[K]) IsBoss() bool {
	return this.Index == 5
}
