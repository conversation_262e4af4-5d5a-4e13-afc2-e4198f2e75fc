package cfg

import "fmt"

const ConstAchievement = "Achievement"

type Achievement[K string] struct {
	TypeId        int                                           `json:"typeId"`
	Lv            int                                           `json:"lv"`
	Target        []*TaskTarget                                 `json:"target"`
	Reward        []map[string]interface{}                      `json:"reward"`
	ConditionType string                                        `json:"-"` // 任务判断的类型
	getNext       func(current *Achievement[K]) *Achievement[K] `json:"-"` // 获取下一个成就
}

func (this *Achievement[K]) GetName() string {
	return ConstAchievement
}

func (this *Achievement[K]) GetUnique() K { return K(fmt.Sprintf("%d-%d", this.TypeId, this.Lv)) }

func (this *Achievement[K]) OnInit() {
}

// Next 获取下一个成就任务
func (this *Achievement[K]) Next() *Achievement[K] {
	return this.getNext(this)
}
