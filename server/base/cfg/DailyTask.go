package cfg

const ConstDailyTask = "DailyTask"

type DailyTask[K int] struct {
	Id        K              `json:"id"`
	Weight    int            `json:"weight"`
	Type      int            `json:"type"`
	Contents  []string       `json:"contents"`
	RandomArg []*ChestReward `json:"randomArg"`
}

func (this *DailyTask[K]) GetName() string { return ConstDailyTask }

func (this *DailyTask[K]) GetUnique() K { return this.Id }

func (this *DailyTask[K]) OnInit() {
}
