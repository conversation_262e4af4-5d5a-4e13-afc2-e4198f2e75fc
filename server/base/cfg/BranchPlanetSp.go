package cfg

const ConstBranchPlanetSp = "BranchPlanetSp"

type BranchPlanetSp[K string] struct {
	Id           K              `json:"id"`
	RewardPoints []int          `json:"rewardPoints"`
	Rewards      []*ChestReward `json:"reward"`
}

func (this *BranchPlanetSp[K]) GetName() string { return ConstBranchPlanetSp }

func (this *BranchPlanetSp[K]) GetUnique() K { return this.Id }

func (this *BranchPlanetSp[K]) OnInit() {
}
