package cfg

import (
	"strings"

	"github.com/spf13/cast"
)

const ConstWantedCondition = "WantedCondition"

type WantedCondition[K string] struct {
	ID     K   `json:"id"`
	Value  int `json:"value"`
	Weight int `json:"weight"`
	Type   int `json:"-"`
}

func (this *WantedCondition[K]) GetName() string { return ConstWantedCondition }

func (this *WantedCondition[K]) GetUnique() K { return this.ID }

func (this *WantedCondition[K]) OnInit() {
	split := strings.Split(string(this.ID), "-")
	this.Type = cast.ToInt(split[0])
}
