package cfg

const ConstMonsterCamp = "MonsterCamp"

type MonsterCamp[K string] struct {
	Id        K     `json:"id"`
	Array     int   `json:"array"`
	LevelMax  int   `json:"levelMax"`
	NoMonster []int `json:"noMonster"`
}

func (this *MonsterCamp[K]) GetName() string { return ConstMonsterCamp }

func (this *MonsterCamp[K]) GetUnique() K { return this.Id }

func (this *MonsterCamp[K]) OnInit() {
}

type RollCfg struct {
	A int `json:"a"` // 分组
	B int `json:"b"` // 星级
	C int `json:"c"` // 序号
}
