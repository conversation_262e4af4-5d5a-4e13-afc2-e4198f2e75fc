package cfg

const ConstBlackHoleEquipLevel = "BlackHoleEquipLevel"

type BlackHoleEquipLevel[K int] struct {
	Id      K                        `json:"id"`
	EquipLv int                      `json:"equipLv"`
	BuyCost []map[string]interface{} `json:"buyCost"`
}

func (this *BlackHoleEquipLevel[K]) GetName() string { return ConstBlackHoleEquipLevel }

func (this *BlackHoleEquipLevel[K]) GetUnique() K { return this.Id }

func (this *BlackHoleEquipLevel[K]) OnInit() {
}
