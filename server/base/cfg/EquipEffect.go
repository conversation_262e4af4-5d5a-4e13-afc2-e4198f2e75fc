package cfg

const ConstEquipEffect = "EquipEffect"

type EquipEffect[K int] struct {
	ID         K   `json:"id"`
	Type       int `json:"type"`
	Target     int `json:"target"`
	EquipIndex int `json:"equipIndex"`
	Min        int `json:"-"`
	Max        int `json:"-"`
	Store      int `json:"-"`
}

func (this *EquipEffect[K]) GetName() string { return ConstEquipEffect }

func (this *EquipEffect[K]) GetUnique() K { return this.ID }

func (this *EquipEffect[K]) OnInit() {
}
