package cfg

import (
	"fmt"
	ut "train/utils"
)

const ConstPlanetMap = "PlanetMap"

// MapBase 地图承载结构体
type MapBase struct {
	Type     int      `json:"type"`
	Id       int      `json:"id"`
	Index    int      `json:"index"`
	MineId   int      `json:"mineId"`
	X        int      `json:"x"`
	Y        int      `json:"y"`
	Pos      *ut.Vec2 `json:"-"`
	TypeId   string   `json:"-"` // 同一类型下的唯一id。用于索引别的配置表
	unique   string   `json:"-"` // 全局唯一id
	IsBranch bool     `json:"-"`
}

func (this *MapBase) GetUnique() string {
	return this.unique
}

type PlanetMap[K string] struct {
	Id       K          `json:"id"`
	PlanetId int        `json:"planetId"`
	Node     []*MapBase `json:"node"`
}

func (this *PlanetMap[K]) GetName() string { return ConstPlanetMap }

func (this *PlanetMap[K]) GetUnique() K { return this.Id }

func (this *PlanetMap[K]) OnInit() {
	for _, base := range this.Node {
		base.TypeId = fmt.Sprintf("%s-%d", this.Id, base.Id)
		base.unique = fmt.Sprintf("%s-%d", this.Id, base.Index)
		// base.Pos = ut.NewVec2(base.X, base.Y)
	}
}
