package cfg

const ConstTrainTechLevel = "TrainTechLevel"

type TrainTechLevel[K string] struct {
	Id     K                  `json:"id"`
	Effect *TrainTechEffect   `json:"effect"`
	Cost   []*ConfigCondition `json:"cost"`
}

func (t *TrainTechLevel[K]) GetName() string { return ConstTrainTechLevel }
func (t *TrainTechLevel[K]) GetUnique() K    { return t.Id }
func (t *TrainTechLevel[K]) OnInit() {
}

type TrainTechEffect struct {
	Type   int `json:"type"`
	Num    int `json:"num"`
	Target int `json:"target"`
}
