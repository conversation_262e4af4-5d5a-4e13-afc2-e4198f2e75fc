package cfg

import (
	"strings"

	"github.com/spf13/cast"
)

const ConstTransportMonster = "TransportMonster"

type TransportMonster[K string] struct {
	Id      K                `json:"id"`
	Lv      int              `json:"lv"`
	Monster []*Monster       `json:"monster"`
	Index   int              `json:"-"`
	Re<PERSON>  []map[string]any `json:"reward"`
}

func (this *TransportMonster[K]) GetName() string { return ConstTransportMonster }

func (this *TransportMonster[K]) GetUnique() K { return this.Id }

func (this *TransportMonster[K]) OnInit() {
	split := strings.Split(string(this.Id), "-")
	this.Index = cast.ToInt(split[1])
}
