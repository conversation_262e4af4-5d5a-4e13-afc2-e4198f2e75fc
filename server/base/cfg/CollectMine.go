package cfg

import ut "train/utils"

const ConstCollectMine = "CollectMine"

type CollectMine[K int] struct {
	Id       K                  `json:"id"`
	Type     int                `json:"type"`
	Reward   []*ConfigCondition `json:"reward"`
	Width    float64            `json:"width"`
	Height   float64            `json:"height"`
	PlanetId int                `json:"planetId"`
}

func (this *CollectMine[K]) GetName() string { return ConstCollectMine }

func (this *CollectMine[K]) GetUnique() K { return this.Id }

func (this *CollectMine[K]) OnInit() {
}

// RandomGetRewards
/*
 * @description 获取奖励
 * @return []*ConfigCondition
 */
func (this *CollectMine[K]) RandomGetRewards() []*ConfigCondition {
	rewards := make([]*ConfigCondition, 0)
	index := ut.RandomIndexByWeight(this.Reward, func(item *ConfigCondition) int {
		return item.Weight
	})
	reward := this.Reward[index]
	rewards = append(rewards, reward)
	return rewards
}

func (this *CollectMine[K]) IsInterference() bool {
	return len(this.Reward) <= 0
}
