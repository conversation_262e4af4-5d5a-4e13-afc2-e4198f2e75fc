package cfg

const ConstStore = "Store"

type Store[K string] struct {
	Id      K                `json:"id"`      // 商店-商店位-序号
	StoreId int              `json:"storeId"` // 商店id
	PId     int              `json:"column"`  // 商店位
	GoodsId int              `json:"goodsId"` // 序号
	Goods   *ConfigCondition `json:"goods"`   // 商品信息
	Price   *ConfigCondition `json:"price"`   // 价格货币类型
	OffId   int              `json:"offId"`   // 折扣id
	Stock   int              `json:"stock"`   // 库存
}

func (this *Store[K]) GetName() string { return ConstStore }

func (this *Store[K]) GetUnique() K { return this.Id }

func (this *Store[K]) OnInit() {
}

func (this *Store[K]) GetWeight() int {
	return this.Goods.Weight
}
