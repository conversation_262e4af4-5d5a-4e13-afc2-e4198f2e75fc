package cfg

const ConstEquipSection = "EquipSection"

type EquipSection[K int] struct {
	Id  K              `json:"id"`
	Min int            `json:"min"`
	Max int            `json:"max"`
	Sec []*effectRange `json:"sec"`
}

func (this *EquipSection[K]) GetName() string { return ConstEquipSection }

func (this *EquipSection[K]) GetUnique() K { return this.Id }

func (this *EquipSection[K]) OnInit() {
}
