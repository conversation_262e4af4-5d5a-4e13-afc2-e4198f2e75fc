package cfg

const ConstDailyTaskItem = "DailyTaskItem"

type DailyTaskItem[K int] struct {
	Id     K                  `json:"id"`
	Type   int                `json:"type"`
	Weight int                `json:"weight"`
	Target []*ConfigCondition `json:"target"`
	Num    *struct {
		Min int `json:"min"`
		Max int `json:"max"`
	} `json:"num"`
	CheckFunction string `json:"checkFunction"`
}

func (this *DailyTaskItem[K]) GetName() string { return ConstDailyTaskItem }

func (this *DailyTaskItem[K]) GetUnique() K { return this.Id }

func (this *DailyTaskItem[K]) OnInit() {
}
