package cfg

const ConstTrainDailyTaskLevel = "TrainDailyTaskLevel"

type TrainDailyTaskLevel[K int] struct {
	Id           K                `json:"id"`
	CostTime     int              `json:"costTime"`
	Point        int              `json:"point"`
	Normal       *ConfigCondition `json:"normal"`
	RewardRandom []*ChestReward   `json:"rewardRandom"`
	Reward       []*ChestReward   `json:"reward"`
	Req          []*ChestReward   `json:"req"`
	RandomCnt    RandomCnt        `json:"randomCnt"`
}

func (this *TrainDailyTaskLevel[K]) GetName() string { return ConstTrainDailyTaskLevel }

func (this *TrainDailyTaskLevel[K]) GetUnique() K { return this.Id }

func (this *TrainDailyTaskLevel[K]) OnInit() {
}
