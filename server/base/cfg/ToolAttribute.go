package cfg

const ConstToolAttribute = "ToolAttribute"

type ToolAttribute struct {
	Id        string              `json:"id"`
	Attack    []*ToolAttributeObj `json:"attack"`
	Hit       []*ToolAttributeObj `json:"hit"`
	Amp       []*ToolAttributeObj `json:"amp"`
	Crit      []*ToolAttributeObj `json:"crit"`
	SellPrice []int               `json:"sellPrice"`
}

func (this *ToolAttribute) GetName() string {
	return ConstToolAttribute
}

func (this *ToolAttribute) GetId() int {
	return 0
}

func (this *ToolAttribute) GetUnique() string {
	return this.Id
}

func (this *ToolAttribute) OnInit() {
}

type ToolAttributeObj struct {
	Min int `json:"min"`
	Max int `json:"max"`
}
