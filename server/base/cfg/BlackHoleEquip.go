package cfg

const ConstBlackHoleEquip = "BlackHoleEquip"

type BlackHoleEquip[K int] struct {
	Id      K                    `json:"id"`
	Target  int                  `json:"target"`
	Trigger BattleSkillTrigger   `json:"trigger"`
	Effects []*BattleSkillEffect `json:"effect"`
	Weight  int                  `json:"weight"`
}

func (this *BlackHoleEquip[K]) GetName() string { return ConstBlackHoleEquip }

func (this *BlackHoleEquip[K]) GetUnique() K { return this.Id }

func (this *BlackHoleEquip[K]) OnInit() {
}
