package cfg

import (
	"train/base/data/condition"
	"train/common/pb"
	ut "train/utils"

	"github.com/spf13/cast"
)

const ConstOreLayer = "OreLayer"

type OreLayer[K int] struct {
	Id           K                  `json:"id"`
	Layer        int                `json:"layer"`
	Level        int                `json:"level"`
	Gray         int                `json:"gray"`
	Blue         int                `json:"blue"`
	Purple       int                `json:"purple"`
	Black        int                `json:"black"`
	Mine         int                `json:"mine"`
	Blank        int                `json:"blank"`
	Lucky        int                `json:"lucky"`
	Next         int                `json:"next"`
	Boom         int                `json:"boom"`
	Drill        int                `json:"drill"`
	NextGray     int                `json:"nextGray"`
	NextBlue     int                `json:"nextBlue"`
	Ore          []*Ore             `json:"ore"`
	Reward       []*ConfigCondition `json:"reward"`
	RewardRandom []*ConfigCondition `json:"rewardRandom"`
}

func (this *OreLayer[K]) GetName() string { return ConstOreLayer }

func (this *OreLayer[K]) GetUnique() K { return this.Id }

func (this *OreLayer[K]) OnInit() {
}

func (this *OreLayer[K]) GetNextPageType() pb.OrePageNextType {
	idx := ut.RandomIndexByTotalWeight([]int{this.Next, this.NextGray, this.NextBlue})
	return pb.OrePageNextType(idx)
}

func (this *OreLayer[K]) GetIsLucky() bool {
	return ut.Chance(this.Lucky)
}

func (this *OreLayer[K]) GetWeightBy(predicate func(lucky, blank, purple, blue, gray, black, boom, drill int) []int) []int {
	return predicate(this.Lucky, this.Blank, this.Purple, this.Blue, this.Gray, this.Black, this.Boom, this.Drill)
}

// RandomHasOre
/*
 * @description 随机是否能产生矿石
 * @param rate 矿石计算概率倍数 最小是1
 * @param max 限制矿石最大产生概率(不超过多少，比如0.8，就是不会超过80%的概率)，0-1.0 默认是1,即不超过100%
 * @return bool
 */
func (this *OreLayer[K]) RandomHasOre(rate int, max float64) bool {
	rate = ut.Max(1, rate)
	max = ut.Min(1, max)
	return ut.ChanceLimitMax(this.Mine*rate, max)
}

func (this *OreLayer[K]) RandomOreReward() *Ore {
	idx := ut.RandomIndexByWeight(this.Ore, func(ore *Ore) int {
		return ore.Weight
	})
	ore := this.Ore[idx]

	decimals := ore.Num - float64(ut.Floor(ore.Num))
	min := ut.Floor(ore.Num)
	max := ut.Ceil(ore.Num)
	num := min
	if min == max {
		min--
		max++
		num = ut.Random(min, max)
	} else {
		pro := ut.Round(decimals * 100.0)
		if ut.Chance(pro) {
			num = max
		}
	}
	if num <= 0 {
		idx = 0
	}
	return &Ore{ID: ore.ID, Num: float64(num)}
}

// GetRandomReward
/*
 * @description 战斗随机奖励
 * @return *ConfigCondition
 */
func (this *OreLayer[K]) GetRandomReward() *ConfigCondition {
	if this.RewardRandom != nil {
		idx := ut.RandomIndexByWeight(this.RewardRandom, func(condition *ConfigCondition) int {
			return condition.Weight
		})
		return this.RewardRandom[idx]
	}
	return nil
}

type Ore struct {
	ID     int     `json:"id"`
	Num    float64 `json:"num"`
	Weight int     `json:"weight"`
}

func (o *Ore) MapData() map[string]any {
	return map[string]any{
		"type": condition.ORE_ITEM,
		"id":   o.ID,
		"num":  cast.ToInt(o.Num),
	}
}
