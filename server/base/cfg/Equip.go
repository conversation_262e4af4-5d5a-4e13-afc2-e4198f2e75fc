package cfg

import "github.com/samber/lo"

const ConstEquip = "Equip"

type Equip[K int] struct {
	Id     K                  `json:"id"`
	RoleId int                `json:"roleId"`
	Index  int                `json:"index"`
	Price  []*ConfigCondition `json:"price"` // 商店价格配置
}

func (this *Equip[K]) GetName() string { return ConstEquip }

func (this *Equip[K]) GetUnique() K { return this.Id }

func (this *Equip[K]) OnInit() {
}

func (this *Equip[K]) GetPrice(priceType int) int {
	if this == nil {
		return 1
	}
	if this.Price == nil {
		return 1
	}
	price, _ := lo.Find(this.Price, func(t *ConfigCondition) bool { return t.Type == priceType })
	if price == nil {
		return 1
	}
	return price.Num
}
