package cfg

const ConstPlanetProfileReward = "PlanetProfileReward"

type PlanetProfileReward[K string] struct {
	Id       K              `json:"id"`
	PlanetId int            `json:"planetId"`
	Area     int            `json:"area"`
	Reward   []*ChestReward `json:"reward"`
}

func (this *PlanetProfileReward[K]) GetName() string { return ConstPlanetProfileReward }

func (this *PlanetProfileReward[K]) GetUnique() K { return this.Id }

func (this *PlanetProfileReward[K]) OnInit() {
}
