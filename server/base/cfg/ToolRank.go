package cfg

const ConstToolRank = "ToolRank"

type ToolRank struct {
	ID     string   `json:"id"`
	Tool   []int    `json:"tool"`
	Prefix []string `json:"prefix"`
	Dps    struct {
		Min int `json:"min"`
		<PERSON> int `json:"max"`
	} `json:"dps"`
	Speed struct {
		Min int `json:"min"`
		Max int `json:"max"`
	} `json:"speed"`
	Hit struct {
		Min int `json:"min"`
		Max int `json:"max"`
	} `json:"hit"`
	Amp struct {
		Min int `json:"min"`
		Max int `json:"max"`
	} `json:"amp"`
	SellPrice int `json:"sellPrice"`
}

func (this *ToolRank) GetName() string {
	return ConstToolRank
}

func (this *ToolRank) GetId() int {
	return 0
}

func (this *ToolRank) GetUnique() string {
	return this.ID
}

func (this *ToolRank) OnInit() {
}
