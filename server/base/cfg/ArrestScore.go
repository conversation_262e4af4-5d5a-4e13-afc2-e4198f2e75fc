package cfg

const ConstArrestScore = "ArrestScore"

type ArrestScore[K int] struct {
	Id     K                    `json:"id"`
	Lv     int                  `json:"lv"`
	Task   []*ArrestScoreTask   `json:"task"`
	Battle []*ArrestScoreBattle `json:"battle"`
}

func (this *ArrestScore[K]) GetName() string { return ConstArrestScore }

func (this *ArrestScore[K]) GetUnique() K { return this.Id }

func (this *ArrestScore[K]) OnInit() {
}

type ArrestScoreTask struct {
	Lv    int `json:"lv"`
	Count int `json:"count"`
}

type ArrestScoreBattle struct {
	Score int `json:"score"`
	Count int `json:"count"`
}
