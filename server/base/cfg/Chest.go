package cfg

import (
	ut "train/utils"

	"github.com/samber/lo"
)

const ConstChest = "Chest"

type Chest[K int] struct {
	Id       K                      `json:"id"`
	Type     int                    `json:"itemId"`
	Medal    int                    `json:"medal"`
	Reward   []*ChestReward         `json:"reward"`
	RdReward []*ChestReward         `json:"rdReward"`
	group    map[int][]*ChestReward `json:"-"`
}

func (this *Chest[K]) GetName() string { return ConstChest }

func (this *Chest[K]) GetUnique() K { return this.Id }

func (this *Chest[K]) OnInit() {
	rewardsGroup := map[int][]*ChestReward{}
	for _, reward := range this.RdReward {
		group, ok := rewardsGroup[reward.Group]
		if !ok {
			group = make([]*ChestReward, 0)
			rewardsGroup[reward.Group] = group
		}
		rewardsGroup[reward.Group] = append(rewardsGroup[reward.Group], reward)
	}
	this.group = rewardsGroup
}

// GetRandomReward 获取随机奖励
func (this *Chest[K]) GetRandomReward() []*ChestReward {
	reward := make([]*ChestReward, 0)

	group := this.group
	if len(group) == 0 {
		return reward
	}
	for _, rewards := range group {
		if len(rewards) == 0 {
			continue
		}
		weightArray := make([]ut.DataWeight, 0)
		lo.ForEach(rewards, func(reward *ChestReward, i int) {
			weightArray = append(weightArray, reward)
		})
		idx := ut.RandomIndexByDataWeight(weightArray)
		reward = append(reward, rewards[idx])
	}

	return reward
}

func (this *Chest[K]) IsFudai() bool { return this.Id < 100 && this.Id != 0 }

type ChestReward struct {
	Type   int     `json:"type"`
	Id     int     `json:"id"`
	Num    int     `json:"num"`
	Base   int     `json:"base"` //基数
	Rate   float64 `json:"rate"`
	Weight int     `json:"weight"`
	Group  int     `json:"group"`
	Pro    int     `json:"pro"`
	MaxNum int     `json:"maxNum"`
	MinNum int     `json:"minNum"`
}

func (r *ChestReward) GetWeight() int {
	return r.Weight
}
