package cfg

import (
	"math"

	"github.com/spf13/cast"
)

const ConstGuide = "Guide"

type Guide[K int] struct {
	Id         K      `json:"id"`   //
	Next       int    `json:"next"` // 服务器暂时没用到
	Skip       int    `json:"skip"`
	UnlockFunc string `json:"unlockFunc"`
	Mark       string `json:"mark"` //特殊标记
	GameTime   *struct {
		Hour int `json:"hour"`
		Min  int `json:"min"`
	} `json:"gameTime"`
	GuideId int
}

func (this *Guide[K]) GetName() string { return ConstGuide }

func (this *Guide[K]) GetUnique() K { return this.Id }

func (this *Guide[K]) OnInit() {
	this.GuideId = cast.ToInt(math.Floor(cast.ToFloat64(this.Id) / 100))
}
