package cfg

import "github.com/spf13/cast"

const ConstToolProficiency = "ToolProficiency"

type ToolProficiency struct {
	Id     int `json:"id"`
	ExpBar int `json:"exp"`
	ToolLv struct {
		Min int `json:"min"`
		Max int `json:"max"`
	} `json:"lv"`
}

func (this *ToolProficiency) GetName() string {
	return ConstToolProficiency
}

func (this *ToolProficiency) GetId() int {
	return this.Id
}

func (this *ToolProficiency) GetUnique() string {
	return cast.ToString(this.Id)
}

func (this *ToolProficiency) OnInit() {
}
