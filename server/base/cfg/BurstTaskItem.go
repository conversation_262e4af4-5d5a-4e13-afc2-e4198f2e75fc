package cfg

const ConstBurstTaskItem = "BurstTaskItem"

type BurstTaskItem[K int] struct {
	Id       K              `json:"id"`
	Reward   []*ChestReward `json:"reward"`
	ReqNum   int            `json:"reqNum"`
	CostTime int            `json:"costTime"`
	Train    []int          `json:"train"`
	Req      []*ChestReward `json:"req"`
}

func (this *BurstTaskItem[K]) GetName() string { return ConstBurstTaskItem }

func (this *BurstTaskItem[K]) GetUnique() K { return this.Id }

func (this *BurstTaskItem[K]) OnInit() {
}
