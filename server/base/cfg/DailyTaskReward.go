package cfg

const ConstDailyTaskReward = "DailyTaskReward"

type DailyTaskReward[K int] struct {
	Id           K              `json:"id"`
	Reward       []*ChestReward `json:"reward"`
	RandomCnt    *RandomCnt     `json:"randomCnt"`
	RandomReward []*ChestReward `json:"randomReward"`
}

func (this *DailyTaskReward[K]) GetName() string { return ConstDailyTaskReward }

func (this *DailyTaskReward[K]) GetUnique() K { return this.Id }

func (this *DailyTaskReward[K]) OnInit() {
}
