package cfg

const ConstToolTable = "ToolTable"

type ToolTable[K int] struct {
	ID        K                        `json:"id"`
	MaxToolLv int                      `json:"maxToolLv"` // 打造工具等级上限
	BuyCost   []map[string]interface{} `json:"buyCost"`   // 升级消耗
}

func (this *ToolTable[K]) GetName() string { return ConstToolTable }

func (this *ToolTable[K]) GetUnique() K { return this.ID }

func (this *ToolTable[K]) OnInit() {}
