package cfg

const ConstBranchPlanetMonster = "BranchPlanetMonster"

type BranchPlanetMonster[K string] struct {
	Id      K              `json:"id"`
	Type    string         `json:"type"`
	Rewards []*ChestReward `json:"reward"`
}

func (this *BranchPlanetMonster[K]) GetName() string { return ConstBranchPlanetMonster }

func (this *BranchPlanetMonster[K]) GetUnique() K { return this.Id }

func (this *BranchPlanetMonster[K]) OnInit() {
}
