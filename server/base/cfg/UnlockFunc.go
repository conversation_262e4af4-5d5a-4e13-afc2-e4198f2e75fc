package cfg

const ConstUnlockFunc = "UnlockFunc"

type UnlockFunc[K int] struct {
	Id     K             `json:"id"`
	Type   string        `json:"type"`
	Target []*TaskTarget `json:"target"`
	IsShow int           `json:"isShow"` //测试用
}

func (this *UnlockFunc[K]) GetName() string { return ConstUnlockFunc }

func (this *UnlockFunc[K]) GetUnique() K { return this.Id }

func (this *UnlockFunc[K]) OnInit() {
}
