package cfg

import (
	"train/base/enum/profile_type"
)

const ConstCharacterProfile = "CharacterProfile"

type CharacterProfile[K int] struct {
	Id          K   `json:"id"`
	Type        int `json:"type"`
	CharacterId int `json:"characterId"`
}

func (this *CharacterProfile[K]) GetName() string { return ConstCharacterProfile }

func (this *CharacterProfile[K]) GetUnique() K { return this.Id }

func (this *CharacterProfile[K]) OnInit() {
}

// IsCommon 判断是否是通用贴纸
func (this *CharacterProfile[K]) IsCommon() bool {
	return this.Type == profile_type.AGE || this.Type == profile_type.ZODIAC
}
