package cfg

import "github.com/spf13/cast"

const ConstChapterPlanetMonster = "ChapterPlanetMonster"

type ChapterPlanetMonster[K string] struct {
	Id      K                      `json:"id"`
	Type    string                 `json:"type"`
	Monster []*Monster             `json:"monster"`
	Rewards []*ChestReward         `json:"reward"`
	Box     map[string]interface{} `json:"box"`
}

func (this *ChapterPlanetMonster[K]) GetName() string { return ConstChapterPlanetMonster }

func (this *ChapterPlanetMonster[K]) GetUnique() K { return this.Id }

func (this *ChapterPlanetMonster[K]) OnInit() {
}

func (this *ChapterPlanetMonster[K]) IsBoss() bool { return this.Type == "BOSS" }

func (this *ChapterPlanetMonster[K]) GetBox(key string) int { return cast.ToInt(this.Box[key]) }
