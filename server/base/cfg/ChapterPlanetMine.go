package cfg

const ConstChapterPlanetMine = "ChapterPlanetMine"

type ChapterPlanetMine[K string] struct {
	Id       K              `json:"id"`
	Hp       int            `json:"hp,omitempty"`
	GameType string         `json:"gameType,omitempty"`
	Rewards  []*ChestReward `json:"reward"`
}

func (this *ChapterPlanetMine[K]) GetName() string { return ConstChapterPlanetMine }

func (this *ChapterPlanetMine[K]) GetUnique() K { return this.Id }

func (this *ChapterPlanetMine[K]) OnInit() {

}
