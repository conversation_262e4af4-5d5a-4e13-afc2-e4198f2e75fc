package cfg

const ConstOutputDuration = "OutputDuration"

type OutputDuration[K int] struct {
	Id       K        `json:"id"`
	Type     string   `json:"type"`
	Param    []string `json:"param"`
	Duration int      `json:"duration"`
}

func (o *OutputDuration[K]) GetName() string { return ConstOutputDuration }

func (o *OutputDuration[K]) GetUnique() K { return o.Id }

func (o *OutputDuration[K]) OnInit() {
}
