package cfg

import (
	"strings"

	"github.com/spf13/cast"
)

const ConstTrainDailyTaskCondition = "TrainDailyTaskCondition"

type TrainDailyTaskCondition[K string] struct {
	ID     K   `json:"id"`
	Value  int `json:"value"`
	Weight int `json:"weight"`
	Type   int `json:"-"`
}

func (this *TrainDailyTaskCondition[K]) GetName() string { return ConstTrainDailyTaskCondition }

func (this *TrainDailyTaskCondition[K]) GetUnique() K { return this.ID }

func (this *TrainDailyTaskCondition[K]) OnInit() {
	split := strings.Split(string(this.ID), "-")
	this.Type = cast.ToInt(split[0])
}
