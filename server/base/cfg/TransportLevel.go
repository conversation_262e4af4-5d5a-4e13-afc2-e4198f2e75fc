package cfg

const ConstTransportLevel = "TransportLevel"

type TransportLevel[K int] struct {
	Id       K              `json:"id"`           // 星级
	Load     int            `json:"load"`         // 最大载重
	RdReward []*ChestReward `json:"rewardRandom"` // 随机奖励列表
	Reward   []*ChestReward `json:"reward"`       // 随机奖励列表
	Exp      int            `json:"-"`            //
}

func (this *TransportLevel[K]) GetName() string { return ConstTransportLevel }

func (this *TransportLevel[K]) GetUnique() K { return this.Id }

func (this *TransportLevel[K]) OnInit() {
}
