package cfg

import (
	"strings"

	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"
)

const ConstTrainItem = "TrainItem"

type TrainItem[K string] struct {
	Id         K        `json:"id"`
	PreId      []string `json:"preId"`      // 前置解锁限制
	UnlockType int      `json:"unlockType"` // 解锁方式 0默认拥有，2需要购买解锁
	Order      int      `json:"order"`      // 设施序号
	Skin       int      `json:"skin"`       //皮肤序号
	Show       int      `json:"show"`
	CarriageId int      `json:"carriageId"` //所属车厢
	SortId     int      `json:"sortId"`
}

func (this *TrainItem[K]) GetName() string { return ConstTrainItem }

func (this *TrainItem[K]) GetUnique() K { return this.Id }

func (this *TrainItem[K]) OnInit() {
}

// IsBelongTrain 判断当前设施是不是属于某个车厢的某个方案
func (this *TrainItem[K]) IsBelongTrain(id, themeId int) bool {
	split := strings.Split(string(this.Id), "-")
	if len(split) < 3 {
		log.Error("IsBelongTrain 错误,这个设施的配置格式不正确!")
	}
	return cast.ToInt(split[0]) == id && cast.ToInt(split[1]) == themeId
}

// IsDefaultHas 判断该设施是否默认拥有
func (this *TrainItem[K]) IsDefaultUnlock() bool {
	return this.UnlockType == 0
}
