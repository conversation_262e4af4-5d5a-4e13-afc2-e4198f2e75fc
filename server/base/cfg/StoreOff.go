package cfg

import (
	ut "train/utils"

	"github.com/samber/lo"
)

const ConstStoreOff = "StoreOff"

type StoreOff[K string] struct {
	Id       K                 `json:"id"`       // 商店id-打折分组
	StoreId  int               `json:"storeId"`  // 商店id
	Goods    []int             `json:"goods"`    // 包含的商品位
	OffGoods []*StoreOffConfig `json:"offGoods"` // 折扣数量随机
}

func (this *StoreOff[K]) GetName() string { return ConstStoreOff }

func (this *StoreOff[K]) GetUnique() K { return this.Id }

func (this *StoreOff[K]) OnInit() {
}

func (this *StoreOff[K]) GetRandomOffGoods() (pos []int) {
	data := make([]ut.DataWeight, 0)
	// 随机组内出打折数量
	lo.ForEach(this.OffGoods, func(t *StoreOffConfig, i int) { data = append(data, t) })
	idx := ut.RandomIndexByDataWeight(data)
	offConfig := this.OffGoods[idx]
	// 打折数量
	cnt := offConfig.Num
	// 不重复式随机cnt个打折位
	goods := make([]int, 0)
	goods = append(goods, this.Goods...)
	for i := 0; i < cnt; i++ {
		idx := ut.Random(0, len(goods)-1)
		pos = append(pos, goods[idx])
		goods = append(goods[:idx], goods[idx+1:]...)
	}
	return
}
