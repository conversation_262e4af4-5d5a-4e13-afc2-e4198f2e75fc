package cfg

const ConstPublicityPlay = "PublicityPlay"

type PublicityPlay[K string] struct {
	Id       K                `json:"id"`
	PlanetId int              `json:"planetId"`
	Lv       int              `json:"lv"`
	Reward   *ConfigCondition `json:"reward"`
	Req      int              `json:"req"`
	Rate     int              `json:"rate"`
	Cost     int              `json:"cost"`
	Min      int              `json:"min"`
	Max      int              `json:"max"`
}

func (p *PublicityPlay[K]) GetName() string { return ConstPublicityPlay }
func (p *PublicityPlay[K]) GetUnique() K    { return p.Id }
func (p *PublicityPlay[K]) OnInit() {
}
