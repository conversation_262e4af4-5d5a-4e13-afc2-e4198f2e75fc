package cfg

import (
	"strings"

	"github.com/spf13/cast"
)

const ConstCharacterSkin = "CharacterSkin"

type CharacterSkin[K string] struct {
	Id          K                `json:"id"`
	CharacterId int              `json:"-"`       // 乘客id
	Index       int              `json:"-"`       // 皮肤索引
	BuyCost     []map[string]any `json:"buyCost"` // 解锁消耗
}

func (this *CharacterSkin[K]) GetName() string { return ConstCharacterSkin }

func (this *CharacterSkin[K]) GetUnique() K { return this.Id }

func (this *CharacterSkin[K]) OnInit() {
	split := strings.Split(string(this.Id), "-")
	if len(split) != 2 {
		panic("CharacterSkin id error")
	}
	this.CharacterId = cast.ToInt(split[0])
	this.Index = cast.ToInt(split[1])
}
