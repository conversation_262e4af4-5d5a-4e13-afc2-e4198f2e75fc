package cfg

const ConstSpaceStone = "SpaceStone"

type SpaceStone[K int] struct {
	Id      K                        `json:"id"`
	MarkCnt int                      `json:"markCnt"`
	Energy  int                      `json:"energy"`
	BuyCost []map[string]interface{} `json:"buyCost"`
}

func (this *SpaceStone[K]) GetName() string { return ConstSpaceStone }

func (this *SpaceStone[K]) GetUnique() K { return this.Id }

func (this *SpaceStone[K]) OnInit() {
}
