package cfg

import (
	"fmt"
	"train/base/enum/black_hole_layer_type"

	"github.com/samber/lo"
)

const ConstBlackHoleLayer = "BlackHoleLayer"

type BlackHoleLayer[K string] struct {
	ID     K              `json:"id"`
	A      float64        `json:"a"`
	B      int            `json:"b"`
	C      int            `json:"c"`
	Fog    int            `json:"fog"`
	Level  int            `json:"level"`
	Layer  int            `json:"layer"`
	Count  int            `json:"count"`
	Type   string         `json:"type"`
	Reward []*ChestReward `json:"reward"` // 必得奖励
}

func (this *BlackHoleLayer[K]) GetName() string { return ConstBlackHoleLayer }

func (this *BlackHoleLayer[K]) GetUnique() K { return this.ID }

func (this *BlackHoleLayer[K]) OnInit() {
}

// boss节点
func (this *BlackHoleLayer[K]) IsBoss() bool {
	nextLayer := this.Layer + 1
	data, _ := BlackHoleLayerContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", this.Level, nextLayer))
	return data != nil && data.Type == black_hole_layer_type.END
}

// 最终层的boss节点
func (this *BlackHoleLayer[K]) IsEndBoss() bool {
	ary := BlackHoleLayerContainer.GetData()
	max := lo.MaxBy(ary, func(a *BlackHoleLayer[string], b *BlackHoleLayer[string]) bool {
		if a.Type == black_hole_layer_type.BATTLE && b.Type == black_hole_layer_type.BATTLE {
			return a.Level > b.Level || (a.Level == b.Level && a.Layer > b.Layer)
		}
		if a.Type == black_hole_layer_type.BATTLE {
			return true
		}
		return false
	})
	return max.ID == string(this.ID)
}
