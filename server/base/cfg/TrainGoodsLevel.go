package cfg

const ConstTrainGoodsLevel = "TrainGoodsLevel"

type TrainGoodsLevel[K string] struct {
	Id        K               `json:"id"`
	GoodsId   string          `json:"goodsId"`
	BuyCost   []*ConfigUnlock `json:"buyCost"` // 升级消耗
	AttrValue int             `json:"attrValue"`
	Level     int             `json:"lv"`
}

func (this *TrainGoodsLevel[K]) GetName() string { return ConstTrainGoodsLevel }

func (this *TrainGoodsLevel[K]) GetUnique() K { return this.Id }

func (this *TrainGoodsLevel[K]) OnInit() {
}

func (this *TrainGoodsLevel[K]) GetBuyCostMap() []map[string]any {
	// 将BuyCost转换为map
	var buyCostMap []map[string]any
	for _, v := range this.BuyCost {
		buyCostMap = append(buyCostMap, map[string]any{
			"type": v.Type,
			"id":   v.Id,
			"num":  v.Num,
		})
	}
	return buyCostMap
}

type ConfigUnlock struct {
	Type int `json:"type"`
	Id   int `json:"id"`
	Num  int `json:"num"`
	Hide int `json:"hide"`
}
