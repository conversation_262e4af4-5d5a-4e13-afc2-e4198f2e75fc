package cfg

import (
	"fmt"
)

const ConstPlanet = "Planet"

type PlanetExplore struct {
	Time      int            `json:"time"`
	Rewards   []*ChestReward `json:"reward"`
	RdRewards []*ChestReward `json:"rewardRandom"`
}

type Planet[K int] struct {
	Id      K                          `json:"id"`
	Lock    map[string]any             `json:"lock"`
	maps    map[int]*PlanetMap[string] `json:"-"`
	Explore *PlanetExplore             `json:"explore"`
}

func (this *Planet[K]) GetName() string { return ConstPlanet }

func (this *Planet[K]) GetUnique() K { return this.Id }

func (this *Planet[K]) OnInit() {
	this.maps = make(map[int]*PlanetMap[string])
}

// GetDefaultMapId 获取当前星球默认mapId
func (this *Planet[K]) GetDefaultMapId() int { return 1 }

// GetMapData 获取地图数据
func (this *Planet[K]) GetMapData(mapId int) []*MapBase {
	mapData, ok := this.maps[mapId]
	if !ok {
		mapData, _ = PlanetMapContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", this.Id, mapId))
		if mapData == nil {
			return []*MapBase{}
		}
		this.maps[mapId] = mapData
	}
	return mapData.Node
}

// GetDefaultNode 获取默认节点  就是配置表中的第一个节点
func (this *Planet[K]) GetDefaultNode(mapId int) (base *MapBase) {
	data := this.GetMapData(mapId)
	if len(data) > 0 {
		return data[0]
	}
	return
}
