package cfg

const ConstBranchPlanetMine = "BranchPlanetMine"

type BranchPlanetMine[K string] struct {
	Id       K              `json:"id"`
	Hp       int            `json:"hp,omitempty"`
	GameType string         `json:"gameType,omitempty"`
	Rewards  []*ChestReward `json:"reward"`
}

func (this *BranchPlanetMine[K]) GetName() string { return ConstBranchPlanetMine }

func (this *BranchPlanetMine[K]) GetUnique() K { return this.Id }

func (this *BranchPlanetMine[K]) OnInit() {
}
