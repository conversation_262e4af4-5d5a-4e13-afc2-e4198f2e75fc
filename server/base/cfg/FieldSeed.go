package cfg

import (
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

const ConstFieldSeed = "FieldSeed"

type FieldSeed[K int] struct {
	Id          K                                     `json:"id"`
	Type        int                                   `json:"type"`
	Time        int                                   `json:"time"`
	Price       *ConfigCondition                      `json:"price"`
	Reward      []*ConfigCondition                    `json:"reward"`
	ref         map[string][]*TrainGoodsLevel[string] `json:"-"`
	itemId      int                                   `json:"-"` // 种子的id和食材的id是不一样的，这个itemid就是食材的id，对应物品表的id
	itemNeedCnt int                                   `json:"-"` // 食材在所有菜谱中的总需求数量
}

func (this *FieldSeed[K]) GetName() string { return ConstFieldSeed }

func (this *FieldSeed[K]) GetUnique() K { return this.Id }

func (this *FieldSeed[K]) OnInit() {
}

// GetRefPlan
/*
 * @description 获取关联的菜谱
 */
func (this *FieldSeed[K]) GetRefPlan() map[string][]*TrainGoodsLevel[string] {
	return this.ref
}

// GetRewardCnt
/*
 * @description 获取种子能产出对应食材的数量
 */
func (this *FieldSeed[K]) GetRewardCnt() int {
	configCondition, _ := lo.Find(this.Reward, func(condition *ConfigCondition) bool {
		return cast.ToInt(condition.Id) == this.itemId
	})
	return configCondition.Num
}
func (this *FieldSeed[K]) GetItemNeedCnt() int { return this.itemNeedCnt }
func (this *FieldSeed[K]) GetItemId() int      { return this.itemId }
