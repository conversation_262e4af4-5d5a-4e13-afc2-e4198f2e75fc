package cfg

import (
	"github.com/samber/lo"
)

const ConstCharacter = "Character"

type Character[K int] struct {
	Id        K     `json:"id"`
	Type      int   `json:"type"`
	Quality   int   `json:"quality"`   // 品质
	LifeSkill []int `json:"lifeSkill"` // 生活技能
	Profile   *struct {
		Race string `json:"race"`
		Age  int    `json:"age"`
		Sign string `json:"sign"`
	} `json:"profile"`
	WorkAt        []int `json:"workAt"` //可以工作的车厢id
	BattleType    int   `json:"battleType"`
	AnimalType    int   `json:"animalType"`
	QualityWeight int   `json:"weight"` //对应品质下的抽卡权重
	Weight        int   `json:"-"`      //全局抽卡权重
	SortId        int   `json:"sortId"`
	HP            int   `json:"hp"`
	Attack        int   `json:"attack"`
	BattleSkill   int   `json:"battleSkill"`
}

func (this *Character[K]) GetName() string { return ConstCharacter }

func (this *Character[K]) GetUnique() K { return this.Id }

func (this *Character[K]) OnInit() {
}

// GetInitStarLv
/*
 * @description 初始星级
 * @return int
 */
func (this *Character[K]) GetInitStarLv() int {
	starUps := lo.Filter(StarUpContainer.GetData(), func(up *StarUp[int], i int) bool {
		return up.Quality == this.Quality
	})
	return starUps[0].Id
}

func (this *Character[K]) GetRace() string { return this.Profile.Race }

// RandomChip 投影 固定1
func (this *Character[K]) RandomChip() int { return 1 }

func (this *Character[K]) QualityIsMatch(qua int) bool { return this.Quality == qua }

func (this *Character[K]) GetAge() int { return this.Profile.Age }

func (this *Character[K]) GetSign() string { return this.Profile.Sign }
