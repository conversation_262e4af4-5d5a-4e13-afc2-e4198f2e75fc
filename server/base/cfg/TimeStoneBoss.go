package cfg

const ConstTimeStoneBoss = "TimeStoneBoss"

type TimeStoneBoss[K int] struct {
	Id      K                `json:"id"`
	Type    string           `json:"type"`
	Monster []*Monster       `json:"monster"`
	Reward  []map[string]any `json:"reward"`
}

func (this *TimeStoneBoss[K]) GetName() string { return ConstTimeStoneBoss }

func (this *TimeStoneBoss[K]) GetUnique() K { return this.Id }

func (this *TimeStoneBoss[K]) OnInit() {
}
