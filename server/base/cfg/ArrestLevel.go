package cfg

const ConstArrestLevel = "ArrestLevel"

type ArrestLevel[K int] struct {
	Id        K              `json:"id"`
	Weight    int            `json:"weight"`
	MonsterLv int            `json:"monsterLv"`
	Reward    []*ChestReward `json:"reward"`       // 奖励列表
	RdReward  []*ChestReward `json:"rewardRandom"` // 随机奖励列表
}

func (this *ArrestLevel[K]) GetName() string { return ConstArrestLevel }

func (this *ArrestLevel[K]) GetUnique() K { return this.Id }

func (this *ArrestLevel[K]) OnInit() {
}
