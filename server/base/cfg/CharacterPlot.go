package cfg

const ConstCharacterPlot = "CharacterPlot"

type CharacterPlot struct {
	Key     string                   `json:"key"`
	Order   int                      `json:"order"`
	Reward  []map[string]interface{} `json:"reward"`
	BuyCost []map[string]interface{} `json:"buyCost"`
}

func (this *CharacterPlot) GetName() string {
	return ConstCharacterPlot
}

func (this *CharacterPlot) GetId() int {
	return 0
}

func (this *CharacterPlot) GetUnique() string {
	return this.Key
}

func (this *CharacterPlot) OnInit() {
}
