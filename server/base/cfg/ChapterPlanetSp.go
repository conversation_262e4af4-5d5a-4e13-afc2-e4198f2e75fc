package cfg

const ConstChapterPlanetSp = "ChapterPlanetSp"

type ChapterPlanetSp[K string] struct {
	Id           K              `json:"id"`
	Rewards      []*ChestReward `json:"reward"`
	DiyRewards   []*ChestReward `json:"diyReward"`
	Time         int            `json:"time"`
	MaxRewardNum int            `json:"maxRewardNum"`
	Count        int            `json:"count"`
}

func (this *ChapterPlanetSp[K]) GetName() string { return ConstChapterPlanetSp }

func (this *ChapterPlanetSp[K]) GetUnique() K { return this.Id }

func (this *ChapterPlanetSp[K]) OnInit() {
}
