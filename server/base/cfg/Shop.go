package cfg

const ConstShop = "Shop"

type Shop[K string] struct {
	ID        K                        `json:"id"`
	Type      int                      `json:"type"`
	ProductId string                   `json:"productId"`
	Product   []map[string]interface{} `json:"product"`
	Gifts     []map[string]interface{} `json:"gifts"`
}

func (this *Shop[K]) GetName() string { return ConstShop }

func (this *Shop[K]) GetUnique() K { return this.ID }

func (this *Shop[K]) OnInit() {
}
