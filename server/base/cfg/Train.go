package cfg

const ConstTrain = "Train"

type Train[K int] struct {
	Id       K                        `json:"id"`
	SortId   int                      `json:"sortId"`   // 排序
	Type     int                      `json:"type"`     // 车厢类型
	RoleCnt  int                      `json:"roleCnt"`  // 寝室入住人口上限
	WorkCnt  int                      `json:"workCnt"`  //工作岗位
	BuyCost  []map[string]interface{} `json:"buyCost"`  // 购买消耗
	IsOpen   int                      `json:"isOpen"`   // 车厢是否开放
	CostTime int                      `json:"costTime"` //建造花费时间
	Load     int                      `json:"load"`
}

func (this *Train[K]) GetName() string { return ConstTrain }

func (this *Train[K]) GetUnique() K { return this.Id }

func (this *Train[K]) OnInit() {

}
