package cfg

import (
	"sort"

	"github.com/samber/lo"
)

const ConstFieldLevel = "FieldLevel"

type FieldLevel[K int] struct {
	Id          K                         `json:"id"`
	Vegetable   int                       `json:"vegetable"`
	Fruit       int                       `json:"fruit"`
	SeedId      []int                     `json:"seedId"`
	Target      []*FieldLevelTargetConfig `json:"target"`
	Penultimate int                       `json:"penultimate"` // 倒数第二格子生成肥料or种子的概率
}

func (this *FieldLevel[K]) GetName() string { return ConstFieldLevel }

func (this *FieldLevel[K]) GetUnique() K { return this.Id }

func (this *FieldLevel[K]) OnInit() {
	arr := FieldLevelContainer.GetData()
	filter := lo.Filter(arr, func(level *FieldLevel[int], i int) bool {
		return level.Id < int(this.Id)
	})
	if len(filter) > 0 {
		if this.SeedId == nil {
			this.SeedId = make([]int, 0)
		}
		lo.ForEach(filter, func(level *FieldLevel[int], i int) {
			this.SeedId = append(this.SeedId, level.SeedId...)
		})
		unDuplicatesMap := make(map[int]bool)
		lo.ForEach(this.SeedId, func(v int, i int) {
			unDuplicatesMap[v] = true
		})
		tmpSeedId := make([]int, 0)
		for k, _ := range unDuplicatesMap {
			tmpSeedId = append(tmpSeedId, k)
		}
		this.SeedId = tmpSeedId
	}

	sort.Slice(this.SeedId, func(i, j int) bool {
		return this.SeedId[i] < this.SeedId[j]
	})
}

type FieldLevelTargetConfig struct {
	Type string `json:"type"`
	Id   int    `json:"id"`
	Num  int    `json:"num"`
}
