package cfg

const ConstEquipMake = "EquipMake"

type EquipMake[K string] struct {
	Id          K                  `json:"id"`
	Cost        []*ConfigCondition `json:"cost"`  // 打造消耗
	Level       int                `json:"level"` // 产出等级
	Times       int                `json:"times"`
	Proficiency int                `json:"proficiency"`
}

func (this *EquipMake[K]) GetName() string { return ConstEquipMake }

func (this *EquipMake[K]) GetUnique() K { return this.Id }

func (this *EquipMake[K]) OnInit() {
}
