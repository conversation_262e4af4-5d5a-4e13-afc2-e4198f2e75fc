package cfg

const ConstBattleSkill = "BattleSkill"

type BattleSkill[K int] struct {
	ID      K                    `json:"id"`
	Trigger *BattleSkillTrigger  `json:"trigger"`
	Object  []*BattleSkillObject `json:"object"`
	Effect  []*BattleSkillEffect `json:"effect"`
}

type BattleSkillTrigger struct {
	Type   string `json:"type"`
	Object string `json:"object"`
	Count  int    `json:"count"`
}

type BattleSkillObject struct {
	Type  string `json:"type"`
	Camp  string `json:"camp"`
	Count int    `json:"count"`
}
type BattleSkillEffect struct {
	Type             string  `json:"type"`
	ValueType        string  `json:"valueType"`
	PerObjectType    string  `json:"perObjectType,omitempty"`
	PerAttributeType string  `json:"perAttributeType,omitempty"`
	Value            float64 `json:"value"`
	Repeats          int     `json:"repeats"`
	Times            int     `json:"times"`
	Prob             int     `json:"prob"`
}

func (this *BattleSkill[K]) GetName() string { return ConstBattleSkill }

func (this *BattleSkill[K]) GetUnique() K { return this.ID }

func (this *BattleSkill[K]) OnInit() {
}
