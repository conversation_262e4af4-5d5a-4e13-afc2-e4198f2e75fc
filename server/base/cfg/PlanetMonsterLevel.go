package cfg

import (
	"strings"

	"github.com/spf13/cast"
)

const ConstPlanetMonsterLevel = "PlanetMonsterLevel"

type PlanetMonsterLevel[K string] struct {
	Id        string `json:"id"`
	Attack    int    `json:"attack"`
	Hp        int    `json:"hp"`
	MonsterId int    `json:"-"`
	Level     int    `json:"-"`
}

func (this *PlanetMonsterLevel[K]) GetName() string {
	return ConstPlanetMonsterLevel
}

func (this *PlanetMonsterLevel[K]) GetUnique() K {
	return K(this.Id)
}

func (this *PlanetMonsterLevel[K]) OnInit() {
	split := strings.Split(this.Id, "-")
	this.MonsterId = cast.ToInt(split[0])
	this.Level = cast.ToInt(split[1])
}
