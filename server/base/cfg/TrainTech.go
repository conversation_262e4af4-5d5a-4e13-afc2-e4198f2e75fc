package cfg

import "fmt"

const ConstTrainTech = "TrainTech"

type TrainTech[K int] struct {
	Id    K      `json:"id"`
	Pre   []int  `json:"pre"`
	Row   int    `json:"row"`
	Ceil  int    `json:"ceil"`
	TmpId string `json:"-"`
}

func (t *TrainTech[K]) GetName() string { return ConstTrainTech }
func (t *TrainTech[K]) GetUnique() K    { return t.Id }
func (t *TrainTech[K]) OnInit() {
	t.TmpId = fmt.Sprintf("%d-%d", t.Row, t.Ceil)
}
