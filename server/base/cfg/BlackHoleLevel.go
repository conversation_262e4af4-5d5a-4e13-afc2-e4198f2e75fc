package cfg

const ConstBlackHoleLevel = "BlackHoleLevel"

type BlackHoleLevel[K int] struct {
	Id    K                      `json:"id"`
	Equip []*BlackHoleLevelEquip `json:"equip"`
}

func (this *BlackHoleLevel[K]) GetName() string { return ConstBlackHoleLevel }

func (this *BlackHoleLevel[K]) GetUnique() K { return this.Id }

func (this *BlackHoleLevel[K]) OnInit() {
}

type BlackHoleLevelEquip struct {
	Lv     int `json:"lv"`
	Weight int `json:"weight"`
}
