package cfg

const ConstGrowSkill = "GrowSkill"

type CharacterLifeSkillObject struct {
	Type string        `json:"type"`
	Id   []interface{} `json:"id"`
}

type CharacterLifeSkillEffect struct {
	Type      string  `json:"type"`
	Value     float64 `json:"value"`
	ValueType string  `json:"valueType"`
}

type GrowSkill[K string] struct {
	Id        K                           `json:"id"`
	Object    []*CharacterLifeSkillObject `json:"object"`
	Effect    []*CharacterLifeSkillEffect `json:"effect"`
	Effective []*CharacterLifeSkillObject `json:"effective"`
}

func (this *GrowSkill[K]) GetName() string { return ConstGrowSkill }

func (this *GrowSkill[K]) GetUnique() K { return this.Id }

func (this *GrowSkill[K]) OnInit() {
}
