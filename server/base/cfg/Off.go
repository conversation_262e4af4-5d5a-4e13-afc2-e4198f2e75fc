package cfg

import (
	ut "train/utils"

	"github.com/samber/lo"
)

const ConstOff = "Off"

type Off[K int] struct {
	Id  K            `json:"id"`
	Off []*OffConfig `json:"off"`
}

func (this *Off[K]) GetName() string { return ConstOff }

func (this *Off[K]) GetUnique() K { return this.Id }

func (this *Off[K]) OnInit() {
}

// RandomOff
/*
 * @description 随机折扣
 * @return float64
 */
func (this *Off[K]) RandomOff() float64 {
	data := make([]ut.DataWeight, 0)
	lo.ForEach(this.Off, func(t *OffConfig, i int) { data = append(data, t) })
	idx := ut.RandomIndexByDataWeight(data)
	return this.Off[idx].Num
}
