package cfg

const ConstWantedLevel = "WantedLevel"

type WantedLevel[K int] struct {
	ID        K              `json:"id"`
	Point     int            `json:"point"`
	NeedPoint int            `json:"needPoint"`
	Weight    int            `json:"weight"`
	NumMax    int            `json:"numMax"`
	CostTime  int            `json:"costTime"`
	Normal    Normal         `json:"normal"`
	AddPeople int            `json:"addPeople"`
	Rewards   []*ChestReward `json:"reward"`
	RdRewards []*ChestReward `json:"rewardRandom"`
	Req       []*ChestReward `json:"req"`
	TaskCnt   int            `json:"taskCnt"`
}
type Normal struct {
	Num int `json:"num"`
}

func (this *WantedLevel[K]) GetName() string { return ConstWantedLevel }

func (this *WantedLevel[K]) GetUnique() K { return this.ID }

func (this *WantedLevel[K]) OnInit() {
}
