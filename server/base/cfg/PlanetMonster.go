package cfg

const ConstPlanetMonster = "PlanetMonster"

type PlanetMonster[K int] struct {
	ID          K      `json:"id"`
	Quality     int    `json:"quality"`
	IsSp        int    `json:"isSp"`
	Name        string `json:"name"`
	BattleSkill int    `json:"battleSkill"`
	IsBoss      int    `json:"isBoss"`
	HP          int    `json:"hp"`
	Attack      int    `json:"attack"`
}

func (this *PlanetMonster[K]) GetName() string { return ConstPlanetMonster }

func (this *PlanetMonster[K]) GetUnique() K { return this.ID }

func (this *PlanetMonster[K]) OnInit() {
}

// 对标乘客id
func (this *PlanetMonster[K]) GetPassengerId() int {
	if int(this.ID) == this.BattleSkill {
		return 0
	}
	return this.BattleSkill
}
