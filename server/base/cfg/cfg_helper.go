package cfg

import (
	"encoding/json"
	"math"
	"sort"
	"train/base/data/condition"
	"train/base/enum"
	"train/base/enum/equip_effect_target"
	"train/base/enum/equip_effect_type"
	ut "train/utils"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"

	"github.com/samber/lo"
)

// AchievementGroupMap 成就数据分组
var AchievementGroupMap = make(map[int][]*Achievement[string], 0)

var RoleBaseAttrMap = make(map[int]map[int][]map[string]int)

var CollectMapCeilAry = make([]*ut.Rect, 0)

var EquipSellMap = make(map[int]int)
var EquipLvMap = make(map[int]map[string]map[int]int, 0)

func Init() {
	groupAchievements()
	initCharaterWeight()
	initRoleAttrs()
	initPlanetMoveTime()
	initFieldRefPlan()
	initTrainGoodsAttr()
	initEquipEffectRangeData()
	initTransportLevelCfgExp()
	initEquipSellMap()
	initEquipLvMap()
	initPublicityData()
}

// 将成就数据按照typeId分组存放
func groupAchievements() {
	next := func(current *Achievement[string]) *Achievement[string] {
		achievements := AchievementGroupMap[current.TypeId]
		if achievements == nil {
			return nil
		}
		achievement, _ := lo.Find(achievements, func(achievement *Achievement[string]) bool {
			return achievement.Lv == current.Lv+1
		})
		return achievement
	}
	achievementData := AchievementContainer.GetData()
	for _, data := range achievementData {
		key := data.TypeId
		if AchievementGroupMap[key] == nil {
			AchievementGroupMap[key] = make([]*Achievement[string], 0)
		}
		typeBean, _ := AchievementTypeContainer.GetBeanById(data.TypeId)
		data.ConditionType = typeBean.Type
		data.getNext = next
		AchievementGroupMap[key] = append(AchievementGroupMap[key], data)
	}
}

func initRoleAttrs() {
	misc := Misc_CContainer.GetObj()
	lvRatio := misc.LvAttrRatio
	roleDatas := CharacterContainer.GetData()
	LOOP_COUNT := ut.Round(1.0 / lvRatio)

	handle := func(id int, baseHp int, baseAtk int) {
		if RoleBaseAttrMap[baseHp] == nil {
			RoleBaseAttrMap[baseHp] = make(map[int][]map[string]int)
		}
		if len(RoleBaseAttrMap[baseHp][baseAtk]) > 0 {
			return
		}

		hpAry := ut.NumAvgSplit(baseHp, LOOP_COUNT)
		atkAry := ut.NumAvgSplit(baseAtk, LOOP_COUNT)

		lvs := make([]map[string]int, LOOP_COUNT)
		firstAry, secondAry := hpAry, atkAry
		curType := 0
		if baseHp < baseAtk {
			firstAry, secondAry = atkAry, hpAry
			curType = 1
		}
		for i := 0; i < LOOP_COUNT; i++ {
			val1 := lo.Max(firstAry)
			val2 := lo.Min(secondAry)
			firstAry = array.Remove(firstAry, val1)
			secondAry = array.Remove(secondAry, val2)

			hp := val1
			atk := val2
			if curType == 1 {
				atk = val1
				hp = val2
			}
			lvs[i] = map[string]int{enum.RoleHP: hp, enum.RoleAttack: atk}

			firstAry, secondAry = secondAry, firstAry
			curType ^= 1
		}
		RoleBaseAttrMap[baseHp][baseAtk] = lvs
	}
	for _, role := range roleDatas {
		handle(role.Id, role.HP, role.Attack)
	}

	//处理迷宫
	attr := misc.BlackHole.BaseAttr
	handle(enum.BLACK_HOLE_ATTR_ID, attr, attr)
}

func GetMaxLvByStarLv(starLv int) int {
	sum := 0
	bean, _ := StarUpContainer.GetBeanById(starLv)
	if bean != nil {
		return bean.LevelMax
	}
	return sum
}

func GetMaxLv() int {
	datas := StarUpContainer.GetData()
	return datas[len(datas)-1].LevelMax
}

// GetMonsterStarByLevel
/*
 * @description 使用等级获取怪物星级
 * @param level
 * @return star
 */
func GetMonsterStarByLevel(level int) (star int) {
	data := MonsterStarUpContainer.GetData()
	for _, d := range data {
		if level <= d.LevelMax {
			return d.Id
		}
	}
	return data[len(data)-1].Id
}

func GetRoleAttr(id int, level int, starLv int, attr string) int {
	baseHp, baseAtk := GetRoleBaseAttr(id)
	if RoleBaseAttrMap[baseHp] == nil || len(RoleBaseAttrMap[baseHp][baseAtk]) <= 0 {
		log.Warning("getRoleAttr not found", id, baseHp, baseAtk)
		return 0
	}
	lvs := RoleBaseAttrMap[baseHp][baseAtk]
	len := len(lvs)
	count := ut.Floor(float64((level - 1)) / float64(len))
	baseVal := baseHp
	if attr == enum.RoleAttack {
		baseVal = baseAtk
	}
	val := baseVal * (count + 1)

	remainLv := (level - 1) - count*len
	for i := 0; i < remainLv; i++ {
		val += lvs[i][attr]
	}
	misc := GetMisc()
	starLvRatio := misc.StarLvAttrRatio
	starRate := 1.0 + starLvRatio*float64(starLv)
	val = ut.Round(float64(val) * starRate)
	return val
}

func GetRoleBaseAttr(id int) (int, int) {
	bean, _ := CharacterContainer.GetBeanById(id)
	baseHp, baseAtk := 0, 0
	if bean != nil {
		baseHp, baseAtk = bean.HP, bean.Attack
	} else {
		bean2, _ := PlanetMonsterContainer.GetBeanById(id)
		if bean2 != nil {
			baseHp, baseAtk = bean2.HP, bean2.Attack
		} else {
			misc := GetMisc()
			baseHp, baseAtk = misc.BlackHole.BaseAttr, misc.BlackHole.BaseAttr
		}
	}
	return baseHp, baseAtk
}

func GetRoleAttrByAdd(id int, level int, starLv int, attr string) int {
	return GetRoleAttr(id, level+1, starLv, attr) - GetRoleAttr(id, 1, starLv, attr)
}

func initCharaterWeight() {
	characterDatas := CharacterContainer.GetData()

	jackDatas := JackpotContainer.GetData()

	jackWeightSum := lo.Reduce(jackDatas, func(sum float64, p *Jackpot[int], i int) float64 { return sum + p.Weight }, 0.0)

	for _, jackData := range jackDatas {
		datas := lo.Filter(characterDatas, func(c *Character[int], i int) bool { return c.Quality == jackData.Id })
		qSum := lo.Reduce(datas, func(sum int, p *Character[int], i int) int { return sum + p.QualityWeight }, 0)
		p1 := float64(jackData.Weight) / float64(jackWeightSum)
		for _, data := range datas {
			p2 := float64(data.QualityWeight) / float64(qSum)
			data.Weight = ut.Round(p1 * p2 * 10000)
		}
	}

	// sum := lo.Reduce(characterDatas, func(sum int, p *Character, i int) int { return sum + p.Weight }, 0)
	// sum2 := 0.0
	// for _, data := range characterDatas {
	// 	p := float64(data.Weight) / float64(sum)
	// 	sum2 += p
	// 	log.Info("%d %d %f", data.Quality, data.Weight, p)
	// }
	// log.Info("%f", sum2)
}

func initPlanetMoveTime() {
	datas := PlanetMoveTimeContainer.GetData()
	for i := 0; i < len(datas); i++ {
		for j := 0; j < len(datas); j++ {
			data1 := datas[i]
			if data1.Time == nil {
				data1.Time = map[int]int{}
			}
			data2 := datas[j]
			if data2.Time == nil {
				data2.Time = map[int]int{}
			}
			time := ut.If(data1.Time[data2.Id] != 0, data1.Time[data2.Id], data2.Time[data1.Id])
			data1.Time[data2.Id] = time
			data2.Time[data1.Id] = time
		}
	}
	bytes, _ := json.Marshal(datas)
	ut.InitPlanetMove(bytes)
	for _, data := range datas {
		for i := range data.Time {
			data.Time[i] = ut.GetMoveTime(data.Id, i)
		}
	}
}

// ConfigGetCharacterLevel 获取乘客升级配置
func GetCharacterLevelData(passengerId, lv int) ([]*ConfigCondition, bool) {
	bean, exists := CharacterLevelCostContainer.GetBeanById(lv)
	if !exists {
		return nil, false
	}
	// 赏金猎犬前三级不消耗星尘
	if passengerId == 1005 && lv < 3 {
		return lo.Filter(bean.BuyCost, func(c *ConfigCondition, i int) bool { return c.Type != condition.STAR_DUST }), true
	}
	return bean.BuyCost, true
}

// ConfigGetCharacterLikeLevel 获取乘客升星配置
func GetCharacterStarLevelData(starLv int) (*StarUp[int], bool) {
	return StarUpContainer.GetBeanById(starLv)
}

func GetMisc() *Misc_C[string] { return Misc_CContainer.GetObj() }

// GetRandomRewardsByFilter
/*
 * @description 使用过滤方法来获取随机奖励
 * @param rdRewards 奖励数据
 * @param rdRewardNum 奖励份数
 * @param filter 过滤方法
 * @return []map[string]interface{}
 */
func GetRandomRewardsByFilter(rdRewardsMap []map[string]any, rdRewardNum int, filter func(typ, id int) bool) []map[string]interface{} {
	rewards := make([]map[string]interface{}, 0)
	rdRewards := array.SliceCopy(rdRewardsMap)
	if filter != nil {
		rdRewards = lo.Filter(rdRewards, func(m map[string]interface{}, i int) bool { return filter(cast.ToInt(m["type"]), cast.ToInt(m["id"])) })
	}
	for i := 0; i < rdRewardNum; i++ {
		idx := ut.RandomIndexByWeight(rdRewards, func(r map[string]interface{}) int { return cast.ToInt(r["weight"]) })
		if idx < 0 {
			break
		}
		rewards = append(rewards, rdRewards[idx])
		rdRewards = array.RemoveIndex(rdRewards, idx)
	}
	return rewards
}

// initFieldRefPlan
/*
 * @description 种子关联的菜谱
 */
func initFieldRefPlan() {
	data := FieldSeedContainer.GetData()
	refData := TrainGoodsLevelContainer.GetData()
	for _, datum := range data {
		datum.ref = make(map[string][]*TrainGoodsLevel[string])
		for _, refDatum := range refData {
			if refDatum.BuyCost == nil {
				continue
			}
			id := 0
			_, exists := lo.Find(refDatum.BuyCost, func(unlock *ConfigUnlock) bool {
				_, exists := lo.Find(datum.Reward, func(configCondition *ConfigCondition) bool {
					if cast.ToInt(configCondition.Id) == unlock.Id {
						id = unlock.Id
						return true
					}
					return false
				})
				return exists
			})
			if !exists {
				continue
			}
			datum.itemId = id
			if _, ok := datum.ref[refDatum.GoodsId]; !ok {
				datum.ref[refDatum.GoodsId] = make([]*TrainGoodsLevel[string], 0)
			}
			datum.ref[refDatum.GoodsId] = append(datum.ref[refDatum.GoodsId], refDatum)
		}
	}
}

// 累加菜品元气值
func initTrainGoodsAttr() {
	data := TrainGoodsLevelContainer.GetData()
	skip := make(map[string]bool)
	for _, datum := range data {
		for i := 1; i < datum.Level; i++ {
			if _, ok := skip[datum.GoodsId]; ok {
				continue
			}
			skip[datum.GoodsId] = true
			filter := lo.Filter(data, func(level *TrainGoodsLevel[string], i int) bool {
				return level.GoodsId == datum.GoodsId
			})
			sort.Slice(filter, func(i, j int) bool {
				return filter[i].Level-filter[j].Level < 0
			})
			for idx, level := range filter {
				if idx > 0 {
					level.AttrValue += filter[idx-1].AttrValue
				}
			}
		}
	}
}

// initEquipEffectRangeData
/*
 * @description 装备效果数值范围
 */
func initEquipEffectRangeData() {
	data := EquipLevelContainer.GetData()

	for _, iData := range data {
		iData.effectRangeData = make(map[int][]*effectRange)
		for _, effect := range iData.Effect {
			bean := array.Find(EquipSectionContainer.GetData(), func(item *EquipSection[int]) bool {
				return item.Max == effect.Max && item.Min == effect.Min
			})
			ary := make([]*effectRange, 0)
			if bean != nil {
				ary = bean.Sec
			}

			iData.effectRangeData[effect.ID] = ary
		}
	}
}

func GetProficiencyLv(level int, pro int) int {
	datas := lo.Filter(EquipMakeContainer.GetData(), func(item *EquipMake[string], _ int) bool {
		return item.Level == level
	})
	sum := 0
	for i := 1; i < len(datas); i++ {
		times := datas[i].Times
		sum += times
		if pro < sum {
			return i
		}
	}
	return len(datas)
}

func GetEquipEffectsLv(id, level, proficiency int) []*EquipEffect[int] {

	lvBean, _ := EquipLevelContainer.GetBeanById(level)
	equipBean, _ := EquipContainer.GetBeanById(id)

	rangeIndex := GetProficiencyLv(level, proficiency) - 1

	effects := make([]*EquipEffect[int], 0)

	for effectId, rangeData := range lvBean.effectRangeData {
		effectBean, _ := EquipEffectContainer.GetBeanById(effectId)
		if effectBean.EquipIndex != equipBean.Index {
			continue
		}

		r := rangeData[rangeIndex]
		min := r.Min
		max := r.Max

		effectData, _ := lo.Find(lvBean.Effect, func(item *equipEffectData) bool {
			return item.ID == effectId
		})

		store := effectData.Store

		effects = append(effects, &EquipEffect[int]{
			ID:         effectBean.ID,
			Type:       effectBean.Type,
			Target:     effectBean.Target,
			EquipIndex: effectBean.EquipIndex,
			Min:        min,
			Max:        max,
			Store:      store,
		})
	}
	sort.Slice(effects, func(i, j int) bool {
		return effects[i].ID < effects[j].ID
	})
	return effects
}

func GetEquipEffectAttrByLv(equipId int, effect *EquipEffect[int], lv int) int {
	equipBean, _ := EquipContainer.GetBeanById(equipId)
	roleId := equipBean.RoleId
	attr := lv
	if effect.Type == equip_effect_type.ATTR {
		attrType := enum.RoleAttack
		if effect.Target == equip_effect_target.HP {
			attrType = enum.RoleHP
		}

		attr = GetRoleAttrByAdd(roleId, lv, 0, attrType)
	}
	return attr
}

func initTransportLevelCfgExp() {
	lvData := TransportLevelContainer.GetData()
	monsterData := TransportMonsterContainer.GetData()
	rareExp := Misc_CContainer.GetObj().Transport.RareExp

	for _, data := range lvData {
		if data.Id == 1 {
			data.Exp = 0
			continue
		}
		ary := lo.Filter(monsterData, func(m *TransportMonster[string], i int) bool {
			return m.Lv == data.Id-1
		})
		data.Exp = len(ary) * rareExp
	}
}

// 初始化装备出售价格映射
func initEquipSellMap() {
	datas := EquipMakeContainer.GetData()
	curLv := 0

	misc := GetMisc()

	Max := 0
	for _, data := range datas {
		var cost *ConfigCondition
		for _, c := range data.Cost {
			if c.Type == condition.STAR_DUST {
				cost = c
				break
			}
		}
		lvCfg, _ := EquipLevelContainer.GetBeanById(data.Level)
		min, max := 0, 0
		ratio := misc.Equip.Sell
		equipIndex := 1

		for _, effect := range lvCfg.Effect {
			sections := lvCfg.effectRangeData[effect.ID]
			if data.Proficiency-1 >= len(sections) {
				continue
			}
			section := sections[data.Proficiency-1]
			effectDetail, _ := EquipEffectContainer.GetBeanById(effect.ID)
			if effectDetail.EquipIndex != equipIndex {
				continue
			}
			if effectDetail.Type == equip_effect_type.SKILL {
				min += section.Min * 5
				max += section.Max * 5
			} else {
				min += section.Min
				max += section.Max
			}
		}
		mid := int(math.Round(float64(min+max) / 2))
		for i := curLv; i <= mid; i++ {
			EquipSellMap[i] = int(math.Round(float64(cost.Num) * ratio))
			Max = ut.Max(Max, EquipSellMap[i])
		}
		curLv = mid + 1
	}
	EquipSellMap[-1] = Max
}

func GetEquipSellPrice(lv int) int {
	val := EquipSellMap[lv]
	if val == 0 {
		val = EquipSellMap[-1]
	}
	return val
}

func GetEquipLv(id int, lv int, attrType string) int {
	equipBean, _ := EquipContainer.GetBeanById(id)
	roleId := equipBean.RoleId
	val := GetRoleAttr(roleId, lv+1, 0, attrType)
	return EquipLvMap[id][attrType][val]
}

func initEquipLvMap() {
	EquipLvMap = make(map[int]map[string]map[int]int)
	handle := func(id int) {
		equipBean, _ := EquipContainer.GetBeanById(id)
		roleId := equipBean.RoleId
		hasMap := make(map[string]map[int]int)
		for _, lvBean := range EquipLevelContainer.GetData() {
			for effectId, rangeData := range lvBean.effectRangeData {
				effectBean, _ := EquipEffectContainer.GetBeanById(effectId)
				if effectBean.EquipIndex != equipBean.Index {
					continue
				}
				if effectBean.Type == equip_effect_type.SKILL {
					continue
				}

				attrType := enum.RoleAttack
				if effectBean.Target == equip_effect_target.HP {
					attrType = enum.RoleHP
				}
				if hasMap[attrType] == nil {
					hasMap[attrType] = make(map[int]int)
				}

				for _, r := range rangeData {
					min := r.Min
					max := r.Max
					// log.Debug("range %d %d", min, max)

					for i := min; i < max; i++ {
						val := GetRoleAttr(roleId, i+1, 0, attrType)
						if hasMap[attrType][val] <= 0 {
							hasMap[attrType][val] = i
							// log.Debug("i %d %d", val, i)
						}
					}
					val := GetRoleAttr(roleId, max+1, 0, attrType)
					hasMap[attrType][val] = max
					// log.Debug("max %d %d", val, max)
				}
			}
		}
		EquipLvMap[id] = hasMap
	}

	equipDatas := EquipContainer.GetData()
	for _, equip := range equipDatas {
		handle(equip.Id)
	}

}

func GetStarLvByQuality(quality int) int {
	datas := StarUpContainer.GetData()
	data := array.Find(datas, func(data *StarUp[int]) bool {
		return quality == data.Quality
	})
	return data.Id
}

func GetQualityByStarLv(starLv int) int {
	datas := StarUpContainer.GetData()
	bean, _ := StarUpContainer.GetBeanById(starLv)
	if bean == nil {
		return datas[len(datas)-1].Quality
	}
	return bean.Quality
}

func GetIdBySkillIndex(mainId int, index int) int {
	return index*10000 + mainId
}

func initPublicityData() {
	ary := PublicityPlayContainer.GetData()
	// 按照星球分类
	grouped := lo.GroupBy(ary, func(item *PublicityPlay[string]) int { return item.PlanetId })
	for _, v := range grouped {
		// 按照等级排序
		sort.Slice(v, func(i, j int) bool { return v[i].Lv < v[j].Lv })
		for i := 1; i < len(v); i++ {
			j := i - 1
			v[i].Req += v[j].Req
			v[i].Rate += v[j].Rate
		}
	}
}
