package cfg

const ConstRobotEquip = "RobotEquip"

type RobotEquip[K int] struct {
	Id     K                    `json:"id"`
	Equip  []*RobotEquipMakeCfg `json:"equip"`
	Talent []int                `json:"talent"`
}

func (r *RobotEquip[K]) GetName() string { return ConstRobotEquip }
func (r *RobotEquip[K]) GetUnique() K    { return r.Id }
func (r *RobotEquip[K]) OnInit() {
}

type RobotEquipMakeCfg struct {
	Quality int `json:"quality"` // 打造台桌子id
	Cnt     int `json:"cnt"`     // 打造次数
}
