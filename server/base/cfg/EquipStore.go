package cfg

const ConstEquipStore = "EquipStore"

type EquipStore[K int] struct {
	Id         K                  `json:"id"`
	Level      int                `json:"level"`
	EquipIndex int                `json:"equipIndex"`
	Cost       []*ConfigCondition `json:"cost"`
}

func (this *EquipStore[K]) GetName() string { return ConstEquipStore }

func (this *EquipStore[K]) GetUnique() K { return this.Id }

func (this *EquipStore[K]) OnInit() {
}
