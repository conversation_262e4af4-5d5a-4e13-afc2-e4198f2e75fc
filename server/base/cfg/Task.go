package cfg

import (
	ut "train/utils"

	"github.com/spf13/cast"
)

const ConstTask = "Task"

type TaskTrigger struct {
	Type string      `json:"type"` // 类型
	Id   interface{} `json:"id"`   // 条件
}

func (this *TaskTrigger) GetId() string {
	return cast.ToString(this.Id)
}

// IsEmpty 触发条件是否为空
func (t TaskTrigger) IsEmpty() bool {
	return ut.IsEmpty(t.Type)
}

type TaskTarget struct {
	Type interface{} `json:"type"` // 类型
	Id   interface{} `json:"id"`   // id
	Num  int         `json:"num"`  // 数量
}

func (this *TaskTarget) GetId() string {
	return cast.ToString(this.Id)
}

func (this *TaskTarget) GetType() string {
	return cast.ToString(this.Type)
}

type Task[K string] struct {
	Id      K                        `json:"id"`      // 任务id
	PreTask string                   `json:"preTask"` // 前置任务
	Trigger *TaskTrigger             `json:"trigger"` // 触发条件
	Target  []*TaskTarget            `json:"target"`  // 任务要求
	Reward  []map[string]interface{} `json:"reward"`  // 任务奖励
	Type    string                   `json:"type"`    // 任务类型
	Mark    string                   `json:"mark"`    //任务标记
}

func (this *Task[K]) GetName() string { return ConstTask }

func (this *Task[K]) GetUnique() K { return this.Id }

func (this *Task[K]) OnInit() {
}
