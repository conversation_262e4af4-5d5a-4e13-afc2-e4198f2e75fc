package cfg

const ConstAchievementType = "AchievementType"

type AchievementType[K int] struct {
	Id      K              `json:"id"`
	Trigger *TriggerConfig `json:"trigger"`
	Type    string         `json:"type"`
}

func (this *AchievementType[K]) GetName() string { return ConstAchievementType }

func (this *AchievementType[K]) GetUnique() K { return this.Id }

func (this *AchievementType[K]) OnInit() {
}
