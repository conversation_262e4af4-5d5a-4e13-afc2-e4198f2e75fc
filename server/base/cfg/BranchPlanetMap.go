package cfg

import "fmt"

const ConstBranchPlanetMap = "BranchPlanetMap"

type BranchPlanetMap[K string] struct {
	Id       K          `json:"id"`
	PlanetId int        `json:"planetId"`
	Node     []*MapBase `json:"node"`
}

func (this *BranchPlanetMap[K]) GetName() string { return ConstBranchPlanetMap }

func (this *BranchPlanetMap[K]) GetUnique() K { return this.Id }

func (this *BranchPlanetMap[K]) OnInit() {
	for _, base := range this.Node {
		base.TypeId = fmt.Sprintf("%s-%d", this.Id, base.Id)
		base.unique = fmt.Sprintf("%s-%d", this.Id, base.Index)
		base.IsBranch = true
		// base.Pos = ut.NewVec2(base.X, base.Y)
	}
}
