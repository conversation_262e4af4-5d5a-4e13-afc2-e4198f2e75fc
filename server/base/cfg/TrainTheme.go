package cfg

const ConstTrainTheme = "TrainTheme"

type TrainTheme[K string] struct {
	Id          K                        `json:"id"`
	RoleCnt     int                      `json:"roleCnt"`
	WorkCnt     int                      `json:"workCnt"`
	BuyCost     []map[string]interface{} `json:"buyCost"` // 解锁消耗
	Order       int                      `json:"order"`
	CarriageId  int                      `json:"carriageId"`
	UnlockLevel int                      `json:"unlockLevel"` //设施达到x级时解锁下一主题
	Add         map[string]interface{}   `json:"add"`         // 属性
}

func (this *TrainTheme[K]) GetName() string { return ConstTrainTheme }

func (this *TrainTheme[K]) GetUnique() K { return this.Id }

func (this *TrainTheme[K]) OnInit() {
}
