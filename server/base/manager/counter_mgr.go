package manager

import (
	"context"
	"time"
	"train/base/structs"
	"train/db"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var counterManagerLock deadlock.Once
var counterManager *CounterManager

// GlobalGetCounterManager 全局获取计数管理
func GlobalGetCounterManager() *CounterManager {
	counterManagerLock.Do(func() {
		counterManager = &CounterManager{}
		counterManager.init()
	})
	return counterManager
}

type CounterManager struct {
	Load     bool // 是否init完成，加载成功
	counters map[string]*structs.Counter
}

// init 初始化时从数据库中load所有counter出来,这个方法不可手动调用.
func (this *CounterManager) init() {
	sTime := time.Now()
	//TODO 0220, 因为counter单个文档很小,且作用域有限，即便是有上万条数据，一次性加载也仅有几m，暂时不分行load，但是这里输出了执行耗时，后续决定是否需要优化
	find, err := db.COUNTER.GetCollection().Find(context.TODO(), &bson.M{}, options.Find())
	if err != nil {
		log.Error("CounterManager - init error: %s", err.Error())
		return
	}
	var arr []structs.Counter
	find.All(context.TODO(), &arr)
	find.Close(context.TODO())
	for _, counter := range arr {
		this.PushCounter(counter.Id, &counter)
	}
	log.Info("CounterManager - init end, total :%d,during:%fs", len(arr), time.Since(sTime).Seconds())
}

// NewCounter 新建一个counter
func (this *CounterManager) NewCounter(id string) (ct *structs.Counter) {
	ct = this.GetCounter(id)
	if ct != nil {
		return
	}
	ct = &structs.Counter{
		Id: id,
	}
	this.counters[id] = ct
	return
}

// GetCounter 使用id获取一个counter
func (this *CounterManager) GetCounter(id string) *structs.Counter {
	if this.counters == nil {
		this.counters = make(map[string]*structs.Counter)
		this.counters[id] = this.NewCounter(id)
	}
	return this.counters[id]
}

// PushCounter 增加counter
func (this *CounterManager) PushCounter(id string, val *structs.Counter) {
	if this.counters == nil {
		this.counters = make(map[string]*structs.Counter)
	}
	this.counters[id] = val
}

// CounterSaveAll 保存counter计数到db中 返回是否全部保存成功
func (this *CounterManager) CounterSaveAll() bool {
	n := 0
	for _, counter := range this.counters {
		if success := counter.Save(); !success {
			n++
		}
	}
	return len(this.counters) == n
}
