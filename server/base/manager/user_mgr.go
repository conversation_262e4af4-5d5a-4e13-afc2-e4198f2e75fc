package manager

import (
	"train/base/structs"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/sasha-s/go-deadlock"
)

var userManagerLock deadlock.Once
var userManager *UserManager

// GlobalGetUserManager 全局获取用户管理中心
func GlobalGetUserManager() *UserManager {
	userManagerLock.Do(func() {
		userManager = &UserManager{
			UserMap: cmap.New[*structs.User](),
		}
	})
	return userManager
}

type UserManager struct {
	UserMap cmap.ConcurrentMap[string, *structs.User]
}

//// AddToMapWithIdleWait 尝试添加用户到管理器,这会为user添加一个idle时间,idle期间user没完成登录就会被清理.
//func (this *UserManager) AddToMapWithIdleWait(user *structs.User) {
//	user.LoginIdleWait = time.Now().Add(data.LoginIdleWaitTime).Unix()
//	this.AddToMap(user)
//}
//
//// AddToMap 添加用户进管理器
//func (this *UserManager) AddToMap(user *structs.User) {
//	id := user.GetUid()
//	value, ok := this.UserMap.Get(id)
//	if ok {
//		if value != nil {
//			if value.LoginIdleWait > 0 {
//				// 如果是idle用户，则只需要清除idle状态
//				value.LoginIdleWait = 0
//				return
//			}
//			// 用户value已经在线,发送下线命令,并关闭通信
//			value.Offline()
//			this.UserMap.Remove(id)
//			log.Info("用户:%s 被顶号登录.", user.GetUid())
//		}
//	}
//	this.UserMap.Set(id, user)
//	log.Info("注册用户到管理中心:%s,当前数量:%d", user.GetUid(), this.UserMap.Count())
//}

////Run job任务挂靠
//func (this *UserManager) Run() {
//	this.ClearIdle()
//}
//
//// ClearIdle 清理空闲用户,缓解登录服链接压力,此任务由job服务定时异步运行,所以要保证数据安全.
//func (this *UserManager) ClearIdle() {
//	log.Info("ClearIdle Job Start.")
//	sTime := time.Now()
//	for item := range this.UserMap.IterBuffered() {
//		user := item.Val
//		if user.LoginIdleWait != 0 && user.LoginIdleWait-sTime.Unix() <= 0 {
//			this.UserMap.Remove(item.Key)
//			log.Warning(user.GetUid())
//		}
//
//	}
//	log.Info("ClearIdle Job End.during at %fs", time.Since(sTime).Seconds())
//}
