package manager

import (
	"context"
	"os"
	"train/base/enum"
	"train/base/structs"
	"train/db"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var areaManagerLock deadlock.Once
var areaManager *AreaManager

// GlobalGetAreaManager 全局获取区服管理中心
func GlobalGetAreaManager() *AreaManager {
	areaManagerLock.Do(func() {
		areaManager = &AreaManager{}
	})
	return areaManager
}

type AreaManager struct {
	area *structs.Area
}

// NewArea 新开区服
func (this *AreaManager) NewArea(id, openTime int, name, desc string) (*structs.Area, error) {
	this.area = &structs.Area{
		Id:       id,
		OpenTime: openTime,
		Name:     name,
		Desc:     desc,
		State:    enum.Close,
	}
	// 自增区服id
	if id == 0 {
		limit := int64(1)
		cur, err := db.AREA.GetCollection().Find(context.TODO(), &bson.M{}, &options.FindOptions{
			Sort: &bson.M{
				"id": -1,
			},
			Limit: &limit,
		})
		if err != nil {
			return nil, err
		}
		defer cur.Close(context.TODO())
		temp := &structs.Area{}
		if cur.TryNext(context.TODO()) {
			err = cur.Decode(temp)
		}
		if err != nil {
			return nil, err
		}
		this.area.Id = temp.Id + 1
	}
	_, err := db.AREA.GetCollection().InsertOne(context.TODO(), this.area)
	return this.area, err
}

// NewTempArea 自启动一个区服时用到,会自增sid
func (this *AreaManager) NewTempArea() *structs.Area {
	area, err := this.NewArea(0, ut.Now(), "测试区", "这个区没有任何说明.")
	if err != nil {
		log.Error("AreaManager - NewArea err: %s", err.Error())
	}
	return area
}

// GetThisArea 获取当前区,为了避免数据串区，传入全局Sid最好,如果不存在并且create=true时会创建一个默认区但不会开放。
func (this *AreaManager) GetThisArea(id int, create bool) (area *structs.Area) {
	area = &structs.Area{}
	err := db.AREA.GetCollection().FindOne(context.TODO(), &bson.M{
		"id": id,
	}).Decode(area)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			log.Warning("sid:%d,server not found in db,LaunchArea - Create", id)
			if create {
				area = this.NewTempArea()
				if area != nil {
					log.Info("create server success!")
				}
				return
			}
			os.Exit(-1)
		}
		log.Error("get area with error :%s.", err.Error())
		os.Exit(-1)
	}
	return
}

// Launch 启动区,如果OpenTime <= time.Now则会把区状态修改为正常可进入
func (this *AreaManager) Launch(id int, create bool) {
	area := this.GetThisArea(id, create)
	if area.OpenTime <= ut.Now() {
		area.State = enum.Normal
		area.Save()
	}
	// 做一些启动区服的事情
	log.Info("启动%d区[ %s ],状态:%d", area.Id, area.Name, area.State)

}

// GetAreaList 获取区列表
func (this *AreaManager) GetAreaList() (list []*structs.Area) {
	cursor, err := db.AREA.GetCollection().Find(context.TODO(), &bson.M{})
	if err != nil {
		log.Error("AreaManager - GetAreaList err:%s", err.Error())
	}
	defer cursor.Close(context.TODO())
	cursor.All(context.TODO(), &list)
	return
}
