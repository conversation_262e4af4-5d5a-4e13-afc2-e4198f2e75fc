package manager

import (
	"fmt"
	"strconv"
	"sync"
	"time"
	"train/base/enum"
	"train/base/enum/logout_reason"
	"train/base/structs"
	comm "train/common"
	"train/common/pb"
	ut "train/utils"

	"github.com/bamzi/jobrunner"
	"github.com/huyangv/vmqant/log"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/sasha-s/go-deadlock"
)

var playerManagerLock deadlock.Once
var playerManager *PlayerManager

// GlobalGetPlayerManager 全局获取玩家管理中心
func GlobalGetPlayerManager() *PlayerManager {
	playerManagerLock.Do(func() {
		playerManager = &PlayerManager{
			playerMap: cmap.New[*structs.Player](),
			lockMap:   cmap.New[*deadlock.Mutex](),
		}
		jobrunner.Schedule("@every 10s", playerManager)
	})
	return playerManager
}

type PlayerManager struct {
	playerMap cmap.ConcurrentMap[string, *structs.Player] // 这里的key是玩家uid 对应player的Uid ↓ 需要同步操作
	lockMap   cmap.ConcurrentMap[string, *deadlock.Mutex] // 用户粒度的锁
	slot      int
}

// AddPlayer 管理实例添加某个player
func (p *PlayerManager) AddPlayer(player *structs.Player) bool {
	log.Debug("AddPlayer %s", player.GetUid())
	uid := player.Id
	temp, exists := p.playerMap.Get(uid)
	if exists {
		if temp != player {
			// 如果出现这个提示，说明有bug
			log.Error("PlayerManager - AddPlayer 警告,玩家会被覆盖.uid:%s.", uid)
			return false
		}
		return true
	}
	p.playerMap.Set(uid, player)

	return true
}

// RemovePlayer 管理实例移除某个player
func (p *PlayerManager) RemovePlayer(player *structs.Player) {
	log.Debug("removePlayer %s", player.GetUid())
	uid := player.GetUid()
	p.playerMap.Remove(uid)
}

// TryGetPlayerByUid 这个id是玩家player uid
func (p *PlayerManager) TryGetPlayerByUid(uid string) (plr *structs.Player, exists bool) {
	plr, exists = p.playerMap.Get(uid)
	return
}

func (p *PlayerManager) ClearById(id string, kick int) {
	plr, exists := p.TryGetPlayerByUid(id)
	if exists {
		p.Clear(plr, kick)
	}
}

// Clear 清理玩家数据,同时标记为不可用状态
// 1 保存玩家数据
// 2 上报离线ta
// 3 删除玩家绑定的逻辑服节点
// 4 从本节点移除玩家
//
// Parameters:
//   - plr *structs.Player
//   - kick bool 是否踢下线
func (p *PlayerManager) Clear(plr *structs.Player, kick int) {
	if !plr.IsValid() {
		return
	}
	plr.OnLeave()
	p.DelPlayerNodeId(plr.GetUid())
	plr.Session.Set(enum.GAME_NODE_ID, "")
	plr.Destroy = true
	p.RemovePlayer(plr)
	if kick != 0 && plr.IsOnline() {
		p.TellPlayerMsg(plr, pb.S2CLogoutMessage, pb.ProtoMarshalForce(&pb.S2C_LogoutMessage{Reason: int32(kick)}))
		plr.Offline()
	}
}

func (p *PlayerManager) Run() {
	defer func() {
		p.slot += 1
		if p.slot > 5 {
			p.slot = 0
		}
	}()
	sTime := time.Now()
	log.Debug("playerMap count: %d.", p.playerMap.Count())
	taskAry := make([]func(), 0)

	for item := range p.playerMap.IterBuffered() {
		key := item.Key
		plr := item.Val
		lastByte, _ := strconv.ParseInt(key[len(key)-2:], 16, 64)
		// 分成6个时间片 只落地属于当前时间片的玩家
		timeSlot := int(lastByte % 6)
		if timeSlot != p.slot {
			continue
		}
		taskAry = append(taskAry, func() {
			lock := p.Lock(key)
			defer ut.Unlock(lock)
			if plr.IsValid() {
				plr.Save()
			}
		})
	}
	p.withConcurrencyLimit(10, taskAry)
	log.Debug("[%d]在线玩家保存写入数据库,during :%fs", p.slot, time.Since(sTime).Seconds())
}

// TellPlayerMsg  给客户端发送消息，用的是SendNR非阻塞
func (p *PlayerManager) TellPlayerMsg(player *structs.Player, topic string, body []byte) bool {
	if player == nil {
		return false
	}
	err := player.Session.SendNR(topic, body)
	if err == "" {
		return true
	}
	log.Error("TellPlayerMsg 发送给客户端消息时出错:%s", err)
	return false
}

// TellPlayerMsgSync 同步发送，尽量不要用
func (p *PlayerManager) TellPlayerMsgSync(player *structs.Player, topic string, body []byte) bool {
	sTime := time.Now()
	if player == nil {
		return false
	}
	err := player.Session.Send(topic, body)
	log.Info("TellPlayerMsgSync, during: %fs", time.Since(sTime).Seconds())
	if err == "" {
		return true
	}
	log.Error("TellPlayerMsgSync 发送给客户端消息时出错:%s", err)
	return false
}

// ClearAll 落地当前服务器所有玩家数据 玩家不会被踢下线，但是会被标记成不可用，无法再处理后续请求
func (p *PlayerManager) ClearAll(kick int) {
	sTime := time.Now()
	count := p.playerMap.Count()
	taskAry := make([]func(), 0)

	for item := range p.playerMap.IterBuffered() {
		key := item.Key
		plr := item.Val
		taskAry = append(taskAry, func() {
			lock := p.Lock(key)
			defer ut.Unlock(lock)
			p.Clear(plr, kick)
		})
	}
	p.withConcurrencyLimit(10, taskAry)
	log.Info("ClearAll-count: %d,during :%fs", count, time.Since(sTime).Seconds())
}

// CheckPlayerVersion 当版本更新时检查在线玩家客户端版本
func (p *PlayerManager) CheckPlayerVersion(version string) {
	taskAry := make([]func(), 0)
	sTime := time.Now()

	for item := range p.playerMap.IterBuffered() {
		plr := item.Val
		if !plr.IsValid() || plr.Session == nil {
			continue
		}
		taskAry = append(taskAry, func() {
			if !plr.IsValid() {
				return
			}
			clientVer := plr.GetClientVersion()
			if ut.CmpVersion(version, clientVer) > 0 {
				lock := p.Lock(item.Key)
				defer ut.Unlock(lock)
				p.Clear(plr, logout_reason.DEFAULT)
				return
			}
		})
	}
	count := len(taskAry)
	p.withConcurrencyLimit(10, taskAry)
	log.Info("CheckPlayerVersion-count: %d,during :%fs", count, time.Since(sTime).Seconds())
}

func (p *PlayerManager) GetAll() cmap.ConcurrentMap[string, *structs.Player] { return p.playerMap }

func (p *PlayerManager) Lock(id string) *deadlock.Mutex {
	lock, exists := p.lockMap.Get(id)
	if exists {
		lock.Lock()
	}
	return lock
}

func (p *PlayerManager) SetLock(id string) *deadlock.Mutex {
	lock, exists := p.lockMap.Get(id)
	if !exists {
		lock = &deadlock.Mutex{}
		p.lockMap.Set(id, lock)
	}
	lock.Lock()
	return lock
}

// 获取player所在节点id
func (p *PlayerManager) GetPlayerNodeId(uid string) (string, error) {
	key := fmt.Sprintf("%s_%s", enum.GAME_NODE_ID, uid)
	return comm.GetConsulValue(key)
}

func (p *PlayerManager) SetPlayerNodeId(uid string, nodeId string) error {
	key := fmt.Sprintf("%s_%s", enum.GAME_NODE_ID, uid)
	return comm.SetConsulValue(key, nodeId)
}

func (p *PlayerManager) DelPlayerNodeId(uid string) error {
	key := fmt.Sprintf("%s_%s", enum.GAME_NODE_ID, uid)
	return comm.DelConsulValue(key)
}

func (p *PlayerManager) withConcurrencyLimit(limit int, tasks []func()) {
	if limit <= 0 || len(tasks) == 0 {
		return
	}
	sem := make(chan struct{}, limit)
	var wg sync.WaitGroup
	for _, task := range tasks {
		wg.Add(1)
		t := task // 创建局部变量捕获当前任务
		go func() {
			sem <- struct{}{}        // 获取信号量
			defer func() { <-sem }() // 释放信号量
			defer wg.Done()
			t()
		}()
	}
	wg.Wait()
}
