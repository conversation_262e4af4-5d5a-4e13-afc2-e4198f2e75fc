package task_type

// 任务类型
const (
	BuildTrainItemLevel    = "BUILD_TRAINITEM_LEVEL"   // 指定车厢完成指定等级设施的建设
	ExplorePlanetComplete  = "EXPLORE_PLANET_COMPLETE" // 完成指定星球的探索
	EntrustComplete        = "ENTRUST_COMPLETE"        // 累计完成value次委托
	ExplorePlanet          = "EXPLORE_PLANET"          //指定id星球探索进度达到value
	GotoPlanet             = "GOTO_PLANET"             //前往指定id星球
	TrainItemLevelUp       = "TRAINITEM_LEVELUP"       //将指定设施升到level级
	TrainItemLevelUp2      = "TRAINITEM_LEVELUP2"      //将指定id[]车厢的所有设施升到level级
	BuildTrainItem         = "BUILD_TRAINITEM"         //建设指定id[]设施
	BuildTrainIndex        = "BUILD_TRAIN_INDEX"       //建设第value节车厢
	BuildTrain             = "BUILD_TRAIN"             //建设指定id[]车厢
	CollectItem            = "GET_ITEM"                //收集指定item[].id材料 数量达到item[].num
	CharacterLevel         = "CHARACTER_LEVEL"         //所有角色等级总计达到value
	CharacterRank          = "CHARACTER_RANK"          //所有角色星级总计达到value
	CharacterLevelUp       = "CHARACTER_LEVELUP"       //value个角色升到level级
	CharacterRankUp        = "CHARACTER_RANKUP"        //value个角色培养到rank星
	CharacterGetOn2        = "CHARACTER_GETON2"        //指定id[]角色入住指定id车厢
	CharacterGetOn         = "CHARACTER_GETON"         //value个角色入住列车
	CharacterIndex         = "GET_CHARACTER_INDEX"     //邀请第value名乘客
	ToolBuild              = "TOOL_BUILD"              //打造num个工具
	ToolChange             = "TOOL_CHANGE"             //更换指定类型的工具
	ToolLevelUp2           = "TOOL_LEVELUP2"           //指定id[]类型的工具（仅限一种），升级value次（不指定id时，不限工具类型，升级value次）
	ToolLevelUp            = "TOOL_LEVELUP"            //指定id[]类型的工具（仅限一种），升到value级（不指定id时，所有工具升到value级）
	ToolRankUp             = "TOOL_RANKUP"             //指定类型的工具，升到value品阶
	ToolTableUp            = "TOOL_TABLEUP"            //打造台升到level级
	EntrustStart           = "ENTRUST_START"           //开始进行指定数量的委托任务
	GotoWork               = "GOTO_WORK"               //安排value个角色去指定id车厢工作
	GoodsUnlock            = "GOODS_UNLOCK"            //解锁车厢内所有商品
	PlanetBattleId         = "PLANET_BATTLE_ID"        //星球战斗id
	TO_DEEP_EXPLORE        = "TO_DEEP_EXPLORE"         //前往深度探索
	DEEP_EXPLORE           = "DEEP_EXPLORE"            //完成深度探索
	TOWER                  = "TOWER"                   //前往时光之境
	PROFILE                = "PROFILE"                 //前往乘客档案
	GOTO_PLANET_ENTRY_1009 = "GOTO_PLANET_ENTRY_1009"  //回宝石星小镇
	GOTO_PLANET_ENTRY_1007 = "GOTO_PLANET_ENTRY_1007"  //回雪星小镇调查
	ORE_PUZZLE             = "ORE_PUZZLE"              //前往挖矿解谜
	EXPLORE_PLANET_AREA    = "EXPLORE_PLANET_AREA"     //前往星球探索
	TO_TRANSPORT           = "TO_TRANSPORT"            //前往运送
	TRANSPORT              = "TRANSPORT"               //前往运送
	INSTANCE_PUZZLE        = "INSTANCE_PUZZLE"         //魔眼谜题
	UNLOCK_THEME           = "UNLOCK_THEME"            //解锁主题
)
