package battle_skill_effect_type

const (
	CHANGE_HP     = "CHANGE_HP"     //增加/减少血
	CHANGE_ATTACK = "CHANGE_ATTACK" //增加/减少攻击
	SET_HP        = "SET_HP"        //设置血量
	IMMUNE_DEADTH = "IMMUNE_DEADTH" //免疫死亡
	REBOUND       = "REBOUND"       //反弹
	DAMAGE        = "DAMAGE"        //直接受到x点伤害 / 负数表示免疫x点伤害
	DEATH         = "DEATH"         //直接死亡

	IMMUNE_DAMAGE    = "IMMUNE_DAMAGE"    //buff 免疫x点伤害
	CHANGE_DAMAGE    = "CHANGE_DAMAGE"    //buff 受到的伤害乘x倍
	SUMMONNER_ATTACK = "SUMMONNER_ATTACK" //赋予召唤物攻击
	SUMMONNER_HP     = "SUMMONNER_HP"     //赋予召唤物血量
	SPLASH           = "SPLASH"           //溅射伤害
	CHANGE_POSITION  = "CHANGE_POSITION"  //更换位置
	WEAKNESS         = "WEAKNESS"         //虚弱buff，改变攻击力
)
