package character_quality

import "train/base/data/item_id"

const (
	BLUE   = 1 // 蓝卡
	PURPLE = 2 // 紫卡
	ORANGE = 3 // 橙卡
	RED    = 4
	WHITE  = 5
)

// GetInviteCardQuality
/*
 * @description 获取邀请卡对应的乘客品质
 * @param typ
 * @return int
 */
func GetInviteCardQuality(typ int) int {
	switch typ {
	case item_id.InviteCard1:
		return BLUE
	case item_id.InviteCard2:
		return PURPLE
	case item_id.InviteCard3:
		return ORANGE
	}
	panic("invalid character quality type")
}
