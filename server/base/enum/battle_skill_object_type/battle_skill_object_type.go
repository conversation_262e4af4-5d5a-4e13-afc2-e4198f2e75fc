package battle_skill_object_type

const (
	NONE       = "NONE"
	SELF       = "SELF"       //自己
	HEAD       = "HEAD"       //从头开始连续几位
	TAIL       = "TAIL"       //从尾开始连续几位
	HEAD_INDEX = "HEAD_INDEX" //从头开始数第x位
	TAIL_INDEX = "TAIL_INDEX" //从末尾开始数第x位
	FRONT      = "FRONT"      //从当前位置往前连续x位
	BEHIND     = "BEHIND"     //从当前位置往后连续x位
	RANDOM     = "RANDOM"     //随机x个
	MIN_HP     = "MIN_HP"     //血量最少的x个
	MAX_ATTACK = "MAX_ATTACK" //攻击最高的x个
	SELF_INDEX = "SELF_INDEX" //自己对应站位，没有选最后一个

	ALL          = "ALL"          //所有
	MAX_HP       = "MAX_HP"       //血量最高的x个
	SUMMONER     = "SUMMONER"     //召唤物 （目前，仅配合trigger=SUMMON_ANY使用）
	SUMMON       = "SUMMON"       //通用召唤技能
	SUMMON_MUMMY = "SUMMON_MUMMY" //队友死亡后在他的位置上召唤
)
