package time_stone_evt

type Jackpot struct {
	DrawType               int  `bson:"drawType"`               // 1车票 2钻石
	DrawPid                int  `bson:"drawPid"`                // 乘客id
	IsConvertIntoFragments bool `bson:"isConvertIntoFragments"` // 是否转换成碎片了
	IsDiamondDiscount      bool `bson:"isDiamondDiscount"`      // 钻石抽取是不是折扣
}

type EquipMake struct {
	MakeId  int    `bson:"makeId"`  // 装备id
	TableId string `bson:"tableId"` // 打造台id
	EquipId string `bson:"equipId"` // 获得的装备id
}
type OreBattle struct{}
type BlackHoleBattle struct{}
type TowerBattle struct{}
