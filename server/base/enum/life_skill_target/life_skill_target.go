package life_skill_target

const (
	TRAIN              = "TRAIN"              //车厢
	TRAIN_SELF         = "TRAIN_SELF"         //乘客自己居住的车厢
	CHARACTER          = "CHARACTER"          // 角色
	CHARACTER_ROOMMATE = "CHARACTER_ROOMMATE" //角色（舍友，排除自己）
	ROOMMATE_NOBATTLE  = "ROOMMATE_NOBATTLE"  //没有战斗力的舍友
	CHARACTER_MEMBER   = "CHARACTER_MEMBER"   //角色（参与委托的人，排除主人）
	ENTRUST_LIFE       = "ENTRUST_LIFE"       //生活委托，一般是对委托效果做增益
	UNLOCK_ENTRUST     = "UNLOCK_ENTRUST"     //解锁生活委托（解锁前，不可刷出）
	DISPATCH_ENTRUST   = "DISPATCH_ENTRUST"   //可派遣委托
)
