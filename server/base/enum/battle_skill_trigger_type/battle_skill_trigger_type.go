package battle_skill_trigger_type

const (
	NONE              = "NONE"              //没技能
	BATTLE_START      = "BATTLE_START"      //战斗开始前
	BATTLE_AFTER      = "BATTLE_AFTER"      //战斗结束后
	ATTACK_BEFORE     = "ATTACK_BEFORE"     //开始攻击前
	ATTACK            = "ATTACK"            //攻击时（可以用来做溅射伤害
	ATTACK_AFTER      = "ATTACK_AFTER"      //攻击结束后
	ATTACK_BEFORE_ANY = "ATTACK_BEFORE_ANY" //任意队友攻击前
	HIT               = "HIT"               //被击中时
	HIT_AHEAD         = "HIT_AHEAD"         //前面的被击中
	HIT_ANY           = "HIT_ANY"           //任意队友被击中
	DEATH             = "DEATH"             //死亡时
	DEATH_AHEAD       = "DEATH_AHEAD"       //前面的死亡时
	DEATH_ANY         = "DEATH_ANY"         //任意队友死亡时

	SUMMON_ANY         = "SUMMON_ANY"         //任意队友召唤后
	ATTACK_AFTER_AHEAD = "ATTACK_AFTER_AHEAD" //前面的队友攻击结束后
	DEATH_BEFORE       = "DEATH_BEFORE"       //自己死亡前，和DEATH动画表现不一样

	ROUND_START = "ROUND_START" //对撞开始前
	ROUND_WIN   = "ROUND_WIN"   //对撞胜利时

	ENEMY_SUMMON_ANY = "ENEMY_SUMMON_ANY" //任意敌方被召唤后
)
