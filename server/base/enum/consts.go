package enum

import (
	"time"
)

// 服务器标记
const (
	ServerTypeLogin = "login" //登录服
	ServerTypeGame  = "game"  //游戏逻辑服
	ServerTypeGate  = "gate"  //网关连接服
	ServerTypeDev   = "dev"   //开发环境
	ServerTypeHttp  = "http"  //工具服
	ServerTypeTask  = "task"  //任务服
)

// 启动标记
const (
	MarkLogin = 1 << iota
	MarkGame
	MarkGate
	MarkHttp
	MarkTask
)

// 区服状态
const (
	Fix    = 1 // 维护中
	Normal     // 正常
	Close      // 暂未开放
)

// 计数器枚举
const (
	CounterItemId = "CounterItemId" //道具id自增计数
)

// LoginTokenDuringTime 登录token维持多少小时不过期
const LoginTokenDuringTime = time.Hour * 72

// PlayerIdleWaitTime 玩家被添加进player管理中心后，idle多长时间就会被清理
const PlayerIdleWaitTime = time.Minute * 0

// PlayerSid  session数据key，玩家game区号
const PlayerSid = "plr_sid"

const CODE_VERSION = "code_version"
const MIN_CLIENT_VERSION = "min_client_version"
const MAX_CLIENT_VERSION = "max_client_version"
const EXECUTE_DIR = "execute_dir"
const WORK_DIR = "work_dir"

// CLUSTER 集群标识
const CLUSTER = "_cluster_"

// 游戏服节点id
const GAME_NODE_ID = "game_node_id"

const STATE = "state"

// DistanceId 访客id
const DistanceId = "distanceId"

// IsReconnect 是否是重连登录
const IsReconnect = "isReconnect"

// ClientVersion 客户端版本
const ClientVersion = "clientVersion"
const Platform = "platform"
const OS = "os"

const CloseGuide = "CloseGuide"
const CloseUnlockFunc = "CloseUnlockFunc"

// 地图节点类型
const (
	MINE       = iota //采集物
	CHECKPOINT        //战斗关卡
	PORTAL            //传送门
	PUZZLE            //解谜
	CHEST             //宝箱
	NONE              //空节点
	QUESTION          //问答
)

const DefaultPlanet = 1001            // 第一个星球
const PlanetExploreFinishedMapId = -1 // 星球探索完后的mapId

// 节点状态
const (
	NORMAL        = ""
	READY_OFFLINE = "1"
	OFFLINE       = "2"
)
