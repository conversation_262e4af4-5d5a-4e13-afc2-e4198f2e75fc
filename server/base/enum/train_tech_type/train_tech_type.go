package train_tech_type

const (
	SHIP              = 1   //飞船数量+x
	CARRIAGE_ROLE_CNT = 2   //寝室床位+x
	TRAIN_SPEED       = 3   //列车行驶速度x%
	TRAIN_DAILY_TASK  = 4   //列车任务数量+x
	DEEP_EXPLORE      = 6   //深度探索效率提升x%
	TIME_MACHINE      = 7   //时光机加速效果提升x%
	TRAIN_LOAD        = 8   //货仓容量+x
	HERT              = 101 //爱心产出+x/h
	HERT_PERCENT      = 102 //爱心产出+x%
	STAR_DUST         = 103 //星尘产出+x/h
	STAR_DUST_PERCENT = 104 //星尘产出+x%
	ELECTRIC          = 105 //电力产出+x/h
	ELECTRIC_PERCENT  = 106 //电力产出+x%
	VITALITY          = 107 //元气值产出+x/h
	VITALITY_PERCENT  = 108 //元气值产出+x%
	WATER             = 109 //水产出+x%
	WATER_PERCENT     = 110 //水产出+x/h
)
