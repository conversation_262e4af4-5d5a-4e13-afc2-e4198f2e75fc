package db

import (
	"fmt"
	ut "train/utils"
)

// 当前最新区服ID
func RKServerAreaIdx() string { return "server_area_idx" }

// 哈希表：存储所有区服人数
func RKServerArea() string { return "server_areas" }

// RKPvpNormalRank 竞技场普通排行key
func RKPvpNormalRank(serverId int) string {
	return fmt.Sprintf("pvp_normal_rank_%d", serverId)
}

// RKPvpNormalRankTodayBackup 竞技场普通排行当日备份key 用于结算
func RKPvpNormalRankTodayBackup(serverId int) string {
	return fmt.Sprintf("pvp_normal_rank_today_backup_%d_%d", serverId, ut.Today())
}

// RKPvpNormalRankSeason 普通竞技场赛季结算倒计时key
func RKPvpNormalRankSeason() string {
	return "pvp_normal_rank_season"
}

func ServerStatus() string {
	return "server_status"
}

// 玩家普通竞技场数据
func RKPvpNormalDataOf(plrId string) string { return fmt.Sprintf("pvp_normal_data_%s", plrId) }

func RKPlayerBaseInfo(plrId string) string { return fmt.Sprintf("player_base_info_%s", plrId) }

// 区服机器人合集
func RKRobotMember(sid int) string { return fmt.Sprintf("robot_member_%d", sid) }
