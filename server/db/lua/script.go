package lua

import (
	"fmt"
	"os"
)

var (
	AllocateAreaScript   = ""
	UpdatePvpScoreScript = ""
	PvpSeasonResetScript = ""
	BackupPvpRankScript  = ""
)

func InitRedisScript() {
	AllocateAreaScript = load("alloacate_area.lua")
	UpdatePvpScoreScript = load("update_pvp_score.lua")
	PvpSeasonResetScript = load("pvp_season_reset.lua")
	BackupPvpRankScript = load("backup_pvp_rank.lua")
}

func load(url string) string {
	bytes, err := os.ReadFile(fmt.Sprintf("bin/conf/lua/%s", url))
	if err != nil {
		panic(err)
	}
	v := string(bytes)
	if v == "" {
		panic("lua script is empty")
	}
	return v
}
