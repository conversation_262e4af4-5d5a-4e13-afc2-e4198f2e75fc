package db

// 被删除的玩家数据 玩家存档数据记录
var DELETE_PLAYER = Def("delete_player_1").Schema(map[string]bool{"id": false})

// gm命令表
var GM_CMD = Def("gm_cmd").Schema(map[string]bool{"cmd": true})

// 公告
var NOTICE = Def("notice").Schema(map[string]bool{"index": true})

// 更新
var VERSION_UPDATE = Def("version_update").
	Schema(map[string]bool{
		"platform": true,  // 平台
		"version":  false, // 版本
	})

// 邀请码
var INVITE_CODE = Def("invite_code").
	Schema(map[string]bool{
		"code": true,
		"bind": false,
	})
