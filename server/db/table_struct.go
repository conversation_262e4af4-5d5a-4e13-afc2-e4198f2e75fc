package db

import (
	"train/common/pb"
)

type BigVersionUpdate struct {
	Platform    string   `bson:"platform"`    // android ios
	Version     string   `bson:"version"`     // eg: 0.0.1
	DownloadUrl string   `bson:"downloadUrl"` // 下载地址
	IsUpdate    bool     `bson:"isUpdate"`    // 是否开启更新
	Force       bool     `bson:"force"`       // 是否强制更新
	History     []string `bson:"history"`     // 历史版本记录
}

type HotfixUpdate struct {
	Platform      string   `bson:"platform"`      // android ios
	HotVersion    string   `bson:"hotVersion"`    // eg: 0.0.1
	PackageUrl    string   `bson:"packageUrl"`    // 下载地址
	ManifestUrl   string   `bson:"manifestUrl"`   // manifest 下载地址
	HotfixHistory []string `bson:"hotfixHistory"` // 历史版本记录
}

// 客户端错误上报
type ErrorReport struct {
	Uid       string `bson:"uid"`
	Platform  int    `bson:"platform"`
	Version   string `bson:"version"`
	Exception string `bson:"exception"`
	Type      string `bson:"type"`
	Level     int    `bson:"level"`
	Time      int    `bson:"time"`
}

type PvpBattleRecord struct {
	Attacker    string           `bson:"attacker"`
	Defender    string           `bson:"defender"`
	Result      int              `bson:"result"`
	Timestamp   int              `bson:"timestamp"`
	ScoreChange int              `bson:"scoreChange"`
	Score1      int              `bson:"score1"`
	Score2      int              `bson:"score2"`
	Rank1       int              `bson:"rank1"`
	Rank2       int              `bson:"rank2"`
	AttackRoles []*pb.BattleRole `bson:"attackerRoles"`
	DefendRoles []*pb.BattleRole `bson:"defenderRoles"`
}
