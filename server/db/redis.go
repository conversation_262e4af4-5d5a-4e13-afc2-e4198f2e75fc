package db

import (
	"github.com/huyangv/vmqant/log"
	"github.com/redis/go-redis/v9"
)

// redis document: https://redis.uptrace.dev/zh/guide/go-redis.html

var (
	rdb *redis.Client = nil
)

// GetRedis 获取redis连接对象
func GetRedis() *redis.Client {
	return rdb
}

// InitRedis 初始化redis
//
// Parameters:
//   - url string
func InitRedis(url string, password string) {
	if url == "" {
		panic(" 错误的redis连接地址! ")
	}
	options := &redis.Options{
		Addr: url,
	}
	if password != "" {
		options.Password = password
	}
	rdb = redis.NewClient(options)

	log.Info("redis init success! " + url)
}
