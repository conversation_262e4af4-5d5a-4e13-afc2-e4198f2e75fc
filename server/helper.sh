project_path="$(cd `dirname $0`; cd ../; pwd)"/

if [ -z $1 ]
then
    echo "命令:平台+操作, 如train wx ls"
    echo "平台: wx_*, *可以为数字，代表服务器编号如wx_0；也可以为ws，代表web后端; wb代表web前端"
    echo "命令: 内置了upload-上传，info/error-查看日志（注意加grep过滤），ip-输出对应平台所有机器ip，不传代表登录"
    exit 0
fi

arr=(${1//_/ })
if [ ${#arr[*]} == 1 ]
then
     index=0
elif [ ${#arr[*]} == 2 ]
then
    index=${arr[1]}
else
    index=${arr[1]}
    index2=${arr[2]}
fi

wx_game_ips=("**************")

wx_web_ip="************"
wx_test_ip="**************"
wx_log_ip="*************"
wx_job_ip="*************"
wx_compute_ip=("************" "*************")
wx_login_ips=("*************" "**************" "*************" "*************" "*************")
wx_crypto_ip=("*************" "*************" "*************")
wx_website_ip="************"

gl_game_ips=("************" "*************" "*************" "**************" "**************")
gl_login_ips=("**************" "*************")
gl_log_ip="*************"
gl_test_ip="************"
gl_web_ip="************"
gl_job_ip="**************"
gl_pay_ip="************"
gl_website_ip="************"

fb_game_ips=("**************")
fb_web_ip="************"

common_files=(common cypto mod router db package-lock.json package.json tsconfig.json client extend payment)
server_files=(${common_files[@]} game.js)
web_files=(${common_files[@]} fix.js web web.js statistics_server.js)
log_files=(${common_files[@]} log_server.js )
job_files=(${common_files[@]} job_server.js )
website_files=(${common_files[@]} website_server.js )

platform=${arr[0]}

remote_server_path="/root/server"
remote_web_path="/root/web"
remote_website_path="/root/website"

remote_server_test_path="/root/server_test"
remote_web_test_path="/root/web_test"

web_path=""$project_path"web/"
server_path=""$project_path"server/"
pem=""$server_path"tw_train.pem"

handleCmd(){
   ip_=$1
   cmd_=$2
   cmd_str="sudo ssh -i "$pem" root@"$ip_" "$cmd_""
   echo $cmd_str
   $cmd_str
}

if [ $platform == "wx" ]
then
    if [[ $index == "ws" || $index == "wb" ]]
    then
        ip=$wx_web_ip
    elif [ $index == "test" ]
    then
        ip=$wx_test_ip
    elif [ $index == "website" ]
    then
        ip=$wx_website_ip
    elif [ $index == "log" ]
    then
        ip=$wx_log_ip
    elif [ $index == "job" ]
    then
        ip=$wx_job_ip
    elif [ $index == "com" ]
    then
        ip=${wx_compute_ip[$index2]}
    elif [ $index == "pay" ]
    then
        ip=$wx_pay_ip
    elif [ $index == "login" ]
    then
        ip=${wx_login_ips[$index2]}
    elif [ $index == "crypto" ]
    then
        ip=${wx_crypto_ip[$index2]}
        pem=""$server_path"hotel_crypto_server.pem"
    elif [[ $index =~ ^-?[0-9]+$ ]]
    then
        ip=${wx_game_ips[$index]}
    else
        echo "ip not found"
        exit 0
    fi
elif [ $platform == "gl" ]
    then
    if [[ $index == "ws" || $index == "wb" ]]
    then
        ip=$gl_web_ip
    elif [ $index == "test" ]
    then
        ip=$gl_test_ip
    elif [ $index == "website" ]
    then
        ip=$gl_website_ip
    elif [ $index == "log" ]
    then
        ip=$gl_log_ip
    elif [ $index == "job" ]
    then
        ip=$gl_job_ip
    elif [ $index == "pay" ]
    then
        ip=$gl_pay_ip
    elif [ $index == "com" ]
    then
        ip=$gl_compute_ip
    elif [ $index == "login" ]
    then
        ip=${gl_login_ips[$index2]}
    elif [[ $index =~ ^-?[0-9]+$ ]]
    then
        ip=${gl_game_ips[$index]}
    else
        echo "ip not found"
        exit 0
    fi
    elif [ $platform == "fb" ]
        then
        if [[ $index == "ws" || $index == "wb" ]]
        then
            ip=$fb_web_ip
            remote_web_path="/root/web_fb"
        elif [[ $index =~ ^-?[0-9]+$ ]]
        then
            ip=${fb_game_ips[$index]}
        else
            echo "ip not found"
            exit 0
        fi
fi

if [ -z $ip ]
then
    echo "找不到ip"
    exit 0
fi

if [ -z $2 ]
then
    handleCmd $ip "$cmd"
    exit 0
else
    cmd=$2
fi

time=$(date "+%Y%m%d-%H%M%S")
code_name="code_"${time}".tar.gz"

if [ $cmd == "upload" ]
then
    if [ $index == "ws" ]
    then
        tar czvf "$server_path""$code_name" "${web_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_web_path"
        cmd1="tar xzvf "$remote_web_path"/"$code_name" -C "$remote_web_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    elif [ $index == "wb" ]
    then
        cm1="npm --prefix "$web_path" run build --wx"
        $cm1

        cm2="sudo scp -i "$pem" -r "$web_path"dist/* root@$ip:"$remote_web_path"/wwwroot/"
        $cm2
    elif [ $index == "website" ]
    then
        tar czvf "$server_path""$code_name" "${website_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_website_path"
        cmd1="tar xzvf "$remote_website_path"/"$code_name" -C "$remote_website_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    elif [ $index == "test" ]
    then
        tar czvf "$server_path""$code_name" "${server_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_server_test_path"
        cmd1="tar xzvf "$remote_server_test_path"/"$code_name" -C "$remote_server_test_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    elif [ $index == "log" ]
    then
        tar czvf "$server_path""$code_name" "${log_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_server_path"
        cmd1="tar xzvf "$remote_server_path"/"$code_name" -C "$remote_server_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    elif [ $index == "job" ]
    then
        tar czvf "$server_path""$code_name" "${job_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_server_path"
        cmd1="tar xzvf "$remote_server_path"/"$code_name" -C "$remote_server_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    elif [ $index == "com" ]
    then
        tar czvf "$server_path""$code_name" "${server_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_server_path"
        cmd1="tar xzvf "$remote_server_path"/"$code_name" -C "$remote_server_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    elif [ $index == "pay" ]
    then
        tar czvf "$server_path""$code_name" "${server_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_server_path"
        cmd1="tar xzvf "$remote_server_path"/"$code_name" -C "$remote_server_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    elif [ $index == "login" ]
    then
        tar czvf "$server_path""$code_name" "${server_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_server_path"
        cmd1="tar xzvf "$remote_server_path"/"$code_name" -C "$remote_server_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    elif [ $index == "crypto" ]
    then
        tar czvf "$server_path""$code_name" "${server_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_server_path"
        cmd1="tar xzvf "$remote_server_path"/"$code_name" -C "$remote_server_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    else
        tar czvf "$server_path""$code_name" "${server_files[@]}"
        sudo scp -r -i "$pem" "$server_path""$code_name" root@"$ip":"$remote_server_path"
        cmd1="tar xzvf "$remote_server_path"/"$code_name" -C "$remote_server_path""
        handleCmd $ip "$cmd1"
        rm -rf "$server_path""$code_name"
    fi
elif [ $cmd == "reload" ]
then
    if [ $index == "ws" ]
    then
        cmd1="/root/web/reload.sh"
        handleCmd $ip "$cmd1"
    elif [ $index == "test" ]
    then
        cmd1="/root/server_test/reload.sh"
        handleCmd $ip "$cmd1"
    else
        cmd1="/root/server/reload.sh"
        handleCmd $ip "$cmd1"
    fi
elif [ $cmd == "info" ]
then
    cmd1="tail -f /root/server/log/info.log"
    handleCmd $ip "$cmd1"
elif [ $cmd == "error" ]
then
    cmd1="tail -f /root/server/log/error.log"
    handleCmd $ip "$cmd1"
elif [ $cmd == "ip" ]
then
    echo "game: "$wx_game_ips""
    echo "web: "$wx_web_ip""
else
    handleCmd $ip "$cmd"
fi
