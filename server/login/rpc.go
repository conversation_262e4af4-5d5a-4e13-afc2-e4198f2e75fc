package login

import "github.com/huyangv/vmqant/log"

func (this *Login) InitRpc() {
	this.GetServer().Register("OnLeave", this.leave)
	this.GetServer().Register("/rollbackOffline", this.rollbackOffline)
	this.GetServer().Register("/rollbackDone", this.rollbackDone)
	this.middleware.Wrap("/Offline", this.Offline)
}

// leave 玩家离线
//
// Parameters:
//   - uid string
//
// Returns:
//   - result interface{}
func (this *Login) leave(uid string) (result interface{}, err string) {
	log.Warning("OnLeave :%s", uid)
	return
}

// rollbackOffline 全体玩家存档回滚开始
//
// Returns:
//   - result interface{}
func (this *Login) rollbackOffline() (result interface{}, err string) {
	this.Offline(false)
	return nil, ""
}

// rollbackDone 全体玩家存档回滚完成
//
// Returns:
//   - result interface{}
func (this *Login) rollbackDone() (result interface{}, err string) {
	this.Normal()
	return nil, ""
}
