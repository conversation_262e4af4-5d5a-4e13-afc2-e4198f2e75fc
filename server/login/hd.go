package login

import (
	"context"
	"fmt"
	"time"
	"train/base/enum"
	"train/base/enum/channel"
	"train/base/enum/user_type"
	"train/base/structs"
	com "train/common"
	"train/common/pb"
	"train/db"
	"train/sdk/apple"
	"train/sdk/fb"
	"train/sdk/google"
	"train/sdk/tap_tap"
	"train/sdk/wx"
	"train/sdk/wx_app"
	ut "train/utils"
	"github.com/golang-jwt/jwt/v4"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitMsgHD 自动生成，不要在这个方法添加任何内容。
func InitMsgHD(this *Login) {
	// 客户端发起注册,返回一个token走免密登录
	this.middleware.Wrap("C2S_RegisterMessage", this.C2sRegisterMessageHandler)
	// 用户使用账号密码登录
	this.middleware.Wrap("C2S_LoginAccountMessage", this.C2sLoginAccountMessageHandler)
	// 用户使用免密token登录
	this.middleware.Wrap("C2S_LoginByTokenMessage", this.C2sLoginByTokenMessageHandler)
	// 游客登录,返回免密token登录
	this.middleware.Wrap("C2S_LoginGuestMessage", this.C2sLoginGuestMessageHandler)
	// fb登录,返回免密token登录
	this.middleware.Wrap("C2S_LoginFBMessage", this.C2sLoginFBMessageHandler)
	// 绑定fb账号,返回免密token登录
	this.middleware.Wrap("C2S_BindFBMessage", this.C2sBindFBMessageHandler)
	// 苹果登录,返回免密token登录
	this.middleware.Wrap("C2S_LoginAppleMessage", this.C2sLoginAppleMessageHandler)
	// 绑定苹果,返回免密token登录
	this.middleware.Wrap("C2S_BindAppleMessage", this.C2sBindAppleMessageHandler)
	// 谷歌登录,返回免密token登录
	this.middleware.Wrap("C2S_LoginGoogleMessage", this.C2sLoginGoogleMessageHandler)
	// 绑定谷歌账号,返回免密token登录
	this.middleware.Wrap("C2S_BindGoogleMessage", this.C2sBindGoogleMessageHandler)
	// 微信小程序登录,返回免密token登录
	this.middleware.Wrap("C2S_LoginWxMessage", this.C2sLoginWxMessageHandler)
	// 微信App登录,返回免密token登录
	this.middleware.Wrap("C2S_LoginWxAppMessage", this.C2sLoginWxAppMessageHandler)
	// 苹果登录,返回免密token登录
	this.middleware.Wrap("C2S_LoginTapTapMessage", this.C2sLoginTapTapMessageHandler)
	// 实名认证
	this.middleware.Wrap("C2S_CertificationMessage", this.C2sCertificationMessageHandler)
	// 选区
	this.middleware.Wrap("C2S_EnterGameServerMessage", this.C2sEnterGameServerMessageHandler)
	// 取消注销
	this.middleware.Wrap("C2S_CancelSignOutMessage", this.C2sCancelSignOutMessageHandler)
}
func (this *Login) C2sRegisterMessageHandler(session gate.Session, msg *pb.C2S_RegisterMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	// if ut.IsEmpty(msg.Username) || ut.IsEmpty(msg.Password) {
	// 	return pb.ProtoMarshal(&pb.S2C_RegisterResultMessage{
	// 		Code: 2, //用户名或密码不规范
	// 	})
	// }
	// user, err2 := structs.NewUser("", msg.Username, msg.Password, msg.Platform, msg.Os)
	// if mongo.IsDuplicateKeyError(err2) {
	// 	return pb.ProtoMarshal(&pb.S2C_RegisterResultMessage{
	// 		Code: 1, //已经存在的用户名
	// 	})
	// }
	// return pb.ProtoMarshal(afterUserRegisterSuccess(user, session, msg.GetDistanceId()))
	return nil
	//@action-code-end
}
func (this *Login) C2sLoginAccountMessageHandler(session gate.Session, msg *pb.C2S_LoginAccountMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	// user := structs.GetUserFromDbByAccount(msg.Username, msg.Password)
	// if user == nil {
	// 	if com.IsDebug() {
	// 		// debug模式下账号登录如果账号不存在就自动注册并返回登录token
	// 		user, _ = structs.NewUser("", msg.Username, msg.Password, "web", "debug")
	// 		if user == nil {
	// 			return pb.ProtoMarshal(&pb.S2C_RegisterResultMessage{
	// 				Code: 1, //已经存在的用户名
	// 			})
	// 		}
	// 		afterUserRegisterSuccess(user, session, msg.Common.GetDistanceId())
	// 		renewToken = false
	// 	} else {
	// 		return pb.ProtoMarshal(&pb.S2C_LoginResultMessage{
	// 			Code: 1,
	// 		})
	// 	}
	// }
	// log.Info("LoginAccount success, uid: %s, common: %v", user.GetUid(), msg.Common)
	// return pb.ProtoMarshal(handleLogin(user, session, msg.Common))
	return nil
	//@action-code-end
}
func (this *Login) C2sLoginByTokenMessageHandler(session gate.Session, msg *pb.C2S_LoginByTokenMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_LoginResultMessage{
			Code: code,
		}
	}
	if !checkPackageSign(msg.Common.GetPackageSign()) {
		return send(InvitePackageError)
	}
	token := com.TryDecodeToken(msg.Token, true)
	if token == nil {
		// token解析失败,说明token不正确
		return send(2)
	}
	id := token["id"]
	if id == nil {
		log.Warning("token解析成功了 但是没得用户id.")
		return send(4)
	}
	user := structs.GetUserFromDbById(cast.ToString(id))
	if user == nil {
		//用从token中绑定的id来查询用户,但是没查到 那肯定是有个错误
		log.Warning("Search for user:%v error.", id)
		return send(5)
	}
	session.SetBatch(map[string]string{
		"token":          msg.Token,
		enum.IsReconnect: ut.If(msg.GetIsReconnect(), "1", ""),
	})
	return handleLogin(user, session, msg.Common)
	//@action-code-end
}
func (this *Login) C2sLoginGuestMessageHandler(session gate.Session, msg *pb.C2S_LoginGuestMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	// 游客的登录名是 guest+纳秒时间戳+4位随机数
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_LoginResultMessage{
			Code: code,
		}
	}
	if !checkPackageSign(msg.Common.GetPackageSign()) {
		return send(InvitePackageError)
	}
	if com.GetChannel() == channel.TAPTAP { //临时这么加
		return send(InvitePackageError)
	}
	user := &structs.User{UserType: user_type.GUEST}
	user.UserName = fmt.Sprintf("guest%d%d", time.Now().UnixNano(), ut.Random(1000, 9999))
	return handleLogin(user, session, msg.Common)
	//@action-code-end
}
func (this *Login) C2sLoginFBMessageHandler(session gate.Session, msg *pb.C2S_LoginFBMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	ret, e := fb.Login(msg.Token, msg.UserId)
	if e != nil {
		log.Error("LoginFacebook HD error : %v", e.Error())
		return &pb.S2C_RegisterResultMessage{
			Code: 1, // facebook第三方登录出错
		}
	}
	info := &structs.User{UserName: ret.Id, NickName: ret.Name, AvatarUrl: ret.Picture.Data.Url, UserType: user_type.FB}
	return handleLogin(info, session, msg.GetCommon())
	//@action-code-end
}
func (this *Login) C2sBindFBMessageHandler(session gate.Session, msg *pb.C2S_BindFBMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_BindResultMessage{Code: code}
	}
	uid := session.GetUserID()
	user := structs.GetUserFromDbById(uid)
	if user == nil {
		return send(1)
	}
	// 游客登录才可以绑定
	if user.UserType != user_type.GUEST {
		log.Error("fbBind not guest uid: %v", uid)
		return send(2)
	}
	token, userId := msg.GetToken(), msg.GetUserId()
	// 检测该fb账号是否绑定过用户
	otherUser := structs.GetUserFromDbByUserName(userId)
	if otherUser != nil {
		log.Error("fbBind already bind uid: %v, openid: %v", uid, userId)
		return send(3)
	}
	ret, e := fb.Login(token, userId)
	if e != nil {
		return send(4)
	}
	user.UserType = user_type.APPLE
	user.NickName = ret.Name
	user.AvatarUrl = ret.Picture.Data.Url
	user.UserName = userId
	e = user.SaveToDb(ut.FieldToBsonAuto(user))
	if e != nil {
		return send(5)
	}
	return &pb.S2C_BindResultMessage{UserType: user.UserType, NickName: user.NickName, AvatarUrl: user.AvatarUrl}
	//@action-code-end
}
func (this *Login) C2sLoginAppleMessageHandler(session gate.Session, msg *pb.C2S_LoginAppleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	_, e := apple.AppleLoginCheck(msg.Code, msg.Token, msg.UserId)
	if e != nil {
		log.Error("LoginApple HD error : %v", e.Error())
		return &pb.S2C_RegisterResultMessage{
			Code: 1, // 苹果第三方登录出错
		}
	}
	info := &structs.User{UserName: msg.UserId, NickName: msg.NickName, UserType: user_type.APPLE}
	return handleLogin(info, session, msg.GetCommon())
	//@action-code-end
}
func (this *Login) C2sBindAppleMessageHandler(session gate.Session, msg *pb.C2S_BindAppleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_BindResultMessage{Code: code}
	}
	uid := session.GetUserID()
	user := structs.GetUserFromDbById(uid)
	if user == nil {
		return send(1)
	}
	// 游客登录才可以绑定
	if user.UserType != user_type.GUEST {
		log.Error("appleBind not guest uid: %v", uid)
		return send(2)
	}
	code, token, userId, nickName := msg.GetCode(), msg.GetToken(), msg.GetUserId(), msg.GetNickName()
	// 检测该apple账号是否绑定过用户
	otherUser := structs.GetUserFromDbByUserName(userId)
	if otherUser != nil {
		log.Error("appleBind already bind uid: %v, openid: %v", uid, userId)
		return send(3)
	}
	_, e := apple.AppleLoginCheck(code, token, userId)
	if e != nil {
		return send(4)
	}
	user.UserType = user_type.APPLE
	user.NickName = nickName
	user.UserName = userId
	e = user.SaveToDb(ut.FieldToBsonAuto(user))
	if e != nil {
		return send(5)
	}
	return &pb.S2C_BindResultMessage{UserType: user.UserType, NickName: user.NickName}
	//@action-code-end
}
func (this *Login) C2sLoginGoogleMessageHandler(session gate.Session, msg *pb.C2S_LoginGoogleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	ret, e := google.Login(msg.GetToken())
	if e != nil {
		log.Error("LoginGoogle HD error : %v", e.Error())
		return &pb.S2C_RegisterResultMessage{
			Code: 1, // 谷歌第三方登录出错
		}
	}
	info := &structs.User{UserName: ret.UserId, NickName: msg.GetNickName(), AvatarUrl: msg.GetAvatarUrl(), UserType: user_type.GOOGLE}
	return handleLogin(info, session, msg.GetCommon())
	//@action-code-end
}
func (this *Login) C2sBindGoogleMessageHandler(session gate.Session, msg *pb.C2S_BindGoogleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_BindResultMessage{Code: code}
	}
	uid := session.GetUserID()
	user := structs.GetUserFromDbById(uid)
	if user == nil {
		return send(1)
	}
	// 游客登录才可以绑定
	if user.UserType != user_type.GUEST {
		log.Error("gooleBind not guest uid: %v", uid)
		return send(2)
	}
	ret, e := google.Login(msg.GetToken())
	if e != nil {
		log.Error("bindGoogle error : %v", e.Error())
		return send(4)
	}
	userId := ret.UserId
	// 检测该google账号是否绑定过用户
	otherUser := structs.GetUserFromDbByUserName(userId)
	if otherUser != nil {
		log.Error("googleBind already bind uid: %v, openid: %v", uid, userId)
		return send(3)
	}
	user.UserType = user_type.APPLE
	user.NickName = msg.GetNickName()
	user.AvatarUrl = msg.GetAvatarUrl()
	user.UserName = userId
	e = user.SaveToDb(ut.FieldToBsonAuto(user))
	if e != nil {
		return send(5)
	}
	return &pb.S2C_BindResultMessage{UserType: user.UserType, NickName: user.NickName, AvatarUrl: user.AvatarUrl}
	//@action-code-end
}
func (this *Login) C2sLoginWxMessageHandler(session gate.Session, msg *pb.C2S_LoginWxMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	e, ret := wx.GetOpenIdByCode(msg.GetCode())
	if e != 0 {
		log.Error("LoginWx HD error : %v", e)
		return &pb.S2C_RegisterResultMessage{
			Code: 1,
		}
	}
	if ret.Unionid == "" {
		log.Error("LoginWx Unionid not found: %+v", ret)
		return &pb.S2C_RegisterResultMessage{
			Code: 2,
		}
	}
	info := &structs.User{UserName: ret.Unionid, NickName: msg.GetNickName(), AvatarUrl: msg.GetAvatarUrl(), UserType: user_type.WX, Openid: ret.Openid}
	return handleLogin(info, session, msg.GetCommon())
	//@action-code-end
}
func (this *Login) C2sLoginWxAppMessageHandler(session gate.Session, msg *pb.C2S_LoginWxAppMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	e, ret := wx_app.GetOpenIdByCode(msg.GetCode())
	if e != 0 {
		log.Error("LoginAppWx HD error : %v", e)
		return &pb.S2C_RegisterResultMessage{
			Code: 1,
		}
	}
	e, ret2 := wx_app.GetUserInfo(ret.AccessToken, ret.Openid)
	if e != 0 {
		log.Error("LoginAppWx HD error : %v", e)
		return &pb.S2C_RegisterResultMessage{
			Code: 2,
		}
	}
	if ret2.Unionid == "" {
		log.Error("LoginAppWx Unionid not found: %+v", ret2)
		return &pb.S2C_RegisterResultMessage{
			Code: 3,
		}
	}
	info := &structs.User{UserName: ret2.Unionid, NickName: ret2.NickName, AvatarUrl: ret2.Headimgurl, UserType: user_type.WX, WxAppOpenid: ret.Openid}
	return handleLogin(info, session, msg.GetCommon())
	//@action-code-end
}
func (this *Login) C2sLoginTapTapMessageHandler(session gate.Session, msg *pb.C2S_LoginTapTapMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_RegisterResultMessage{
			Code: code,
		}
	}
	if !checkPackageSign(msg.Common.GetPackageSign()) {
		return send(InvitePackageError)
	}
	ret := tap_tap.Login(msg.GetKid(), msg.GetMacKey())
	if ret.Code != 0 {
		log.Error("LoginTapTap HD error : %v", ret)
		return send(1)
	}
	info := &structs.User{UserName: ret.Unionid, NickName: ret.Name, AvatarUrl: ret.Avatar, UserType: user_type.TAP_TAP, Openid: ret.Openid}
	return handleLogin(info, session, msg.GetCommon())
	//@action-code-end
}
func (this *Login) C2sCertificationMessageHandler(session gate.Session, msg *pb.C2S_CertificationMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	// 实名认证是在登录服完成
	uid := session.GetUserID()
	age, code := structs.TryUpdateUserCertification(uid, msg.RealName, msg.IdCard)
	return &pb.S2C_CertificationResultMessage{
		Code: cast.ToInt32(code),
		Age:  cast.ToInt32(age),
	}
	//@action-code-end
}
func (this *Login) C2sEnterGameServerMessageHandler(session gate.Session, msg *pb.C2S_EnterGameServerMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	sid := com.GetPlayerSid(session)
	if sid != -1 {
		//TODO 已经有其他区在线了，是否需要踢下线操作？
	}
	id := session.GetUserID()
	if id == "" {
		log.Warning("用户发起选区，但是session没有用户id.")
		return &pb.S2C_EnterGameServerResultMessage{
			Code: -1,
		}
	}
	sid = cast.ToInt(msg.Sid)
	// 验证msg.Token
	session.SetPush(enum.PlayerSid, cast.ToString(sid))
	if msg.CloseGuide {
		session.Set(enum.CloseGuide, "1")
	}
	// if msg.CloseUnlockFunc {
	// 	session.Set(enum.CloseUnlockFunc, "1")
	// }
	// this.Invoke(com.CallGameServer(sid), "OnPlayerLogin", session)
	log.Info("用户:%s，选择区服:%d %v", id, sid, msg.CloseGuide)
	return &pb.S2C_EnterGameServerResultMessage{
		Code: 0,
	}
	//@action-code-end
}
func (this *Login) C2sCancelSignOutMessageHandler(session gate.Session, msg *pb.C2S_CancelSignOutMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	get := session.Get(enum.PlayerSid)
	replay := &pb.S2C_SignOutRespMessage{}
	// 保证注销session和目标用户相符合
	if ut.IsEmpty(get) {
		replay.Code = 1
		return replay
	}
	user := structs.GetUserFromDbById(get)
	// 账号未处于注销状态 无需操作
	if user == nil || !user.IsSignOuting {
		replay.Code = 2
		return replay
	}
	// 账号已经被注销，无法撤销了
	if user != nil && user.IsSignOuting && user.SignOutTime < ut.Now() {
		replay.Code = 3
		return replay
	}
	// 取消注销
	user.CancelSignOut()
	return replay
	//@action-code-end
}

// @logic-code-start 自定义代码必须放在start和end之间
func setLoginCommon(session gate.Session, common *pb.LoginCommon) {
	session.SetBatch(map[string]string{
		enum.DistanceId:    common.GetDistanceId(),
		enum.ClientVersion: common.GetVer(),
		enum.Platform:      common.GetPlatform(),
		enum.OS:            common.GetOs(),
		enum.PlayerSid:     "1",
		enum.CloseGuide:    ut.If(common.GetCloseGuide(), "1", ""),
	})
}

const (
	InviteCodeNone     = 70  // 新玩家注册必须携带邀请码
	InviteCodeInvalid  = 71  // 邀请码无效
	InviteCodeUsed     = 72  // 邀请码被他人使用
	InviteCodeVoid     = 74  // 邀请码被作废 不允许登录 or 使用
	InviteCodeNotStart = 75  // 邀请码未开始
	InviteCodeExpired  = 76  // 邀请码已过期
	InvitePackageError = 101 // 包签名错误
)

func judgeInviteCode(doc bson.M, usrId string) int {
	if !com.IsInvite() {
		return 0
	}
	startTime := doc["startTime"].(int64)
	endTime := doc["endTime"].(int64)
	state := doc["state"].(int32)
	bind := doc["bind"].(string)
	now := int64(ut.Now())
	if usrId == "" && bind != "" {
		return InviteCodeUsed
	}
	if state != 0 {
		return InviteCodeVoid
	}
	if now < startTime {
		return InviteCodeNotStart
	}
	if bind == "" && now > endTime {
		return InviteCodeExpired
	}
	return 0
}

func handleOldUsrWithInviteCode(user *structs.User, code string) int {
	if !com.IsInvite() {
		return 0
	}
	r := db.INVITE_CODE.GetCollection().FindOne(context.TODO(), bson.M{"bind": user.GetUid()})
	var result bson.M
	err := r.Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 72 // 在数据库中查不到玩家绑定的邀请码
		}
	}
	return judgeInviteCode(result, user.GetUid())
}

func handleNewUsrWithInviteCode(code string) int {
	if !com.IsInvite() {
		return 0
	}
	if code == "" {
		return InviteCodeNone
	}
	r := db.INVITE_CODE.GetCollection().FindOne(context.TODO(), bson.M{"code": code})
	var result bson.M
	err := r.Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return InviteCodeInvalid
		}
	}
	return judgeInviteCode(result, "")
}

// handleLogin 处理登录公共逻辑
func handleLogin(info *structs.User, session gate.Session, common *pb.LoginCommon) *pb.S2C_LoginResultMessage {

	user := info
	isNew := false
	if info.UserName != "" {
		user = structs.GetUserFromDbByUserName(info.UserName)
	}
	if user.GetUid() == "" {
		// 新注册时判断邀请码
		if code := handleNewUsrWithInviteCode(common.InviteCode); code != 0 {
			return &pb.S2C_LoginResultMessage{Code: int32(code)}
		}
		user = structs.NewUser(info)
		isNew = true
	}

	// 登录时判断邀请码
	if !isNew {
		if code := handleOldUsrWithInviteCode(user, common.InviteCode); code != 0 {
			return &pb.S2C_LoginResultMessage{Code: int32(code)}
		}
	}

	setLoginCommon(session, common)
	user.Bind(session)
	if isNew {
		user.TaTrackRegister()
		// 绑定邀请码
		if com.IsInvite() && common.InviteCode != "" {
			db.INVITE_CODE.GetCollection().FindOneAndUpdate(context.TODO(), bson.M{"code": common.InviteCode}, bson.M{"$set": bson.M{"bind": user.GetUid()}})
		}
	}
	if user.IsSignOuting {
		if ut.Now() < user.SignOutTime {
			// 存一下uid 取消注销请求时校对session
			session.Set(enum.PlayerSid, user.GetUid())
			// 账号注销中
			return &pb.S2C_LoginResultMessage{
				Code: 4,
				UserInfo: &pb.UserInfo{
					Uid:         user.GetUid(),
					SignOutTime: cast.ToInt32(user.SignOutTime - ut.Now()),
				},
			}
		} else {
			// 返回用户名或密码错误，改账号不让登录了
			return &pb.S2C_LoginResultMessage{
				Code: 1,
			}
		}
	}
	user.LastLoginTime = ut.Now()
	if info.NickName != "" {
		user.NickName = info.NickName
	}
	if info.AvatarUrl != "" {
		user.AvatarUrl = info.AvatarUrl
	}
	// 存一下db
	bson := ut.FieldToBsonAuto(user)
	user.SaveToDb(bson)
	userInfo := &pb.UserInfo{
		Uid:       user.GetUid(),
		Age:       user.GetAge(),
		NickName:  user.NickName,
		AvatarUrl: user.AvatarUrl,
		UserType:  user.UserType,
	}
	userType := user.UserType
	if userType != user_type.GUEST {
		if userType == user_type.WX {
			if common.Platform == "wx" {
				userInfo.Openid = user.Openid
			} else {
				userInfo.Openid = user.WxAppOpenid
			}
		} else {
			userInfo.Openid = user.UserName
		}
	}
	token := session.Get("token")
	// 不是走token登录的，下发token
	if token == "" {
		userInfo.LToken = user.GenToken()
	}
	log.Info("[%s] login", user.GetUid())
	return &pb.S2C_LoginResultMessage{
		Code:     0,
		UserInfo: userInfo,
	}
}

// afterUserRegisterSuccess 用户注册成功后
func afterUserRegisterSuccess(user *structs.User, session gate.Session, distanceId string) *pb.S2C_RegisterResultMessage {
	// 注册成功后生成登录token，并返回给客户端
	token := com.GetTokenByRsa(&jwt.MapClaims{
		"exp": time.Now().Add(enum.LoginTokenDuringTime).Unix(),
		"id":  user.GetUid(),
	})
	session.Set(enum.DistanceId, distanceId)
	user.Bind(session)
	user.CreateTime = ut.Now()
	// 存一下db
	bson := ut.FieldToBsonAuto(user)
	user.SaveToDb(bson)
	user.TaTrackRegister()
	return &pb.S2C_RegisterResultMessage{
		Id:    user.GetUid(),
		Code:  0,
		Token: token,
	}
}

//@logic-code-end
