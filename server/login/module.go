package login

import (
	"os"
	"train/base/enum"
	"train/net"

	mqrpc "github.com/huyangv/vmqant/rpc"

	"github.com/gin-gonic/gin"
	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/huyangv/vmqant/server"
)

var Module = func() module.Module {
	return new(Login)
}

type Login struct {
	basemodule.BaseModule
	middleware *net.Middleware
	httpServer *gin.Engine
}

func (this *Login) GetType() string {
	return "login" //很关键,需要与配置文件中的Module配置对应
}

func (this *Login) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// 最低支持的客户端版本
func (this *Login) GetMinClientVersion() string {
	return "1.0.0"
}

// 最大支持的客户端版本
func (this *Login) GetMaxClientVersion() string {
	return ""
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Login) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
}

func (this *Login) OnInit(app module.App, settings *conf.ModuleSettings) {
	// 可执行文件所在目录
	eDir, _ := os.Executable()
	// 工作目录
	wDir, _ := os.Getwd()

	this.BaseModule.OnInit(this, app, settings, func(op *server.Options) {
		op.Metadata = map[string]string{
			enum.CODE_VERSION:       this.Version(),
			enum.MIN_CLIENT_VERSION: this.GetMinClientVersion(),
			enum.MAX_CLIENT_VERSION: this.GetMaxClientVersion(),
			enum.STATE:              enum.NORMAL,
			enum.EXECUTE_DIR:        eDir,
			enum.WORK_DIR:           wDir,
		}
	})

	// 作为集群启动
	//if script.IsClusterMod() {
	//	this.GetServer().Options().Metadata["cluster"] = cast.ToString(script.Cluster)
	//}
	this.middleware = net.Create(this)
	InitMsgHD(this)
	this.InitRpc()
	this.InitHttp()
}

func (this *Login) Run(closeSig chan bool) {
	//run
	go func() {
		this.RunHttp()
	}()
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	this.Offline(false)
	//stop
}

func (this *Login) OnDestroy() {
	this.BaseModule.OnDestroy()
}

func (this *Login) GetModuleServer() server.Server {
	return this.GetServer()
}
func (this *Login) GetRpcServer() mqrpc.RPCServer {
	return this.GetServer().GetRpcServer()
}

// 这个参数没使用，只是为了保持与game module一致
func (this *Login) Offline(kick bool) {
	this.setMeta(enum.STATE, enum.OFFLINE)
}

func (this *Login) Normal() {
	this.setMeta(enum.STATE, enum.NORMAL)
}

func (this *Login) setMeta(key string, value string) {
	this.GetServer().Options().Metadata[key] = value
	this.GetServer().ServiceRegister()
}
