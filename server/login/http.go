package login

import (
	"context"
	"fmt"
	"net/http"
	"train/base/enum"
	comm "train/common"
	"train/db"
	ut "train/utils"

	"github.com/gin-gonic/gin"
	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func (this *Login) InitHttp() {
	gin.SetMode(ut.If(comm.IsDebug(), gin.DebugMode, gin.ReleaseMode))
	s := gin.Default()
	this.httpServer = s

	s.Use(func(ctx *gin.Context) {
		ctx.Header("Access-Control-Allow-Origin", "*")
		ctx.Header("Access-Control-Allow-Methods", "POST, GET")
		ctx.Header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization")
		ctx.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
		ctx.Header("Access-Control-Allow-Credentials", "true")
		if ctx.Request.Method == "OPTIONS" {
			ctx.AbortWithStatus(204)
			return
		}
		ctx.Next()
	})

	s.GET("/login/checkVersion", this.checkVersion)
	s.GET("/login/getSwitch", this.getSwitch)
}

func (this *Login) RunHttp() {
	port, ok := this.GetModuleSettings().Settings["Port"]
	if !ok {
		log.Warning("Login Http port not found")
		return
	}
	log.Debug("Login Http模块已经启动,端口:%v", port)
	this.httpServer.Run(fmt.Sprintf(":%v", port))
}

// 检车更新
func (this *Login) checkVersion(c *gin.Context) {
	version := c.Query("version")
	platform := c.Query("platform")

	dbResult := db.VERSION_UPDATE.GetCollection().FindOne(context.TODO(), bson.M{"platform": platform})
	//先看大版本
	bigVersion := db.BigVersionUpdate{}
	err := dbResult.Decode(&bigVersion)
	if err != nil {
		// 不存在平台？
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusOK, gin.H{"code": enum.INVALID_PLATFORM})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"code": enum.NOT_UPDATE,
		})
		log.Error("checkVersion-bigVersion error: %s", err.Error())
		return
	}
	if bigVersion.IsUpdate && bigVersion.Force && ut.CmpVersion(bigVersion.Version, version) > 0 { //去商店下载
		c.JSON(http.StatusOK, gin.H{
			"code":        enum.BIG_UPDATE,
			"downloadUrl": bigVersion.DownloadUrl,
		})
		return
	}

	//热更
	hotUpdate := db.HotfixUpdate{}
	err = dbResult.Decode(&hotUpdate)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": enum.NOT_UPDATE,
		})
		log.Error("checkVersion-hotUpdate error: %s", err.Error())
		return
	}
	if ut.CmpVersion(hotUpdate.HotVersion, version) > 0 { //走热更下载
		c.JSON(http.StatusOK, gin.H{
			"code":       enum.HOT_UPDATE,
			"url":        hotUpdate.ManifestUrl,
			"packageUrl": hotUpdate.PackageUrl,
			"newVer":     hotUpdate.HotVersion,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code": enum.NOT_UPDATE,
	})
}

// 获取功能开关
func (this *Login) getSwitch(c *gin.Context) {
	// platform := c.Query("platform")
	// version := c.Query("version")
	c.JSON(http.StatusOK, gin.H{
		"switch": 0,
	})
}
