{"setting": {"keyCompare": "global=gl,inland=in"}, "list": {"test": {"game": [{"name": "[国内]列车测试服务器", "host": "************", "pem": "tw_hotel.pem", "compress": "bin", "compressIgnore": "*.go logs dist bi", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/train_test", "reload": "pm2 reload 7", "check": "pm2 show 7 |grep 'status' |awk '{print $4}'"}]}, "taptap": {"game": [{"name": "[Taptap]列车测试服务器", "host": "**************", "pem": "tw_hotel.pem", "compress": "bin", "compressIgnore": "*.go logs dist bi", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/train_test", "reload": "pm2 reload 7", "check": "pm2 show 7 |grep 'status' |awk '{print $4}'"}]}, "wx": {"game": [{"name": "[Taptap]列车测试服务器", "host": "**************", "pem": "tw_hotel.pem", "compress": "bin", "compressIgnore": "*.go logs dist bi", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/train_test", "reload": "pm2 reload 7", "check": "pm2 show 7 |grep 'status' |awk '{print $4}'"}]}, "inland": {"game": [{"name": "列车逻辑1", "host": "***************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "logs dist bi server.json", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/server", "reload": "pm2 reload 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}, {"name": "列车逻辑2", "host": "**************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "logs dist bi server.json", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/server", "reload": "pm2 reload 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}], "gate": [{"name": "列车网关1", "host": "************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "logs dist bi server.json", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/server", "reload": "pm2 reload 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}], "login": [{"name": "列车登录1", "host": "***************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "logs dist bi server.json", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/server", "reload": "pm2 reload 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}], "http": [{"name": "列车工具服", "host": "**************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "logs dist bi server.json", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/server", "reload": "pm2 restart 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}], "task": [{"name": "列车任务服", "host": "**************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "logs dist bi server.json", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/server", "reload": "pm2 restart 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}], "log": [{"name": "列车日志服", "host": "***************", "pem": "tw_train.pem", "compress": "", "compressIgnore": "*", "build": "GOOS=linux GOARCH=amd64 go build -C ../rlog -o ../server ", "directory": "/root", "reload": "pm2 restart 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}]}}}