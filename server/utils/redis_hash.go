package ut

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/redis/go-redis/v9"
)

// RedisHashSerializer Redis Hash 序列化工具
type RedisHashSerializer struct {
	client redis.Cmdable
}

// NewRedisHashSerializer 创建新的序列化器
func NewRedisHashSerializer(client redis.Cmdable) *RedisHashSerializer {
	return &RedisHashSerializer{client: client}
}

// HSetStruct 将整个结构体存储到 Redis Hash
// 使用 JSON 序列化结构体，然后将每个字段作为 hash 字段存储
// func (r *RedisHashSerializer) HSetStruct(ctx context.Context, key string, v interface{}) error {
// 	if v == nil {
// 		return fmt.Errorf("input is nil")
// 	}

// 	// 先用 JSON 序列化为 map[string]interface{}
// 	jsonData, err := json.Marshal(v)
// 	if err != nil {
// 		return fmt.Errorf("json marshal failed: %w", err)
// 	}

// 	var doc map[string]interface{}
// 	err = json.Unmarshal(jsonData, &doc)
// 	if err != nil {
// 		return fmt.Errorf("json unmarshal to map failed: %w", err)
// 	}

// 	// 将 map 转换为 Redis Hash 格式
// 	hashData := make(map[string]interface{})
// 	for k, v := range doc {
// 		// 将值序列化为 JSON 字符串
// 		valueData, err := json.Marshal(v)
// 		if err != nil {
// 			return fmt.Errorf("marshal field %s failed: %w", k, err)
// 		}
// 		hashData[k] = string(valueData)
// 	}

// 	if len(hashData) == 0 {
// 		return nil
// 	}

// 	return r.client.HSet(ctx, key, hashData).Err()
// }

// HGetStruct 从 Redis Hash 读取整个结构体
func (r *RedisHashSerializer) HGetStruct(ctx context.Context, key string, v interface{}) error {
	if v == nil {
		return fmt.Errorf("output is nil")
	}

	result := r.client.HGetAll(ctx, key)
	if result.Err() != nil {
		return result.Err()
	}

	data := result.Val()
	if len(data) == 0 {
		return redis.Nil
	}

	// 将 Redis Hash 数据转换回 map[string]interface{}
	doc := make(map[string]interface{})
	for k, v := range data {
		var value interface{}
		err := json.Unmarshal([]byte(v), &value)
		if err != nil {
			return fmt.Errorf("unmarshal field %s failed: %w", k, err)
		}
		doc[k] = value
	}

	// 将 map 反序列化为目标结构体
	jsonData, err := json.Marshal(doc)
	if err != nil {
		return fmt.Errorf("marshal map failed: %w", err)
	}

	err = json.Unmarshal(jsonData, v)
	if err != nil {
		return fmt.Errorf("unmarshal to struct failed: %w", err)
	}

	return nil
}

// HSetField 存储单个字段到 Redis Hash
func (r *RedisHashSerializer) HSetField(ctx context.Context, key, field string, v interface{}) error {
	if v == nil {
		return r.client.HDel(ctx, key, field).Err()
	}

	// 直接使用 JSON 序列化，无需包装
	data, err := json.Marshal(v)
	if err != nil {
		return fmt.Errorf("marshal field failed: %w", err)
	}

	return r.client.HSet(ctx, key, field, string(data)).Err()
}
