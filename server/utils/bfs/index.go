package bfs

const (
	NONE  = iota // 空
	BLOCK        // 阻挡
)

type Point struct {
	X, Y int
}

var directions = []Point{
	{-1, 0},
	{1, 0},
	{0, -1},
	{0, 1},
}

// Find
/*
 * @description : 广度优先搜索 给定一个二维数组，起点和终点，找到一条从起点到终点的最短路径
 * @param grid
 * @param start
 * @param target
 * @return []Point
 */
func Find(grid [][]int, start, target *Point) []*Point {
	queue := []*Point{start}
	visited := make([][]bool, len(grid))
	prev := make([][]*Point, len(grid))
	for i := range visited {
		visited[i] = make([]bool, len(grid[0]))
		prev[i] = make([]*Point, len(grid[0]))
	}

	visited[start.Y][start.X] = true

	for len(queue) > 0 {
		point := queue[0]
		queue = queue[1:]

		if point.X == target.X && point.Y == target.Y {
			var path []*Point
			for point != start {
				path = append([]*Point{point}, path...)
				point = prev[point.Y][point.X]
			}
			path = append([]*Point{start}, path...)
			return path
		}
		for i := 0; i < 4; i++ {
			newX, newY := point.X+directions[i].X, point.Y+directions[i].Y
			if newX >= 0 && newX < len(grid[0]) && newY >= 0 && newY < len(grid) && grid[newY][newX] == NONE && !visited[newY][newX] {
				visited[newY][newX] = true
				prev[newY][newX] = point
				queue = append(queue, &Point{newX, newY})
			}
		}
	}
	return nil
}

// IsReachableWithStart
/*
 * @description : 广度优先搜索 给定一个二维数组，起点和终点，判断终点是否可达
 * @param grid
 * @param start
 * @param target
 * @return bool
 */
func IsReachableWithStart(grid [][]int, start *Point, target *Point) bool {
	return Find(grid, start, target) != nil
}

// IsReachable
/*
 * @description : 广度优先搜索 给定一个二维数组和终点，判断终点是否可达
 * @param grid
 * @param target
 * @return bool
 */
func IsReachable(grid [][]int, target *Point) bool {
	lineLen := len(grid[0])
	line := make([]int, 0)
	for i := 0; i < lineLen; i++ {
		line = append(line, grid[0][i])
	}
	tmp := make([][]int, 0)
	tmp = append(tmp, line)
	tmp = append(tmp, grid...)
	target.Y += 1
	for x := 0; x < lineLen; x++ {
		if grid[0][x] == NONE {
			if Find(tmp, &Point{x, 1}, target) != nil {
				return true
			}
		}
	}
	return false
}

func FindDeepestPoint(grid [][]int) (int, int) {
	maxY := -1
	var deepestPoint *Point
	for x := 0; x < len(grid[0]); x++ {
		for y := 0; y < len(grid); y++ {
			target := &Point{x, y}
			if IsReachable(grid, target) && y > maxY {
				maxY = y
				deepestPoint = target
			}
		}
	}
	return deepestPoint.X, deepestPoint.Y
}
