package sensitive

import (
	"errors"
	"regexp"
	"strings"
	com "train/common"
)

const reg_str = "[a-zA-Z0-9\u4E00-\u9FA5\\.\\,\\/\\?\\<\\>\\\\。\\\\，\\\\“\\\\”\"\\-\\\\——\\=\\;\\@\\#\\%\\^\\&\\*\\\\《\\\\》\\\\？\\\\、\\\\~\\\\`\\\\|\\\\（\\\\）\\\\(\\\\)\\_\\\\！\\!\\+\\$]"

// true表示有 敏感字
func Validate(text string) bool {
	ok, _ := com.GlobalGetTencentMod().TextModeration(text)
	return ok
}

// 检测名字 1.有敏感字 2.有特殊字符 0.可用
func CheckName(text string) int {
	if text == "" {
		return 2
	} else if ok, _ := com.GlobalGetTencentMod().TextModeration(text); ok {
		return 1
	}
	res, _ := ReplaceStringByRegex(text, reg_str, "")
	res = strings.ReplaceAll(res, " ", "")
	if res != "" {
		return 2
	}
	return 0
}

// 把敏感字替换为*号
func Replace(text string) string {
	if text == "" {
		return text
	}
	_, str := com.GlobalGetTencentMod().TextModeration(text)
	return str
}

// 正则表达式替换字符
func ReplaceStringByRegex(str, rule, replace string) (string, error) {
	reg, err := regexp.Compile(rule)
	if reg == nil || err != nil {
		return "", errors.New("正则表达式错误:" + err.Error())
	}
	return reg.ReplaceAllString(str, replace), nil
}
