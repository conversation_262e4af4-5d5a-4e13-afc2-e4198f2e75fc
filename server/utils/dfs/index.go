package dfs

type Point struct {
	X, Y int
}

// 可达点位，分别是左、右、上、下
var directions = []Point{
	{-1, 0},
	{1, 0},
	{0, -1},
	{0, 1},
}

func dfs1(grid [][]int, start Point, visited [][]bool) Point {
	deepest := start
	stack := []Point{start}
	for len(stack) > 0 {
		point := stack[len(stack)-1]
		stack = stack[:len(stack)-1]
		for _, dir := range directions {
			newX, newY := point.X+dir.X, point.Y+dir.Y
			if newX >= 0 && newX < len(grid[0]) && newY >= 0 && newY < len(grid) && grid[newY][newX] == 0 && !visited[newY][newX] {
				visited[newY][newX] = true
				stack = append(stack, Point{newX, newY})
				if newY > deepest.Y {
					deepest = Point{newX, newY}
				}
			}
		}
	}
	return deepest
}

// FindDeepestPoint
/*
 * @description 查找最深的可达点
 * @param grid
 * @return int
 * @return int
 */
func FindDeepestPoint(grid [][]int) (int, int) {
	deepest := Point{-1, -1}
	visited := make([][]bool, len(grid))
	for i := range visited {
		visited[i] = make([]bool, len(grid[0]))
	}
	for x := 0; x < len(grid[0]); x++ {
		if grid[0][x] == 0 {
			visited[0][x] = true
			point := dfs1(grid, Point{x, 0}, visited)
			if point.Y > deepest.Y {
				deepest = point
			}
		}
	}
	return deepest.X, deepest.Y
}
