package ut

import (
	"encoding/json"
	"math"
)

type Map struct {
	Id   int         `json:"Id"`
	Time map[int]int `json:"Time"`
}

var dist [][]int
var idToIndex map[int]int

func InitPlanetMove(mapObjString []byte) {
	var maps []Map
	_ = json.Unmarshal(mapObjString, &maps)
	idToIndex = make(map[int]int)
	for i, m := range maps {
		idToIndex[m.Id] = i
	}
	dist = floydWarshall(maps, idToIndex)
}

func floydWarshall(maps []Map, idToIndex map[int]int) [][]int {
	n := len(maps)
	dist := make([][]int, n)
	for i := range dist {
		dist[i] = make([]int, n)
		for j := range dist[i] {
			if i == j {
				dist[i][j] = 0
			} else {
				dist[i][j] = math.MaxInt32
			}
		}
	}

	for _, m := range maps {
		for id, time := range m.Time {
			if time != 0 {
				dist[idToIndex[m.Id]][idToIndex[id]] = time
			}
		}
	}

	for k := 0; k < n; k++ {
		for i := 0; i < n; i++ {
			for j := 0; j < n; j++ {
				if dist[i][k] != math.MaxInt32 && dist[k][j] != math.MaxInt32 && dist[i][k]+dist[k][j] < dist[i][j] {
					dist[i][j] = dist[i][k] + dist[k][j]
				}
			}
		}
	}

	return dist
}

func shortestTime(dist [][]int, start, end int, idToIndex map[int]int) int {
	return dist[idToIndex[start]][idToIndex[end]]
}

func GetMoveTime(start, end int) int {
	return shortestTime(dist, start, end, idToIndex)
}
