1 proto3数据类型对照表 https://protobuf.dev/programming-guides/proto3/#scalar
2 说明:
    假设 :正在/Train/base/proto 目录下执行命令(proto-generator 已经添加到环境变量中)
    proto-generator -in ./ -go-out ../../server/common -ts-out /Users/<USER>/IdeaProjects/Train/Train/assets/app/proto -go-logic /Users/<USER>/Desktop/p5/TrainServer/server
    -in         : 【相对路径】xml消息文件路径 ./
    -go-out     : 【相对路径】服务器消息包父级目录 ../../server/common
    -ts-out     : 【绝对路径】客户端proto目录    /Train/Train/assets/app/proto
    -go-logic   : 【绝对路径】服务器逻辑代码目录   /TrainServer/server


    所需依赖在首次执行命令时自动下载，部分电脑需要提权运行，否则无法创建文件。
    protoc和protoc-gen-go会下载到go环境的bin目录下，所以需要安装go语言环境。
    pbjs和pbts 使用的命令分别是 npm i protobufjs -g , npm i protobufjs-cli -g,这两个命令在网上搜出来的安装命令方式各种各样,必须用这个命令安装的才是正确的.



windows下
在D:\code\TrainServer\base\proto执行
..\tool\proto-generator -in .\ -go-out ..\..\server\common\ -ts-out D:\code\Train\Train\assets\app\proto -go-logic D:\code\TrainServer\server


压测 army (参数说明 army -h)
./army -c 2 -qps 1 -url ws://127.0.0.1:4653