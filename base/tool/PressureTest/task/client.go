package task

import (
	"fmt"
	"log"
	"net/url"
	"pressure_test/env"
	"strings"
	"sync"
	"time"
	"train/common/pb"

	mq "github.com/eclipse/paho.mqtt.golang"
	"github.com/golang/protobuf/proto"
)

// Client 表示一个模拟客户端
type Client struct {
	ID            int
	Ready         chan struct{} // 用于同步启动
	Stop          chan struct{} // 用于停止
	Wg            *sync.WaitGroup
	Messages      []*Message // 持有全部消息副本
	init          bool
	mqc           mq.Client
	lock          sync.Mutex
	waiting_queue map[string]func(client mq.Client, msg mq.Message)
	uid           string
	token         string
	common        *pb.LoginCommon
	CurrentMsgID  int  // 当前消息id
	Done          bool // 是否完成
}

// Run 启动客户端，严格按相对时间调度消息
func (c *Client) Run() {
	defer c.Wg.Done()
	<-c.Ready
	log.Printf("[Client %d] 启动", c.ID)
	start := time.Now()
	c.CurrentMsgID = 0
	n := len(c.Messages)
	for c.CurrentMsgID < n {
		msg := c.Messages[c.CurrentMsgID]
		wait := msg.RelativeTime - int64(time.Since(start)/time.Millisecond)
		if wait > 0 {
			select {
			case <-c.Stop:
				log.Printf("[Client %d] 停止", c.ID)
				return
			case <-time.After(time.Duration(wait) * time.Millisecond):
			}
		}
		for c.CurrentMsgID < n && c.Messages[c.CurrentMsgID].RelativeTime == msg.RelativeTime {
			// 更新当前消息id
			reply, err := c.sendMessage(c.Messages[c.CurrentMsgID])
			if err != nil {
				log.Printf("[Client %d] 发送消息失败: %v", c.ID, err)
			}
			if reply != nil {
				topic := reply.Topic()
				bytes := reply.Payload()
				switch true {
				case strings.Contains(topic, "login/C2S_LoginGuestMessage"):
					// 游客登录完成后 记录当前客户端id和token 方便重连
					r := &pb.S2C_LoginResultMessage{}
					err := proto.Unmarshal(bytes, r)
					if err != nil {
						log.Printf("[Client %d] 解析登录结果失败: %v", c.ID, err)
						return
					}
					log.Printf("[Client %d %s] 登录成功", c.ID, r.UserInfo.Uid)
					c.uid = r.UserInfo.Uid
					c.token = r.UserInfo.LToken
				}
			}
			c.CurrentMsgID++
		}
		select {
		case <-c.Stop:
			log.Printf("[Client %d] 停止", c.ID)
			return
		default:
		}
	}
	log.Printf("[Client %d] 所有消息已发送完毕", c.ID)
	c.Done = true // 所有消息已发送完毕
}

func (c *Client) connect() error {
	if c.init {
		return nil
	}
	c.lock.Lock()
	defer c.lock.Unlock()

	opts := mq.NewClientOptions()
	opts.AddBroker(env.Address)
	opts.SetClientID(fmt.Sprintf("%d", c.ID))
	opts.SetUsername("")
	opts.SetPassword("")
	opts.SetCleanSession(false)
	opts.SetProtocolVersion(3)
	opts.SetAutoReconnect(false)
	opts.SetDefaultPublishHandler(func(client mq.Client, msg mq.Message) {
		//收到消息
		c.lock.Lock()
		defer c.lock.Unlock()
		if callback, ok := c.waiting_queue[msg.Topic()]; ok {
			//有等待消息的callback 还缺一个信息超时的处理机制
			_, err := url.Parse(msg.Topic())
			if err != nil {
				ts := strings.Split(msg.Topic(), "/")
				if len(ts) > 2 {
					//这个topic存在msgid 那么这个回调只使用一次
					delete(c.waiting_queue, msg.Topic())
				}
			}
			go callback(client, msg)
		}
	}).SetConnectionLostHandler(func(client mq.Client, err error) {
		log.Printf("[Client %d] 连接断开: %v", c.ID, err)
		c.init = false
	}).SetOnConnectHandler(func(client mq.Client) {
		c.init = true
	})
	c.mqc = mq.NewClient(opts)
	if token := c.mqc.Connect(); token.Wait() && token.Error() != nil {
		return token.Error()
	}
	return nil
}

// 监听回应
func (c *Client) on(topic string, callback func(client mq.Client, msg mq.Message)) {
	c.lock.Lock()
	defer c.lock.Unlock()
	if c.waiting_queue == nil {
		c.waiting_queue = make(map[string]func(client mq.Client, msg mq.Message))
	}
	c.waiting_queue[topic] = callback
}

// 发送消息
func (c *Client) sendMessage(msg *Message) (mq.Message, error) {
	if msg.IsConnectMsg() {
		if c.mqc != nil && c.mqc.IsConnected() {
			// 断开旧的
			c.mqc.Disconnect(0)
			c.mqc = nil
			c.init = false
		}
		// 连接新的
		if err := c.connect(); err != nil {
			return nil, fmt.Errorf("连接失败: %v", err)
		}
	} else if msg.IsGuestLoginMsg() {
		// 游客登录时，保存公共参数部分数据
		r := &pb.C2S_LoginGuestMessage{}
		err := proto.Unmarshal(msg.Data.Body(), r)
		if err != nil {
			return nil, fmt.Errorf("解析登录请求失败: %v", err)
		}
		c.common = r.Common
	} else if msg.IsTokenLoginMsg() {
		// token登录时，用预先保存的数据来登录
		r := &pb.C2S_LoginByTokenMessage{
			Id:          c.uid,
			IsReconnect: false,
			Token:       c.token,
			Common:      c.common,
		}
		b, err := proto.Marshal(r)
		if err != nil {
			return nil, fmt.Errorf("序列化登录请求失败: %v", err)
		}
		msg.Data.body = b
	}

	result := make(chan mq.Message)
	c.on(msg.Route, func(client mq.Client, msg mq.Message) {
		result <- msg
	})
	c.mqc.Publish(msg.Route, 0, false, msg.Data.Body())
	r, ok := <-result
	if !ok {
		return nil, fmt.Errorf("client closed")
	}
	return r, nil
}
