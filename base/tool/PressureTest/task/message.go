package task

// Message 表示一条待发送的消息
// RelativeTime 表示相对首条消息的毫秒偏移
// Data 建议处理为[]byte或json字符串，方便后续发送
// 你可以根据需要扩展字段
// Data 兼容 data 为 map[string]int 或 "" 的情况
// 如果是 ""，则 Data.Map 为空
import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type Data struct {
	Map  map[string]int
	body []byte
}

func (d *Data) Body() []byte {
	if d.body == nil {
		ary := make([]byte, 0)
		keys := lo.Keys(d.Map)
		sort.Slice(keys, func(i, j int) bool {
			return cast.ToInt(keys[i]) < cast.ToInt(keys[j])
		})
		for _, k := range keys {
			ary = append(ary, byte(d.Map[k]))
		}
		d.body = ary
	}
	return d.body
}

func (d *Data) UnmarshalJSON(b []byte) error {
	if string(b) == "\"\"" {
		// 空字符串
		d.Map = map[string]int{}
		return nil
	}
	var m map[string]int
	if err := json.Unmarshal(b, &m); err != nil {
		return fmt.Errorf("data字段解析失败: %w", err)
	}
	d.Map = m
	return nil
}

type Message struct {
	Route        string `json:"route"`
	Data         Data   `json:"data"`
	Time         int64  `json:"time"`
	RelativeTime int64  // 运行时计算
}

// 是不是连接消息
func (m *Message) IsConnectMsg() bool {
	return strings.HasPrefix(m.Route, "gate/")
}

func (m *Message) IsGuestLoginMsg() bool {
	return strings.Contains(m.Route, "C2S_LoginGuestMessage")
}

func (m *Message) IsTokenLoginMsg() bool {
	return strings.Contains(m.Route, "C2S_LoginByTokenMessage")
}
