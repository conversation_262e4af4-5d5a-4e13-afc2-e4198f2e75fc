package task

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sort"
	"sync"
)

// Task 管理所有客户端和调度
type Task struct {
	Clients   []*Client
	ReadyChan chan struct{} // 广播启动
	StopChan  chan struct{} // 广播停止
	Messages  []*Message    // 所有消息
	Wg        sync.WaitGroup
	Started   bool
	Mu        sync.Mutex
}

// NewTask 创建Task并初始化客户端
func NewTask(concurrency int) *Task {
	t := &Task{
		Clients:   nil,
		Ready<PERSON>han: make(chan struct{}),
		StopChan:  make(chan struct{}),
	}
	return t
}

// LoadMessages 加载并预处理消息文件，排序并计算相对时间
func (t *Task) LoadMessages(path string) error {
	data, err := os.ReadFile(path)
	if err != nil {
		return err
	}
	var msgs []*Message
	if err := json.Unmarshal(data, &msgs); err != nil {
		return err
	}
	if len(msgs) == 0 {
		return nil
	}
	// 按time升序排序
	sort.Slice(msgs, func(i, j int) bool { return msgs[i].Time < msgs[j].Time })
	base := msgs[0].Time
	last := base
	for _, m := range msgs {
		m.RelativeTime = m.Time - base
		// 时间差超过30秒就发出警告
		if m.Time-last > 30*1000 {
			log.Printf("消息时间差超过30秒: %s,重置成30秒", m.Route)
			m.RelativeTime = 30 * 1000
		} else {
			last = m.Time
		}
	}
	t.Messages = msgs
	return nil
}

// InitClients 初始化所有客户端，分配消息副本
func (t *Task) InitClients(concurrency int) {
	t.Clients = make([]*Client, concurrency)
	for i := 0; i < concurrency; i++ {
		c := &Client{
			ID:       i + 1,
			Ready:    t.ReadyChan,
			Stop:     t.StopChan,
			Wg:       &t.Wg,
			Messages: t.Messages, // 所有客户端持有同一份消息副本
		}
		t.Clients[i] = c
	}
}

func (t *Task) Start() {
	t.Mu.Lock()
	defer t.Mu.Unlock()
	if t.Started {
		fmt.Println("任务已在运行")
		return
	}
	log.Println("启动所有客户端...")
	t.Wg.Add(len(t.Clients))
	for _, c := range t.Clients {
		// if err := c.connect(); err != nil {
		// 	log.Printf("[Client %d] 连接失败: %v", c.ID, err)
		// 	continue
		// }
		go c.Run()
	}
	t.Started = true
	close(t.ReadyChan) // 广播启动
}

func (t *Task) Stop() {
	t.Mu.Lock()
	defer t.Mu.Unlock()
	if !t.Started {
		fmt.Println("任务未启动")
		return
	}
	log.Println("停止所有客户端...")
	close(t.StopChan) // 广播停止
	t.Wg.Wait()
	t.Started = false
}

func (t *Task) Stat() {
	fmt.Printf("当前客户端数: %d\n", len(t.Clients))
	file, err := os.Create("stat.txt")
	if err != nil {
		fmt.Printf("写入stat.txt失败: %v\n", err)
		return
	}
	defer file.Close()
	fmt.Fprintf(file, "ID\t当前消息id\t是否完成\tuid\ttoken\n")
	for _, c := range t.Clients {
		fmt.Fprintf(file, "%d\t%d\t%v\t%s\t%s\n", c.ID, c.CurrentMsgID, c.Done, c.uid, c.token)
	}
	fmt.Println("更多信息已写入stat.txt")
}
