package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"pressure_test/env"
	"pressure_test/task"
)

func init() {
	flag.StringVar(&env.Address, "address", "ws://127.0.0.1:4653", "目标服务器地址")
	flag.IntVar(&env.Core, "core", 1, "并发数量")
	flag.StringVar(&env.Json, "json", "", "消息json文件所在地址")
}

func main() {
	flag.Parse()
	log.Println("目标服务器:", env.Address)
	log.Println("并发数:", env.Core)
	if env.Json == "" {
		env.Json = "request.json"
	}

	ts := task.NewTask(env.Core)
	if err := ts.LoadMessages(env.Json); err != nil {
		log.Fatalf("加载消息文件失败: %v", err)
	}
	ts.InitClients(env.Core)
	menu := `
========= 控制台菜单 =========
1. 开始
2. 停止
3. 统计
0. 退出
============================
请输入操作: `
	for {
		var choice int
		fmt.Print(menu)
		_, err := fmt.Scanln(&choice)
		if err != nil {
			fmt.Println("输入有误，请重新输入")
			continue
		}
		switch choice {
		case 1:
			ts.Start()
		case 2:
			ts.Stop()
		case 3:
			ts.Stat()
		case 0:
			log.Println("退出程序...")
			if ts.Started {
				ts.Stop()
			}
			os.Exit(0)
		default:
			fmt.Println("无效选项，请重新输入")
		}
	}
}
