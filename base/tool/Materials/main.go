package main

import (
	"encoding/json"
	"fmt"
	"log"
	"materials/obj"
	"os"
	"sort"
	"strings"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

/*
使用说明
1 复制主表B列，覆盖event.txt文件，并且需要将车厢名字换成id
2 复制主表E列，覆盖planet.txt文件
3 分别复制对应的车厢进度列，覆盖对应车厢Id.txt文件（例如1号寝室-> 1013.txt）
4 修改 trainProgressAdd ，也就是车厢设施起点在trainItemLevel对应行的偏移
fixed.txt 是手调的部分 粘贴进来后会保留 手调的部分
结果会输出到 result.txt

但是需要注意 材料id只支持了101-118 要修改代码
*/
var trainProgressAdd = map[int]int{
	1013: 4 - 4,
	1016: 709 - 4,
	1014: 254 - 4,
	1017: 1094 - 4,
	//1019: 1163 - 4,
}
var fixedAllocation = make([][]int, 0)

func main() {
	parseProgress()
	parseJson()

	Calculate()
}

func parseProgress() {
	obj.ProgressMap = make([]*obj.Progress, 0)

	bytes, _ := os.ReadFile("planet.txt")
	planet := string(bytes)

	bytes, _ = os.ReadFile("event.txt")
	event := string(bytes)

	bytes, _ = os.ReadFile("fixed.txt")
	if bytes != nil {
		fixed := string(bytes)
		lines := strings.Split(fixed, "\n")
		for _, line := range lines {
			split := strings.Split(line, "\t")
			tmp := make([]int, 15)
			for i, v := range split {
				tmp[i] = cast.ToInt(v)
			}
			fixedAllocation = append(fixedAllocation, tmp)
		}
	}

	trains := lo.Keys(trainProgressAdd)
	sort.Slice(trains, func(i, j int) bool { return trains[i] < trains[j] })

	trainsMap := make(map[int][]string)
	for _, v := range trains {
		bytes, _ = os.ReadFile(fmt.Sprintf("%d.txt", v))
		trainsMap[v] = strings.Split(string(bytes), "\n")
	}

	planet_lines := strings.Split(planet, "\n")
	event_lines := strings.Split(event, "\n")
	for idx, line := range planet_lines {
		progress := &obj.Progress{Line: cast.ToInt(line)}
		if progress.Data == nil {
			progress.Data = make(map[int]int)
		}
		for _, v := range trains {
			progress.Data[v] = cast.ToInt(trainsMap[v][idx])
		}
		// 事件节点 主要是修建车厢
		progress.Events = lo.Map(event_lines[:idx+1], func(s string, _ int) int {
			return cast.ToInt(s)
		})
		obj.ProgressMap = append(obj.ProgressMap, progress)
	}
}

func parseJson() {
	parseJsonNormal("/Users/<USER>/IdeaProjects/Train/Train/assets/resources/common/json/PlanetNodes.json", &obj.PlanetNodeAry)
	parseJsonNormal("/Users/<USER>/Desktop/p5/TrainServer/server/bin/conf/json/ChapterPlanetMine.json", &obj.ChapterPlanetMineAry)
	parseJsonNormal("/Users/<USER>/Desktop/p5/TrainServer/server/bin/conf/json/TrainItemLevel.json", &obj.TrainItemLevelAry)
	parseJsonNormal("/Users/<USER>/Desktop/p5/TrainServer/server/bin/conf/json/Train.json", &obj.TrainAry)
	parseJsonNormal("/Users/<USER>/Desktop/p5/TrainServer/server/bin/conf/json/ChapterPlanetSp.json", &obj.ChapterPlanetMineSpAry)
	parseJsonNormal("/Users/<USER>/Desktop/p5/TrainServer/server/bin/conf/json/ToolLevel.json", &obj.ToolLevelAry)
	// 特殊处理一下 学院星那个胡桃夹子节点
	head := make([]*obj.ChapterPlanetMine, 13)
	copy(head, obj.ChapterPlanetMineAry[0:13])
	tail := make([]*obj.ChapterPlanetMine, len(obj.ChapterPlanetMineAry)-13)
	copy(tail, obj.ChapterPlanetMineAry[13:])
	head = append(head, &obj.ChapterPlanetMine{
		Id:     "1024-1024-1",
		MineId: 1024,
		Reward: []*obj.Condition{{Type: 11, Id: 102, Num: 1}, {Type: 11, Id: 105, Num: 2}},
	})
	obj.ChapterPlanetMineAry = append(head, tail...)
	// 处理一下工具等级和id
	for _, v := range obj.ToolLevelAry {
		s := strings.Split(v.Id, "-")
		v.Type = cast.ToInt(s[0])
		v.Level = cast.ToInt(s[1])
	}
}

func parseJsonNormal(file string, target interface{}) {
	bytes, err := os.ReadFile(file)
	if err != nil {
		log.Fatal(err)
	}
	err = json.Unmarshal(bytes, &target)
	if err != nil {
		log.Fatal(err)
	}
}
