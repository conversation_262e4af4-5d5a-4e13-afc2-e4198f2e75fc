syntax = "proto3";

package proto;

option go_package = "./pb";

//After are enums.
//After are structs.
//After are messages.
message C2S_CreatePayOrderMessage {
  string productId = 1; //商品id
  string platform = 2; //平台
}
message S2C_CreatePayOrderMessage {
  int32 code = 1; //0
  string uid = 2; //内部订单号
}
message C2S_VerifyPayOrderMessage {
  string productId = 1; //商品id
  string orderId = 2; //外部订单号
  string cpOrderId = 3; //内部订单号
  string token = 4; //验证数据
  string platform = 5; //支付方式
  int64 purchaseTime = 6; //支付时间
  string currencyType = 7; //支付币种
  double payAmount = 8; //支付金额
  int32 quantity = 9; //数量
}
message S2C_VerifyPayOrderMessage {
  int32 code = 1; //1货币不足2物品库存不足3商店数据不存在
  string uid = 2; //内部订单号
}
message C2S_GetPayRewardsMessage {
  string uid = 1; //订单id
}
message S2C_GetPayRewardsMessage {
  int32 code = 1; //0
}
