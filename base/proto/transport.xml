<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="TransportStart" module="game" explain="领取护送任务">
        <field class="int32" name="index" explain="list下标"/>
    </message>
    <message type="S2C" name="TransportStart" explain="领取护送任务">
        <field class="int32" name="code" explain="1不存在的任务"/>
    </message>
    <message type="C2S" name="TransportFight" module="game" explain="护送挑战(临时使用)">
        <field class="int32" name="planetId" explain="星球id，通过目的地区分任务"/>
        <field class="bool" name="succ" explain="挑战成功"/>
    </message>
    <message type="S2C" name="TransportFight" explain="护送挑战(临时使用)">
        <field class="int32" name="code" explain="1不存在的任务"/>
    </message>
    <message type="C2S" name="TransportRewardGet" module="game" explain="领取护送奖励">
        <field class="int32" name="index" explain="list下标"/>
    </message>
    <message type="S2C" name="TransportRewardGet" explain="结果">
        <field class="int32" name="code" explain="1任务不存在，2奖励已经领取，3任务未完成"/>
    </message>
    <message type="C2S" name="TransportBack" module="game" explain="返回雪星">
    </message>
    <message type="S2C" name="TransportBack" explain="返回雪星">
        <field class="int32" name="code" explain="1没有任务，2任务未失败"/>
    </message>
    <message type="C2S" name="SyncTransport" module="game" explain="同步运送数据">
    </message>
    <message type="S2C" name="SyncTransport" explain="同步运送数据">
        <field class="int32" name="code" explain="code"/>
        <field class="struct.Transport" name="transport" explain="0"/>
    </message>
</messages>