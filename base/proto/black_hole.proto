syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_ReadyStartBlackHoleMessage {
  int32 level = 1; //难度
}
message S2C_ReadyStartBlackHoleMessage {
  int32 code = 1; //code
  repeated BattleRole boss = 2; //当前难度下的boss阵容
}
message C2S_StartBlackHoleMessage {
  int32 level = 1; //难度
  repeated int32 roles = 2; //选择的乘客
}
message S2C_StartBlackHoleMessage {
  int32 code = 1; //code
  BlackHole blackHole = 2; //黑洞数据
}
message C2S_SelectBlackHoleNodeMessage {
  string nodeId = 1; //节点id
  BlackHoleBuff buff = 2; //选择的buff
  BlackHoleEquip equip = 3; //选择的装备
  string aid = 4; //选择援助的角色uid
  BattleResult battle = 5; //战斗结果
}
message S2C_SelectBlackHoleNodeMessage {
  int32 code = 1; //0
  string curId = 2; //当前id
  string nextId = 3; //下一步id
  BlackHoleBuff buff = 4; //最终生成的buff
  string rebirth = 5; //复活的角色uid
  BattleResult battle = 6; //战斗结果
  repeated BlackHoleEquip equips = 7; //装备
}
message C2S_SyncBlackHoleMessage {
}
message S2C_SyncBlackHoleMessage {
  int32 code = 1; //
  BlackHole blackHole = 2; //
}
message C2S_UnlockBlackHoleMessage {
}
message S2C_UnlockBlackHoleMessage {
  int32 code = 1; //
  BlackHole blackHole = 2; //
}
