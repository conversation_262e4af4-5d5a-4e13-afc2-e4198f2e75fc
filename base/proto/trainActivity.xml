<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="GetTrainActivity" module="game" explain="获取列车活动数据">
    </message>
    <message type="S2C" name="GetTrainActivity" module="game" explain="获取列车活动数据">
        <field class="int32" name="code" explain=""/>
        <field class="struct.TrainActivity" name="data" explain=""/>
    </message>
    <message type="C2S" name="ArrangeTrainActivity" module="game" explain="安排列车活动">
        <array class="int32" name="ary" explain="活动id列表"/>
    </message>
    <message type="S2C" name="ArrangeTrainActivity" module="game" explain="安排列车活动">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="GetTrainActivityReward" module="game" explain="领取列车活动奖励">
        <field class="int32" name="index" explain="序号"/>
        <field class="bool" name="isUnget" explain="是否领取未领取奖励"/>
        <field class="int32" name="trainId" explain="车厢id 未领取奖励时需要"/>
    </message>
    <message type="S2C" name="GetTrainActivityReward" module="game" explain="领取列车活动奖励">
        <field class="int32" name="code" explain=""/>
    </message>
</messages>