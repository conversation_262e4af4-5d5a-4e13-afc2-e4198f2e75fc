syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_WearEquipMessage {
  string uid = 1; //装备唯一id
}
message S2C_WearEquipMessage {
  int32 code = 1; //1找不到装备
}
message C2S_UnWearEquipMessage {
  string uid = 1; //装备唯一id
}
message S2C_UnWearEquipMessage {
  int32 code = 1; //1找不到穿戴中的装备
}
message C2S_MakeEquipMessage {
  int32 id = 1; //装备id
  int32 tableId = 2; //打造台id
}
message S2C_MakeEquipMessage {
  int32 code = 1; //0
  EquipItem equip = 2; //装备
}
message C2S_BuyEquipMessage {
  int32 id = 1; //装备id
  int32 level = 2; //装备等级
}
message S2C_BuyEquipMessage {
  int32 code = 1; //0
  EquipItem equip = 2; //装备
}
message C2S_SellEquipMessage {
  repeated string uid = 1; //装备id
}
message S2C_SellEquipMessage {
  int32 code = 1; //0
}
