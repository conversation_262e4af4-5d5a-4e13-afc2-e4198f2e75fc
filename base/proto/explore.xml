<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="SyncExplore" module="game" explain="同步探索状态">
        <field class="int32" name="planetId" explain="星球ID"/>
    </message>
    <message type="S2C" name="SyncExplore" explain="同步探索状态">
        <field class="int32" name="code" explain="0"/>
        <field class="int64" name="surplusTime" explain="剩余时间"/>
    </message>
    <message type="C2S" name="StartExplore" module="game" explain="开始探索">
        <field class="int32" name="planetId" explain="星球ID"/>
        <array class="int32" name="roles" explain="派遣的角色id"/>
    </message>
    <message type="S2C" name="StartExplore" explain="开始探索">
        <field class="int32" name="code" explain="0"/>
        <array class="struct.Condition" name="rewards" explain="探索奖励"/>
    </message>
    <message type="C2S" name="ClaimExploreReward" module="game" explain="领取探索奖励">
        <field class="int32" name="planetId" explain="星球ID"/>
    </message>
    <message type="S2C" name="ClaimExploreReward" explain="领取探索奖励">
        <field class="int32" name="code" explain="0"/>
        <field class="int64" name="surplusTime" explain="剩余时间"/>
    </message>
    <message type="C2S" name="GetExploreArea" module="game" explain="获取要探索的区域">
        <field class="int32" name="planetId" explain="星球ID"/>
    </message>
    <message type="S2C" name="GetExploreArea" explain="获取要探索的区域">
        <field class="int32" name="code" explain="0"/>
        <field class="int32" name="area" explain="区域"/>
    </message>
</messages>