<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="BuyCarriage" module="game" explain="解锁车厢">
        <field class="int32" name="id" explain="车厢配置id"/>
    </message>
    <message type="S2C" name="BuyCarriageResult" explain="解锁车厢结果">
        <field class="int32" name="code" explain="code=0代表无错误,否则就去读failList,里面存检查失败的条件列表"/>
        <array class="struct.Condition" name="failList" explain="条件check失败的列表"/>
        <field class="struct.CarriageInfo" name="carriage" explain="新增的车厢"/>
    </message>
    <message type="C2S" name="GetCarriageBuildInfo" module="game" explain="获取车厢建造信息">
        <field class="int32" name="id" explain="车厢id"/>
    </message>
    <message type="S2C" name="GetCarriageBuildInfoRes" explain="车厢建造完成">
        <field class="int32" name="code" explain="code=0"/>
        <field class="int32" name="buildTime" explain="建造剩余时间"/>
        <field class="bool" name="openDoor" explain="是否打开猫猫门"/>
    </message>
    <message type="C2S" name="OpenCarriageDoor" module="game" explain="车厢门打开">
        <field class="int32" name="id" explain="车厢id"/>
    </message>
    <message type="S2C" name="OpenCarriageDoorRes" explain="车厢门打开">
        <field class="int32" name="code" explain="code=0"/>
    </message>
    <message type="C2S" name="BuildLevelUp" module="game" explain="升级设施等级">
        <field class="int32" name="carriageId" explain="车厢id"/>
        <field class="int32" name="order" explain="设施序号"/>
    </message>
    <message type="S2C" name="BuildLevelUp" explain="升级设施等级">
        <field class="int32" name="code" explain="code=0"/>
    </message>
    <message type="C2S" name="ChangeBuildSkin" module="game" explain="改变设施皮肤">
        <field class="int32" name="carriageId" explain="车厢id"/>
        <field class="int32" name="order" explain="设施序号"/>
        <field class="int32" name="skin" explain="设施皮肤"/>
    </message>
    <message type="S2C" name="ChangeBuildSkin" explain="改变设施皮肤">
        <field class="int32" name="code" explain="code=0"/>
    </message>
    <message type="C2S" name="CarriageThemeLvUp" module="game" explain="提升车厢主题等级">
        <field class="int32" name="carriageId" explain="车厢id"/>
    </message>
    <message type="S2C" name="CarriageThemeLvUp" explain="提升车厢主题等级">
        <field class="int32" name="code" explain="code=0"/>
    </message>
    <message type="C2S" name="UnlockGoods" module="game" explain="解锁货物">
        <field class="string" name="id" explain="id"/>
        <array class="struct.Condition" name="extra" explain="扩展列表"/>
    </message>
    <message type="S2C" name="UnlockGoods" explain="解锁货物">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="LevelUpGoods" module="game" explain="升级货物">
        <field class="string" name="id" explain="id"/>
        <field class="int32" name="level" explain="等级"/>
    </message>
    <message type="S2C" name="LevelUpGoods" explain="升级货物">
        <field class="int32" name="code" explain=""/>
    </message>
</messages>