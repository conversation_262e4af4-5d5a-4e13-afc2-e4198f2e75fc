syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_ProfileBranchPassNodeMessage {
  int32 nodeId = 1; //节点id
}
message S2C_ProfileBranchPassNodeMessage {
  int32 code = 1; //0成功
  ProfileBranchLevel level = 2; //记忆阁当前关卡
}
message C2S_ProfileBranchQuestionMessage {
}
message S2C_ProfileBranchQuestionMessage {
  int32 code = 1; //0成功
  int32 energy = 2; //体力
  int32 surplusTime = 3; //剩余时间
}
message C2S_ProfileBranchSyncEnergyMessage {
}
message S2C_ProfileBranchSyncEnergyMessage {
  int32 code = 1; //0成功
  int32 energy = 2; //体力
  int32 surplusTime = 3; //剩余时间
}
message C2S_ProfileBranchUnlockMessage {
  int32 id = 1; //记忆阁id
}
message S2C_ProfileBranchUnlockMessage {
  int32 code = 1; //0成功
}
