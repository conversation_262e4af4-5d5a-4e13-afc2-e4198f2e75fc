<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="TowerBattle" module="game" explain="爬塔战斗">
    </message>
    <message type="S2C" name="TowerBattle" explain="爬塔战斗结果">
        <field class="int32" name="code" explain="0"/>
    </message>
    <message type="C2S" name="TowerBattleWin" module="game" explain="战斗胜利(临时)">
    </message>
    <message type="S2C" name="TowerBattleWin" explain="战斗胜利">
        <field class="int32" name="code" explain="0"/>
        <field class="struct.Tower" name="tower" explain="爬塔数据"/>
    </message>
</messages>