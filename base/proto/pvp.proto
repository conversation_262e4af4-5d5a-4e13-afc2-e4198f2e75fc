syntax = "proto3";

package proto;

option go_package = "./pb";

import "enum.proto";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_UpdateFormationMessage {
  PvpType type = 1; //竞技场类型
  repeated int32 idAry = 2; //乘客id
}
message S2C_UpdateFormationMessage {
  int32 code = 1; //code=0代表无错误
}
message C2S_GetRankListMessage {
  PvpType type = 1; //竞技场类型
}
message S2C_GetRankListMessage {
  int32 code = 1; //code=0代表无错误
  repeated PvpSimplePlayerData list = 2; //数据
  int32 rank = 3; //自己的排名
  int32 score = 4; //自己的分数
}
message C2S_GetRivalMessage {
  PvpType type = 1; //竞技场类型
  bool refresh = 2; //刷新
}
message S2C_GetRivalMessage {
  int32 code = 1; //code=0代表无错误
  repeated PvpSimplePlayerData list = 2; //数据
}
message C2S_PvpFightMessage {
  PvpType type = 1; //竞技场类型
  int32 rivalIndex = 2; //对手序号
  int32 result = 3; //0平局1胜2输
}
message S2C_PvpFightMessage {
  int32 code = 1; //code=0代表无错误
  int32 score = 2; //最新分数
  int32 rank = 3; //最新排名
}
message C2S_PvpBattleRecordListMessage {
  PvpType type = 1; //竞技场类型
}
message S2C_PvpBattleRecordListMessage {
  int32 code = 1; //code=0代表无错误
  repeated PvpBattleRecordData list = 2; //战绩列表
}
message C2S_PvpBattleReplayMessage {
  PvpType type = 1; //竞技场类型
  string docId = 2; //文档id
}
message S2C_PvpBattleReplayMessage {
  int32 code = 1; //code=0代表无错误
  repeated BattleRole attacker = 2; //攻击方
  repeated BattleRole defender = 3; //防守方
}
message C2S_PvpModuleDataMessage {
}
message S2C_PvpModuleDataMessage {
  PvpNormalData normal = 1; //
}
