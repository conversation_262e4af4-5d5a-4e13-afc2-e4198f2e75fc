syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
// 操作类型
enum CeilOperationType{
  Invalid = 0; //无效操作，因为枚举必须存在0值，可以不用但一定要有
  Unlock = 1; //解锁格子
  Sow = 2; //播种
  Water = 3; //浇水
  Fertilizer = 4; //施肥
  Harvest = 5; //收获
}

// 操作结果
enum CeilOperationCode{
  Success = 0; //操作成功
  CeilAlreadyUnlock = 1; //格子已经被解锁
  NotEnoughCostForUnlock = 2; //解锁格子消耗不足
  CeilNotUnlock = 3; //格子未被解锁
  CeilAlreadyPlant = 4; //格子已经有作物
  CeilPlantIdError = 5; //格子类型与作物类型不匹配
  CeilNeedNotWater = 6; //格子不需要浇水(没有作物or作物成熟)
  NotEnoughCostForWater = 7; //水能不足，无法浇水
  CeilNeedNotFertilizer = 8; //格子不需要施肥(没有作物or作物成熟)
  NotEnoughCostForFertilizer = 9; //肥料不足
  CeilNotHasPlanet = 10; //格子没有作物，无法收获
  CeilCanNotHarvest = 11; //作物未成熟，无法收获
  NotEnoughCostForPlant = 12; //种子不足，种植失败
  CeilMax = 13; //格子数量达到上限，解锁失败
  SureCeilSort = 14; //格子数量解锁顺序不对
}

//After are structs.
//After are messages.
message C2S_CeilOperationMessage {
  int32 id = 1; //顺位id
  CeilOperationType type = 2; //操作类型
  int32 powerValue = 3; //
}
message S2C_CeilOperationMessage {
  CeilOperationCode code = 1; //
  FieldCeil data = 2; //操作之后的格子数据
  repeated Condition rewards = 3; //收获奖励数据
}
message C2S_CeilSyncMessage {
  int32 id = 1; //顺位id
}
message S2C_CeilSyncMessage {
  int32 code = 1; //
  FieldCeil data = 2; //格子数据
}
