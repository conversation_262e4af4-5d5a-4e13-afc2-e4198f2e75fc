syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_TrainNavigationMessage {
  int32 planetId = 1; //星球id
  bool plus = 2; //使用次元晶石
}
message S2C_TrainNavigationResultMessage {
  int32 code = 1; //1当前正在航行,2该星球已经探索完毕,3星球id配置读取错误,4星球前置条件未达到,5次元晶石不足
  int32 time = 2; //航行结束剩余时间>=0
}
message C2S_GetPlanetMoveSurplusTimeMessage {
}
message S2C_GetPlanetMoveSurplusTimeRespMessage {
  int32 code = 1; //0
  int32 time = 2; //剩余时间
}
message C2S_LandPlanetMessage {
  int32 planetId = 1; //星球id
}
message S2C_LandPlanetRespMessage {
  int32 code = 1; //1正在航行中,时间没到
}
message C2S_ChapterPassMessage {
  int32 planetId = 1; //星球id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
}
message S2C_ChapterPassResultMessage {
  int32 code = 1; //
  int32 mapId = 2; //当前地图id,如果是-1就代表星球探索完毕
  int32 nextId = 3; //下一个节点的id,可能为空(可能会涉及到当前地图上限，需要切换地图)
  repeated Condition rewards = 4; //奖励
}
message C2S_PassBranchPlanetNodeMessage {
  string branchId = 1; //支线id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
}
message S2C_PassBranchPlanetNodeMessage {
  int32 code = 1; //
  int32 mapId = 2; //当前地图id,如果是-1就代表星球探索完毕
  int32 nextId = 3; //下一个节点的id,可能为空(可能会涉及到当前地图上限，需要切换地图)
  repeated Condition rewards = 4; //奖励
}
message C2S_ClaimBranchPlanetNodeRewardMessage {
  string branchId = 1; //支线id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
  int32 index = 4; //奖励下标
}
message S2C_ClaimBranchPlanetNodeRewardMessage {
  int32 code = 1; //0
}
message C2S_CancelMoveToPlanetMessage {
}
message S2C_CancelMoveToPlanetMessage {
  int32 code = 1; //0
}
message C2S_UnlockProfileMessage {
  int32 planetId = 1; //星球id
  int32 id = 2; //贴纸id
  int32 index = 3; //位置
}
message S2C_UnlockProfileMessage {
  int32 code = 1; //
}
message C2S_ProfileCollectRewardMessage {
  int32 planetId = 1; //星球id
  int32 area = 2; //星球区域
  int32 step = 3; //进度
}
message S2C_ProfileCollectRewardMessage {
  int32 code = 1; //
}
message C2S_PlanetProfileSortChangeMessage {
  int32 planetId = 1; //星球id
  map<int32,int32> sort = 2; //排序数据
}
message S2C_PlanetProfileSortChangeMessage {
  int32 code = 1; //code=0代表无错误
}
message C2S_ChapterPassRandomBoxMessage {
  int32 planetId = 1; //星球id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
}
message S2C_ChapterPassRandomBoxMessage {
  int32 code = 1; //
  int32 mapId = 2; //当前地图id,如果是-1就代表星球探索完毕
  int32 nextId = 3; //下一个节点的id,可能为空(可能会涉及到当前地图上限，需要切换地图)
  repeated Condition rewards = 4; //奖励
}
message C2S_ChapterStartTimeLimitBoxMessage {
  int32 planetId = 1; //星球id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
}
message S2C_ChapterStartTimeLimitBoxMessage {
  int32 code = 1; //
  int32 surplusTime = 2; //剩余时间
}
message C2S_ChapterSyncTimeLimitBoxMessage {
  int32 planetId = 1; //星球id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
  int32 clicks = 4; //点击次数
}
message S2C_ChapterSyncTimeLimitBoxMessage {
  int32 code = 1; //
  int32 state = 2; //状态
  int32 surplusTime = 3; //剩余时间
  repeated Condition rewards = 4; //奖励
}
message C2S_ChapterPassTimeLimitBoxMessage {
  int32 planetId = 1; //星球id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
}
message S2C_ChapterPassTimeLimitBoxMessage {
  int32 code = 1; //
}
message C2S_ChapterPassMonsterBoxMessage {
  int32 planetId = 1; //星球id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
  int32 num = 4; //受击次数
}
message S2C_ChapterPassMonsterBoxMessage {
  int32 code = 1; //
  repeated Condition rewards = 2; //奖励
}
message C2S_ChapterPassToolBlessMessage {
  int32 planetId = 1; //星球id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
}
message S2C_ChapterPassToolBlessMessage {
  int32 code = 1; //
}
message C2S_ChapterPassRageModeMessage {
  int32 planetId = 1; //星球id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //节点id
}
message S2C_ChapterPassRageModeMessage {
  int32 code = 1; //
}
message C2S_DoPublicityMessage {
  int32 planetId = 1; //星球id
}
message S2C_DoPublicityMessage {
  int32 code = 1; //
  int32 num = 2; //招募人口数
  repeated Condition rewards = 3; //奖励列表
  int32 publicityUnGetOutputTime = 4; //未领取的产出时长
}
message C2S_GetPublicityRewardMessage {
  int32 planetId = 1; //星球id,-1是全部
}
message S2C_GetPublicityRewardMessage {
  int32 code = 1; //
  repeated Condition rewards = 2; //奖励列表
  map<int32,int32> timeMap = 3; //时间数据
}
