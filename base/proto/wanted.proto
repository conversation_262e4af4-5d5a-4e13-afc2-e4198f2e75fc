syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_SyncWantedMessage {
  int32 index = 1; //悬赏下标，从0开始
}
message S2C_SyncWantedMessage {
  int32 code = 1; //0
  WantedInfo data = 2; //悬赏数据
}
message C2S_RefrehWantedMessage {
  int32 index = 1; //悬赏下标，从0开始
}
message S2C_RefrehWantedMessage {
  int32 code = 1; //0
  WantedInfo data = 2; //悬赏数据
}
message C2S_StartWantedMessage {
  int32 index = 1; //悬赏下标，从0开始
  repeated int32 roles = 2; //派遣的角色id
}
message S2C_StartWantedMessage {
  int32 code = 1; //0
}
message C2S_ClaimWantedRewardMessage {
  int32 index = 1; //悬赏下标，从0开始
}
message S2C_ClaimWantedRewardMessage {
  int32 code = 1; //0
}
message C2S_SyncAllWantedMessage {
}
message S2C_SyncAllWantedMessage {
  int32 code = 1; //
  Wanted data = 2; //
}
