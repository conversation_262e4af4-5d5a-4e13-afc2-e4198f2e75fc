syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_TicketMergeMessage {
  int32 num = 1; //合成数量
}
message S2C_TicketMergeMessage {
  int32 code = 1; //0成功，1来源物品数量不足以兑换cnt次,2目标物品或者来源物品无法被置换
}
message C2S_DropItemMessage {
  Condition item = 1; //物品
}
message S2C_DropItemMessage {
  int32 code = 1; //0成功，1来源物品数量不足以兑换cnt次,2目标物品或者来源物品无法被置换
}
message C2S_SpaceStoneLvUpMessage {
}
message S2C_SpaceStoneLvUpMessage {
  int32 code = 1; //0成功，1来源物品数量不足以兑换cnt次,2目标物品或者来源物品无法被置换
}
message C2S_UseSpaceStoneMessage {
  int32 id = 1; //标记id
}
message S2C_UseSpaceStoneMessage {
  int32 code = 1; //
}
message C2S_MarkSpaceStoneMessage {
  int32 id = 1; //标记id
  int32 removeId = 2; //替换id
}
message S2C_MarkSpaceStoneMessage {
  int32 code = 1; //
}
message C2S_SyncItemMessage {
  string uid = 1; //uid
}
message S2C_SyncItemMessage {
  int32 code = 1; //
  int32 surplusTime = 2; //剩余时间
}
message C2S_UseItemMessage {
  Condition item = 1; //物品
}
message S2C_UseItemMessage {
  int32 code = 1; //code=0代表无错误
  repeated Condition rewards = 2; //奖励列表
}
