syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_AcceptArrestMessage {
  string id = 1; //通缉令id
}
message S2C_AcceptArrestMessage {
  int32 code = 1; //
}
message C2S_SetArrestBattleResultMessage {
  string id = 1; //通缉令id
  BattleResult result = 2; //战斗结果，非0失败
}
message S2C_SetArrestBattleResultMessage {
  int32 code = 1; //
  Arrest data = 2; //触发逃跑后的通缉令信息
}
message C2S_FinishArrestMessage {
  string id = 1; //通缉令id
}
message S2C_FinishArrestMessage {
  int32 code = 1; //
}
message C2S_DestroyArrestMessage {
  string id = 1; //通缉令id
}
message S2C_DestroyArrestMessage {
  int32 code = 1; //
}
message C2S_RefreshAllArrestMessage {
}
message S2C_RefreshAllArrestMessage {
  repeated Arrest data = 1; //列表
}
message C2S_ShowArrestResultMessage {
}
message S2C_ShowArrestResultMessage {
  int32 code = 1; //
}
