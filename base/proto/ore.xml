<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="OreAction" module="game" explain="矿场格子操作">
        <field class="int32" name="x" explain="行"/>
        <field class="int32" name="y" explain="列"/>
        <field class="enum.OreCeilActionType" name="type" explain="操作类型"/>
        <field class="int32" name="level" explain="难度"/>
        <field class="int32" name="extra" explain="奖励计算参数"/>
    </message>
    <message type="S2C" name="OreAction" explain="矿场格子操作">
        <field class="enum.OreCeilActionCode" name="code" explain=""/>
        <array class="struct.OreRowData" name="data" explain="增加的数据，可能有"/>
        <array class="struct.Condition" name="rewards" explain="奖励数据，可能有"/>
    </message>
    <message type="C2S" name="OreSyncBreakItemTime" module="game" explain="同步稿子恢复时间">
    </message>
    <message type="S2C" name="OreSyncBreakItemTime" explain="矿场格子操作">
        <field class="int32" name="code" explain=""/>
        <field class="struct.ItemInfo" name="item" explain="镐子数据"/>
        <field class="int32" name="time" explain="下一次恢复倒计时"/>
    </message>
    <message type="C2S" name="OreLevelFight" module="game" explain="矿场难度挑战成功">
        <field class="int32" name="level" explain="挑战的等级"/>
    </message>
    <message type="S2C" name="OreLevelFight" explain="矿场难度挑战成功">
        <field class="int32" name="code" explain="1已经解锁难度,2不存在的难度配置"/>
    </message>
    <message type="C2S" name="GetOreLevelData" module="game" explain="获取矿场指定难度矿洞的数据">
        <field class="int32" name="level" explain="等级"/>
    </message>
    <message type="S2C" name="GetOreLevelData" explain="获取矿场指定难度矿洞的数据">
        <field class="int32" name="code" explain=""/>
        <field class="struct.OreLevelData" name="data" explain="矿洞数据"/>
    </message>
    <message type="C2S" name="UnlockOre" module="game" explain="解锁矿场">
    </message>
    <message type="S2C" name="UnlockOre" explain="解锁矿场">
        <field class="int32" name="code" explain=""/>
        <field class="struct.Ore" name="ore" explain=""/>
    </message>
</messages>