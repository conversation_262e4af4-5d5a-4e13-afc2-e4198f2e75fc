<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="StoreRefresh" module="game" explain="刷新商店">
        <field class="int32" name="id" explain="商店id"/>
    </message>
    <message type="S2C" name="StoreRefresh" explain="刷新商店">
        <field class="int32" name="code" explain="1钻石不足2刷新次数上限"/>
        <field class="struct.StoreInfo" name="info" explain="刷新后的商店数据列表"/>
    </message>
    <message type="C2S" name="StoreBuy" module="game" explain="商店购买">
        <field class="int32" name="id" explain="商店id"/>
        <field class="int32" name="pos" explain="物品位置0开始"/>
    </message>
    <message type="S2C" name="StoreBuy" explain="商店购买">
        <field class="int32" name="code" explain="1货币不足2物品库存不足3商店数据不存在"/>
        <field class="struct.Condition" name="item" explain="购买的物品"/>
    </message>
    <message type="C2S" name="SyncStore" module="game" explain="同步商店">
        <field class="int32" name="id" explain="商店id"/>
    </message>
    <message type="S2C" name="SyncStore" explain="同步商店">
        <field class="int32" name="code" explain="0"/>
        <field class="struct.StoreInfo" name="info" explain="刷新后的商店数据列表"/>
    </message>
</messages>