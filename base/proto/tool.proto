syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_ToolMakeMessage {
  int32 type = 1; //工具类型1|2|3
}
message S2C_ToolMakeRespMessage {
  int32 code = 1; //0成功1消耗品不足2其他错误3打造台等级上限
}
message C2S_FurnaceUpgradeMessage {
}
message S2C_FurnaceUpgradeRespMessage {
  int32 code = 1; //0成功1消耗不足2数据错误
  repeated Condition failList = 2; //条件check失败的列表
}
