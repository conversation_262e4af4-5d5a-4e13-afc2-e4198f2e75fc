<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="ReadyStartBlackHole" module="game" explain="预加载黑洞的一些信息">
        <field class="int32" name="level" explain="难度"/>
    </message>
    <message type="S2C" name="ReadyStartBlackHole" module="game" explain="预加载黑洞的一些信息">
        <field class="int32" name="code" explain="code"/>
        <array class="struct.BattleRole" name="boss" explain="当前难度下的boss阵容"/>
    </message>
    <message type="C2S" name="StartBlackHole" module="game" explain="开始黑洞玩法">
        <field class="int32" name="level" explain="难度"/>
        <array class="int32" name="roles" explain="选择的乘客"/>
    </message>
    <message type="S2C" name="StartBlackHole" module="game" explain="开始黑洞玩法">
        <field class="int32" name="code" explain="code"/>
        <field class="struct.BlackHole" name="blackHole" explain="黑洞数据"/>
    </message>
    <message type="C2S" name="SelectBlackHoleNode" module="game" explain="选择下一步节点">
        <field class="string" name="nodeId" explain="节点id"/>
        <field class="struct.BlackHoleBuff" name="buff" explain="选择的buff"/>
        <field class="struct.BlackHoleEquip" name="equip" explain="选择的装备"/>
        <field class="string" name="aid" explain="选择援助的角色uid"/>
        <field class="struct.BattleResult" name="battle" explain="战斗结果"/>
    </message>
    <message type="S2C" name="SelectBlackHoleNode" explain="选择下一步节点">
        <field class="int32" name="code" explain="0"/>
        <field class="string" name="curId" explain="当前id"/>
        <field class="string" name="nextId" explain="下一步id"/>
        <field class="struct.BlackHoleBuff" name="buff" explain="最终生成的buff"/>
        <field class="string" name="rebirth" explain="复活的角色uid"/>
        <field class="struct.BattleResult" name="battle" explain="战斗结果"/>
        <array class="struct.BlackHoleEquip" name="equips" explain="装备"/>
    </message>
    <message type="C2S" name="SyncBlackHole" module="game" explain="同步黑洞数据">
    </message>
    <message type="S2C" name="SyncBlackHole" module="game" explain="同步黑洞数据">
        <field class="int32" name="code" explain=""/>
        <field class="struct.BlackHole" name="blackHole" explain=""/>
    </message>
    <message type="C2S" name="UnlockBlackHole" module="game" explain="解锁黑洞">
    </message>
    <message type="S2C" name="UnlockBlackHole" module="game" explain="解锁黑洞">
        <field class="int32" name="code" explain=""/>
        <field class="struct.BlackHole" name="blackHole" explain=""/>
    </message>
</messages>