<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="TicketMerge" module="game" explain="车票碎片合成">
        <field class="int32" name="num" explain="合成数量"/>
    </message>
    <message type="S2C" name="TicketMerge" explain="合成车票">
        <field class="int32" name="code" explain="0成功，1来源物品数量不足以兑换cnt次,2目标物品或者来源物品无法被置换"/>
    </message>
    <message type="C2S" name="DropItem" module="game" explain="丢弃物品">
        <field class="struct.Condition" name="item" explain="物品"/>
    </message>
    <message type="S2C" name="DropItem" explain="丢弃物品">
        <field class="int32" name="code" explain="0成功，1来源物品数量不足以兑换cnt次,2目标物品或者来源物品无法被置换"/>
    </message>
    <message type="C2S" name="SpaceStoneLvUp" module="game" explain="空间宝石升级">
    </message>
    <message type="S2C" name="SpaceStoneLvUp" explain="空间宝石升级">
        <field class="int32" name="code" explain="0成功，1来源物品数量不足以兑换cnt次,2目标物品或者来源物品无法被置换"/>
    </message>
    <message type="C2S" name="UseSpaceStone" module="game" explain="使用空间宝石传送">
        <field class="int32" name="id" explain="标记id"/>
    </message>
    <message type="S2C" name="UseSpaceStone" explain="使用空间宝石传送">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="MarkSpaceStone" module="game" explain="使用空间宝石标记">
        <field class="int32" name="id" explain="标记id"/>
        <field class="int32" name="removeId" explain="替换id"/>
    </message>
    <message type="S2C" name="MarkSpaceStone" explain="使用空间宝石标记">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="SyncItem" module="game" explain="同步物品">
        <field class="string" name="uid" explain="uid"/>
    </message>
    <message type="S2C" name="SyncItem" explain="同步物品">
        <field class="int32" name="code" explain=""/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
    </message>
    <message type="C2S" name="UseItem" module="game" explain="使用物品">
        <field class="Condition" name="item" explain="物品"/>
    </message>
    <message type="S2C" name="UseItem" explain="使用物品">
        <field class="int32" name="code" explain="code=0代表无错误"/>
        <array class="struct.Condition" name="rewards" explain="奖励列表"/>
    </message>
</messages>