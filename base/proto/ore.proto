syntax = "proto3";

package proto;

option go_package = "./pb";

import "enum.proto";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_OreActionMessage {
  int32 x = 1; //行
  int32 y = 2; //列
  OreCeilActionType type = 3; //操作类型
  int32 level = 4; //难度
  int32 extra = 5; //奖励计算参数
}
message S2C_OreActionMessage {
  OreCeilActionCode code = 1; //
  repeated OreRowData data = 2; //增加的数据，可能有
  repeated Condition rewards = 3; //奖励数据，可能有
}
message C2S_OreSyncBreakItemTimeMessage {
}
message S2C_OreSyncBreakItemTimeMessage {
  int32 code = 1; //
  ItemInfo item = 2; //镐子数据
  int32 time = 3; //下一次恢复倒计时
}
message C2S_OreLevelFightMessage {
  int32 level = 1; //挑战的等级
}
message S2C_OreLevelFightMessage {
  int32 code = 1; //1已经解锁难度,2不存在的难度配置
}
message C2S_GetOreLevelDataMessage {
  int32 level = 1; //等级
}
message S2C_GetOreLevelDataMessage {
  int32 code = 1; //
  OreLevelData data = 2; //矿洞数据
}
message C2S_UnlockOreMessage {
}
message S2C_UnlockOreMessage {
  int32 code = 1; //
  Ore ore = 2; //
}
