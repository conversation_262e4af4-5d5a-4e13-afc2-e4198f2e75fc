<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="SyncAllBurstTask" module="game" explain="同步任务数据">
        <field class="int32" name="index" explain="任务下标,从0开始,-1同步全部"/>
    </message>
    <message type="S2C" name="SyncAllBurstTask" module="game" explain="同步任务数据">
        <field class="int32" name="code" explain=""/>
        <array class="struct.BurstTaskItem" name="data" explain=""/>
    </message>
    <message type="C2S" name="StartBurstTask" module="game" explain="开始任务">
        <field class="int32" name="index" explain="任务下标,从0开始"/>
        <array class="int32" name="roles" explain="派遣的角色id"/>
    </message>
    <message type="S2C" name="StartBurstTask" explain="开始任务">
        <field class="int32" name="code" explain="0"/>
    </message>
    <message type="C2S" name="ClaimBurstTaskReward" module="game" explain="领取任务奖励">
        <field class="int32" name="index" explain="任务下标,从0开始"/>
    </message>
    <message type="S2C" name="ClaimBurstTaskReward" explain="领取任务奖励">
        <field class="int32" name="code" explain="0"/>
    </message>
</messages>