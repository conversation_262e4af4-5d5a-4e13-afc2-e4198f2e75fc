<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="CeilOperation" module="game" explain="农场格子操作">
        <field class="int32" name="id" explain="顺位id"/>
        <field class="CeilOperationType" name="type" explain="操作类型"/>
        <field class="int32" name="powerValue" explain=""/>
    </message>
    <message type="S2C" name="CeilOperation" explain="操作结果">
        <field class="CeilOperationCode" name="code" explain=""/>
        <field class="struct.FieldCeil" name="data" explain="操作之后的格子数据"/>
        <array class="struct.Condition" name="rewards" explain="收获奖励数据"/>
    </message>
    <message type="C2S" name="CeilSync" module="game" explain="格子数据同步">
        <field class="int32" name="id" explain="顺位id"/>
    </message>
    <message type="S2C" name="CeilSync" explain="格子数据同步">
        <field class="int32" name="code" explain=""/>
        <field class="struct.FieldCeil" name="data" explain="格子数据"/>
    </message>
    <enum name="CeilOperationType" explain="操作类型">
        <type name="Invalid" number="0" explain="无效操作，因为枚举必须存在0值，可以不用但一定要有" />
        <type name="Unlock" number="1" explain="解锁格子" />
        <type name="Sow" number="2" explain="播种" />
        <type name="Water" number="3" explain="浇水" />
        <type name="Fertilizer" number="4" explain="施肥" />
        <type name="Harvest" number="5" explain="收获" />
    </enum>
    <enum name="CeilOperationCode" explain="操作结果">
        <type name="Success" number="0" explain="操作成功" />
        <type name="CeilAlreadyUnlock" number="1" explain="格子已经被解锁" />
        <type name="NotEnoughCostForUnlock" number="2" explain="解锁格子消耗不足" />
        <type name="CeilNotUnlock" number="3" explain="格子未被解锁" />
        <type name="CeilAlreadyPlant" number="4" explain="格子已经有作物" />
        <type name="CeilPlantIdError" number="5" explain="格子类型与作物类型不匹配" />
        <type name="CeilNeedNotWater" number="6" explain="格子不需要浇水(没有作物or作物成熟)" />
        <type name="NotEnoughCostForWater" number="7" explain="水能不足，无法浇水" />
        <type name="CeilNeedNotFertilizer" number="8" explain="格子不需要施肥(没有作物or作物成熟)" />
        <type name="NotEnoughCostForFertilizer" number="9" explain="肥料不足" />
        <type name="CeilNotHasPlanet" number="10" explain="格子没有作物，无法收获" />
        <type name="CeilCanNotHarvest" number="11" explain="作物未成熟，无法收获" />
        <type name="NotEnoughCostForPlant" number="12" explain="种子不足，种植失败" />
        <type name="CeilMax" number="13" explain="格子数量达到上限，解锁失败" />
        <type name="SureCeilSort" number="14" explain="格子数量解锁顺序不对" />
    </enum>
</messages>