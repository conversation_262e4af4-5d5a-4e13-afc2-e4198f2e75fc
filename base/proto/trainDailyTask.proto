syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_SyncAllTrainDailyTaskMessage {
  int32 index = 1; //列车日常任务下标,从0开始,-1同步全部
}
message S2C_SyncAllTrainDailyTaskMessage {
  int32 code = 1; //
  repeated TrainDailyTaskItem data = 2; //
}
message C2S_StartTrainDailyTaskMessage {
  int32 index = 1; //列车日常任务下标,从0开始
  repeated int32 roles = 2; //派遣的角色id
}
message S2C_StartTrainDailyTaskMessage {
  int32 code = 1; //0
}
message C2S_ClaimTrainDailyTaskRewardMessage {
  int32 index = 1; //列车日常任务下标,从0开始
}
message S2C_ClaimTrainDailyTaskRewardMessage {
  int32 code = 1; //0
}
