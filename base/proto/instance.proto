syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_InstanceFightMessage {
  int32 level = 1; //副本id
}
message S2C_InstanceFightMessage {
  int32 code = 1; //1要挑战的不是最新关卡,2要挑战的关卡不存在
  repeated Condition rewards = 2; //奖励
}
message C2S_SyncInstanceMessage {
}
message S2C_SyncInstanceMessage {
  int32 code = 1; //
  Instance data = 2; //
}
message C2S_UnlockInstanceMessage {
}
message S2C_UnlockInstanceMessage {
  int32 code = 1; //
}
message C2S_CompleteInstancePuzzleMessage {
}
message S2C_CompleteInstancePuzzleMessage {
  int32 code = 1; //
}
