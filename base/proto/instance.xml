<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="InstanceFight" module="game" explain="发起副本挑战(临时使用)">
        <field class="int32" name="level" explain="副本id"/>
    </message>
    <message type="S2C" name="InstanceFight" explain="副本挑战结果(临时使用)">
        <field class="int32" name="code" explain="1要挑战的不是最新关卡,2要挑战的关卡不存在"/>
        <array class="struct.Condition" name="rewards" explain="奖励"/>
    </message>
    <message type="C2S" name="SyncInstance" module="game" explain="同步副本数据">
    </message>
    <message type="S2C" name="SyncInstance" module="game" explain="同步副本数据">
        <field class="int32" name="code" explain=""/>
        <field class="struct.Instance" name="data" explain=""/>
    </message>
    <message type="C2S" name="UnlockInstance" module="game" explain="解锁副本">
    </message>
    <message type="S2C" name="UnlockInstance" explain="解锁副本">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="CompleteInstancePuzzle" module="game" explain="完成解密">
    </message>
    <message type="S2C" name="CompleteInstancePuzzle" explain="完成解密">
        <field class="int32" name="code" explain=""/>
    </message>
</messages>