<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="ToolMake" module="game" explain="打造工具">
        <field class="int32" name="type" explain="工具类型1|2|3"/>
    </message>
    <message type="S2C" name="ToolMakeResp" explain="工具打造结果">
        <field class="int32" name="code" explain="0成功1消耗品不足2其他错误3打造台等级上限"/>
    </message>
    <message type="C2S" name="FurnaceUpgrade" module="game" explain="打造台升级">
    </message>
    <message type="S2C" name="FurnaceUpgradeResp" explain="打造台升级结果">
        <field class="int32" name="code" explain="0成功1消耗不足2数据错误"/>
        <array class="struct.Condition" name="failList" explain="条件check失败的列表"/>
    </message>
</messages>