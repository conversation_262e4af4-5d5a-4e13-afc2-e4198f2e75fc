<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="ChangePassengerDorm" module="game" explain="修改乘客入住信息">
        <field class="int32" name="id" explain="乘客id"/>
        <field class="int32" name="index" explain="位置"/>
        <field class="int32" name="dormId" explain="车厢id, null为离开"/>
    </message>
    <message type="S2C" name="ChangePassengerDormResp" explain="修改乘客入住信息结果">
        <field class="int32" name="code" explain="code=0代表无错误，1车厢满了"/>
    </message>
    <message type="C2S" name="PassengerLevelUp" module="game" explain="乘客升级/升星">
        <field class="int32" name="type" explain="操作类型，1升级2升星"/>
        <field class="int32" name="id" explain="乘客id"/>
    </message>
    <message type="S2C" name="PassengerLevelUpResult" explain="乘客操作结果">
        <field class="int32" name="code" explain="code=0代表无错误"/>
    </message>
    <message type="C2S" name="ChangePassengerWork" module="game" explain="修改乘客工作信息">
        <field class="int32" name="id" explain="乘客id"/>
        <field class="int32" name="workId" explain="车厢id, null为离开"/>
        <field class="int32" name="workIndex" explain="车厢工位下标, null为离开"/>
    </message>
    <message type="S2C" name="ChangePassengerWork" explain="修改乘客工作信息结果">
        <field class="int32" name="code" explain="code=0代表无错误"/>
    </message>
    <message type="C2S" name="CompletePassengerPlot" module="game" explain="完成乘客剧情">
        <field class="string" name="id" explain="剧情id"/>
    </message>
    <message type="S2C" name="CompletePassengerPlot" explain="完成乘客剧情">
        <field class="int32" name="code" explain="code=0代表无错误"/>
    </message>
    <message type="C2S" name="UnlockSkin" module="game" explain="解锁乘客皮肤">
        <field class="string" name="id" explain="皮肤id"/>
    </message>
    <message type="S2C" name="UnlockSkin" explain="解锁乘客皮肤">
        <field class="int32" name="code" explain="1皮肤不存在2解锁消耗不足"/>
    </message>
    <message type="C2S" name="ChangeSkin" module="game" explain="更换乘客皮肤">
        <field class="int32" name="passengerId" explain="乘客id"/>
        <field class="int32" name="skinIndex" explain="皮肤序号"/>
    </message>
    <message type="S2C" name="ChangeSkin" explain="更换乘客皮肤">
        <field class="int32" name="code" explain="1皮肤不存在"/>
    </message>
    <message type="C2S" name="TalentLevelUp" module="game" explain="升级天赋">
        <field class="int32" name="passengerId" explain="乘客id"/>
        <field class="int32" name="id" explain="天赋id"/>
    </message>
    <message type="S2C" name="TalentLevelUp" explain="升级天赋">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="FragMerge" module="game" explain="投影合成">
        <field class="int32" name="id" explain="投影id"/>
        <field class="int32" name="num" explain="合成数量"/>
    </message>
    <message type="S2C" name="FragMerge" explain="投影合成">
        <field class="int32" name="code" explain=""/>
        <array class="struct.Condition" name="rewards" explain=""/>
    </message>
    <message type="C2S" name="PassengerUnlockProfile" module="game" explain="乘客解锁资料">
        <field class="int32" name="profileId" explain="资料id"/>
        <field class="int32" name="passengerId" explain="乘客id"/>
        <field class="int32" name="position" explain="位置"/>
    </message>
    <message type="S2C" name="PassengerUnlockProfile" explain="乘客解锁资料">
        <field class="int32" name="code" explain="code=0代表无错误"/>
    </message>
    <message type="C2S" name="TransPassenger" module="game" explain="乘客转换">
        <field class="int32" name="fromId" explain="被转换乘客id"/>
        <field class="int32" name="toId" explain="乘客id"/>
    </message>
    <message type="S2C" name="TransPassenger" explain="乘客转换">
        <field class="int32" name="code" explain="code=0代表无错误"/>
    </message>
    <message type="C2S" name="PassengerProfileSortChange" module="game" explain="乘客资料排序更换">
        <field class="int32" name="passengerId" explain="乘客id"/>
        <field class="map" name="sort" explain="排序数据">
            <key class="int32" explain="资料类型"/>
            <value class="int32" explain="位置"/>
        </field>
    </message>
    <message type="S2C" name="PassengerProfileSortChange" explain="乘客资料排序更换">
        <field class="int32" name="code" explain="code=0代表无错误"/>
    </message>
</messages>