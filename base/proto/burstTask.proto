syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_SyncAllBurstTaskMessage {
  int32 index = 1; //任务下标,从0开始,-1同步全部
}
message S2C_SyncAllBurstTaskMessage {
  int32 code = 1; //
  repeated BurstTaskItem data = 2; //
}
message C2S_StartBurstTaskMessage {
  int32 index = 1; //任务下标,从0开始
  repeated int32 roles = 2; //派遣的角色id
}
message S2C_StartBurstTaskMessage {
  int32 code = 1; //0
}
message C2S_ClaimBurstTaskRewardMessage {
  int32 index = 1; //任务下标,从0开始
}
message S2C_ClaimBurstTaskRewardMessage {
  int32 code = 1; //0
}
