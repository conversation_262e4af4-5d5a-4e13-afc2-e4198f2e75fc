<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="AcceptArrest" module="game" explain="领取通缉令任务">
        <field class="string" name="id" explain="通缉令id"/>
    </message>
    <message type="S2C" name="AcceptArrest" explain="领取通缉令任务">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="SetArrestBattleResult" module="game" explain="通缉令战斗结果">
        <field class="string" name="id" explain="通缉令id"/>
        <field class="struct.BattleResult" name="result" explain="战斗结果，非0失败"/>
    </message>
    <message type="S2C" name="SetArrestBattleResult" explain="通缉令战斗结果">
        <field class="int32" name="code" explain=""/>
        <field class="struct.Arrest" name="data" explain="触发逃跑后的通缉令信息"/>
    </message>
    <message type="C2S" name="FinishArrest" module="game" explain="通缉令任务领奖">
        <field class="string" name="id" explain="通缉令id"/>
    </message>
    <message type="S2C" name="FinishArrest" explain="通缉令任务领奖">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="DestroyArrest" module="game" explain="销毁通缉令">
        <field class="string" name="id" explain="通缉令id"/>
    </message>
    <message type="S2C" name="DestroyArrest" explain="销毁通缉令">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="RefreshAllArrest" module="game" explain="刷新通缉令">
    </message>
    <message type="S2C" name="RefreshAllArrest" explain="刷新通缉令">
        <array class="struct.Arrest" name="data" explain="列表"/>
    </message>
    <message type="C2S" name="ShowArrestResult" module="game" explain="展示战报">
    </message>
    <message type="S2C" name="ShowArrestResult" explain="展示战报">
        <field class="int32" name="code" explain=""/>
    </message>
</messages>