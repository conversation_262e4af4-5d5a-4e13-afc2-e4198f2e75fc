<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="CreatePayOrder" module="game" explain="创建订单">
        <field class="string" name="productId" explain="商品id"/>
        <field class="string" name="platform" explain="平台"/>
    </message>
    <message type="S2C" name="CreatePayOrder" explain="创建订单">
        <field class="int32" name="code" explain="0"/>
        <field class="string" name="uid" explain="内部订单号"/>
    </message>
    <message type="C2S" name="VerifyPayOrder" module="game" explain="验证订单">
        <field class="string" name="productId" explain="商品id"/>
        <field class="string" name="orderId" explain="外部订单号"/>
        <field class="string" name="cpOrderId" explain="内部订单号"/>
        <field class="string" name="token" explain="验证数据"/>
        <field class="string" name="platform" explain="支付方式"/>
        <field class="int64" name="purchaseTime" explain="支付时间"/>
        <field class="string" name="currencyType" explain="支付币种"/>
        <field class="double" name="payAmount" explain="支付金额"/>
        <field class="int32" name="quantity" explain="数量"/>
    </message>
    <message type="S2C" name="VerifyPayOrder" explain="商店购买">
        <field class="int32" name="code" explain="1货币不足2物品库存不足3商店数据不存在"/>
        <field class="string" name="uid" explain="内部订单号"/>
    </message>
    <message type="C2S" name="GetPayRewards" module="game" explain="领取订单奖励">
        <field class="string" name="uid" explain="订单id"/>
    </message>
    <message type="S2C" name="GetPayRewards" explain="领取订单奖励">
        <field class="int32" name="code" explain="0"/>
    </message>
</messages>