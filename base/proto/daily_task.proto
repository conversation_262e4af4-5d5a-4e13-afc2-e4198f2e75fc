syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_FinishDailyTaskMessage {
  int32 id = 1; //任务id -1是大奖
  repeated string extras = 2; //提交的物品的扩展
}
message S2C_FinishDailyTaskMessage {
  int32 code = 1; //code
  DailyTask task = 2; //下一个任务
}
message C2S_SyncDailyTaskInfoMessage {
}
message S2C_SyncDailyTaskInfoMessage {
  int32 code = 1; //code
  DailyTaskInfo info = 2; //0
}
message C2S_DialogTaskDoneMessage {
  int32 index = 1; //任务序号
  int32 dialogIndex = 2; //对话序号
}
message S2C_DialogTaskDoneMessage {
  int32 code = 1; //code
}
message C2S_BattleTaskDoneTestMessage {
  int32 index = 1; //任务序号
}
message S2C_BattleTaskDoneTestMessage {
  int32 code = 1; //code
}
