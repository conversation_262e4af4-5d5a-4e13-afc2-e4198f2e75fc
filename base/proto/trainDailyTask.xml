<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="SyncAllTrainDailyTask" module="game" explain="同步列车日常任务数据">
        <field class="int32" name="index" explain="列车日常任务下标,从0开始,-1同步全部"/>
    </message>
    <message type="S2C" name="SyncAllTrainDailyTask" module="game" explain="同步列车日常任务数据">
        <field class="int32" name="code" explain=""/>
        <array class="struct.TrainDailyTaskItem" name="data" explain=""/>
    </message>
    <message type="C2S" name="StartTrainDailyTask" module="game" explain="开始列车日常任务">
        <field class="int32" name="index" explain="列车日常任务下标,从0开始"/>
        <array class="int32" name="roles" explain="派遣的角色id"/>
    </message>
    <message type="S2C" name="StartTrainDailyTask" explain="开始列车日常任务">
        <field class="int32" name="code" explain="0"/>
    </message>
    <message type="C2S" name="ClaimTrainDailyTaskReward" module="game" explain="领取列车日常任务奖励">
        <field class="int32" name="index" explain="列车日常任务下标,从0开始"/>
    </message>
    <message type="S2C" name="ClaimTrainDailyTaskReward" explain="领取列车日常任务奖励">
        <field class="int32" name="code" explain="0"/>
    </message>
</messages>