<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="ProfileBranchPassNode" module="game" explain="通过节点">
        <field class="int32" name="nodeId" explain="节点id"/>
    </message>
    <message type="S2C" name="ProfileBranchPassNode" explain="通过节点">
        <field class="int32" name="code" explain="0成功"/>
        <field class="struct.ProfileBranchLevel" name="level" explain="记忆阁当前关卡"/>
    </message>
    <message type="C2S" name="ProfileBranchQuestion" module="game" explain="开始问答">
    </message>
    <message type="S2C" name="ProfileBranchQuestion" explain="开始问答">
        <field class="int32" name="code" explain="0成功"/>
        <field class="int32" name="energy" explain="体力"/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
    </message>
    <message type="C2S" name="ProfileBranchSyncEnergy" module="game" explain="同步体力">
    </message>
    <message type="S2C" name="ProfileBranchSyncEnergy" explain="同步体力">
        <field class="int32" name="code" explain="0成功"/>
        <field class="int32" name="energy" explain="体力"/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
    </message>
    <message type="C2S" name="ProfileBranchUnlock" module="game" explain="开启记忆阁">
        <field class="int32" name="id" explain="记忆阁id"/>
    </message>
    <message type="S2C" name="ProfileBranchUnlock" explain="开启记忆阁">
        <field class="int32" name="code" explain="0成功"/>
    </message>
</messages>