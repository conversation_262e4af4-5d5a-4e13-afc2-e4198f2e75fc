<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="SyncWanted" module="game" explain="同步悬赏状态">
        <field class="int32" name="index" explain="悬赏下标，从0开始"/>
    </message>
    <message type="S2C" name="SyncWanted" explain="同步悬赏状态">
        <field class="int32" name="code" explain="0"/>
        <field class="struct.WantedInfo" name="data" explain="悬赏数据"/>
    </message>
    <message type="C2S" name="RefrehWanted" module="game" explain="手动刷新悬赏">
        <field class="int32" name="index" explain="悬赏下标，从0开始"/>
    </message>
    <message type="S2C" name="RefrehWanted" explain="手动刷新悬赏">
        <field class="int32" name="code" explain="0"/>
        <field class="struct.WantedInfo" name="data" explain="悬赏数据"/>
    </message>
    <message type="C2S" name="StartWanted" module="game" explain="开始悬赏">
        <field class="int32" name="index" explain="悬赏下标，从0开始"/>
        <array class="int32" name="roles" explain="派遣的角色id"/>
    </message>
    <message type="S2C" name="StartWanted" explain="开始悬赏">
        <field class="int32" name="code" explain="0"/>
    </message>
    <message type="C2S" name="ClaimWantedReward" module="game" explain="领取悬赏奖励">
        <field class="int32" name="index" explain="悬赏下标，从0开始"/>
    </message>
    <message type="S2C" name="ClaimWantedReward" explain="领取悬赏奖励">
        <field class="int32" name="code" explain="0"/>
    </message>
    <message type="C2S" name="SyncAllWanted" module="game" explain="同步悬赏数据">
    </message>
    <message type="S2C" name="SyncAllWanted" module="game" explain="同步悬赏数据">
        <field class="int32" name="code" explain=""/>
        <field class="struct.Wanted" name="data" explain=""/>
    </message>
</messages>