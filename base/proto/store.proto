syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_StoreRefreshMessage {
  int32 id = 1; //商店id
}
message S2C_StoreRefreshMessage {
  int32 code = 1; //1钻石不足2刷新次数上限
  StoreInfo info = 2; //刷新后的商店数据列表
}
message C2S_StoreBuyMessage {
  int32 id = 1; //商店id
  int32 pos = 2; //物品位置0开始
}
message S2C_StoreBuyMessage {
  int32 code = 1; //1货币不足2物品库存不足3商店数据不存在
  Condition item = 2; //购买的物品
}
message C2S_SyncStoreMessage {
  int32 id = 1; //商店id
}
message S2C_SyncStoreMessage {
  int32 code = 1; //0
  StoreInfo info = 2; //刷新后的商店数据列表
}
