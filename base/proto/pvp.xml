<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="UpdateFormation" module="game" explain="更新竞技场阵容">
        <field class="enum.PvpType" name="type" explain="竞技场类型"/>
        <array class="int32" name="idAry" explain="乘客id"/>
    </message>
    <message type="S2C" name="UpdateFormation" explain="更新竞技场阵容">
        <field class="int32" name="code" explain="code=0代表无错误"/>
    </message>
    <message type="C2S" name="GetRankList" module="game" explain="获取竞技场排名">
        <field class="enum.PvpType" name="type" explain="竞技场类型"/>
    </message>
    <message type="S2C" name="GetRankList" explain="获取竞技场排名">
        <field class="int32" name="code" explain="code=0代表无错误"/>
        <array class="struct.PvpSimplePlayerData" name="list" explain="数据"/>
        <field class="int32" name="rank" explain="自己的排名"/>
        <field class="int32" name="score" explain="自己的分数"/>
    </message>
    <message type="C2S" name="GetRival" module="game" explain="获取竞技场对手">
        <field class="enum.PvpType" name="type" explain="竞技场类型"/>
        <field class="bool" name="refresh" explain="刷新"/>
    </message>
    <message type="S2C" name="GetRival" explain="获取竞技场对手">
        <field class="int32" name="code" explain="code=0代表无错误"/>
        <array class="struct.PvpSimplePlayerData" name="list" explain="数据"/>
    </message>
    <message type="C2S" name="PvpFight" module="game" explain="竞技场挑战">
        <field class="enum.PvpType" name="type" explain="竞技场类型"/>
        <field class="int32" name="rivalIndex" explain="对手序号"/>
        <field class="int32" name="result" explain="0平局1胜2输"/>
    </message>
    <message type="S2C" name="PvpFight" explain="竞技场挑战">
        <field class="int32" name="code" explain="code=0代表无错误"/>
        <field class="int32" name="score" explain="最新分数"/>
        <field class="int32" name="rank" explain="最新排名"/>
    </message>
    <message type="C2S" name="PvpBattleRecordList" module="game" explain="获取战绩列表">
        <field class="enum.PvpType" name="type" explain="竞技场类型"/>
    </message>
    <message type="S2C" name="PvpBattleRecordList" explain="获取战绩列表">
        <field class="int32" name="code" explain="code=0代表无错误"/>
        <array class="struct.PvpBattleRecordData" name="list" explain="战绩列表"/>
    </message>
    <message type="C2S" name="PvpBattleReplay" module="game" explain="pvp战斗重放">
        <field class="enum.PvpType" name="type" explain="竞技场类型"/>
        <field class="string" name="docId" explain="文档id"/>
    </message>
    <message type="S2C" name="PvpBattleReplay" explain="pvp战斗重放">
        <field class="int32" name="code" explain="code=0代表无错误"/>
        <array class="struct.BattleRole" name="attacker" explain="攻击方"/>
        <array class="struct.BattleRole" name="defender" explain="防守方"/>
    </message>
    <message type="C2S" name="PvpModuleData" module="game" explain="pvp模块数据">
    </message>
    <message type="S2C" name="PvpModuleData" explain="pvp模块数据">
        <field class="struct.PvpNormalData" name="normal" explain=""/>
    </message>
</messages>