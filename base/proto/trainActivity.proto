syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_GetTrainActivityMessage {
}
message S2C_GetTrainActivityMessage {
  int32 code = 1; //
  TrainActivity data = 2; //
}
message C2S_ArrangeTrainActivityMessage {
  repeated int32 ary = 1; //活动id列表
}
message S2C_ArrangeTrainActivityMessage {
  int32 code = 1; //
}
message C2S_GetTrainActivityRewardMessage {
  int32 index = 1; //序号
  bool isUnget = 2; //是否领取未领取奖励
  int32 trainId = 3; //车厢id 未领取奖励时需要
}
message S2C_GetTrainActivityRewardMessage {
  int32 code = 1; //
}
