syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_BuyCarriageMessage {
  int32 id = 1; //车厢配置id
}
message S2C_BuyCarriageResultMessage {
  int32 code = 1; //code=0代表无错误,否则就去读failList,里面存检查失败的条件列表
  repeated Condition failList = 2; //条件check失败的列表
  CarriageInfo carriage = 3; //新增的车厢
}
message C2S_GetCarriageBuildInfoMessage {
  int32 id = 1; //车厢id
}
message S2C_GetCarriageBuildInfoResMessage {
  int32 code = 1; //code=0
  int32 buildTime = 2; //建造剩余时间
  bool openDoor = 3; //是否打开猫猫门
}
message C2S_OpenCarriageDoorMessage {
  int32 id = 1; //车厢id
}
message S2C_OpenCarriageDoorResMessage {
  int32 code = 1; //code=0
}
message C2S_BuildLevelUpMessage {
  int32 carriageId = 1; //车厢id
  int32 order = 2; //设施序号
}
message S2C_BuildLevelUpMessage {
  int32 code = 1; //code=0
}
message C2S_ChangeBuildSkinMessage {
  int32 carriageId = 1; //车厢id
  int32 order = 2; //设施序号
  int32 skin = 3; //设施皮肤
}
message S2C_ChangeBuildSkinMessage {
  int32 code = 1; //code=0
}
message C2S_CarriageThemeLvUpMessage {
  int32 carriageId = 1; //车厢id
}
message S2C_CarriageThemeLvUpMessage {
  int32 code = 1; //code=0
}
message C2S_UnlockGoodsMessage {
  string id = 1; //id
  repeated Condition extra = 2; //扩展列表
}
message S2C_UnlockGoodsMessage {
  int32 code = 1; //
}
message C2S_LevelUpGoodsMessage {
  string id = 1; //id
  int32 level = 2; //等级
}
message S2C_LevelUpGoodsMessage {
  int32 code = 1; //
}
