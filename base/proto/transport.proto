syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_TransportStartMessage {
  int32 index = 1; //list下标
}
message S2C_TransportStartMessage {
  int32 code = 1; //1不存在的任务
}
message C2S_TransportFightMessage {
  int32 planetId = 1; //星球id，通过目的地区分任务
  bool succ = 2; //挑战成功
}
message S2C_TransportFightMessage {
  int32 code = 1; //1不存在的任务
}
message C2S_TransportRewardGetMessage {
  int32 index = 1; //list下标
}
message S2C_TransportRewardGetMessage {
  int32 code = 1; //1任务不存在，2奖励已经领取，3任务未完成
}
message C2S_TransportBackMessage {
}
message S2C_TransportBackMessage {
  int32 code = 1; //1没有任务，2任务未失败
}
message C2S_SyncTransportMessage {
}
message S2C_SyncTransportMessage {
  int32 code = 1; //code
  Transport transport = 2; //0
}
