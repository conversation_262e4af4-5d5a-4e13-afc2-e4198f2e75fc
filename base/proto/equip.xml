<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="WearEquip" module="game" explain="穿戴装备">
        <field class="string" name="uid" explain="装备唯一id"/>
    </message>
    <message type="S2C" name="WearEquip" explain="穿戴装备结果">
        <field class="int32" name="code" explain="1找不到装备"/>
    </message>
    <message type="C2S" name="UnWearEquip" module="game" explain="卸下装备">
        <field class="string" name="uid" explain="装备唯一id"/>
    </message>
    <message type="S2C" name="UnWearEquip" explain="卸下装备结果">
        <field class="int32" name="code" explain="1找不到穿戴中的装备"/>
    </message>
    <message type="C2S" name="MakeEquip" module="game" explain="打造装备">
        <field class="int32" name="id" explain="装备id"/>
        <field class="int32" name="tableId" explain="打造台id"/>
    </message>
    <message type="S2C" name="MakeEquip" explain="打造装备">
        <field class="int32" name="code" explain="0"/>
        <field class="struct.EquipItem" name="equip" explain="装备"/>
    </message>
    <message type="C2S" name="BuyEquip" module="game" explain="购买装备">
        <field class="int32" name="id" explain="装备id"/>
        <field class="int32" name="level" explain="装备等级"/>
    </message>
    <message type="S2C" name="BuyEquip" explain="购买装备">
        <field class="int32" name="code" explain="0"/>
        <field class="struct.EquipItem" name="equip" explain="装备"/>
    </message>
    <message type="C2S" name="SellEquip" module="game" explain="出售装备">
        <array class="string" name="uid" explain="装备id"/>
    </message>
    <message type="S2C" name="SellEquip" explain="出售装备">
        <field class="int32" name="code" explain="0"/>
    </message>
</messages>