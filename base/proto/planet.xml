<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="TrainNavigation" module="game" explain="申请航行至某个星球">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="bool" name="plus" explain="使用次元晶石"/>
    </message>
    <message type="S2C" name="TrainNavigationResult" explain="星球航行结果">
        <field class="int32" name="code" explain="1当前正在航行,2该星球已经探索完毕,3星球id配置读取错误,4星球前置条件未达到,5次元晶石不足"/>
        <field class="int32" name="time" explain="航行结束剩余时间>=0"/>
    </message>
    <message type="C2S" name="GetPlanetMoveSurplusTime" module="game" explain="获取星球航行剩余时间">
    </message>
    <message type="S2C" name="GetPlanetMoveSurplusTimeResp" explain="获取星球航行剩余时间">
        <field class="int32" name="code" explain="0"/>
        <field class="int32" name="time" explain="剩余时间"/>
    </message>
    <message type="C2S" name="LandPlanet" module="game" explain="登陆星球">
        <field class="int32" name="planetId" explain="星球id"/>
    </message>
    <message type="S2C" name="LandPlanetResp" explain="登陆星球结果">
        <field class="int32" name="code" explain="1正在航行中,时间没到"/>
    </message>
    <message type="C2S" name="ChapterPass" module="game" explain="请求通过关卡节点">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
    </message>
    <message type="S2C" name="ChapterPassResult" explain="通过关卡节点响应">
        <field class="int32" name="code" explain=""/>
        <field class="int32" name="mapId" explain="当前地图id,如果是-1就代表星球探索完毕"/>
        <field class="int32" name="nextId" explain="下一个节点的id,可能为空(可能会涉及到当前地图上限，需要切换地图)"/>
        <array class="struct.Condition" name="rewards" explain="奖励"/>
    </message>
    <message type="C2S" name="PassBranchPlanetNode" module="game" explain="通过星球支线节点">
        <field class="string" name="branchId" explain="支线id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
    </message>
    <message type="S2C" name="PassBranchPlanetNode" explain="通过星球支线节点">
        <field class="int32" name="code" explain=""/>
        <field class="int32" name="mapId" explain="当前地图id,如果是-1就代表星球探索完毕"/>
        <field class="int32" name="nextId" explain="下一个节点的id,可能为空(可能会涉及到当前地图上限，需要切换地图)"/>
        <array class="struct.Condition" name="rewards" explain="奖励"/>
    </message>
    <message type="C2S" name="ClaimBranchPlanetNodeReward" module="game" explain="领取星球节点内的奖励">
        <field class="string" name="branchId" explain="支线id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
        <field class="int32" name="index" explain="奖励下标"/>
    </message>
    <message type="S2C" name="ClaimBranchPlanetNodeReward" explain="领取星球节点内的奖励">
        <field class="int32" name="code" explain="0"/>
    </message>
    <message type="C2S" name="CancelMoveToPlanet" module="game" explain="取消航行">
    </message>
    <message type="S2C" name="CancelMoveToPlanet" explain="取消航行">
        <field class="int32" name="code" explain="0"/>
    </message>
    <message type="C2S" name="UnlockProfile" module="game" explain="解锁星球贴纸">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="id" explain="贴纸id"/>
        <field class="int32" name="index" explain="位置"/>
    </message>
    <message type="S2C" name="UnlockProfile" explain="解锁星球贴纸">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="ProfileCollectReward" module="game" explain="领取星球贴纸奖励">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="area" explain="星球区域"/>
        <field class="int32" name="step" explain="进度"/>
    </message>
    <message type="S2C" name="ProfileCollectReward" explain="领取星球贴纸奖励">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="PlanetProfileSortChange" module="game" explain="星球资料排序更换">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="map" name="sort" explain="排序数据">
            <key class="int32" explain="资料id"/>
            <value class="int32" explain="位置"/>
        </field>
    </message>
    <message type="S2C" name="PlanetProfileSortChange" explain="星球资料排序更换">
        <field class="int32" name="code" explain="code=0代表无错误"/>
    </message>
    <message type="C2S" name="ChapterPassRandomBox" module="game" explain="请求通过关卡节点">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
    </message>
    <message type="S2C" name="ChapterPassRandomBox" explain="通过关卡节点响应">
        <field class="int32" name="code" explain=""/>
        <field class="int32" name="mapId" explain="当前地图id,如果是-1就代表星球探索完毕"/>
        <field class="int32" name="nextId" explain="下一个节点的id,可能为空(可能会涉及到当前地图上限，需要切换地图)"/>
        <array class="struct.Condition" name="rewards" explain="奖励"/>
    </message>
    <message type="C2S" name="ChapterStartTimeLimitBox" module="game" explain="请求通过关卡节点">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
    </message>
    <message type="S2C" name="ChapterStartTimeLimitBox" explain="通过关卡节点响应">
        <field class="int32" name="code" explain=""/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
    </message>
    <message type="C2S" name="ChapterSyncTimeLimitBox" module="game" explain="请求同步时间">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
        <field class="int32" name="clicks" explain="点击次数"/>
    </message>
    <message type="S2C" name="ChapterSyncTimeLimitBox" explain="同步时间响应">
        <field class="int32" name="code" explain=""/>
        <field class="int32" name="state" explain="状态"/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
        <array class="struct.Condition" name="rewards" explain="奖励"/>
    </message>
    <message type="C2S" name="ChapterPassTimeLimitBox" module="game" explain="请求通过关卡节点">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
    </message>
    <message type="S2C" name="ChapterPassTimeLimitBox" explain="通过关卡节点响应">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="ChapterPassMonsterBox" module="game" explain="请求通过关卡节点">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
        <field class="int32" name="num" explain="受击次数"/>
    </message>
    <message type="S2C" name="ChapterPassMonsterBox" explain="通过关卡节点响应">
        <field class="int32" name="code" explain=""/>
        <array class="struct.Condition" name="rewards" explain="奖励"/>
    </message>
    <message type="C2S" name="ChapterPassToolBless" module="game" explain="请求通过关卡节点">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
    </message>
    <message type="S2C" name="ChapterPassToolBless" explain="通过关卡节点响应">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="ChapterPassRageMode" module="game" explain="请求通过关卡节点">
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="节点id"/>
    </message>
    <message type="S2C" name="ChapterPassRageMode" explain="通过关卡节点响应">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="DoPublicity" module="game" explain="进行宣传">
        <field class="int32" name="planetId" explain="星球id"/>
    </message>
    <message type="S2C" name="DoPublicity" explain="宣传响应">
        <field class="int32" name="code" explain=""/>
        <field class="int32" name="num" explain="招募人口数"/>
        <array class="struct.Condition" name="rewards" explain="奖励列表"/>
        <field class="int32" name="publicityUnGetOutputTime" explain="未领取的产出时长"/>
    </message>
    <message type="C2S" name="GetPublicityReward" module="game" explain="领取宣传奖励">
        <field class="int32" name="planetId" explain="星球id,-1是全部"/>
    </message>
    <message type="S2C" name="GetPublicityReward" explain="领取宣传奖励">
        <field class="int32" name="code" explain=""/>
        <array class="struct.Condition" name="rewards" explain="奖励列表"/>
        <field class="map" name="timeMap" explain="时间数据">
            <key class="int32" explain="星球id"/>
            <value class="int32" explain="未领取的产出时长"/>
        </field>
    </message>
</messages>