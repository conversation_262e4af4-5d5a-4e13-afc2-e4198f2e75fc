syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_ChangePassengerDormMessage {
  int32 id = 1; //乘客id
  int32 index = 2; //位置
  int32 dormId = 3; //车厢id, null为离开
}
message S2C_ChangePassengerDormRespMessage {
  int32 code = 1; //code=0代表无错误，1车厢满了
}
message C2S_PassengerLevelUpMessage {
  int32 type = 1; //操作类型，1升级2升星
  int32 id = 2; //乘客id
}
message S2C_PassengerLevelUpResultMessage {
  int32 code = 1; //code=0代表无错误
}
message C2S_ChangePassengerWorkMessage {
  int32 id = 1; //乘客id
  int32 workId = 2; //车厢id, null为离开
  int32 workIndex = 3; //车厢工位下标, null为离开
}
message S2C_ChangePassengerWorkMessage {
  int32 code = 1; //code=0代表无错误
}
message C2S_CompletePassengerPlotMessage {
  string id = 1; //剧情id
}
message S2C_CompletePassengerPlotMessage {
  int32 code = 1; //code=0代表无错误
}
message C2S_UnlockSkinMessage {
  string id = 1; //皮肤id
}
message S2C_UnlockSkinMessage {
  int32 code = 1; //1皮肤不存在2解锁消耗不足
}
message C2S_ChangeSkinMessage {
  int32 passengerId = 1; //乘客id
  int32 skinIndex = 2; //皮肤序号
}
message S2C_ChangeSkinMessage {
  int32 code = 1; //1皮肤不存在
}
message C2S_TalentLevelUpMessage {
  int32 passengerId = 1; //乘客id
  int32 id = 2; //天赋id
}
message S2C_TalentLevelUpMessage {
  int32 code = 1; //
}
message C2S_FragMergeMessage {
  int32 id = 1; //投影id
  int32 num = 2; //合成数量
}
message S2C_FragMergeMessage {
  int32 code = 1; //
  repeated Condition rewards = 2; //
}
message C2S_PassengerUnlockProfileMessage {
  int32 profileId = 1; //资料id
  int32 passengerId = 2; //乘客id
  int32 position = 3; //位置
}
message S2C_PassengerUnlockProfileMessage {
  int32 code = 1; //code=0代表无错误
}
message C2S_TransPassengerMessage {
  int32 fromId = 1; //被转换乘客id
  int32 toId = 2; //乘客id
}
message S2C_TransPassengerMessage {
  int32 code = 1; //code=0代表无错误
}
message C2S_PassengerProfileSortChangeMessage {
  int32 passengerId = 1; //乘客id
  map<int32,int32> sort = 2; //排序数据
}
message S2C_PassengerProfileSortChangeMessage {
  int32 code = 1; //code=0代表无错误
}
