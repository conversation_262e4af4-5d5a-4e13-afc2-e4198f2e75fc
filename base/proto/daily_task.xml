<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="FinishDailyTask" module="game" explain="完成每日任务，领取奖励">
        <field class="int32" name="id" explain="任务id -1是大奖"/>
        <array class="string" name="extras" explain="提交的物品的扩展"/>
    </message>
    <message type="S2C" name="FinishDailyTask" explain="完成每日任务">
        <field class="int32" name="code" explain="code"/>
        <field class="struct.DailyTask" name="task" explain="下一个任务"/>
    </message>
    <message type="C2S" name="SyncDailyTaskInfo" module="game" explain="同步每日任务模块数据">
    </message>
    <message type="S2C" name="SyncDailyTaskInfo" explain="同步每日任务模块数据">
        <field class="int32" name="code" explain="code"/>
        <field class="struct.DailyTaskInfo" name="info" explain="0"/>
    </message>
    <message type="C2S" name="DialogTaskDone" module="game" explain="完成任务对话">
        <field class="int32" name="index" explain="任务序号"/>
        <field class="int32" name="dialogIndex" explain="对话序号"/>
    </message>
    <message type="S2C" name="DialogTaskDone" explain="完成任务对话">
        <field class="int32" name="code" explain="code"/>
    </message>
    <message type="C2S" name="BattleTaskDoneTest" module="game" explain="完成战斗任务">
        <field class="int32" name="index" explain="任务序号"/>
    </message>
    <message type="S2C" name="BattleTaskDoneTest" explain="完成战斗任务">
        <field class="int32" name="code" explain="code"/>
    </message>
</messages>